import os
import re
import json
import requests
import shutil

# Ruta del directorio donde está ubicado el script
output_dir = os.path.dirname(__file__)
# Ruta del directorio que contiene los templates
template_dir = os.path.join(output_dir, '..', '..', 'muaytax', 'templates')

# Ruta para guardar el archivo JSON que mapea las URLs de CDN a rutas locales
output_file = os.path.join(output_dir, 'cdnscss_templates_mapping.json')
# Ruta para guardar el archivo JSON de errores y exclusiones
errors_mapping_file = os.path.join(output_dir, 'cdnscss_errors.json')

# Lista de URLs a excluir
exclusion_list = [
    # "https://use.fontawesome.com/releases/v6.2.1/css/all.css",
    # "https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.css",
    # "https://cdn.datatables.net/2.0.7/css/dataTables.bootstrap5.min.css",
    # Agrega más URLs a excluir aquí
]

# Expresión regular para detectar URLs de CDN en los templates
cdn_pattern = re.compile(r'https?://[^\s\'"]+', re.IGNORECASE | re.DOTALL)
# Expresión regular para detectar versiones numéricas en las URLs
version_pattern = re.compile(r'\b\d+(\.\d+)*\b')

# Diccionario para almacenar el mapeo de URLs de CDN a rutas locales
css_mapping = {'css': {}}
# Lista para almacenar los errores encontrados durante la descarga
errors = []
# Diccionario para almacenar las URLs excluidas
excluded_urls = {}
# Diccionario para almacenar los errores en el mapeo de CDN y las exclusiones
errors_mapping = {'css': {}, 'excluidos': {}}

# Función para crear un nombre de archivo único si ya existe un archivo con el mismo nombre
def generate_unique_filename(folder, base_name, ext):
    counter = 1
    potential_name = f"{base_name}.{ext}"
    while os.path.exists(os.path.join(folder, potential_name)):
        potential_name = f"{base_name}-v{counter}.{ext}"
        counter += 1
    return potential_name

# Función para extraer la clave de agrupación (dominio principal) de una URL
def extract_group_key(url):
    match = re.search(r'https?://([^/]+)', url)
    if match:
        domain = match.group(1)
        group_key = domain.split('.')[0]
        return group_key
    return "unknown"

# Función para extraer el tipo de archivo basado en el nombre base del archivo
def get_type(path):
    match = re.match(r'^[a-zA-Z]+', path)
    if match:
        return match.group(0)
    return ""

# Función para descargar un archivo desde una URL y guardarlo en una ruta específica
def download_file(url, local_path):
    try:
        # Asegurarse de que el directorio existe
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        response = requests.get(url)
        response.raise_for_status()  # Verificar si la solicitud fue exitosa (status code 200)
        with open(local_path, 'wb') as file:  # Modo escritura binaria
            file.write(response.content)
        print(f"Archivo descargado y guardado en {local_path}")
        return True
    except requests.exceptions.RequestException as e:
        errors.append({"url": url, "error": str(e)})
        return False

# Función para procesar recursos adicionales (URLs dentro de archivos CSS)
def process_additional_resources(local_path, base_dir, group, file_type, url):
    additional_url_pattern = re.compile(r'url\(["\']?(.*?)["\']?\)', re.IGNORECASE)
    success = True
    
    with open(local_path, 'r', encoding='utf-8') as file:  # Modo lectura
        content = file.read()
        matches = additional_url_pattern.findall(content)  # Buscar URLs dentro de los recursos CSS
        
        for match in matches:
            # Excluir rutas que comienzan con "data:" o no son URLs válidas
            if match.startswith(('data:', 'http://', 'https://')) or not match:
                continue

            # Construir URL completa relativa a la ubicación del archivo original
            match = os.path.join(os.path.dirname(url), match).replace("\\", "/")

            file_url = match.split('?')[0]  # Eliminar parámetros de consulta
            file_name = os.path.basename(file_url)  # Obtener el nombre del archivo
            relative_path = os.path.relpath(os.path.dirname(file_url), os.path.dirname(url))  # Ruta relativa

            # Construir la ruta completa donde se guardará el archivo
            new_dir = os.path.join(base_dir, group, file_type, relative_path)
            new_path = os.path.join(new_dir, file_name)
            
            if not download_file(file_url, new_path):
                success = False
                errors.append({"url": file_url, "error": "Failed to download additional resource"})
        
        # Si todas las descargas fueron exitosas, guardar el contenido actualizado
        if success:
            with open(local_path, 'w', encoding='utf-8') as file:
                file.write(content)
        return success

# Crear el directorio base si no existe
def create_base_dir(base_dir):
    try:
        os.makedirs(base_dir, exist_ok=True)
        print(f"Directorio base creado o ya existe: {base_dir}")
    except OSError as e:
        print(f"Error al crear el directorio base: {e}")
        raise

# --- Primera parte: Recorrer los archivos de los templates para encontrar y mapear URLs de CDNs ---
for root, dirs, files in os.walk(template_dir):
    for file in files:
        if file.endswith(".html"):  # Solo se consideran archivos HTML
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                matches = cdn_pattern.findall(content)  # Buscar todas las URLs de CDN
                for url in matches:
                    # Excluir URLs en la lista de exclusión
                    if any(url.startswith(exclusion) for exclusion in exclusion_list):
                        if url not in excluded_urls:
                            excluded_urls[url] = {"veces_encontrado": 1}
                        else:
                            excluded_urls[url]["veces_encontrado"] += 1
                        continue
                    
                    filename = url.split('/')[-1]  # Obtener el nombre del archivo desde la URL
                    file_ext = filename.split('.')[-1]  # Obtener la extensión del archivo
                    
                    # Buscar si hay una versión en la URL
                    version_match = version_pattern.search(url)
                    if version_match:
                        version = f"-v{version_match.group(0)}"
                    else:
                        version = ""
                    
                    # Determinar si el archivo es CSS
                    if file_ext == 'css':
                        group = 'css'
                    else:
                        continue  # Ignorar archivos que no sean .css
                    
                    sub_group = extract_group_key(url)  # Extraer el grupo clave (dominio principal)
                    
                    # Inicializar el subgrupo si no existe en el mapeo
                    if sub_group not in css_mapping[group]:
                        css_mapping[group][sub_group] = {}
                    
                    base_name = filename.rsplit('.', 1)[0]  # Obtener el nombre base del archivo sin extensión
                    file_type = get_type(base_name)  # Obtener el tipo de archivo basado en su nombre base

                    new_filename = f"{file_type}/{base_name}{version}.{file_ext}"  # Crear el nuevo nombre de archivo
                    
                    # Si el nombre de archivo ya existe, generar uno único
                    if new_filename in css_mapping[group][sub_group].values():
                        new_filename = generate_unique_filename(os.path.join(group, sub_group), base_name, file_ext)
                    
                    # Crear la ruta local para el archivo mapeado
                    local_path = os.path.join('assets', 'cdns_locals', group, new_filename).replace("\\", "/")
                    
                    # Descargar el archivo antes de agregarlo al mapeo
                    if download_file(url, local_path):
                        css_mapping[group][sub_group][url] = local_path
                    else:
                        # Si no se pudo descargar, agregarlo a errors_mapping
                        if sub_group not in errors_mapping[group]:
                            errors_mapping[group][sub_group] = {}
                        errors_mapping[group][sub_group][url] = local_path

# Guardar el mapeo en un archivo JSON
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(css_mapping, f, indent=4)

print(f"Todos los CDNs de CSS encontrados y mapeados en {output_file}")

# --- Segunda parte: Crear estructura de archivos y descargar el contenido ---

# Definir la ruta base donde se crearán las carpetas css dentro de muaytax/static/assets
base_dir = os.path.join(output_dir, '..', '..', 'muaytax', 'static', 'assets', 'cdns_locals')

# Crear las carpetas y archivos según el JSON, y descargar el contenido de las URLs
def create_structure_from_json(json_file, base_dir):
    with open(json_file, 'r', encoding='utf-8') as f:
        cdn_mapping = json.load(f)

    for group in cdn_mapping:
        for sub_group in list(cdn_mapping[group]):
            for url, local_path in list(cdn_mapping[group][sub_group].items()):
                file = os.path.basename(local_path)  # Obtener el nombre del archivo
                file_type = get_type(file)  # Obtener el tipo de archivo basado en su nombre base

                # Definir la nueva ruta con la estructura deseada
                new_dir = os.path.join(base_dir, group, file_type)
                new_path = os.path.join(new_dir, file)

                # Descargar y guardar el archivo desde la URL
                if download_file(url, new_path):
                    # Procesar recursos adicionales si los hay
                    if process_additional_resources(new_path, base_dir, group, file_type, url):
                        # Crear el directorio si no existe
                        os.makedirs(new_dir, exist_ok=True)
                    else:
                        # Si la descarga de recursos adicionales falla, eliminar el archivo local y omitir sustitución
                        os.remove(new_path)
                        del cdn_mapping[group][sub_group][url]
                else:
                    del cdn_mapping[group][sub_group][url]

    print(f"Carpetas y archivos CSS creados y contenidos descargados con éxito en {base_dir}")

    # Eliminar la estructura temporal de la primera parte
    temp_dir = os.path.join(output_dir, 'assets')
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
        print(f"Estructura temporal en {temp_dir} eliminada.")

# Crear la estructura desde el JSON generado
create_structure_from_json(output_file, base_dir)

# Guardar los errores y exclusiones en un archivo JSON
if excluded_urls or errors:
    if excluded_urls:
        errors_mapping['excluidos'] = excluded_urls
    with open(errors_mapping_file, 'w', encoding='utf-8') as f:
        json.dump(errors_mapping, f, indent=4)
    print(f"Archivo JSON con el mapeo de errores y excluidos guardado en: {errors_mapping_file}")
else:
    print("Proceso completado sin errores ni exclusiones.")

# --- Tercera parte: Reemplazar URLs en templates ---

# Cargar el mapeo de URLs desde el archivo JSON
with open(output_file, 'r', encoding='utf-8') as f:
    cdn_mapping = json.load(f)

files_modified_count = 0  # Contador de archivos modificados

# Función para reemplazar las URLs en el contenido del template
def replace_urls_in_content(content, cdn_mapping):
    modified = False
    for group in cdn_mapping:
        for sub_group in cdn_mapping[group]:
            for cdn_url, local_url in cdn_mapping[group][sub_group].items():
                # Reemplazar la URL CDN por la versión local con STATIC_URL
                replacement = f"{{{{ STATIC_URL }}}}{local_url}"
                if cdn_url in content:
                    content = content.replace(cdn_url, replacement)
                    modified = True  # Marcar que el archivo ha sido modificado
    return content, modified

# Recorrer los archivos de los templates y aplicar el reemplazo de URLs
for root, dirs, files in os.walk(template_dir):
    for file in files:
        if file.endswith(".html"):
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            new_content, modified = replace_urls_in_content(content, cdn_mapping)

            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                files_modified_count += 1  # Incrementar el contador de archivos modificados

# Imprimir el número de archivos modificados
print(f"Total de archivos CSS modificados: {files_modified_count}")
