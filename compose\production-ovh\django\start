#!/bin/bash
echo "###########"
echo "#  START  #"
echo "###########"

set -o errexit
set -o pipefail
set -o nounset

# Set DJANGO_ENV
export DJANGO_ENV=prod

# DJANGO_LOADING_DATA is a flag to load data from json file
export DJANGO_LOADING_DATA=1

if [ -d "import-db" ]; then
    echo " "
    echo " "
    echo "######################################################"
    echo "# IMPORT DATABASE BACKUP                             #"
    echo "######################################################"

    echo "Flush database"
    python manage.py flush --noinput

    echo "Load database From Multiple Files"
    python manage.py loaddata --ignorenonexistent --exclude auth.permission --exclude contenttypes import-db/*.json
    
    echo "Remove import-db folder"
    rm -rf import-db
elif [ -f "import-db.json" ]; then
    echo " "
    echo " "
    echo "######################################################"
    echo "# IMPORT DATABASE BACKUP                             #"
    echo "######################################################"

    echo "Flush database"
    python manage.py flush --noinput

    echo "Load database From One File"
    python manage.py loaddata --ignorenonexistent --exclude auth.permission --exclude contenttypes import-db.json
    
    echo "Remove import-db.json"
    rm -rf import-db.json
fi

# Unset DJANGO_LOADING_DATA
unset DJANGO_LOADING_DATA

echo " "
echo " "
echo "######################################################"
echo "# Migrate                                            #"
echo "######################################################"
python manage.py migrate
echo "Completed ✅"


# echo " "
# echo " "
# echo "######################################################"
# echo "# Compile Messages (Translations)                    #"
# echo "######################################################"
# python manage.py compilemessages
# echo "Completed ✅"


echo " "
echo " "
echo "######################################################"
echo "# Collect Static Files                               #"
echo "######################################################"
python /app/manage.py collectstatic --noinput
echo "Completed ✅"


echo " "
echo " "
echo "######################################################"
echo "# Starting Gunicorn WSGI Server                      #"
echo "######################################################"
exec gunicorn config.wsgi --bind 0.0.0.0:8000 --workers 4 --timeout 36060 --limit-request-line 0 --reload


echo " "
echo " "
echo "#########"
echo "#  END  #"
echo "#########"
echo " "
echo " "

# Kevin OLD Config
# python /app/manage.py collectstatic --noinput
# /usr/local/bin/gunicorn config.wsgi --bind 0.0.0.0:5000 --chdir=/app