import json
import traceback
from celery import shared_task, group
from celery.signals import task_prerun

from django.conf import settings
from django.db.models import OuterRef, Subquery, Q, Value, Count

from muaytax.celery import app
from muaytax.app_ocr.utils import UpdateInvoiceFromOCR
from muaytax.app_ocr.controller import MuaytaxOCRProcessor
from muaytax.app_ocr.choices import ProcessingDocument

from muaytax.app_invoices.models import Invoice
from muaytax.app_sellers.models import Seller
from muaytax.app_invoices.electronic_invoice.veriFactu import *
# from muaytax.app_invoices.electronic_invoice.veriFactu import send_invoice_toVeriFactu_auto

@app.task(queue='invoice_processing')
def process_invoices_with_ocr(invoice_pks: list):
    """Processes the invoices with OCR using their primary keys."""

    try:
        invoices = Invoice.objects.filter(pk__in=invoice_pks)
        if not invoices.exists():
            print(f"\r\033[91mNo se encontraron facturas con los ids {invoice_pks}\033[0m")
            return False

        seller = invoices.first().seller
        processor = MuaytaxOCRProcessor(ProcessingDocument.INVOICE, seller=seller)

        for invoice in invoices:
            try:
                print(f"\r\033[92m**********Procesando la factura {invoice.pk} con OCR******\033[0m")
                extracted_text = processor.extract_text(invoice.file.path)
                json_output = processor.process_extracted_text(extracted_text)

                update_invoice = UpdateInvoiceFromOCR(invoice, json_output, extracted_text)
                update_invoice.process()

                print(f"\r\033[92m**********Factura {invoice.pk} procesada con éxito**********\033[0m")

            except Exception as e:
                print(f"\r\033[91mError al procesar la factura {invoice.pk}, por el motivo: {e}\033[0m")

        return True
    
    except Exception as e:
        print(f"\r\033[91mError general en la función para procesar facturas: {e}\033[0m")
        return False

@shared_task
def auto_invoice_toVeriFactu(invoice, **kwargs):
    print("Empieza la función de VeriFactu")
    # if settings.IS_PRODUCTION:
    send_invoice_toVeriFactu_auto(invoice, kwargs)
    print("Termina la función de VeriFactu")

@shared_task
def process_invoices_batch(jsonArgs={}, offset=0, batch_size=1000):

    """
    Función que envía lotes de facturas a Verifactu automáticamente.
    Se trata de una función recursiva que envía lotes de facturas mientras haya facturas pendientes de enviar.
    """

    print("Empieza la funcion de procesamiento de facturas por lotes")
    

    seller = Seller.objects.get(id=jsonArgs.get('filters_qs').get('seller_id', None))

    # Subconsulta base: último registro válido por factura
    latest_verifactu_base = VerifactuInv.objects.filter(
        seller=seller,
        invoice=OuterRef('pk'),
        status_in_verifactu__in=['Correcto', 'AceptadoConErrores'],
    ).order_by('-created_at')

    # Dependiendo de si se quiere cancelar o no, se filtran las facturas
    qs_invoices = Invoice.objects.annotate(
        latest_verifactu_id=Subquery(latest_verifactu_base.values('id')[:1]),
        latest_verifactu_operation_type=Subquery(latest_verifactu_base.values('operation_type')[:1]),
    ).filter(
        latest_verifactu_id__isnull = not jsonArgs.get('isCancel'),
        **jsonArgs.get('filters_qs')
    ).exclude(**jsonArgs.get('exlude_qs_copy'))\
    .exclude(**jsonArgs.get('exlude_qs_trans'))

    # Si se pretende cancelar facturas, solo se pueden cancelar las que están en estado 'Alta'
    if jsonArgs.get('isCancel'):
        qs_invoices = qs_invoices.filter(latest_verifactu_operation_type='Alta')
    
    qs_invoices = qs_invoices.order_by('id')
    qs_invoices_ref = list(qs_invoices)


    if offset == 0 and len(qs_invoices_ref) == 0: # Estamos en la primera llamada de la función actualizamos que no hay facturas pendientes
        jsonArgs.update({"avoid_sending": "El txt no contiene facturas pendientes que deban enviarse a VeriFactu.",})
        update_processing_statusTXT(qs_invoices_ref, offset, batch_size, jsonArgs)
        return


    invoices = list(qs_invoices[offset:offset + batch_size])


    if not invoices:
        print("✅ Proceso finalizado.")
        return "Done"
    
    update_processing_statusTXT(qs_invoices_ref, offset, batch_size, jsonArgs)

    print(f"Procesando batch de {len(invoices)} desde offset {offset}")

    veriFactu = VeriFactu()
    try:
        xml_content, data_set = veriFactu.generate_batch_xml(seller, invoices, {"isCancel": jsonArgs.get('isCancel', False)})
    except Exception as e:
        jsonArgs.update({
                "error_processing": {
                "type": type(e).__name__,
                "message": str(e),
                "traceback": traceback.format_exc()
            }
        })
        update_processing_statusTXT(qs_invoices_ref, offset, batch_size, jsonArgs)
        return f"Error al generar el XML: {e}"

    verify_cert, cert_path, cert_password, _ = verify_cert_digital(seller)
    if not verify_cert:
        _, cert_path, cert_password, _ = get_muaytax_cert()

    try:
        response = send_xml_VeriFactu_to_AEAT(xml_content, cert_path, cert_password)

        response_dict = response_from_VeriFactu(data_set, response, invoices, jsonArgs)

        status_response = response_dict.get('RespuestaRegFactuSistemaFacturacion', '').get('EstadoEnvio', '') if response_dict.get('error', None) is None else ''
        status_response = status_response if status_response !='' and status_response is not None else 'error'

        update_processing_statusTXT(qs_invoices_ref, offset, batch_size, jsonArgs, status_response)
        
        if response_dict.get('error') or response_dict.get('RespuestaRegFactuSistemaFacturacion').get('EstadoEnvio') == 'Incorrecto':
            return send_error_emailVeriFactu_to_IT(qs_invoices_ref, response_dict, response.content)
        
    except Exception as e:
        tb = traceback.format_exc()
        send_error_emailVeriFactu_to_IT(qs_invoices_ref, {"error": f"{type(e).__name__}: {str(e)}\nTraceback:\n{tb}"})

    # Función de testing, generaion de los XML en local y no envía a VeriFactu
    # testing_generate_xml_in_local(qs_invoices_ref, offset, batch_size, jsonArgs, xml_content, invoices)



    # Lanza el siguiente batch si quedan más
    total_count = Invoice.objects.filter(**jsonArgs.get('filters_qs')).order_by('id').count()
    if offset + batch_size < total_count:
        process_invoices_batch.delay(jsonArgs, offset + batch_size)

    return f"✅ Procesado batch {offset}-{offset + batch_size}"



# @task_prerun.connect
# def lock_seller_invoices_task(sender=None, task_id=None, task=None, args=None, **kwargs):
#     """Evita que múltiples tareas de procesamiento de facturas del mismo vendedor se ejecuten al mismo tiempo."""
#     print(f"\033[93mSENDER {sender.name}\033[0m")
#     print(f"\033[93mSELER {args[0]}\033[0m")