from django.contrib import admin

class ModelFormBE15Admin(admin.ModelAdmin):
    list_display = ('seller', 'year', 'is_processed', 'resp_contacted_bea', 'resp_request_bea')
    list_filter = ['is_processed', 'year', ('seller', admin.RelatedOnlyFieldListFilter)]
    search_fields = ('seller__name', 'year')
    list_editable = ('is_processed',)
    # readonly_fields = ('resp_contacted_bea', 'resp_request_bea')

    class Media:
        js = ('admin/js/disable_resp_request_bea.js',)