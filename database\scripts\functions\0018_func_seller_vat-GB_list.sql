DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_sellervat_countryGB_list_json') THEN
        DROP FUNCTION func_sellervat_countryGB_list_json(date_year INTEGER, date_period VARCHAR, first_date VARCHAR, last_date VARCHAR, presentation_type VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_sellervat_countryGB_list_json(
	date_year INTEGER,
	date_period VARCHAR,
	first_date VARCHAR,
	last_date VARCHAR,
	presentation_type VARCHAR
)RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN

    FOR inv_data IN
	
			SELECT DISTINCT 
					-- id
					sel.id,

					-- seller name
					sel.name AS seller_name,

					-- shortname
					sel.shortname AS seller_shortname,

					-- email
					usr.email AS email,

					-- user name
					usr.name AS user_name,

					-- last login
					usr.last_login AS last_login,

					-- Num Invoices
					COUNT(DISTINCT inv.id) AS num_invoices,

					-- Num Pending Invoices
					COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

					-- Percentage Pending Invoices
					ROUND(COALESCE(
						100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
						0
					), 2) AS percentage_pending_invoices,

					-- VAT Proof
					MAX(
					CASE
						WHEN pm.gb_vat_proof IS NOT NULL THEN pm.gb_vat_proof
						WHEN pmf.gb_vat_proof IS NOT NULL THEN pmf.gb_vat_proof
					ELSE CASE
						WHEN
							1 = 1
							AND (sv.activation_date < TO_DATE(last_date, 'YYYY-MM-DD') AND (sv.deactivation_date IS NULL OR sv.deactivation_date >= TO_DATE(first_date, 'YYYY-MM-DD')))
						THEN 'required'
					ELSE 'not-required' END
					END
				) AS vat_proof

					-- -- prev Month 11
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_11 is True
					-- 	THEN true ELSE false END
					-- ) AS prev_month_11,

					-- -- prev Month 12
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_12 is True
					-- 	THEN true ELSE false END
					-- ) AS prev_month_12,

					-- -- Month 1
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_1 is True
					-- 	THEN true ELSE false END
					-- ) AS month_1,

					-- -- Month 2
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_2 is True
					-- 	THEN true ELSE false END
					-- ) AS month_2,

					-- -- Month 3
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_3 is True
					-- 	THEN true ELSE false END
					-- ) AS month_3,

					-- -- Month 4
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_4 is True
					-- 	THEN true ELSE false END
					-- ) AS month_4,

					-- -- Month 5
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_5 is True
					-- 	THEN true ELSE false END
					-- ) AS month_5,

					-- -- Month 6
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_6 is True
					-- 	THEN true ELSE false END
					-- ) AS month_6,

					-- -- Month 7
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_7 is True
					-- 	THEN true ELSE false END
					-- ) AS month_7,

					-- -- Month 8
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_8 is True
					-- 	THEN true ELSE false END
					-- ) AS month_8,

					-- -- Month 9
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_9 is True
					-- 	THEN true ELSE false END
					-- ) AS month_9,

					-- -- Month 10
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_10 is True
					-- 	THEN true ELSE false END
					-- ) AS month_10,

					-- -- Month 11
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_11 is True
					-- 	THEN true ELSE false END
					-- ) AS month_11,

					-- -- Month 12
					-- BOOL_OR (
					-- 	CASE WHEN
					-- 	txt.month_12 is True
					-- 	THEN true ELSE false END
					-- ) AS month_12
					
					FROM sellers_seller sel
					LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
					LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
					-- LEFT JOIN (
					-- 	SELECT
					-- 		sel.id as seller_id,
					-- 		BOOL_OR(CASE WHEN month = 11 AND year = (date_year - 1) THEN true ELSE false END) AS prev_month_11,
					-- 		BOOL_OR(CASE WHEN month = 12 AND year = (date_year - 1) THEN true ELSE false END) AS prev_month_12,
					-- 		BOOL_OR(CASE WHEN month = 1 AND year = date_year THEN true ELSE false END) AS month_1,
					-- 		BOOL_OR(CASE WHEN month = 2 AND year = date_year THEN true ELSE false END) AS month_2,
					-- 		BOOL_OR(CASE WHEN month = 3 AND year = date_year THEN true ELSE false END) AS month_3,
					-- 		BOOL_OR(CASE WHEN month = 4 AND year = date_year THEN true ELSE false END) AS month_4,
					-- 		BOOL_OR(CASE WHEN month = 5 AND year = date_year THEN true ELSE false END) AS month_5,
					-- 		BOOL_OR(CASE WHEN month = 6 AND year = date_year THEN true ELSE false END) AS month_6,
					-- 		BOOL_OR(CASE WHEN month = 7 AND year = date_year THEN true ELSE false END) AS month_7,
					-- 		BOOL_OR(CASE WHEN month = 8 AND year = date_year THEN true ELSE false END) AS month_8,
					-- 		BOOL_OR(CASE WHEN month = 9 AND year = date_year THEN true ELSE false END) AS month_9,
					-- 		BOOL_OR(CASE WHEN month = 10 AND year = date_year THEN true ELSE false END) AS month_10,
					-- 		BOOL_OR(CASE WHEN month = 11 AND year = date_year THEN true ELSE false END) AS month_11,
					-- 		BOOL_OR(CASE WHEN month = 12 AND year = date_year THEN true ELSE false END) AS month_12
					-- 	FROM sellers_seller sel
					-- 	LEFT JOIN importers_amazontxteur txt ON sel.id = txt.seller_id
					-- 	WHERE txt.year IS NOT NULL
					-- 	GROUP BY sel.id
					-- 	ORDER BY sel.id
					-- ) AS txt ON sel.id = txt.seller_id
					LEFT JOIN (
						SELECT
						sel.id as seller_id,
						MAX(CASE WHEN pm.model_id::text = 'GB-VAT-PROOF' THEN pm.status_id END) as gb_vat_proof
						FROM sellers_seller sel
						LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
						WHERE
							1 = 1
							AND pm.year = date_year
							AND pm.period_id = date_period
							AND pm.country_id = 'GB' 
						GROUP BY sel.id		
					) AS pm ON sel.id = pm.seller_id
					LEFT JOIN (
						SELECT
						sel.id as seller_id,
						MAX(CASE WHEN pmf.model_id::text = 'GB-VAT-PROOF' THEN pmf.status_id END) as gb_vat_proof
						FROM sellers_seller sel
						LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
						WHERE
							1 = 1
							AND pmf.year = date_year
							AND pmf.period_id = date_period
							AND pmf.country_id = 'GB' 
						GROUP BY sel.id		
					) AS pmf ON sel.id = pmf.seller_id
					LEFT JOIN users_user usr ON sel.user_id = usr.id
					WHERE
						1 = 1
						AND sv.is_contracted = True
						AND sv.vat_country_id = 'GB'
						AND sv.quarter_id = presentation_type
					GROUP BY sel.id, usr.email, usr.name, usr.last_login
					ORDER BY sel.id
			
			LOOP
			result_json := result_json || jsonb_build_object(
				'seller_id', inv_data.id,
				'seller_name', inv_data.seller_name,
				'shortname', inv_data.seller_shortname,
				'email', inv_data.email,
				'user_name', inv_data.user_name,
				'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
				'num_pending_invoices', inv_data.num_pending_invoices,
				'percentage_pending_invoices', inv_data.percentage_pending_invoices,
				'vat_proof', inv_data.vat_proof
				-- 'prev_month11', inv_data.prev_month_11,
				-- 'prev_month12', inv_data.prev_month_12,
				-- 'month1', inv_data.month_1,
				-- 'month2', inv_data.month_2,
				-- 'month3', inv_data.month_3,
				-- 'month4', inv_data.month_4,
				-- 'month5', inv_data.month_5,
				-- 'month6', inv_data.month_6,
				-- 'month7', inv_data.month_7,
				-- 'month8', inv_data.month_8,
				-- 'month9', inv_data.month_9,
				-- 'month10', inv_data.month_10,
				-- 'month11', inv_data.month_11,
				-- 'month12', inv_data.month_12		
			);
			
		END LOOP;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- SELECT func_sellervat_countryGB_list_json(2024, 'M1', '2023-11-01', '2024-02-01', '01' )