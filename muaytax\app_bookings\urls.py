from django.urls import path
from muaytax.app_bookings.views import *

app_name = "app_bookings"
urlpatterns = [
    path(
        "sellers/<shortname>/bookings/",
        view=BookingsListSellerView.as_view(),
        name="list_bookings_seller",
    ),
    path(
        "hub/<username>/bookings/",
        view=BookingsListManagerView.as_view(),
        name="list_bookings_manager",
    ),
    path(
        "hub/<username>/bookings/absences/",
        view=ManagerAbsenceList.as_view(),
        name="manager_absence_list",
    ),
    path(
        "hub/<username>/bookings/absences/new/",
        view=ManagerAbsenceCreate.as_view(),
        name="manager_absence_new",
    ),
    path(
        "hub/<username>/bookings/absences/<pk>/update/",
        view=ManagerAbsenceUpdate.as_view(),
        name="manager_absence_update",
    ),
    path(
        "hub/<username>/bookings/absences/<pk>/delete/",
        view=ManagerAbsenceDelete,
        name="manager_absence_delete",
    ),
    path(
        "hub/<username>/bookings/schedule/",
        view=ManagerScheduleUpdateView.as_view(),
        name="manager_schedule_update",
    ),
    path(
        "hub/<username>/bookings/new/",
        view=BookingsCreateManagerView.as_view(),
        name="new_booking_manager",
    ),
    path(
        "sellers/<shortname>/bookings/new/",
        view=BookingsCreateView.as_view(),
        name="new_booking",
    ),
    path(
        "sellers/<shortname>/bookings/new/<subject>",
        view=BookingsCreateSellerView.as_view(),
        name="create_new_booking",
    ),
    path(
        "bookings/<shortname>/find-first-day-available/",
        view=BookingsGetFirstAvailableView.as_view(),
        name="find_first_available"
    ),
    path(
        "bookings/<shortname>/get-booking-spots/",
        view=BookingsGetBookingSpotsView.as_view(),
        name="get_booking_spots"
    ),
    path(
        "bookings/guest-users/",
        view=GuestUsersList.as_view(),
        name="guest-users-list",
    ),
    path(
        "bookings/upcoming-holidays/",
        view=UpcomingHolidaysList.as_view(),
        name="upcoming-holidays-list",
    ),
]
# paths publicos para citas
urlpatterns += [
    path(
        "bookings/public-book/new/",
        view=BookingsCreatePublicView.as_view(),
        name="booking_public_new",
    ),
    path(
        "bookings/public-book/new/success/",
        view=PublicBookingSuccess.as_view(),
        name="public_booking_success",
    ),
    path(
        "bookings/public-book/new/first-available/",
        view=PublicBookingGetFirstAvailable.as_view(),
        name="public_first_available",
    ),
    path(
        "bookings/public-book/new/get-booking-spots/",
        view=PublicBookingGetBookingSpots.as_view(),
        name="public_get_booking_spots",
    ),
    path(
        "bookings/public-book/upcoming-holidays/",
        view=PublicUpcomingHolidaysList.as_view(),
        name="public_upcoming_holidays_list",
    ),
    path(
        "bookings/public-book/get-countries-phone-code/",
        view=PublicBookingCountryList.as_view(),
        name="public_booking_country_list",
    ),
    path(
        "bookings/public-book/submit-seller-info/",
        view=PublicBookingSubmitSellerInfo.as_view(),
        name="public_booking_submit_seller_info",
    ),
]