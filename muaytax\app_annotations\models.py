import logging

from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models

from muaytax.utils.mixins import CustomTimeStampedModel

logger = logging.getLogger(__name__)
User = get_user_model()

class Annotations(CustomTimeStampedModel):
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        related_name="annotation_user",
        limit_choices_to={'role': 'manager'},
        verbose_name="Creado por",
        null=True,
        blank=True,
    )
    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="annotation_seller",
        verbose_name="Vendedor",
    )
    dictionary_content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        related_name="annotation_content_type",
        verbose_name="Tipo de Diccionario",
        help_text="Este es el tipo de diccionario al que se le está haciendo la anotación",
        null=True,  # Hacerlo opcional
        blank=True,
    )
    dictionary_object_id = models.CharField(
        max_length=255,
        verbose_name="ID de objeto en Diccionario",
        help_text="Este es el ID del objeto de diccionario al que se le está haciendo la anotación",
        null=True,  # Hacerlo opcional
        blank=True,
    )
    dictionary_object_selected = GenericForeignKey(
        'dictionary_content_type', 'dictionary_object_id'
    )
    register = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Registro/valor de diccionario",
    )
    comment = models.TextField(
        blank=True,
        null=True,
        verbose_name="Comentario",
    )

    def __str__(self):
        return f'{self.id} - {self.seller}'

    class Meta:
        verbose_name = "Anotación de Usuario"
        verbose_name_plural = "Anotaciones de Usuario"

    def save(self, *args, **kwargs):
        logger.info(f"[DEBUG] Antes de guardar. pk={self.pk}, dictionary_content_type={self.dictionary_content_type}, register={self.register}")

        # Saber si es un objeto completamente nuevo (no existe en BD)
        is_new = self.pk is None

        # 1) Guardado normal: si es nuevo, en este punto Django asignará self.pk
        super().save(*args, **kwargs)

        # 2) Si es una anotación recién creada y dictionary_content_type == None => "general"
        if is_new and self.dictionary_content_type is None:
            new_register = f"General-({self.pk})"
            # Solo actualizamos si self.register sigue en None o distinto
            if self.register != new_register:
                self.register = new_register
                # Guardado rápido de solo el campo "register"
                super().save(update_fields=['register'])

                logger.info(f"[DEBUG] Se actualizó register a {self.register} (Anotación general, pk={self.pk})")

        # 3) (Opcional) También puedes actualizar `register` si hay diccionario:
        elif self.dictionary_content_type and self.dictionary_object_selected:
            # Tomamos su descripción
            new_register = self.dictionary_object_selected.description or ""
            if self.register != new_register:
                self.register = new_register
                super().save(update_fields=['register'])
                logger.info(f"[DEBUG] Se actualizó register a {self.register} (Anotación de diccionario)")

        logger.info(f"[DEBUG] Después de guardar. pk={self.pk}, register={self.register}")


