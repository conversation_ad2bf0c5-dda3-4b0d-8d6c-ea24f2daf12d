import datetime
import numpy as np
import json
import re
from datetime import timedelta

import numpy as np
import pandas as pd
from crispy_forms.helper import FormHelper
from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from slugify import slugify

from muaytax.app_address.models.address import Address
from muaytax.app_documents.models import Document
from muaytax.app_documents.models.model_5472_1120 import TRANSACTION_TYPE_CHOICES
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_184 import ActivityCountry
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.app_sellers.models.seller_yield_record import SellerYieldRecord
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.currencies import Currency
from muaytax.dictionaries.models.economic_activity import EconomicActivity
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.dictionaries.models.province_code import ProvinceCode
from muaytax.dictionaries.models.sellervat_epigraph_regime import (
    SellerVatEpigraphRegime,
)
from muaytax.utils.helpers import localized_choices
from muaytax.utils.mixins import CustomPhoneNumberPrefixWidget

User = get_user_model()


class sellerForm(forms.ModelForm):
    class Meta:
        model = Seller
        exclude = [""]

class sellerChangeForm(forms.ModelForm):
    class Meta:
        model = Seller
        fields = ['logo', 'trade_name', 'name', 'first_name', 'last_name', 'contact_phone', 'valid_emails']
        labels = {
            'name': 'Razón Social',
            'trade_name': 'Nombre Comercial',
            'first_name': 'Nombre ',
            'last_name': 'Apellidos ',
        }


    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)  # Extrae el usuario pasado como argumento
        super().__init__(*args, **kwargs)

        # if  self.instance.legal_entity=='self-employed':
        self.fields['name'].required = False
        self.fields['name'].widget.attrs = {
            'readonly': 'true',
            'style': 'background-color: #e9ecef; opacity: 1;',
            'onmousedown': 'return false;',
            'onfocus': 'this.blur();',
        }
        if  self.instance.legal_entity=='self-employed':
            self.initial['name'] = f"{self.instance.first_name} {self.instance.last_name}"
            self.fields['first_name'].required = True
            self.fields['last_name'].required = True

        # Bloquear edición de Nombre y Apellidos si el usuario no es manager
        if not user or not user.groups.filter(name='manager').exists():
            for field in ['first_name', 'last_name']:
                self.fields[field].widget.attrs.update({
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                })
        # else:
        #     self.fields['trade_name'].required = False
        #     self.fields['trade_name'].widget.attrs = {
        #         'readonly': 'true',
        #         'style': 'background-color: #e9ecef; opacity: 1;',
        #         'onmousedown': 'return false;',
        #         'onfocus': 'this.blur();'
        #     }

class sellerChangeFormManager(forms.ModelForm):
    class Meta:
        model = Seller
        fields = '__all__'
        exclude = [
            "user",
            "shortname",
            "seller_address",
            "seller_bank_address",
            "seller_provider_address",
            "seller_iae",  # todo quitar cuando se elimine el campo
            "api_usage",
            "reg_wizard_steps",
            "reg_wizard_step",
            'limit_invoice'
        ]

    def __init__(self, *args, **kwargs):
        super(sellerChangeFormManager, self).__init__(*args, **kwargs)

        # Make member_address field readonly
        if 'member_address' in self.fields:
            self.fields['member_address'].disabled = True
            # Optional: Add visual styling to indicate it's readonly
            if 'attrs' not in self.fields['member_address'].widget.attrs:
                self.fields['member_address'].widget.attrs = {}
            self.fields['member_address'].widget.attrs.update({
                'readonly': 'readonly',
                'style': 'background-color: #f8f9fa; color: #6c757d;'
            })
            # Optional: Add help text to explain why it's readonly
            self.fields['member_address'].help_text = "Este campo es de solo lectura y no puede ser modificado."

        # Títulos de secciones
        self.section_titles = {
            'personal_info': 'INFORMACIÓN PERSONAL Y DE IDENTIFICACIÓN',
            'fiscal_info': 'INFORMACIÓN FISCAL Y LEGAL',
            'economic_info': 'INFORMACIÓN ECONÓMICA',
            'bank_info': 'INFORMACIÓN BANCARIA',
            'amazon_info': 'AMAZON Y VENTAS',
            'accounting_es': 'CONTABILIDAD ESPAÑA',
            'accounting_usa': 'CONTABILIDAD USA',
            'llc_maintenance': 'MANTENIMIENTO LLC',
            'other_services': 'OTROS SERVICIOS CONTRATADOS',
            'flat_rate': 'TARIFA PLANA INSS',
            'oss': 'OSS',
            'affiliate_programs': 'PROGRAMAS DE AFILIADOS',
            'other': 'OTROS DATOS ADMINISTRATIVOS'
        }

        # Reorganizar todos los campos del formulario en grupos lógicos
        # Primero obtenemos todos los campos disponibles
        available_fields = list(self.fields.keys())
        ordered_fields = []

        # Grupo 1: Información personal y de identificación
        personal_info_fields = [
            'name', 'trade_name', 'first_name', 'last_name', 'birthdate_seller', 'gender',
            'birth_country', 'phone_country', 'phone', 'contact_phone', 'signature_image',
            'member_address', 'logo', 'package_brand_name'
        ]

        # Grupo 2: Información fiscal y legal
        fiscal_info_fields = [
            'legal_entity', 'is_fiscally_transparent', 'is_eu_seller', 'country_registration',
            'state_agency_registration', 'nif_registration', 'establishment_date',
            'incorporation_llc_date', 'vat_no_origin_country', 'eori', 'ein', 'business_id_eeuu',
            'representative_id', 'name_representative', 'last_name_representative',
            'tax_information_start_date', 'tax_information_end_date',
            'share_capital', 'shares_number', 'share_value', 'is_direct_estimation',
            'check_model_es_123', 'check_model_es_131', 'check_model_es_216',
            'check_model_es_111', 'check_model_es_115'
        ]

        # Grupo 3: Información económica
        economic_info_fields = [
            'total_turnover', 'total_benefits', 'total_assests', 'total_liabilities',
            'activity_type', 'products_and_services', 'desc_main_activity',
            'manufacture_products', 'provider_name'
        ]

        # Grupo 4: Información bancaria
        bank_info_fields = [
            'iban', 'swift', 'bank_name'
        ]

        # Grupo 5: Amazon y ventas
        amazon_fields = [
            'amazon_sell', 'amazon_name', 'amazon_merchant_token', 'amazon_account_ids', 'is_amz_report'
        ]

        # Grupo 6: Servicios contratados - Contabilidad ES
        accounting_fields = [
            'contracted_accounting',
            'contracted_accounting_date',
            'contracted_accounting_end_date',
            'tax_agency_accounting_date',
            'tax_agency_accounting_end_date',
            'contracted_accounting_payment_date',
            # Añadido: Servicios de registro/cancelación para España
            'service_registration_purchase_date',
            'service_cancellation_purchase_date'
        ]

        # Grupo 7: Servicios contratados - Contabilidad USA
        accounting_usa_fields = [
            'contracted_accounting_usa_date',
            'contracted_accounting_usa_end_date',
            'contracted_accounting_usa_payment_date',
            'contracted_accounting_usa_basic_date',
            'contracted_accounting_usa_basic_end_date',
            'contracted_accounting_usa_basic_payment_date'
        ]

        # Grupo 8: Servicios contratados - LLC
        llc_fields = [
            'contracted_maintenance_llc',
            'contracted_maintenance_llc_date',
            'contracted_maintenance_llc_end_date',
            'contracted_maintenance_llc_payment_date',
            'maintenance_type',
            # Añadido: Servicios de registro/cancelación para LLC
            'service_llc_registration_purchase_date',
            'service_llc_cancellation_purchase_date'
        ]

        # Grupo 9: Otros servicios contratados
        other_services_fields = [
            'contracted_accounting_txt',
            'contracted_accounting_txt_date',
            'contracted_accounting_txt_end_date',
            'contracted_accounting_txt_payment_date',
            'contracted_model_presentation',
            'contracted_model_presentation_date',
            'contracted_model_presentation_end_date',
            'contracted_model_presentation_payment_date',
            'is_contracted_corporate_payroll',
            'contracted_corporate_payroll_date',
            'contracted_corporate_payroll_end_date',
            'contracted_corporate_payroll_payment_date',
            'is_contracted_labor_payroll',
            'contracted_labor_payroll_date',
            'contracted_labor_payroll_end_date',
            'contracted_labor_payroll_payment_date',
            'withholdings_payments_account_date',
            'withholdings_payments_account_end_date',
            'withholdings_payments_account_payment_date',
            'is_184_contracted',
            'is_b15_contracted',
            'is_5472_1120_contracted',
            'is_5472_1120_inactive_contracted',
            'is_boir_contracted',
            'is_llc_premium_direction'
        ]

        # Grupo 10: Tarifa plana INSS
        flat_rate_fields = [
            'flat_rate_inss_date',
            'flat_rate_inss_extension_count',
            'flat_rate_inss_extension_date',
            'flat_rate_inss_extension_start_date',
            'flat_rate_inss_extension_end_date',
            'flat_rate_inss_next_expiration_send_email'
        ]

        # Grupo 12: OSS
        oss_fields = [
            'oss',
            'oss_date',
            'oss_end_date',
            'oss_payment_date',
            'oss_country',
            'eqtax'
        ]

        # Grupo 13: Programas de afiliados
        affiliate_fields = [
            'affiliate_program',
            'affiliate_start_date',
            'affiliate_end_date',
            'affiliatebpa_program',
            'affiliatebpa_start_date',
            'affiliatebpa_end_date'
        ]

        # Grupo 14: Otros
        other_fields = [
            'is_inactive',
            'valid_emails',
            'limit_invoice_promoted'
        ]

        # Lista de todos los grupos con sus títulos
        field_groups = [
            ('personal_info', personal_info_fields),
            ('fiscal_info', fiscal_info_fields),
            ('economic_info', economic_info_fields),
            ('bank_info', bank_info_fields),
            ('amazon_info', amazon_fields),
            ('accounting_es', accounting_fields),
            ('accounting_usa', accounting_usa_fields),
            ('llc_maintenance', llc_fields),
            ('other_services', other_services_fields),
            ('flat_rate', flat_rate_fields),
            ('oss', oss_fields),
            ('affiliate_programs', affiliate_fields),
            ('other', other_fields)
        ]

        # Creamos una nueva lista ordenada con los campos disponibles
        for section_key, group in field_groups:
            section_fields = []
            for field in group:
                if field in available_fields:
                    section_fields.append(field)

            # Solo añadimos sección si tiene al menos un campo
            if section_fields:
                # Añadimos el separador de sección con el título
                section_title = self.section_titles.get(section_key, 'SECCIÓN')
                self.add_section_header(section_title, ordered_fields)

                # Añadimos los campos de la sección
                ordered_fields.extend(section_fields)

        # Añadimos cualquier campo que haya quedado fuera (por si hay campos nuevos)
        remaining_fields = [f for f in available_fields if f not in ordered_fields]
        if remaining_fields:
            self.add_section_header('CAMPOS ADICIONALES', ordered_fields)
            ordered_fields.extend(remaining_fields)

        # Establecer el nuevo orden de campos
        self.order_fields(ordered_fields)

    def add_section_header(self, title, ordered_fields):
        """Añade un campo de tipo separador con título de sección al formulario"""
        header_field = f"section_header_{len(ordered_fields)}"
        self.fields[header_field] = forms.CharField(
            label='',
            required=False,
            widget=forms.TextInput(attrs={
                'readonly': 'readonly',
                'class': 'section-header',
                'value': title,
                'style': 'font-weight: bold; font-size: 1.2em; margin-top: 20px; margin-bottom: 10px; border: none; background: none; width: 100%; border-bottom: 1px solid #dee2e6; padding-bottom: 5px;'
            })
        )
        ordered_fields.append(header_field)

class SellerChangeInformationForm(forms.ModelForm):

    class Meta:
        model = Seller
        fields = [
                'name',
                'trade_name',
                'first_name',
                'last_name',
                'establishment_date',
                'incorporation_llc_date',
                'nif_registration',
        ]
        widgets = {
            'establishment_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'incorporation_llc_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
        }
        labels = {
            'name': 'Razón Social',
            'trade_name': 'Nombre Comercial',
            'first_name': 'Nombre del freelance',
            'last_name': 'Apellidos del freelance',
            'establishment_date': 'Fecha de registro SL/autónomo',

            'nif_registration': 'Número de identificación fiscal',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if self.instance.legal_entity:
            if  self.instance.legal_entity=='self-employed':
                self.fields['name'].required = False
                self.fields['name'].widget.attrs = {
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                }
                # self.fields['is_direct_estimation'].required = True
                self.fields['first_name'].required = True
                self.fields['last_name'].required = True
            else:
                self.fields['name'].required = True
                self.fields['first_name'].required = False
                self.fields['first_name'].widget.attrs = {
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                }
                self.fields['last_name'].required = False
                self.fields['last_name'].widget.attrs = {
                    'readonly': 'true',
                    'style': 'background-color: #e9ecef; opacity: 1;',
                    'onmousedown': 'return false;',
                    'onfocus': 'this.blur();',
                }
                # self.fields['trade_name'].required = False
                # self.fields['trade_name'].widget.attrs = {
                #     'readonly': 'true',
                #     'style': 'background-color: #e9ecef; opacity: 1;',
                #     'onmousedown': 'return false;',
                #     'onfocus': 'this.blur();'
                # }


        # if self.instance.seller_address and self.instance.seller_address.address_country:
        #     vies = SellerVat.objects.filter(
        #         seller=self.instance,
        #         vat_address__address_country__iso_code=self.instance.seller_address.address_country.iso_code,
        #         is_local=True
        #     ).first()

    def save(self, commit=True):
        seller = super().save()
        # crear seller_vat local si no existe y es sl o self-employed
        if seller.legal_entity in ['sl', 'self-employed'] and seller.country_registration:
            vat = seller.vat_seller.filter(Q(vat_country=seller.country_registration) | Q(is_local=True))
            if vat.exists():
                vat_c = vat.filter(vat_country=seller.country_registration).first()
                vat_l = vat.filter(is_local=True).first()
                if (vat_c or vat_l) and vat_l != vat_c:
                    if vat_l:
                        vat_l.is_local = False
                        vat_l.save()

                    if vat_c:
                        vat_c.is_local = True
                        vat_c.save()
            else:
                SellerVat.objects.create(
                    seller=seller,
                    vat_country=seller.country_registration,
                    vat_number='',
                    is_contracted=seller.contracted_accounting or False,
                    is_local=True,
                )
        return seller

class SellerChangeAditionalInformationESForm(forms.ModelForm):
    is_direct_estimation = forms.ChoiceField(
        required=False,
        label="Estimación directa simplificada",
        choices=[(True, 'Sí'), (False, 'No')],
    )

    check_model_es_123 = check_model_es_131 = check_model_es_216 = forms.ChoiceField(
        required=True,
        choices=[(True, 'Requerido'), (False, 'No Requerido')],
    )

    check_model_es_111 = forms.ChoiceField(
        required=True,
        choices=[(True, 'Requerido'), (False, 'No Requerido')],
        label="Modelo 111"
    )
    check_model_es_115 = forms.ChoiceField(
        required=True,
        choices=[(True, 'Requerido'), (False, 'No Requerido')],
        label="Modelo 115"
    )

    class Meta:
        model = Seller
        fields = [
            'is_direct_estimation',
            'check_model_es_123',
            'check_model_es_131',
            'check_model_es_216',
            'check_model_es_111',
            'check_model_es_115',
        ]
        widgets = {
        }
        labels = {
            'is_direct_estimation': 'Estimación directa simplificada',
            'check_model_es_123': 'Modelo 123',
            'check_model_es_131': 'Modelo 131',
            'check_model_es_216': 'Modelo 216',
        }

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)

    # def save(self, commit=True):
    #     seller = super().save()
    #     return seller

class SellerChangeFormSellPlatform(forms.ModelForm):
    class Meta:
        model = Seller
        fields = ['amazon_sell', 'amazon_name', 'amazon_merchant_token', 'amazon_account_ids']

class SellerFlatRateINSSForm(forms.ModelForm):
    extend_flat_rate = forms.IntegerField(
        required=False,
        label="Extender Tarifa Plana",
        help_text="Ingrese 1 para extender la tarifa plana por un año adicional o 0 para no extenderla.",
        min_value=0,
        max_value=1,
        initial=0
    )
    class Meta:
        model = Seller
        fields = ['flat_rate_inss_date']
        labels = {
            'flat_rate_inss_date': 'Fecha de Inscripción',
        }
        help_texts = {
            'flat_rate_inss_date': 'Seleccione la fecha de inscripción a la tarifa plana.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        seller = self.instance

        # Solo mostrar la opción de extender si el vendedor puede extender su tarifa plana
        if not hasattr(seller, 'can_extend_flat_rate') or not seller.can_extend_flat_rate:
            self.fields['extend_flat_rate'].widget = forms.HiddenInput()

    def clean_flat_rate_inss_date(self):
        flat_rate_inss_date = self.cleaned_data.get('flat_rate_inss_date')

        # Validaciones relacionadas con el modelo
        seller = self.instance
        if flat_rate_inss_date and seller.legal_entity != "self-employed":
            raise ValidationError(
                'La fecha de inscripción a tarifa plana solo se aplica a vendedores autónomos (self-employed).'
            )
         #26-03-25: Delete validation | 0x7xyp01
        """
        # if flat_rate_inss_date and seller.contracted_accounting_date:
        #     if flat_rate_inss_date < seller.contracted_accounting_date:
        #         formatted_date = seller.contracted_accounting_date.strftime("%d/%m/%Y")
        #         raise ValidationError(
        #             f'La fecha de inscripción a tarifa plana no puede ser anterior a la fecha de alta como autónomo {formatted_date}'
        #         )
        """
        return flat_rate_inss_date

    def clean(self):
        cleaned_data = super().clean()
        extend_flat_rate = cleaned_data.get('extend_flat_rate')
        flat_rate_inss_date = cleaned_data.get('flat_rate_inss_date')

        if extend_flat_rate == 1:
            # 1. Verificar que sea un vendedor autónomo
            if self.instance.legal_entity != "self-employed":
                raise ValidationError({
                    'extend_flat_rate': 'La extensión de tarifa plana solo se aplica a vendedores autónomos (self-employed).'
                })

            # 2. Verificar que tenga una fecha de inscripción (ya sea existente o nueva del formulario)
            if not flat_rate_inss_date and not self.instance.flat_rate_inss_date:
                raise ValidationError({
                    'extend_flat_rate': 'Debe tener una fecha de inscripción a tarifa plana para poder extenderla.'
                })

            # 3. Verificar que la tarifa plana esté actualmente vigente o haya estado vigente recientemente
            # Usar la fecha del formulario si existe, sino la fecha guardada
            fecha_a_validar = flat_rate_inss_date or self.instance.flat_rate_inss_date
            if fecha_a_validar:
                today = timezone.now().date()
                # Calcular si la tarifa plana podría estar actualmente activa
                # Considerando extensión: máximo 2 años desde la fecha original
                max_vigencia = fecha_a_validar + timedelta(days=730)  # 2 años máximo

                if today > max_vigencia:
                    raise ValidationError({
                        'extend_flat_rate': 'No se puede extender la tarifa plana porque ha excedido el período máximo de vigencia (2 años desde la inscripción original).'
                    })

            # 4. Verificar si ya se ha utilizado la extensión previamente
            if self.instance.flat_rate_inss_extension_count >= 1:
                raise ValidationError({
                    'extend_flat_rate': 'No se puede extender la tarifa plana porque ya se ha utilizado la única extensión permitida.'
                })

        return cleaned_data

    def save(self, commit=True):
        seller = super().save(commit=False)
        extend_flat_rate = self.cleaned_data.get('extend_flat_rate')

        # Si se ingresa 1, aplicar la extensión
        if extend_flat_rate == 1:
            # Establecer el contador de extensiones a 1
            seller.flat_rate_inss_extension_count = 1
            # Establecer la fecha de extensión como la fecha actual
            seller.flat_rate_inss_extension_date = timezone.now().date()

            # Calcular la fecha de inicio de la prórroga (12 meses después de la fecha de inscripción)
            if seller.flat_rate_inss_date:
                # La fecha de inicio de la prórroga es un año después de la fecha de inscripción
                seller.flat_rate_inss_extension_start_date = seller.flat_rate_inss_date + timedelta(days=365)
                # La fecha de fin de la prórroga es un año después de la fecha de inicio de la prórroga menos 1 día
                # Esto asegura que la prórroga termine el día anterior al aniversario
                seller.flat_rate_inss_extension_end_date = seller.flat_rate_inss_extension_start_date + timedelta(days=364)

        if commit:
            seller.save()
        return seller

class SellerChangeFormContractedServices(forms.ModelForm):
    class Meta:
        model = Seller
        fields = [
            'contracted_accounting',
            'contracted_accounting_date',
            'contracted_accounting_end_date',
            'oss',
            'oss_date',
            'oss_end_date',
            'contracted_maintenance_llc',
            'contracted_maintenance_llc_date',
            'contracted_maintenance_llc_end_date',
            'contracted_model_presentation',
            'contracted_model_presentation_date',
            'contracted_model_presentation_end_date',
            'contracted_accounting_txt',
            'contracted_accounting_txt_date',
            'contracted_accounting_txt_end_date',
            'is_contracted_corporate_payroll',
            'contracted_corporate_payroll_date',
            'contracted_corporate_payroll_end_date',
            'is_contracted_labor_payroll',
            'contracted_labor_payroll_date',
            'contracted_labor_payroll_end_date',
            'withholdings_payments_account_date',
            'withholdings_payments_account_end_date',
        ]
        widgets = {
            'contracted_accounting_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_accounting_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'oss_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'oss_end_date': forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_accounting_txt_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_accounting_txt_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_model_presentation_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_model_presentation_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_maintenance_llc_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_maintenance_llc_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_corporate_payroll_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_corporate_payroll_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_labor_payroll_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'contracted_labor_payroll_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'withholdings_payments_account_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            'withholdings_payments_account_end_date': forms.TextInput(
                attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
        }
        labels = {
            'contracted_accounting_date': 'Fecha de alta servicio Contabilidad',
            'contracted_accounting_end_date': 'Fecha de baja servicio Contabilidad',
            'oss_date': 'Fecha de alta servicio OSS',
            'oss_end_date': 'Fecha de baja servicio OSS',
            'contracted_accounting_txt': 'Traducción TXT',
            'contracted_accounting_txt_date': 'Fecha de alta servicio Traducción TXT',
            'contracted_accounting_txt_end_date': 'Fecha de baja servicio Traducción TXT',
            'contracted_model_presentation': 'Presentación Modelos',
            'contracted_model_presentation_date': 'Fecha de alta servicio Presentación Modelos',
            'contracted_model_presentation_end_date': 'Fecha de baja servicio Presentación Modelos',
            'contracted_maintenance_llc': 'Mantenimiento LLC',
            'contracted_maintenance_llc_date': 'Fecha de alta servicio Mantenimiento LLC',
            'contracted_maintenance_llc_end_date': 'Fecha de baja servicio Mantenimiento LLC',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # campos booleanos ocultos
        del self.fields['contracted_accounting']
        del self.fields['oss']
        del self.fields['contracted_maintenance_llc']
        del self.fields['contracted_model_presentation']
        del self.fields['contracted_accounting_txt']
        del self.fields['is_contracted_corporate_payroll']
        del self.fields['is_contracted_labor_payroll']

        # verifica de que si no tiene al menos un documento de alta o de baja de pais iva españa,
        # no puede editar las fechas de alta o baja de Retenciones e ingresos a cuenta
        if not Document.objects.filter(
            Q(seller=self.instance),
            Q(documentType__code__in=['ES-036', 'ES-VATSUSPENSION']),
        ).exists():
            self.fields['withholdings_payments_account_date'].widget.attrs.update(readonly='true')
            self.fields['withholdings_payments_account_end_date'].widget.attrs.update(readonly='true')

        if self.instance and self.instance.legal_entity == 'llc':
            # del self.fields['contracted_accounting']
            del self.fields['contracted_accounting_date']
            del self.fields['contracted_accounting_end_date']
            # del self.fields['oss']
            del self.fields['oss_date']
            del self.fields['oss_end_date']
            # del self.fields['contracted_accounting_txt']
            # del self.fields['contracted_accounting_txt_date']
            # del self.fields['contracted_accounting_txt_end_date']

        else:
            # del self.fields['contracted_model_presentation']
            del self.fields['contracted_model_presentation_date']
            del self.fields['contracted_model_presentation_end_date']
            # del self.fields['contracted_maintenance_llc']
            del self.fields['contracted_maintenance_llc_date']
            del self.fields['contracted_maintenance_llc_end_date']

    def clean(self):
        cleaned_data = super().clean()

        contracted_accounting_date = cleaned_data.get('contracted_accounting_date')
        contracted_accounting_end_date = cleaned_data.get('contracted_accounting_end_date')
        oss_date = cleaned_data.get('oss_date')
        oss_end_date = cleaned_data.get('oss_end_date')
        contracted_accounting_txt_date = cleaned_data.get('contracted_accounting_txt_date')
        contracted_accounting_txt_end_date = cleaned_data.get('contracted_accounting_txt_end_date')
        contracted_model_presentation_date = cleaned_data.get('contracted_model_presentation_date')
        contracted_model_presentation_end_date = cleaned_data.get('contracted_model_presentation_end_date')
        contracted_maintenance_llc_date = cleaned_data.get('contracted_maintenance_llc_date')
        contracted_maintenance_llc_end_date = cleaned_data.get('contracted_maintenance_llc_end_date')
        contracted_corporate_payroll_date = cleaned_data.get('contracted_corporate_payroll_date')
        contracted_corporate_payroll_end_date = cleaned_data.get('contracted_corporate_payroll_end_date')
        contracted_labor_payroll_date = cleaned_data.get('contracted_labor_payroll_date')
        contracted_labor_payroll_end_date = cleaned_data.get('contracted_labor_payroll_end_date')
        witholdings_payments_account_date = cleaned_data.get('withholdings_payments_account_date')
        witholdings_payments_account_end_date = cleaned_data.get('withholdings_payments_account_end_date')

        if contracted_accounting_date and contracted_accounting_end_date:
            if contracted_accounting_date > contracted_accounting_end_date:
                self.add_error('contracted_accounting_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if oss_date and oss_end_date:
            if oss_date > oss_end_date:
                self.add_error('oss_date', "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_accounting_txt_date and contracted_accounting_txt_end_date:
            if contracted_accounting_txt_date > contracted_accounting_txt_end_date:
                self.add_error('contracted_accounting_txt_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")

        if contracted_accounting_end_date and not contracted_accounting_date:
            self.add_error('contracted_accounting_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")
        if oss_end_date and not oss_date:
            self.add_error('oss_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")
        if contracted_accounting_txt_end_date and not contracted_accounting_txt_date:
            self.add_error('contracted_accounting_txt_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_model_presentation_date and contracted_model_presentation_end_date:
            if contracted_model_presentation_date > contracted_model_presentation_end_date:
                self.add_error('contracted_model_presentation_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_model_presentation_end_date and not contracted_model_presentation_date:
            self.add_error('contracted_model_presentation_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_maintenance_llc_date and contracted_maintenance_llc_end_date:
            if contracted_maintenance_llc_date > contracted_maintenance_llc_end_date:
                self.add_error('contracted_maintenance_llc_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_maintenance_llc_end_date and not contracted_maintenance_llc_date:
            self.add_error('contracted_maintenance_llc_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_corporate_payroll_date and contracted_corporate_payroll_end_date:
            if contracted_corporate_payroll_date > contracted_corporate_payroll_end_date:
                self.add_error('contracted_corporate_payroll_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_corporate_payroll_end_date and not contracted_corporate_payroll_date:
            self.add_error('contracted_corporate_payroll_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if contracted_labor_payroll_date and contracted_labor_payroll_end_date:
            if contracted_labor_payroll_date > contracted_labor_payroll_end_date:
                self.add_error('contracted_labor_payroll_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if contracted_labor_payroll_end_date and not contracted_labor_payroll_date:
            self.add_error('contracted_labor_payroll_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        if witholdings_payments_account_date and witholdings_payments_account_end_date:
            if witholdings_payments_account_date > witholdings_payments_account_end_date:
                self.add_error('withholdings_payments_account_date',
                               "La fecha de alta no puede ser posterior a la fecha de baja")
        if witholdings_payments_account_end_date and not witholdings_payments_account_date:
            self.add_error('withholdings_payments_account_date',
                           "Para introducir una fecha de baja, debe introducir una fecha de alta")

        return cleaned_data

    def save(self, commit=True):
        seller = super().save()
        vat_local = seller.vat_seller.filter(is_local=True).first()
        if vat_local:
            vat_local.is_contracted = seller.contracted_accounting or False
            if seller.legal_entity in ['sl', 'self-employed']:
                vat_local.activation_date = seller.contracted_accounting_date
                vat_local.start_contracting_date = seller.contracted_accounting_date

                vat_local.deactivation_date = seller.contracted_accounting_end_date
                vat_local.end_contracting_date = seller.contracted_accounting_end_date
            vat_local.save()
            if seller.country_registration:
                econ_act = EconomicActivity.objects.filter(
                    code__startswith=seller.country_registration.iso_code,
                    code__endswith='665'
                ).first() or EconomicActivity.objects.get(
                    code='665'
                )
                SellerVatActivity.objects.get_or_create(
                    sellervat=vat_local,
                    sellervat_activity_iae=econ_act,
                )
        return seller

class SellerChangeAddressInformationForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label="Dirección")
    address_number = forms.CharField(max_length=10, required=False, label="Número")
    address_continue = forms.CharField(max_length=100, required=False, label="Dirección (Continuación)")
    address_zip = forms.CharField(max_length=10, label="Código Postal")
    address_city = forms.CharField(max_length=50, required=False, label="Ciudad")
    address_state = forms.CharField(max_length=50, required=False, label="Estado / Provincia / Región")
    # address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País")

    class Meta:
        model = Seller
        fields = ["address", "address_number", "address_continue", "address_zip", "address_city", "address_state"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        seller = kwargs.get('instance')
        if seller and seller.seller_address:
            address = seller.seller_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_state'].initial = address.address_state

    def save(self, commit=True):
        seller = super().save(commit=False)
        address = seller.seller_address

        if address is None:
            address = Address()
            seller.seller_address = address

        address.address_name = f"Direccion {seller.shortname}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_state = self.cleaned_data['address_state']
        address.save()

        if commit:
            seller.save()

        return seller

class LegalEntityActivityIVACountryForm(forms.Form):
    vat_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País IVA")

    def __init__(self, *args, **kwargs):
        seller = kwargs.pop('seller', None)
        super().__init__(*args, **kwargs)
        if seller:
            query_country = Country.objects.filter(seller_vat_country__in=seller.vat_seller.all())
            vat_countries = SellerVat.objects.filter(seller=seller)
            self.fields['vat_country'].queryset = query_country
            sellervat = seller.vat_seller.filter(is_local=True).first()
            if sellervat:
                self.fields['vat_country'].initial = sellervat.vat_country
            if sellervat:
                self.fields['vat_country'].empty_label = None
            if not vat_countries.exists():
                self.fields['vat_country'].required = False

class LegalEntityActivityChangeForm(forms.ModelForm):
    regime = forms.ModelChoiceField(
        queryset=SellerVatEpigraphRegime.objects.all(),
        empty_label='---------',
        required=True
    )

    def __init__(self, *args, **kwargs):

        instance = kwargs.get('instance')
        iae = kwargs.pop('iae', None)
        super().__init__(*args, **kwargs)
        if iae:
            self.fields['sellervat_activity_iae'].queryset = iae

        if instance:
            self.fields['sellervat_activity_iae'].empty_label = None
            self.fields['regime'].empty_label = None

    class Meta:
        model = SellerVatActivity
        fields = [
            "sellervat_activity_iae",
            "regime",
            "date",
            "end_date"
        ]
        widgets = {
            "date": forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
            "end_date": forms.TextInput(attrs={'placeholder': 'Seleccione una fecha', 'type': 'date'}),
        }

    def clean(self):
        cleaned_data = super().clean()

        date = cleaned_data.get('date')
        end_date = cleaned_data.get('end_date')

        if date and end_date:
            if date > end_date:
                self.add_error('date',
                               "La fecha de alta de actividad no puede ser posterior a la fecha de baja de actividad")

        if end_date and not date:
            self.add_error('date',
                           "Para introducir una fecha de baja de actividad, debe introducir una fecha de alta de actividad")

        return cleaned_data

class BaseActivityFormset(forms.BaseInlineFormSet):
    def clean(self):
        super().clean()

        if any(self.errors):
            return

        # Check if activity doesnt repeat
        activities = set()
        for form in self.forms:
            if form.cleaned_data:
                activity = form.cleaned_data.get('sellervat_activity_iae')
                if activity in activities:
                    raise forms.ValidationError("No puede haber Epígrafes IAE repetidos")
                activities.add(activity)

    def save(self, commit=True, seller=None):
        super().save(commit=commit)
        if seller:
            vat_local = seller.vat_seller.filter(is_local=True).first()
            if vat_local:
                for form in self.forms:
                    if 'sellervat_activity_iae' in form.changed_data:
                        activity = form.cleaned_data.get('sellervat_activity_iae')
                        if activity:
                            code = activity.code.split('-')[-1]
                            iae = EconomicActivity.objects.filter(
                                code__startswith=f'{vat_local.vat_country.iso_code}-',
                                code__endswith=f'{code}'
                            ).first()
                            if not iae:
                                iae = EconomicActivity.objects.filter(code=code).first()

                            SellerVatActivity.objects.get_or_create(
                                sellervat=vat_local,
                                sellervat_activity_iae=iae,
                            )

class sellerAddressChangeForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label="Dirección")
    address_number = forms.CharField(max_length=10, required=False, label="Número")
    address_continue = forms.CharField(max_length=100, required=False, label="Dirección (Continuación)")
    address_zip = forms.CharField(max_length=10, label="Código Postal")
    address_city = forms.CharField(max_length=50, required=False, label="Ciudad")
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País")
    address_catastral = forms.CharField(max_length=100, required=False, label="Referencia Catastral",
                                        help_text="Solo aplica a España")

    class Meta:
        model = Seller
        fields = ["address", "address_number", "address_continue", "address_zip", "address_city", "address_country",
                  "address_catastral"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        seller = kwargs.get('instance')
        if seller and seller.seller_address:
            address = seller.seller_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_country'].initial = address.address_country
            self.fields['address_catastral'].initial = address.address_catastral

    def save(self, commit=True):
        seller = super().save(commit=False)
        address = seller.seller_address

        if address is None:
            address = Address()
            seller.seller_address = address

        address.address_name = f"Direccion {seller.shortname}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_country = self.cleaned_data['address_country']
        address.address_catastral = self.cleaned_data['address_catastral']
        address.save()

        if commit:
            seller.save()

        return seller

class sellerBankChangeForm(forms.ModelForm):
    # Campos de dirección
    address = forms.CharField(max_length=100, required=False, label="Dirección")
    address_number = forms.CharField(max_length=10, required=False, label="Número")
    address_continue = forms.CharField(max_length=100, required=False, label="Dirección (Continuación)")
    address_zip = forms.CharField(max_length=10, label="Código Postal")
    address_city = forms.CharField(max_length=50, required=False, label="Ciudad")
    address_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="País")
    address_catastral = forms.CharField(max_length=100, required=False, label="Referencia Catastral",
                                        help_text="Solo aplica a España")

    class Meta:
        model = Seller
        fields = ["iban", "swift", "bank_name", "address", "address_number", "address_continue", "address_zip",
                  "address_city", "address_country"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        seller = kwargs.get('instance')
        if seller and seller.seller_bank_address:
            address = seller.seller_bank_address
            self.fields['address'].initial = address.address
            self.fields['address_number'].initial = address.address_number
            self.fields['address_continue'].initial = address.address_continue
            self.fields['address_zip'].initial = address.address_zip
            self.fields['address_city'].initial = address.address_city
            self.fields['address_country'].initial = address.address_country

    def save(self, commit=True):
        seller = super().save(commit=False)
        print(seller)
        address = seller.seller_bank_address

        if (address is None):
            address = Address()
            seller.seller_bank_address = address

        address.address_name = f"Direccion Banco {seller.shortname}"
        address.address = self.cleaned_data['address']
        address.address_number = self.cleaned_data['address_number']
        address.address_continue = self.cleaned_data['address_continue']
        address.address_zip = self.cleaned_data['address_zip']
        address.address_city = self.cleaned_data['address_city']
        address.address_country = self.cleaned_data['address_country']
        address.save()

        if commit:
            seller.save()

        return seller

class Sellerintratolocal(forms.Form):
    name = forms.CharField(label='name', max_length=100)

class SellerDeleteTxt(forms.Form):
    name = forms.CharField(label='name', max_length=100)
    month = forms.CharField(label='month', max_length=100)
    year = forms.CharField(label='year', max_length=100)

class SellerSqlExec(forms.Form):
    sql = forms.CharField(label='sql')

class SellerCsvImportForm(forms.Form):
    csv_upload = forms.FileField(validators=[FileExtensionValidator(["csv"])])

class SellerXlsxImportForm(forms.Form):
    xlsx_upload = forms.FileField(validators=[FileExtensionValidator(["xlsx"])])

class SellerTxtImportForm(forms.Form):
    txt_upload = forms.FileField(validators=[FileExtensionValidator(["txt"])])

class SellerTxtCsvImportForm(forms.Form):
    txt_upload = forms.FileField(validators=[FileExtensionValidator(["txt"])])

class SellerListDataCsvUpdateForm(forms.Form):
    file = forms.FileField(validators=[FileExtensionValidator(["csv"])])

class SellerM184Form(forms.ModelForm):
    email = forms.EmailField(
        label="Correo electrónico",
        required=False,
        widget=forms.TextInput(attrs={'readonly': 'true'})
    )
    nif_spanish = forms.CharField(
        label="Nº CIF",
        widget=forms.TextInput(attrs={'maxlength': '9'}),
    )
    contact_first_name = forms.CharField(
        label="Nombre de contacto",
    )
    contact_last_name = forms.CharField(
        label="Apellidos de contacto",
    )
    # TODO: CAMBIAR A REPRESENTANTE DE LA TABLA DE REPRESENTANTES
    representative_id = forms.CharField(
        label="NIF del representante",
        widget=forms.TextInput(attrs={'maxlength': '9'}),
    )

    def __init__(self, *args, **kwargs):
        m_form = kwargs.pop('m_form', None)
        required = kwargs.pop('required', False)
        raw_processed_form = kwargs.pop('processed_form', None)
        processed_form = json.loads(raw_processed_form) if raw_processed_form else raw_processed_form

        super().__init__(*args, **kwargs)

        for field in self.fields.values():
            field.disabled = m_form and m_form.is_processed
            field.required = required
            field.widget.attrs['required'] = 'required'

        self.helper = FormHelper()
        self.helper.form_show_labels = False

        sellervat = self.instance.vat_seller.filter(vat_country="ES").first()
        sel = ''
        if sellervat:
            sel = sellervat.vat_number
            if sel and sel.startswith('ES'):
                sel = sel[2:]

        # ESTABLECER VALORES INICIALES RECUPERANDO LA INFORMACION DEL JSON DEL FORMULARIO PROCESADO SI EXISTE
        self.fields['email'].initial = processed_form.get('form').get('email') if processed_form and processed_form.get('form', None) else self.instance.user.email
        self.fields['nif_spanish'].initial =  processed_form.get('form').get('nif_spanish') if processed_form and processed_form.get('form', None) else sel
        self.fields['name'].initial = processed_form.get('form').get('name') if processed_form and processed_form.get('form', None) else self.instance.name
        self.fields['ein'].initial = processed_form.get('form').get('ein') if processed_form and processed_form.get('form', None) else self.instance.ein
        self.fields['contact_first_name'].initial =  processed_form.get('form').get('contact_first_name') if processed_form and processed_form.get('form', None) else self.instance.user.first_name
        self.fields['contact_last_name'].initial = processed_form.get('form').get('contact_last_name') if processed_form and processed_form.get('form', None) else self.instance.user.last_name
        self.fields['contact_phone'].initial = processed_form.get('form').get('contact_phone') if processed_form and processed_form.get('form', None) else self.instance.contact_phone
        self.fields['representative_id'].initial = processed_form.get('form').get('representative_id') if processed_form and processed_form.get('form', None) else self.instance.representative_id
        self.fields['name_representative'].initial = processed_form.get('form').get('name_representative') if processed_form and processed_form.get('form', None) else self.instance.name_representative
        self.fields['last_name_representative'].initial = processed_form.get('form').get('last_name_representative') if processed_form and processed_form.get('form', None) else self.instance.last_name_representative

    def clean_nif_spanish(self):
        cif = self.cleaned_data['nif_spanish']
        cif.upper() if cif else cif

        regex_cif = re.compile(r'^[ABCDEFGHKLMNPQSX][A-Za-z0-9]{8}$')
        sumPar = 0
        sumImpar = 0
        sumTotal = 0

        if regex_cif.match(cif):
            for i in range(1, len(cif) - 1):
                number = int(cif[i])
                if np.isnan(number):
                    raise forms.ValidationError("CIF no válido.")
                if i % 2 == 0:
                    sumPar += number
                else:
                    double = number * 2
                    double = double // 10 + double % 10 if double > 9 else double
                    sumImpar += double
            sumTotal = sumPar + sumImpar
            unit = sumTotal % 10
            control = (10- unit) % 10

            lastChar = cif[-1]
            if lastChar.isdigit():
                if control == int(lastChar):
                    return cif
            else:
                controlChar = 'JABCDEFGHI'[control]
                if lastChar == controlChar:
                    return cif
        else:
            raise forms.ValidationError("El CIF introducido no es válido.")

        return cif

    def save(self, commit=True):
        seller = super().save(commit=False)
        seller.user.first_name = self.cleaned_data['contact_first_name']
        seller.user.last_name = self.cleaned_data['contact_last_name']
        seller.user.save()

        nif_spanish = self.cleaned_data['nif_spanish']
        if nif_spanish.startswith('ES'):
            nif_spanish = nif_spanish[2:]

        seller.save()

        spa_sellervat = seller.vat_seller.filter(vat_country__iso_code="ES")
        if spa_sellervat.exists():
            spa_sellervat.update(vat_number=nif_spanish)
        else:
            SellerVat.objects.create(
                seller=seller,
                vat_country=Country.objects.get(iso_code="ES"),
                vat_number=nif_spanish,
                is_contracted=False,
            )

        return seller

    class Meta:
        model = Seller
        fields = ['email', 'nif_spanish', 'name', 'ein', 'contact_first_name', 'contact_last_name',
                  'contact_phone', 'representative_id', 'name_representative', 'last_name_representative',
                #   'percent_entity' TODO: AÑADIR ESTE CAMPO Y CALCULARLO
                  ]
        widgets = {
            'name': forms.TextInput(attrs={'readonly': 'true'}),
            'percent_entity': forms.NumberInput(attrs={'min': '1', 'max': '100', 'value': '1', 'readonly': 'true'}),
            'contact_phone': CustomPhoneNumberPrefixWidget(
                country_choices=localized_choices(),
                attrs={'phone_prefix': 'True'}
            ),
        }
        labels = {
            'contact_phone': 'Teléfono de la persona de contacto',
            'ein': 'EIN',
            # 'percent_entity': 'Porcentaje de miembros residentes fiscales en España',
        }

class BaseActivityCountryFormSet(forms.BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        required = kwargs.pop('required', False)
        super().__init__(*args, **kwargs)
        # obtiene el country del primer form y lo pone readonly
        first_country_field = self[0].fields['country']
        first_country_field.widget.attrs = {
            'style': 'background-color: #e9ecef; opacity: 1;',
            'onmousedown': 'return false;',
            'onfocus': 'this.blur();',
        }

    def clean(self):
        super().clean()

        # Check if activity doesnt repeat
        countries = set()
        for form in self.forms:
            if form.cleaned_data:
                country = form.cleaned_data.get('country')
                if country:
                    if country in countries:
                        raise forms.ValidationError("No puede haber países repetidos")
                    countries.add(country)

class CustomNumberInput(forms.NumberInput):
    def __init__(self, *args, **kwargs):
        data_type = kwargs.pop('data_type', 'expense')
        super().__init__(*args, **kwargs)
        self.attrs = {
            'value': '',
            'step': '0.01',
            'min': '0',
            'data-type': data_type,
        }

class ActivityCountryForm(forms.ModelForm):

    def clean_economic_activity_type(self):
        income_origin = self.cleaned_data['income_origin']
        data = self.cleaned_data['economic_activity_type']
        if income_origin == '1' and not data:
            raise forms.ValidationError("Este campo es requerido.")
        return data

    def clean_epigraph_iae(self):
        income_origin = self.cleaned_data['income_origin']
        data = self.cleaned_data['epigraph_iae']
        if income_origin == '1' and not data:
            raise forms.ValidationError("Este campo es requerido.")
        return data

    def __init__(self, *args, **kwargs):
        required = kwargs.pop('required', False)
        m_form = kwargs.pop('m_form', None)
        processed_form = kwargs.pop('processed_form', None)
        super().__init__(*args, **kwargs)

        for field in self.fields.values():
            field.disabled = m_form and m_form.is_processed
            field.required = required
            field.widget.attrs['required'] = 'required'

        self.fields['economic_activity_type'].label += '*'
        self.fields['epigraph_iae'].label += '*'
        self.fields['epigraph_iae'].queryset = self.fields['epigraph_iae'].queryset.exclude(
            code__in=['ES-432', 'ES-665.1', 'ES-763']  # epigrafes que no aparecen en hacienda cuando se presenta el 184
        )

    class Meta:
        model = ActivityCountry
        fields = '__all__'
        exclude = ['account_info_m184']
        widgets = {
            'full_income': CustomNumberInput(data_type='income'),
            'personal_expenses': CustomNumberInput(),
            'operating_expenses': CustomNumberInput(),
            'deductible_taxes': CustomNumberInput(),
            'rental_expenses_fee': CustomNumberInput(),
            'repair_costs': CustomNumberInput(),
            'professional_services': CustomNumberInput(),
            'supplies': CustomNumberInput(),
            'financial_expenses': CustomNumberInput(),
            'other_deductibe_expenses': CustomNumberInput(),
        }

class ProcessExcelForm(forms.Form):
    excel = forms.FileField(
        required=False,
        validators=[FileExtensionValidator(["xlsx", "xls"])],
        widget=forms.FileInput(attrs={'accept': 'xlsx, xls'}),
        label="",
    )

    def is_valid(self):
        return super().is_valid() and self.check_excel_validation()

    def transaction_by_reportable(self):
        return {
            'Reportables 1': ['Compra de productos', 'Compra de servicios', 'Venta de productos', 'Venta de servicios'],
            'Reportables 2': ['Contribución', 'Constitución', 'Disolución', 'Adquisición', 'Distribución'],
        }

    def check_excel_validation(self):
        today = datetime.date.today()
        current_presentation_year = today.year - 1

        try:
            presentation_year = self.data.get('presentation_year')
            if presentation_year and len(str(presentation_year)) > 0 and len(str(presentation_year)) == 4:
                current_presentation_year = int(presentation_year)
        except Exception as e:
            print(f"No se ha podido obtener el presentation_year desde el front: {e}")
            pass

        excel = self.cleaned_data['excel']
        error_list = []
        exc = pd.read_excel(excel, sheet_name=None)
        # obtener las hojas del excel
        sheets = exc.keys()
        # verificar si el excel no tiene exactamente 2 hojas
        if len(sheets) != 2:
            error_list.append(_('El archivo excel debe tener exactamente 2 hojas'))
        # verificar si las hojas no contienen "Reportables 1" ni "Reportables 2"
        elif 'Reportables 1' not in sheets or 'Reportables 2' not in sheets:
            error_list.append(
                _('El archivo excel debe tener dos hojas llamadas "Reportables 1" y "Reportables 2".')
            )
        else:
            for sheet in sheets:
                # slugify sheet
                slug_sh = slugify(sheet)
                # crea una lista vacía en el cleaned_data del formulario con el nombre de la hoja slugificada
                self.cleaned_data[f'{slug_sh}'] = []
                # obtener la hoja, desde la 1ra columna hasta la 5ta, y descarta las filas que estén vacías por completo
                df = exc[sheet].iloc[:, :5].dropna(how='all')
                # verificar si la hoja no tiene los encabezados correctos
                if not df.columns.equals(
                    pd.Index(['Fecha', 'Tipo de transacción', 'Descripción', 'Total', 'Moneda'])
                ):
                    error_list.append(_(f'La hoja {sheet} no tiene los encabezados correctos.'))
                else:
                    for index, row in df.iterrows():
                        row_dict = {}
                        exc_index = index + 2
                        date = row['Fecha']
                        transaction = row['Tipo de transacción']
                        desc = row['Descripción']
                        total = row['Total']
                        currency = row['Moneda']

                        # verificar si la fecha no es válida
                        if pd.isna(date):
                            error_list.append(_(f'La fecha en la fila {exc_index} de la hoja {sheet} está vacía.'))
                        elif pd.isna(pd.to_datetime(date, errors='coerce')):
                            error_list.append(_(f'La fecha en la fila {exc_index} de la hoja {sheet} no es válida.'))
                        elif pd.to_datetime(date).year != current_presentation_year:
                            error_list.append(
                                _(f'La fecha en la fila {exc_index} de la hoja {sheet} no corresponde al año de la presentación de este formulario: {current_presentation_year}')
                            )
                        else:
                            row_dict['date'] = pd.to_datetime(date)

                        # verificar si el tipo de transacción no es válido
                        if pd.isna(transaction):
                            error_list.append(
                                _(f'El tipo de transacción en la fila {exc_index} de la hoja {sheet} está vacío.'))
                        elif transaction not in self.transaction_by_reportable()[sheet]:
                            error_list.append(
                                _(f'El tipo de transacción en la fila {exc_index} de la hoja {sheet} no es válido.')
                            )
                        else:
                            for clave, texto in TRANSACTION_TYPE_CHOICES:
                                if texto == _(transaction):
                                    row_dict['transaction_type'] = clave

                        # verificar si la descripción está vacía
                        if pd.isna(desc):
                            error_list.append(
                                _(f'La descripción en la fila {exc_index} de la hoja {sheet} está vacía.'))
                        else:
                            row_dict['description'] = desc

                        # verificar si el total no es válido
                        if pd.isna(total):
                            error_list.append(_(f'El total en la fila {exc_index} de la hoja {sheet} está vacío.'))
                        elif not isinstance(total, (int, float)):
                            error_list.append(_(f'El total en la fila {exc_index} de la hoja {sheet} no es válido.'))
                        else:
                            row_dict['amount'] = abs(total)

                        # verificar si la moneda no es válida
                        if pd.isna(currency):
                            error_list.append(_(f'La moneda en la fila {exc_index} de la hoja {sheet} está vacía.'))
                        elif not re.match(r'^[A-Z]{3}$', str(currency)) or not Currency.objects.filter(
                            code=currency).exists():
                            error_list.append(_(f'La moneda en la fila {exc_index} de la hoja {sheet} no es válida.'))
                        else:
                            row_dict['currency'] = Currency.objects.get(code=currency)

                        # añade el diccionario al cleaned_data del formulario
                        self.cleaned_data[f'{slug_sh}'].append(row_dict)
        if error_list:
            for error in error_list:
                self.add_error('excel', error)
            return False
        return True

class ExcelUploadDateForm(forms.Form):
    excel_file = forms.FileField(label='Selecciona el archivo Excel')

    def clean_excel_file(self):
        file = self.cleaned_data.get('excel_file')
        if not file.name.endswith(('.xls', '.xlsx')):
            raise ValidationError("El archivo debe ser un archivo Excel con extensión .xls o .xlsx")
        return file

class SellerNetYieldsForm(forms.ModelForm):
    class Meta:
        model = SellerYieldRecord
        fields = ['seller', 'year', 'period', 'model','net_yields', 'assessment_type']

# Form SL Seller
class SellerSLForm(forms.ModelForm):
    seller_address_first = forms.CharField(
        label='Dirección de la Empresa',
        max_length=100,
        required=False)
    seller_address_city = forms.CharField(
        label='Ciudad',
        max_length=50,
        required=False
    )
    seller_address_zip = forms.IntegerField(
        label='Código postal',
        required=False,
    )
    seller_address_number = forms.CharField(
        label='Número',
        max_length=10,
        required=False
    )
    seller_address_continue = forms.CharField(
        label='Dirección (Continuación)',
        max_length=100,
        required=False
    )
    seller_address_state = forms.CharField(
        label='Provincia',
        max_length=50,
        required=False
    )
    seller_address_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        label='País',
        required=False
    )
    seller_address_catastral = forms.CharField(
        label='Referencia Catastral',
        max_length=100,
        required=False,
        help_text='Solo aplica a España'
    )
    seller_address_province = forms.ModelChoiceField(
        queryset=ProvinceCode.objects.all(),
        label='Provincia',
        required=False
    )

    main_iae = forms.ModelChoiceField(
        queryset=EconomicActivity.objects.all(),
        label=_('Actividad Económica Principal'),
        required=False
    )

    # Electronic certificate fields
    certified_document = forms.FileField(
        label='Certificado Electrónico',
        required=False,
        widget=forms.FileInput(attrs={
            'accept': '.p12',
            'class': 'form-control'
        }),
        help_text='Sube una copia de tu certificado electrónico en formato .p12'
    )
    certified_document_password = forms.CharField(
        label='Contraseña del Certificado',
        max_length=100,
        required=False,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Ingresa la contraseña del certificado',
            'autocomplete': 'new-password'
        }),
        help_text='Contraseña del certificado electrónico'
    )
    


    class Meta:
        model = Seller
        fields = [
            'name', 
            'social_security_number', 
            'iban', 
            'simultaneous_employment', 
            'mutual_insurance_preferences', 
            'share_capital',
            'management_body',
            'statutes',
            'percentage_affected_activity',
            'contact_phone', 
            'establishment_date', 
            'activity_start_date', 
            'seller_iae'
        ]
        widgets = {
            'contact_phone': CustomPhoneNumberPrefixWidget(
                country_choices=localized_choices(),
                attrs={'phone_prefix': 'True'}
            ),
            'seller_address_first': forms.TextInput(attrs={'maxlength': '100'}),
            'seller_iae': forms.SelectMultiple(attrs={'class': 'select2-multiple'}),   # Widget para selección múltiple
            'simultaneous_employment': forms.Select(choices=[
                ('', '-- Seleccionar --'),
                (True, 'Sí'),
                (False, 'No')
            ]),
            'mutual_insurance_preferences': forms.TextInput(attrs={'placeholder': 'Indica 3 opciones de mutua en orden de preferencia'}),
            'share_capital': forms.NumberInput(attrs={'min': '1', 'step': '1', 'value': '1'}),
            'establishment_date': forms.DateInput(attrs={
                'type': 'date',
                'placeholder': 'dd/mm/yyyy',
                'class': 'form-control'
            }, format='%d/%m/%Y'),
            'activity_start_date': forms.DateInput(attrs={
                'type': 'date',
                'placeholder': 'dd/mm/yyyy',
                'class': 'form-control'
            }, format='%d/%m/%Y'),
        }
        labels = {
            'name': 'Denominación social',
            'social_security_number': 'Número de seguridad social',
            'iban': 'Número de cuenta',
            'simultaneous_employment': '¿Ha estado de alta como autónomo en los últimos dos años?',
            'mutual_insurance_preferences': 'Mutua',
            'share_capital': 'Importe capital',
            'management_body': 'Órgano de administración',
            'statutes': 'Estatutos',
            'percentage_affected_activity': 'Porcentaje de metros de domicilio afecto a la actividad',
            'contact_phone': "Teléfono de contacto",
            'establishment_date': 'Fecha de constitución',
            'activity_start_date': 'Fecha de inicio de actividad',
            'seller_iae': 'Epígrafes IAE',
        }
        help_texts = {
            'contact_phone': 'Número de teléfono de contacto de la empresa',
            'seller_iae': 'Impuesto sobre Actividades Económicas. Puedes seleccionar hasta 4 actividades económicas adicionales a la actividad principal.',
            'iban': 'Es para domiciliar el pago de las cuotas de la Seguridad Social del administrador. Aunque ya sea autónomo nos obligan a ponerlo.',
            'regime_choice': 'Indica tu elección de régimen. Recuerda que si te das de alta en el epígrafe 665 y no eres fabricante de tus propios productos, deberás estar en Recargo de Equivalencia.',
            'simultaneous_employment': 'Es imprescindible informar si trabajas simultáneamente por cuenta ajena.',
            'mutual_insurance_preferences': 'Indica 3 opciones de mutua en orden de preferencia, preferiblemente cercanas a tu domicilio.',
            'certified_document': 'Sube una copia de tu certificado electrónico en formato .p12',
            'certified_document_password': 'Contraseña del certificado electrónico',
            'main_iae': 'Selecciona la actividad económica principal de tu negocio. Esta será la actividad principal registrada en Hacienda.',
        }

    def __init__(self, *args, **kwargs):
        m_form = kwargs.pop('m_form', None)
        required = kwargs.pop('required', False)
        processed_form = json.loads(kwargs.pop('processed_form', None)) if kwargs.get('processed_form') != None else kwargs.pop('processed_form', None)
        super().__init__(*args, **kwargs)

        # Acceder directamente a la estructura de datos
        seller_form_data = processed_form.get('sellerForm') if processed_form else None

        # ESTABLECER VALORES INICIALES
        # Para campos de dirección
        self.fields['seller_address_first'].initial = seller_form_data.get("seller_address_first") if seller_form_data else (self.instance.seller_address.address if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_city'].initial = seller_form_data.get("seller_address_city") if seller_form_data else (self.instance.seller_address.address_city if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_zip'].initial = seller_form_data.get("seller_address_zip") if seller_form_data else (self.instance.seller_address.address_zip if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_province'].initial = seller_form_data.get("seller_address_province") if seller_form_data else (self.instance.seller_address.address_province.code if hasattr(self.instance, 'seller_address') and self.instance.seller_address and self.instance.seller_address.address_province else None)
        self.fields['main_iae'].initial = seller_form_data.get("main_iae") if seller_form_data else None
        self.fields['seller_iae'].help_text = 'Puedes seleccionar hasta 4 actividades económicas adicionales'

        # Para campos del modelo Seller
        self.initial['name'] = seller_form_data.get("name") if seller_form_data else self.instance.name
        self.initial['social_security_number'] = seller_form_data.get("social_security_number") if seller_form_data else self.instance.social_security_number
        self.initial['iban'] = seller_form_data.get("iban") if seller_form_data else self.instance.iban
        self.initial['simultaneous_employment'] = seller_form_data.get("simultaneous_employment") if seller_form_data else self.instance.simultaneous_employment
        self.initial['mutual_insurance_preferences'] = seller_form_data.get("mutual_insurance_preferences") if seller_form_data else self.instance.mutual_insurance_preferences
        # Ensure share_capital always has a default value of 1 if None
        share_capital_value = seller_form_data.get("share_capital") if seller_form_data else self.instance.share_capital
        self.initial['share_capital'] = share_capital_value if share_capital_value is not None else 1
        self.initial['contact_phone'] = seller_form_data.get("contact_phone") if seller_form_data else self.instance.contact_phone
        self.initial['establishment_date'] = seller_form_data.get("establishment_date") if seller_form_data else self.instance.establishment_date
        self.initial['activity_start_date'] = seller_form_data.get("activity_start_date") if seller_form_data else self.instance.activity_start_date
        self.initial['percentage_affected_activity'] = seller_form_data.get("percentage_affected_activity") if seller_form_data else self.instance.percentage_affected_activity
        
        # Para campos de certificado electrónico (custom fields)
        self.fields['certified_document'].initial = seller_form_data.get("certified_document") if seller_form_data else None
        self.fields['certified_document_password'].initial = seller_form_data.get("certified_document_password") if seller_form_data else None
        
        # Para campos de datos mercantiles (model fields)
        self.initial['management_body'] = seller_form_data.get("management_body") if seller_form_data else self.instance.management_body
        self.initial['statutes'] = seller_form_data.get("statutes") if seller_form_data else self.instance.statutes
        self.fields['seller_address_number'].initial = seller_form_data.get("seller_address_number") if seller_form_data else (self.instance.seller_address.address_number if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_continue'].initial = seller_form_data.get("seller_address_continue") if seller_form_data else (self.instance.seller_address.address_continue if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_state'].initial = seller_form_data.get("seller_address_state") if seller_form_data else (self.instance.seller_address.address_state if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_country'].initial = seller_form_data.get("seller_address_country") if seller_form_data else (self.instance.seller_address.address_country_id if hasattr(self.instance, 'seller_address') and self.instance.seller_address and self.instance.seller_address.address_country else None)
        self.fields['seller_address_catastral'].initial = seller_form_data.get("seller_address_catastral") if seller_form_data else (self.instance.seller_address.address_catastral if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')

        # Para el campo ManyToMany seller_iae
        if seller_form_data and seller_form_data.get("seller_iae"):
            # Asumiendo que en el JSON se guarda como lista de IDs
            self.initial['seller_iae'] = seller_form_data.get("seller_iae")
        # Si no hay datos en el formulario procesado, usar los valores actuales de la instancia
        else:
            self.initial['seller_iae'] = self.instance.seller_iae.all().values_list('code', flat=True) if self.instance.pk else []
        
        # Establecer la actividad principal desde los datos existentes
        if not seller_form_data or not seller_form_data.get("main_iae"):
            # Si no hay datos del formulario, intentar obtener la actividad principal de seller_iae
            existing_iae = self.instance.seller_iae.all()
            if existing_iae.exists():
                # Tomar la primera actividad como principal
                self.fields['main_iae'].initial = existing_iae.first()

        # Configurar formatos de fecha
        if 'establishment_date' in self.fields:
            self.fields['establishment_date'].input_formats = ['%d/%m/%Y', '%Y-%m-%d']
        if 'activity_start_date' in self.fields:
            self.fields['activity_start_date'].input_formats = ['%d/%m/%Y', '%Y-%m-%d']

        # Configurar campos
        for field_name, field in self.fields.items():
            # Desactivar campos si el formulario ya está procesado
            field.disabled = m_form and m_form.is_form_processed
            field.required = required
            
            # Hacer campos requeridos en el frontend excepto los especificados
            if field_name not in ['seller_address_first', 'seller_address_city', 'seller_address_zip']:
                field.widget.attrs['required'] = 'required'

    def clean_share_capital(self):
        """Ensure share_capital is never None and has a minimum value of 1"""
        share_capital = self.cleaned_data.get('share_capital')
        if share_capital is None or share_capital == '':
            return 1
        try:
            share_capital = int(share_capital)
            if share_capital < 1:
                raise forms.ValidationError('El capital social debe ser al menos 1 euro.')
            return share_capital
        except (ValueError, TypeError):
            return 1

    def clean(self):
        cleaned_data = super().clean()
        seller_iae = cleaned_data.get('seller_iae')
        main_iae = cleaned_data.get('main_iae')

        # Verificar que no se seleccionen más de 4 actividades adicionales (5 en total incluyendo la principal)
        if seller_iae and len(seller_iae) > 4:
            self.add_error('seller_iae', 'No puedes seleccionar más de 4 actividades económicas adicionales')

        # Verificar que la actividad principal no esté también en las actividades adicionales
        if main_iae and seller_iae and main_iae in seller_iae:
            self.add_error('seller_iae', 'La actividad principal no debe estar incluida en las actividades adicionales')

        return cleaned_data

    def save(self, commit=True):
        # Guardar la instancia del modelo pero no enviarla a la base de datos todavía
        instance = super().save(commit=False)
        
        # Manejar la dirección
        if not hasattr(instance, 'seller_address') or not instance.seller_address:
            instance.seller_address = Address()
        
        # Asignar valores de dirección
        instance.seller_address.address = self.cleaned_data['seller_address_first']
        instance.seller_address.address_city = self.cleaned_data['seller_address_city']
        instance.seller_address.address_zip = self.cleaned_data['seller_address_zip']
        instance.seller_address.address_number = self.cleaned_data['seller_address_number']
        instance.seller_address.address_continue = self.cleaned_data['seller_address_continue']
        instance.seller_address.address_state = self.cleaned_data['seller_address_state']
        instance.seller_address.address_country = self.cleaned_data['seller_address_country']
        instance.seller_address.address_catastral = self.cleaned_data['seller_address_catastral']
        instance.seller_address.address_province = self.cleaned_data['seller_address_province']
        instance.seller_address.squared_meter = self.cleaned_data.get('seller_squared_meter')
        instance.seller_address.commercial_use_percentage = self.cleaned_data.get('seller_commercial_use_percentage')
        
        # Guardar la dirección
        instance.seller_address.save()
        
        if commit:
            # Guardar la instancia principal
            instance.save()
            
            # Guardar relaciones many-to-many si existen
            self.save_m2m()
            
            # Manejar la actividad económica principal
            main_iae = self.cleaned_data.get('main_iae')
            if main_iae:
                # Limpiar actividades existentes
                instance.seller_iae.clear()
                
                # Agregar la actividad principal primero
                instance.seller_iae.add(main_iae)
                
                # Agregar las actividades adicionales si existen
                seller_iae = self.cleaned_data.get('seller_iae')
                if seller_iae:
                    for activity in seller_iae:
                        if activity != main_iae:  # Evitar duplicados
                            instance.seller_iae.add(activity)
        
        return instance

class SellerPersonalInfoForm(forms.ModelForm):
    dni_nie = forms.CharField(
        label="DNI/NIE",
        widget=forms.TextInput(attrs={'maxlength': '9'}),
    )
    dni_nie_document = forms.FileField(
        label='Adjuntar DNI/NIE',
        required=False,
        help_text='Sube una copia de tu DNI o NIE en formato PDF, JPG o PNG'
    )
    member_address_first = forms.CharField(
        label='Dirección Personal',
        max_length=100,
        required=False)
    member_address_city = forms.CharField(
        label='Ciudad',
        max_length=50,
        required=False
    )
    member_address_zip = forms.IntegerField(
        label='Código postal',
        required=False,
    )
    member_address_number = forms.CharField(
        label='Número',
        max_length=10,
        required=False
    )
    member_address_continue = forms.CharField(
        label='Dirección (Continuación)',
        max_length=100,
        required=False
    )
    member_address_state = forms.CharField(
        label='Provincia',
        max_length=50,
        required=False
    )
    member_address_province = forms.ModelChoiceField(
        queryset=ProvinceCode.objects.all(),
        label='Provincia',
        required=False
    )
    member_address_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        label='País',
        required=False
    )

    member_address_catastral = forms.CharField(
        label='Referencia Catastral',
        max_length=100,
        required=False,
        help_text='Solo aplica a España'
    )

    member_squared_meter = forms.FloatField(
        label='Metros cuadrados',
        required=False,
    )

    class Meta:
        model = Seller
        fields = [
            'first_name',
            'last_name',
            'email',
            'birthdate_seller',
            'birth_country',
            'phone',
            'marital_status',
            'marital_status_start_date',
            'spouse_regime',
            'spouse_first_name',
            'spouse_last_name',
            'social_security_number',
        ]
        widgets = {
            'phone': CustomPhoneNumberPrefixWidget(
                country_choices=localized_choices(),
                attrs={'phone_prefix': 'True'}
            ),
            'birthdate_seller': forms.DateInput(attrs={'type': 'date'}),
            'marital_status': forms.Select(),
            'marital_status_start_date': forms.DateInput(attrs={'type': 'date'}),
        }
        labels = {
            'first_name': 'Nombre',
            'last_name': 'Apellidos',
            'email': 'Correo electrónico',
            'birthdate_seller': 'Fecha de nacimiento',
            'birth_country': 'País de nacimiento',
            'phone': 'Teléfono',
            'marital_status': 'Estado civil',
            'marital_status_start_date': 'Fecha de inicio de estado civil',
            'spouse_regime': "Régimen económico matrimonial",
            'spouse_first_name': 'Nombre del cónyuge',
            'spouse_last_name': 'Apellidos del cónyuge',
            'social_security_number': 'Número de Seguridad Social',
        }
        help_texts = {
            'phone': 'Número de teléfono personal',
            'marital_status': 'Estado civil actual',
        }

    def __init__(self, *args, **kwargs):
        m_form = kwargs.pop('m_form', None)
        required = kwargs.pop('required', False)
        processed_form = json.loads(kwargs.pop('processed_form', None)) if kwargs.get('processed_form') != None else kwargs.pop('processed_form', None)
        super().__init__(*args, **kwargs)
        
        # Store required flag for use in clean() method
        self.required_validation = required

        # Acceder directamente a la estructura de datos
        personal_form_data = processed_form.get('personalForm') if processed_form else None

        # ESTABLECER VALORES INICIALES - Para dni_nie
        nif_value = personal_form_data.get("dni_nie") if personal_form_data else getattr(self.instance, 'nif_registration', '')
        self.fields['dni_nie'].initial = nif_value

        # Hacer el campo DNI/NIE de solo lectura si ya tiene un valor en la base de datos
        if hasattr(self.instance, 'nif_registration') and self.instance.nif_registration:
            self.fields['dni_nie'].widget.attrs['readonly'] = 'readonly'

        # Para campos de dirección
        self.fields['member_address_first'].initial = personal_form_data.get("member_address_first") if personal_form_data else (self.instance.member_address.address if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        self.fields['member_address_city'].initial = personal_form_data.get("member_address_city") if personal_form_data else (self.instance.member_address.address_city if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        self.fields['member_address_zip'].initial = personal_form_data.get("member_address_zip") if personal_form_data else (self.instance.member_address.address_zip if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        self.fields['member_address_number'].initial = personal_form_data.get("member_address_number") if personal_form_data else (self.instance.member_address.address_number if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        self.fields['member_address_continue'].initial = personal_form_data.get("member_address_continue") if personal_form_data else (self.instance.member_address.address_continue if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        self.fields['member_address_state'].initial = personal_form_data.get("member_address_state") if personal_form_data else (self.instance.member_address.address_state if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        self.fields['member_address_country'].initial = personal_form_data.get("member_address_country") if personal_form_data else (self.instance.member_address.address_country_id if hasattr(self.instance, 'member_address') and self.instance.member_address and self.instance.member_address.address_country else None)
        self.fields['member_address_province'].initial = personal_form_data.get("member_address_province") if personal_form_data else (self.instance.member_address.address_province.code if hasattr(self.instance, 'member_address') and self.instance.member_address and self.instance.member_address.address_province else None)
        self.fields['member_address_catastral'].initial = personal_form_data.get("member_address_catastral") if personal_form_data else (self.instance.member_address.address_catastral if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        self.fields['member_squared_meter'].initial = personal_form_data.get("member_squared_meter") if personal_form_data else (self.instance.member_address.squared_meter if hasattr(self.instance, 'member_address') and self.instance.member_address else '')
        # Eliminar esta línea duplicada
        # self.fields['dni_nie'].initial = personal_form_data.get("dni_nie") if personal_form_data else getattr(self.instance, 'nif_registration', '')

        # Para campos del modelo Seller
        self.initial['first_name'] = personal_form_data.get("first_name") if personal_form_data else self.instance.first_name
        self.initial['last_name'] = personal_form_data.get("last_name") if personal_form_data else self.instance.last_name
        self.initial['email'] = personal_form_data.get("email") if personal_form_data else self.instance.email
        self.initial['birthdate_seller'] = personal_form_data.get("birthdate_seller") if personal_form_data else self.instance.birthdate_seller
        self.initial['birth_country'] = personal_form_data.get("birth_country") if personal_form_data else self.instance.birth_country_id

        self.initial['phone'] = personal_form_data.get("phone") if personal_form_data else self.instance.phone
        self.initial['marital_status'] = personal_form_data.get("marital_status") if personal_form_data else self.instance.marital_status
        self.initial['marital_status_start_date'] = personal_form_data.get("marital_status_start_date") if personal_form_data else self.instance.marital_status_start_date
        self.initial['social_security_number'] = personal_form_data.get("social_security_number") if personal_form_data else self.instance.social_security_number
        
        # Handle file fields - check both JSON data and existing Document records
        dni_file_from_json = personal_form_data.get("dni_nie_document") if personal_form_data else None
        dni_file_from_db = None
        
        # Check for existing Document record for DNI/NIE
        if self.instance and self.instance.pk:
            from muaytax.app_documents.models import Document, DocumentType
            try:
                doc_type = DocumentType.objects.get(code='DOC-DNI')
                existing_doc = Document.objects.filter(
                    seller=self.instance, 
                    documentType=doc_type
                ).first()
                if existing_doc and existing_doc.file:
                    dni_file_from_db = existing_doc.file.name
            except DocumentType.DoesNotExist:
                pass
        
        # Use JSON data first, then fall back to DB
        current_dni_file = dni_file_from_json or dni_file_from_db
        if current_dni_file:
            self.fields['dni_nie_document'].help_text = f"Documento actual: {current_dni_file}. Sube un nuevo documento para reemplazarlo."

        # Configurar campos
        for field_name, field in self.fields.items():
            # Desactivar campos si el formulario ya está procesado, excepto campos que siempre deben ser editables
            if field_name in ['phone', 'email']:  # Campos que siempre deben ser editables
                field.disabled = False
            else:
                field.disabled = m_form and m_form.is_form_processed
            field.required = required
            
            # Solo hacer campos requeridos en el frontend si required=True
            if required and field_name not in ['member_address_first', 'member_address_city', 'member_address_zip', 'member_address_number', 'member_address_continue', 'member_address_state', 'member_address_country', 'member_address_catastral', 'birth_country', 'marital_status']:
                field.widget.attrs['required'] = 'required'
            else:
                # Remover el atributo required si no es requerido
                if 'required' in field.widget.attrs:
                    del field.widget.attrs['required']
    
    def clean(self):
        cleaned_data = super().clean()
        marital_status = cleaned_data.get('marital_status')
        marital_status_start_date = cleaned_data.get('marital_status_start_date')
        
        # Validar que la fecha de inicio de estado civil sea requerida para casados y parejas de hecho
        # Solo aplicar esta validación cuando required_validation es True (finalizar formulario)
        if (self.required_validation and 
            marital_status in ['married', 'unmarried_couples'] and 
            not marital_status_start_date):
            self.add_error('marital_status_start_date', _('Este campo es requerido para casados y parejas de hecho.'))
        
        return cleaned_data
    
    def clean_dni_nie(self):
        dni_nie = self.cleaned_data.get('dni_nie')
        
        if not dni_nie:
            return dni_nie
        
        # Eliminar espacios y convertir a mayúsculas
        dni_nie = dni_nie.strip().upper()
        
        # Validar NIE (Comienza con X, Y o Z + 7 dígitos + letra)
        if dni_nie.startswith(('X', 'Y', 'Z')):
            if not re.match(r'^[XYZ]\d{7}[A-Z]$', dni_nie):
                raise forms.ValidationError(_('El formato del NIE no es válido. Debe ser una letra (X, Y, Z), seguida de 7 dígitos y una letra.'))
            
            # Reemplazar la letra inicial por su correspondiente número para el cálculo
            nie_number = dni_nie[1:8]
            nie_first_char = dni_nie[0]
            if nie_first_char == 'X':
                nie_number = '0' + nie_number
            elif nie_first_char == 'Y':
                nie_number = '1' + nie_number
            elif nie_first_char == 'Z':
                nie_number = '2' + nie_number
                
            # Obtener letra de control
            control_letter = dni_nie[8]
            calculated_letter = self._calculate_control_letter(int(nie_number))
            
            if control_letter != calculated_letter:
                raise forms.ValidationError(_('La letra de control del NIE no es válida.'))
        
        # Validar DNI (8 dígitos + letra)
        elif re.match(r'^\d{8}[A-Z]$', dni_nie):
            dni_number = dni_nie[:8]
            control_letter = dni_nie[8]
            calculated_letter = self._calculate_control_letter(int(dni_number))
            
            if control_letter != calculated_letter:
                raise forms.ValidationError(_('La letra de control del DNI no es válida.'))
        
        # Otros formatos no son válidos
        else:
            raise forms.ValidationError(_('El formato del DNI/NIE no es válido. Debe ser un DNI (8 dígitos y letra) o un NIE (letra, 7 dígitos y letra).'))
        return dni_nie

    def _calculate_control_letter(self, number):
        """Calcula la letra de control para un DNI/NIE basado en el algoritmo oficial."""
        letters = 'TRWAGMYFPDXBNJZSQVHLCKE'
        return letters[number % 23]

    def save(self, commit=True):
        # Guardar la instancia del modelo pero no enviarla a la base de datos todavía
        instance = super().save(commit=False)
        
        # Asegurar que el campo phone se guarde correctamente
        if 'phone' in self.cleaned_data:
            instance.phone = self.cleaned_data['phone']
        

        
        # Manejar la dirección
        if not hasattr(instance, 'member_address') or not instance.member_address:
            instance.member_address = Address()
        
        # Asignar valores de dirección
        instance.member_address.address = self.cleaned_data['member_address_first']
        instance.member_address.address_city = self.cleaned_data['member_address_city']
        instance.member_address.address_zip = self.cleaned_data['member_address_zip']
        instance.member_address.address_number = self.cleaned_data['member_address_number']
        instance.member_address.address_continue = self.cleaned_data['member_address_continue']
        instance.member_address.address_state = self.cleaned_data['member_address_state']
        instance.member_address.address_country = self.cleaned_data['member_address_country']
        instance.member_address.address_province = self.cleaned_data['member_address_province']
        instance.member_address.address_catastral = self.cleaned_data['member_address_catastral']
        instance.member_address.squared_meter = self.cleaned_data['member_squared_meter']
        
        # Asignar el valor del dni_nie al campo nif_registration
        instance.nif_registration = self.cleaned_data.get('dni_nie', '')

        # Guardar la dirección
        instance.member_address.save()
        
        if commit:
            # Guardar la instancia principal
            instance.save()

            # Procesar el documento del DNI/NIE si se ha proporcionado
            dni_nie_document = self.cleaned_data.get('dni_nie_document')
            
            if dni_nie_document:
                try:
                    doc_type = DocumentType.objects.get(code='DOC-DNI')
                except DocumentType.DoesNotExist:
                    # Si no existe, crear el tipo de documento
                    doc_type = DocumentType.objects.create(
                        code='DOC-DNI',
                        description='Documento Nacional de Identidad / Número de Identidad de Extranjero'
                    )
                
                existing_doc = Document.objects.filter(
                    seller=instance, 
                    documentType=doc_type
                ).first()
                
                if existing_doc:
                    # Actualizar documento existente
                    existing_doc.file = dni_nie_document
                    existing_doc.save()
                else:
                    # Crear nuevo documento
                    doc = Document(
                        file=dni_nie_document,
                        documentType=doc_type,
                        seller=instance,
                        privacy='private'  # Asumimos que es un documento privado
                    )
                    doc.save()
        return instance

# Form Self Employed Seller

class SellerSelfEmployedForm(forms.ModelForm):
    # Self-employed form for electronic certificate and password
    certified_document = forms.FileField(
        label='Adjuntar Certificado Electrónico',
        required=False,
        widget=forms.FileInput(attrs={
            'accept': '.p12',
            'class': 'form-control'
        }),
        help_text='Sube una copia de tu certificado electrónico en formato .p12'
    )
    certified_document_password = forms.CharField(
        label='Contraseña del Certificado',
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Ingresa la contraseña del certificado',
            'autocomplete': 'new-password'
        }),
        help_text='Contraseña del certificado electrónico'
    )
    document_file = forms.FileField(
        label='Documento Sepa completado',
        required=False,
        validators=[FileExtensionValidator(["pdf", "PDF"])],
        widget=forms.FileInput(attrs={
            'accept': '.pdf',
            'class': 'form-control'
        }),
        help_text='Sube el formulario PDF después de completarlo'
    )
    seller_address_first = forms.CharField(
        label='Dirección de la Empresa',
        max_length=100,
        required=False
    )
    seller_address_city = forms.CharField(
        label='Ciudad',
        max_length=50,
        required=False
    )
    seller_address_zip = forms.IntegerField(
        label='Código postal',
        required=False,
    )
    seller_address_number = forms.CharField(
        label='Número',
        max_length=10,
        required=False
    )
    seller_address_continue = forms.CharField(
        label='Dirección (Continuación)',
        max_length=100,
        required=False
    )
    seller_address_state = forms.CharField(
        label='Provincia',
        max_length=50,
        required=False
    )
    seller_address_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        label='País',
        required=False
    )
    seller_address_catastral = forms.CharField(
        label='Referencia Catastral',
        max_length=100,
        required=False,
        help_text='Solo aplica a España'
    )
    seller_address_province = forms.ModelChoiceField(
        queryset=ProvinceCode.objects.all(),
        label='Provincia',
        required=False
    )
    seller_commercial_use_percentage = forms.FloatField(
        label='Uso comercial (%)',
        required=False,
        help_text='Porcentaje de uso comercial del domicilio fiscal'
    )
    seller_squared_meter = forms.FloatField(
        label='Metros cuadrados',
        required=False,
    )

    # Main economic activity field
    main_iae = forms.ModelChoiceField(
        queryset=EconomicActivity.objects.all(),
        label=_('Actividad Económica'),
        required=False,
        help_text=_('Selecciona la actividad económica principal de tu negocio')
    )

    class Meta:
        model = Seller
        fields = ['trade_name', 'contact_phone', 'establishment_date', 'activity_start_date', 'seller_iae', 'iban', 'regime_choice', 'simultaneous_employment', 'mutual_insurance_preferences', 'document_file', 'certified_document', 'certified_document_password', 'seller_address_first', 'seller_address_city', 'seller_address_zip', 'seller_address_number', 'seller_address_continue', 'seller_address_state', 'seller_address_country', 'seller_address_catastral', 'seller_address_province', 'seller_commercial_use_percentage', 'seller_squared_meter']
        widgets = {
            'contact_phone': CustomPhoneNumberPrefixWidget(
                country_choices=localized_choices(),
                attrs={'phone_prefix': 'True'}
            ),
            'activity_start_date': forms.DateInput(attrs={'type': 'date'}),
            'seller_address_first': forms.TextInput(attrs={'maxlength': '100'}),
            'simultaneous_employment': forms.RadioSelect(choices=((True, 'Sí'), (False, 'No'))),
            'mutual_insurance_preferences': forms.TextInput(attrs={
                'maxlength': '100',
                'placeholder': 'Indica tus 3 opciones de mutua en orden de preferencia',
                }),
            'main_iae': forms.Select(attrs={'class': 'select2'}),
        }
        labels = {
            'trade_name': 'Nombre Comercial',
            'contact_phone': "Teléfono de contacto",
            'activity_start_date': 'Fecha de inicio de actividad',
            'seller_iae': 'Epígrafes IAE',
            'iban' : 'IBAN',
            'regime_choice': 'Elección de régimen',
            'simultaneous_employment': '¿Vas a trabajar simultáneamente por cuenta ajena y por cuenta propia?',
            'mutual_insurance_preferences': 'Preferencias de Mutua',
            'certified_document': 'Certificado Electrónico',
            'certified_document_password': 'Contraseña del Certificado',
            'main_iae': 'Actividad Económica Principal',
        }
        help_texts = {
            'contact_phone': 'Número de teléfono de contacto de la empresa',
            'seller_iae': 'Impuesto sobre Actividades Económicas. Puedes seleccionar hasta 4 actividades económicas adicionales a la actividad principal.',
            'iban': 'Nº de cuenta para cuota de autónomo',
            'regime_choice': 'Indica tu elección de régimen. Recuerda que si te das de alta en el epígrafe 665 y no eres fabricante de tus propios productos, deberás estar en Recargo de Equivalencia.',
            'simultaneous_employment': 'Es imprescindible informar si trabajas simultáneamente por cuenta ajena.',
            'mutual_insurance_preferences': 'Indica 3 opciones de mutua en orden de preferencia, preferiblemente cercanas a tu domicilio.',
            'certified_document': 'Sube una copia de tu certificado electrónico en formato .p12',
            'certified_document_password': 'Contraseña del certificado electrónico',
            'main_iae': 'Selecciona la actividad económica principal de tu negocio. Esta será la actividad principal registrada en Hacienda.',
        }

    def __init__(self, *args, **kwargs):
        m_form = kwargs.pop('m_form', None)
        required = kwargs.pop('required', False)
        processed_form = kwargs.pop('processed_form', None)
        seller_form_data = kwargs.pop('seller_form_data', None)
        
        # Handle processed_form if it's a JSON string
        if processed_form and isinstance(processed_form, str):
            try:
                processed_form = json.loads(processed_form)
            except (json.JSONDecodeError, TypeError):
                processed_form = None
        
        super().__init__(*args, **kwargs)

        # print(f"ID de la instancia: {getattr(self.instance, 'id', 'No ID')}")
        # print(f"NIF en instancia al inicializar: {getattr(self.instance, 'nif_registration', 'No encontrado')}")

        # Acceder directamente a la estructura de datos
        if not seller_form_data and processed_form:
            seller_form_data = processed_form.get('sellerForm') if processed_form else None

        # Ensure seller_form_data is a dictionary, not a JSON string
        if seller_form_data and isinstance(seller_form_data, str):
            try:
                seller_form_data = json.loads(seller_form_data)
            except (json.JSONDecodeError, TypeError):
                seller_form_data = None

        # ESTABLECER VALORES INICIALES
        # Para campos de dirección
        self.fields['seller_address_first'].initial = seller_form_data.get("seller_address_first") if seller_form_data else (self.instance.seller_address.address if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_city'].initial = seller_form_data.get("seller_address_city") if seller_form_data else (self.instance.seller_address.address_city if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_zip'].initial = seller_form_data.get("seller_address_zip") if seller_form_data else (self.instance.seller_address.address_zip if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_number'].initial = seller_form_data.get("seller_address_number") if seller_form_data else (self.instance.seller_address.address_number if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_continue'].initial = seller_form_data.get("seller_address_continue") if seller_form_data else (self.instance.seller_address.address_continue if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_state'].initial = seller_form_data.get("seller_address_state") if seller_form_data else (self.instance.seller_address.address_state if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_country'].initial = seller_form_data.get("seller_address_country") if seller_form_data else (self.instance.seller_address.address_country_id if hasattr(self.instance, 'seller_address') and self.instance.seller_address and self.instance.seller_address.address_country else None)
        self.fields['seller_address_catastral'].initial = seller_form_data.get("seller_address_catastral") if seller_form_data else (self.instance.seller_address.address_catastral if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_address_province'].initial = seller_form_data.get("seller_address_province") if seller_form_data else (self.instance.seller_address.address_province.code if hasattr(self.instance, 'seller_address') and self.instance.seller_address and self.instance.seller_address.address_province else None)
        self.fields['seller_squared_meter'].initial = seller_form_data.get("seller_squared_meter") if seller_form_data else (self.instance.seller_address.squared_meter if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['seller_commercial_use_percentage'].initial = seller_form_data.get("seller_commercial_use_percentage") if seller_form_data else (self.instance.seller_address.commercial_use_percentage if hasattr(self.instance, 'seller_address') and self.instance.seller_address else '')
        self.fields['main_iae'].initial = seller_form_data.get("main_iae") if seller_form_data else None

        # Para campos del modelo Seller
        self.initial['contact_phone'] = seller_form_data.get("contact_phone") if seller_form_data else self.instance.contact_phone
        self.initial['activity_start_date'] = seller_form_data.get("activity_start_date") if seller_form_data else self.instance.activity_start_date
        self.initial['iban'] = seller_form_data.get("iban") if seller_form_data else self.instance.iban
        self.initial['trade_name'] = seller_form_data.get("trade_name") if seller_form_data else self.instance.trade_name
        self.initial['regime_choice'] = seller_form_data.get("regime_choice") if seller_form_data else self.instance.regime_choice
        self.initial['simultaneous_employment'] = seller_form_data.get("simultaneous_employment") if seller_form_data else self.instance.simultaneous_employment
        self.initial['mutual_insurance_preferences'] = seller_form_data.get("mutual_insurance_preferences") if seller_form_data else self.instance.mutual_insurance_preferences
        self.initial['seller_iae'] = seller_form_data.get("seller_iae") if seller_form_data else self.instance.seller_iae
        
        # Add missing field initializations
        self.initial['certified_document_password'] = seller_form_data.get("certified_document_password") if seller_form_data else getattr(self.instance, 'certified_document_password', '')
        self.initial['establishment_date'] = seller_form_data.get("establishment_date") if seller_form_data else getattr(self.instance, 'establishment_date', None)
        
        # Handle file fields - check both JSON data and existing Document records
        sepa_file_from_json = seller_form_data.get("document_file") if seller_form_data else None
        cert_file_from_json = seller_form_data.get("certified_document") if seller_form_data else None
        sepa_file_from_db = None
        cert_file_from_db = None
        
        # Check for existing Document records
        if self.instance and self.instance.pk:
            from muaytax.app_documents.models import Document, DocumentType
            
            # Check for SEPA document
            try:
                doc_type = DocumentType.objects.get(code='DOC-SEPA')
                existing_doc = Document.objects.filter(
                    seller=self.instance, 
                    documentType=doc_type
                ).first()
                if existing_doc and existing_doc.file:
                    sepa_file_from_db = existing_doc.file.name
            except DocumentType.DoesNotExist:
                pass
            
            # Check for certificate document
            try:
                doc_type = DocumentType.objects.get(code='DOC-CERT')
                existing_doc = Document.objects.filter(
                    seller=self.instance, 
                    documentType=doc_type
                ).first()
                if existing_doc and existing_doc.file:
                    cert_file_from_db = existing_doc.file.name
            except DocumentType.DoesNotExist:
                pass
        
        # Use JSON data first, then fall back to DB
        current_sepa_file = sepa_file_from_json or sepa_file_from_db
        current_cert_file = cert_file_from_json or cert_file_from_db
        
        if current_sepa_file:
            self.fields['document_file'].help_text = f"Archivo actual: {current_sepa_file}. Sube un nuevo archivo para reemplazarlo."
        
        if current_cert_file:
            self.fields['certified_document'].help_text = f"Certificado actual: {current_cert_file}. Sube un nuevo certificado para reemplazarlo."

        # Para el campo ManyToMany seller_iae
        if seller_form_data and seller_form_data.get("seller_iae"):
            # Asumiendo que en el JSON se guarda como lista de IDs
            self.initial['seller_iae'] = seller_form_data.get("seller_iae")
        # Si no hay datos en el formulario procesado, usar los valores actuales de la instancia
        else:
            self.initial['seller_iae'] = self.instance.seller_iae.all().values_list('code', flat=True) if self.instance.pk else []

        # Configurar campos
        for field_name, field in self.fields.items():
            # Desactivar campos si el formulario ya está procesado
            field.disabled = m_form and m_form.is_form_processed
            field.required = required
            
            # Solo hacer campos requeridos en el frontend si required=True
            if required and field_name not in ['seller_address_first', 'seller_address_city', 'seller_address_zip']:
                field.widget.attrs['required'] = 'required'
            else:
                # Remover el atributo required si no es requerido
                if 'required' in field.widget.attrs:
                    del field.widget.attrs['required']

    def clean(self):
        cleaned_data = super().clean()
        
        # Only apply IAE validation if these fields are actually being submitted
        # Check if the current request contains these fields in the data
        if hasattr(self, 'data') and self.data:
            # Check if main_iae or seller_iae are in the current POST data
            main_iae_in_request = 'main_iae' in self.data
            seller_iae_in_request = 'seller_iae' in self.data
            
            # Only validate if at least one of these fields is being submitted
            if main_iae_in_request or seller_iae_in_request:
                seller_iae = cleaned_data.get('seller_iae')
                main_iae = cleaned_data.get('main_iae')
                
                # Verificar que no se seleccionen más de 4 actividades adicionales (5 en total incluyendo la principal)
                if seller_iae and len(seller_iae) > 4:
                    self.add_error('seller_iae', 'No puedes seleccionar más de 4 actividades económicas adicionales')
                
                # Verificar que la actividad principal no esté también en las actividades adicionales
                if main_iae and seller_iae and main_iae in seller_iae:
                    self.add_error('seller_iae', 'La actividad principal no debe estar incluida en las actividades adicionales')
        
        return cleaned_data

    def save(self, commit=True):
        # Guardar la instancia del modelo pero no enviarla a la base de datos todavía
        instance = super().save(commit=False)
        
        # Manejar la dirección
        if not hasattr(instance, 'seller_address') or not instance.seller_address:
            instance.seller_address = Address()
        
        # Asignar valores de dirección
        instance.seller_address.address = self.cleaned_data['seller_address_first']
        instance.seller_address.address_city = self.cleaned_data['seller_address_city']
        instance.seller_address.address_zip = self.cleaned_data['seller_address_zip']
        instance.seller_address.address_number = self.cleaned_data['seller_address_number']
        instance.seller_address.address_continue = self.cleaned_data['seller_address_continue']
        instance.seller_address.address_state = self.cleaned_data['seller_address_state']
        instance.seller_address.address_country = self.cleaned_data['seller_address_country']
        instance.seller_address.address_catastral = self.cleaned_data['seller_address_catastral']
        instance.seller_address.address_province = self.cleaned_data['seller_address_province']
        instance.seller_address.squared_meter = self.cleaned_data.get('seller_squared_meter')
        instance.seller_address.commercial_use_percentage = self.cleaned_data.get('seller_commercial_use_percentage')
        # Guardar la dirección
        instance.seller_address.save()
        
        if commit:
            # Guardar la instancia principal
            instance.save()
            
            # Guardar relaciones many-to-many si existen
            self.save_m2m()
            
            # Manejar la actividad económica principal
            main_iae = self.cleaned_data.get('main_iae')
            if main_iae:
                # Limpiar actividades existentes
                instance.seller_iae.clear()
                
                # Agregar la actividad principal primero
                instance.seller_iae.add(main_iae)
                
                # Agregar las actividades adicionales si existen
                seller_iae = self.cleaned_data.get('seller_iae')
                if seller_iae:
                    for activity in seller_iae:
                        if activity != main_iae:  # Evitar duplicados
                            instance.seller_iae.add(activity)
            
            # Handle file uploads - save to Document model
            from muaytax.app_documents.models import Document, DocumentType
            
            # Handle SEPA document
            document_file = self.cleaned_data.get('document_file')
            if document_file:
                try:
                    doc_type = DocumentType.objects.get(code='DOC-SEPA')
                except DocumentType.DoesNotExist:
                    doc_type = DocumentType.objects.create(
                        code='DOC-SEPA',
                        description='Documento SEPA completado'
                    )
                
                existing_doc = Document.objects.filter(
                    seller=instance, 
                    documentType=doc_type
                ).first()
                
                if existing_doc:
                    existing_doc.file = document_file
                    existing_doc.save()
                else:
                    doc = Document(
                        file=document_file,
                        documentType=doc_type,
                        seller=instance,
                        privacy='private'
                    )
                    doc.save()
            
            # Handle electronic certificate
            certified_document = self.cleaned_data.get('certified_document')
            if certified_document:
                try:
                    doc_type = DocumentType.objects.get(code='DOC-CERT')
                except DocumentType.DoesNotExist:
                    doc_type = DocumentType.objects.create(
                        code='DOC-CERT',
                        description='Certificado electrónico en formato .p12'
                    )
                
                existing_doc = Document.objects.filter(
                    seller=instance, 
                    documentType=doc_type
                ).first()
                
                if existing_doc:
                    existing_doc.file = certified_document
                    existing_doc.save()
                else:
                    doc = Document(
                        file=certified_document,
                        documentType=doc_type,
                        seller=instance,
                        privacy='private'
                    )
                    doc.save()
        
        return instance

class SellerOSSInfoForm(forms.ModelForm):
    class Meta:
        model = Seller
        fields = [
                'oss_country'
        ]
