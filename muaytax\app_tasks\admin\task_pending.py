from django.contrib import admin
from django.utils import timezone
from muaytax.app_tasks.forms.task_pending import TaskPendingAdminForm

class UserRoleFilter(admin.SimpleListFilter):
    title = 'Tipo de Usuario'
    parameter_name = 'user_role'

    def lookups(self, request, model_admin):
        roles = set(model_admin.model.objects.select_related('user').values_list('user__role', flat=True))
        return [(role, role) for role in roles]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(user__role=self.value())
        return queryset

class TaskPendingAdmin(admin.ModelAdmin):
    form = TaskPendingAdminForm
    list_display = ['user_shortname', 'user_role', 'task_type', 'created_at', 'notification_type', 'due_date', 'seen', 'completed', 'modified_at']
    list_filter = (UserRoleFilter, 'task_type', 'notification_type')
    search_fields = ['description', 'user__username', 'task_type__name']
    readonly_fields = ['modified_at', 'user_shortname', 'last_action_display']

    fieldsets = (
        (None, {
            'fields': ('user', )
        }),
        ('Detalles de la Tarea', {
            'fields': ('task_type', 'description', 'created_at', 'due_date')
        }),
        ('Detalles de Notificaciones', {
            'fields': ('notification_type', 'notification_days_before_deadline')
        }),
        ('Estados', {
            'fields': ('seen', 'completed')
        }),
    )
    
    ordering = ('-due_date',)

    def user_role(self, obj):
        return obj.user_role
    user_role.short_description = 'Tipo de Usuario'

    def user_shortname(self, obj):
        return obj.user_shortname
    user_shortname.short_description = 'Nombre Corto del Usuario'
    
    def last_action_display(self, obj):
        return 'Completado fue cambiado a SI' if obj.last_action else 'Completado fue cambiado a NO'
    last_action_display.short_description = 'Última Acción'

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editando | objeto existente
            return ["user", "user_shortname", "modified_at", "last_action_display"]
        else:  # Añadiendo | nuevo objeto
            return ["user_shortname"]

    def save_model(self, request, obj, form, change):
        if 'completed' in form.changed_data:
            obj.modified_at = timezone.now()
        super().save_model(request, obj, form, change)
        
    class Media:
        js = ('assets/js/admin_task_pending.js',)
        