DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_managementES_list_json_not_model_txt') THEN
        DROP FUNCTION func_managementES_list_json_not_model_txt(date_year INTEGER, date_period VARCHAR);
		DROP FUNCTION func_managementES_list_json_not_model_txt(date_year INTEGER, date_period VARCHAR, entity VARCHAR);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_managementES_list_json_not_model_txt(date_year INTEGER, date_period VARCHAR, entity VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
	legal_entity VARCHAR;
	first_month DATE;
	last_month DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	IF date_period = 'Q1' THEN
					first_month := date_year || '-01-01';
					last_month := date_year || '-04-01';
	ELSIF date_period = 'Q2' THEN
					first_month := date_year || '-04-01';
					last_month := date_year || '-07-01';
	ELSIF date_period = 'Q3' THEN
					first_month := date_year || '-07-01';
					last_month := date_year || '-10-01';
	ELSIF date_period = 'Q4' THEN
					first_month := date_year || '-10-01';
					last_month := (date_year + 1) || '-01-01';
				END IF;


    FOR inv_data IN

		SELECT DISTINCT
		-- id
		sel.id,

		-- seller name
		sel.name AS seller_name,

		-- shortname
		sel.shortname AS seller_shortname,

		-- email
		(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

		-- user name
		(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

		-- last login
		(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

		-- Num Invoices
		COUNT(DISTINCT inv.id) AS num_invoices,

		-- Num Pending Invoices
		COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

		-- Percentage Pending Invoices
		ROUND(COALESCE(
			100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
			0
		), 2) AS percentage_pending_invoices


		FROM sellers_seller sel
		LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
		LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
		WHERE
			(
				( entity = 'all' AND sel.legal_entity IN ('sl', 'self-employed') ) OR
				( entity = sel.legal_entity )
			)					
			AND
			(
				(sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
				OR
				(sel.oss_date < last_month AND (sel.oss_end_date IS NULL OR sel.oss_end_date >= first_month ))
				OR
				(sel.oss IS True)
			)
		-- AND (inv.status_id IN ('pending', 'revision-pending') OR (inv.accounting_date >= first_month AND inv.accounting_date < last_month))
		GROUP BY sel.id
		ORDER BY sel.id


		LOOP
			result_json := result_json || jsonb_build_object(
				'seller_id', inv_data.id,
				'seller_name', inv_data.seller_name,
				'shortname', inv_data.seller_shortname,
				'email', inv_data.email,
				'user_name', inv_data.user_name,
				'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
				'num_pending_invoices', inv_data.num_pending_invoices,
				'percentage_pending_invoices', inv_data.percentage_pending_invoices
			);

		END LOOP;

	RETURN result_json;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION func_managementES_list_json_not_model_txt(date_year INTEGER, date_period VARCHAR) 
RETURNS jsonb AS $$
BEGIN
    RETURN func_managementES_list_json_not_model_txt(date_year, date_period, 'all');
END;
$$ LANGUAGE plpgsql;
