from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.views.generic import UpdateView
from datetime import datetime
from django.core.serializers import serialize

from muaytax.users.permissions import IsSellerShortnamePermission, IsSellerRolePermission
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_documents.models.model_BE15 import ModelFormBE15
from muaytax.app_documents.forms.model_BE15_form import ModelBE15Form
from muaytax.app_documents.forms.general import UserModelsForm
from muaytax.dictionaries.models.product_service import ProductService
from muaytax.utils.general import form_to_dict_for_translation, translate_fields_american_models

class ModelFormBE15View(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), UpdateView):
    model = Seller
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name_suffix = '_request_model_BE15'
    form_class = ModelBE15Form
    current_model_instance = None

    def get_success_url(self):
        return reverse("app_sellers:summary", args=[self.object.shortname])
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['current_model'] = self.current_model_instance
        kwargs['required'] = self.is_required_fields()
        return kwargs
    
    def prepare_forms(self):
        required = self.is_required_fields()
        form = self.get_form()
        user_form = UserModelsForm(
                current_model=self.current_model_instance,
                instance=self.request.user,
                required=required
                )
        
        return {
            'form': form,
            'user_form': user_form, 
        }

    def get_object(self, **kwargs):
        current_year = datetime.now().year - 1
        obj = super().get_object()
        self.current_model_instance = obj.modelbe15_seller.filter(year=current_year).first()
        return obj

    def get_context_data(self, **kwargs):
        kwargs.update(self.prepare_forms())
        kwargs['product_service'] = serialize('json', ProductService.objects.all())
        is_processed = self.current_model_instance.is_processed if self.current_model_instance else False
        kwargs['is_processed'] = is_processed
        print(f"se ha procesado: {is_processed}")
        
        try:
            # Convertir el valore del campo resp_contacted_bea en "Sí" o "No"
            resp_contacted_bea_value = 'Sí' if self.current_model_instance.resp_contacted_bea else 'No' if self.current_model_instance.resp_contacted_bea == False else None
            # Convertir el valore del campo resp_request_bea en "Sí", "No" o None
            resp_request_bea_value = 'Sí' if self.current_model_instance.resp_request_bea else 'No' if self.current_model_instance.resp_request_bea == False else None

            kwargs['resp_contacted_bea_value'] = resp_contacted_bea_value
            kwargs['resp_request_bea_value'] = resp_request_bea_value

        except Exception as e:
            print(f"Error al convertir los valores de los campos: {e}")
        
        return super().get_context_data(**kwargs)       
    
    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        user_form = UserModelsForm(request.POST, instance=self.request.user)

        if form.is_valid() and user_form.is_valid():
            return self.form_valid(form, user_form)
        else:
            return self.form_invalid(form, user_form)
        
    def form_valid(self, form, user_form):
        current_year = datetime.now().year - 1
        information_instance = form.save()
        user_form.save()
        modelBE15_instance, created = ModelFormBE15.objects.get_or_create(
            seller=information_instance,
            year=current_year,
        )
        data_for_translation = form_to_dict_for_translation(form)
        print(f'==========================={data_for_translation}')
        if 'process-submit' in self.request.POST:
            data_for_translation = form_to_dict_for_translation(form)
            translated_data = translate_fields_american_models(data_for_translation)

            modelBE15_instance.is_processed = True
            modelBE15_instance.translations = translated_data

            self.request.session['submitBE15'] = True
            
        modelBE15_instance.resp_contacted_bea = form.cleaned_data.get('resp_contacted_bea')
        modelBE15_instance.resp_request_bea = form.cleaned_data.get('resp_request_bea')
        
        modelBE15_instance.save()

        return HttpResponseRedirect(self.get_success_url())
    
    def form_invalid(self, form, user_form):
        return self.render_to_response(self.get_context_data(form=form, user_form=user_form))
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
    def is_required_fields(self):
        return 'proccess-submit' in self.request.POST or self.request.method == 'GET'