--QUERY PARA SABER EL NÚMERO DE REGISTROS AFECTADOS
--SELECT count(*) FROM sellers_sellervat WHERE it_representation_type = '1' AND vat_representative_id IS NULL

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_representative_insert') THEN
        DROP FUNCTION func_representative_insert(seller_limit INTEGER);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_representative_insert(seller_limit INTEGER)
RETURNS VOID AS $$

DECLARE
sellervat RECORD;
month_cf JSONB := jsonb_build_object(
    'A', '01',
    'B', '02',
    'C', '03',
    'D', '04',
    'E', '05',
    'H', '06',
    'L', '07',
    'M', '08',
    'P', '09',
    'R', '10',
    'S', '11',
    'T', '12'
);

representative RECORD;
birth_date DATE;
gender_rep VARCHAR;
country VARCHAR := 'ES';
day_b VARCHAR;
day_int INTEGER;
month_b VARCHAR;
year_b VARCHAR;

BEGIN
    FOR sellervat IN (
        SELECT DISTINCT
            sv.seller_id AS sellerid,
            sv.it_fiscal_representative AS fiscal_code,
            sv.it_fiscal_representative_name AS name_rep
        FROM sellers_sellervat sv
        WHERE it_representation_type = '1'
        AND vat_country_id = 'IT'
        AND vat_representative_id IS NULL
        ORDER BY sv.seller_id
        LIMIT seller_limit
    )
    LOOP
        IF sellervat.fiscal_code IS NOT NULL AND sellervat.fiscal_code != '-'  THEN
            gender_rep := 'M';
            year_b := SUBSTRING(sellervat.fiscal_code FROM 7 FOR 2);
            IF SUBSTRING(year_b FROM 1 FOR 2) = '0' THEN
                year_b := '20' || year_b;
            ELSE
                year_b := '19' || year_b;
            END IF;


            day_b:= SUBSTRING(sellervat.fiscal_code FROM 10 FOR 2);
            IF day_b::INTEGER > 40 THEN
                day_b:= (day_b::INTEGER - 40)::VARCHAR;
                gender_rep := 'F';
            END IF;

            month_b:= month_cf->>SUBSTRING(sellervat.fiscal_code FROM 9 FOR 1);
            birth_date := year_b || '-' || month_b || '-'|| day_b;

            INSERT INTO representatives_representative
                (
                    first_name,
                    last_name,
                    representative_id,
                    birthdate,
                    gender,
                    birth_country_id,
                    seller_id,
                    created_at,
                    modified_at)
                    VALUES(
                        sellervat.name_rep,
                        NULL,
                        sellervat.fiscal_code,
                        birth_date,
                        gender_rep,
                        country,
                        sellervat.sellerid,
                        CURRENT_TIMESTAMP,
                        CURRENT_TIMESTAMP
                    )RETURNING id INTO representative;

            UPDATE sellers_sellervat
            SET vat_representative_id = representative.id
            WHERE seller_id = sellervat.sellerid
            AND it_representation_type = '1'
            AND vat_country_id = 'IT'
            ;

        END IF;

    END LOOP;


END;
$$ LANGUAGE plpgsql;

--select func_representative_insert(5);