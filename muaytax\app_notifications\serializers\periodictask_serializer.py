from rest_framework import serializers
from django_celery_beat.models import PeriodicTask, ClockedSchedule
from datetime import datetime
from django.utils import timezone

from muaytax.app_notifications.models import NotificationTask
from muaytax.app_notifications.choices import NotificationMethod
from muaytax.app_notifications.controllers.task_mapping import TASK_MAPPING
from muaytax.dictionaries.models import MuaytaxDepartment

class NotificationTaskSerializer(serializers.ModelSerializer):
    sender = serializers.SerializerMethodField()

    class Meta:
        model = NotificationTask
        fields = [
            'id',
            'sender',
            'title',
            'description',
            'notification_method',
            'scheduled_datetime',
            'status',
        ]

    def get_sender(self, obj):
        return {
            'code': obj.sender.code,
            'name': obj.sender.name,
            'email': obj.sender.email,
        }

class NotificationTaskCreateSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=255)
    description = serializers.CharField(required=False, allow_blank=True)
    scheduled_date = serializers.DateField()
    scheduled_time = serializers.TimeField()
    task_function = serializers.CharField(max_length=255)
    sender = serializers.PrimaryKeyRelatedField(queryset=MuaytaxDepartment.objects.all(), required=True, allow_null=True)
    notification_method = serializers.ChoiceField(choices=NotificationMethod.choices, required=True)

    def validate(self, data):
        schedule_date = data.get('scheduled_date')
        schedule_time = data.get('scheduled_time')

        if not schedule_date or not schedule_time:
            raise serializers.ValidationError("Both scheduled_date and scheduled_time are required")
        
        combined_datetime = datetime.combine(schedule_date, schedule_time)
        if timezone.is_aware(combined_datetime):
            scheduled_datetime = combined_datetime
        else:
            scheduled_datetime = timezone.make_aware(combined_datetime)

        # if scheduled_datetime < timezone.now() + timezone.timedelta(hours=1):
        if scheduled_datetime < timezone.now():
            raise serializers.ValidationError("La fecha programada debe ser al menos 1 hora en el futuro")
        
        data['scheduled_datetime'] = combined_datetime

        return data

    def create(self, validated_data):
        title = validated_data.get('title')
        description = validated_data.get('description', '')
        scheduled_datetime = validated_data.get('scheduled_datetime')
        task_function_key = validated_data.get('task_function')
        sender = validated_data.get('sender')
        notification_method = validated_data.get('notification_method')

        task_function = TASK_MAPPING.get(task_function_key)

        if not task_function:
            raise serializers.ValidationError(f"Invalid task function: {task_function_key}")

        # Step 1: Create the NotificationTask first
        notification_task = NotificationTask.objects.create(
            title=title,
            description=description,
            scheduled_datetime=scheduled_datetime,
            sender=sender,
            notification_method=notification_method,
        )

        # Step 2: Create the ClockedSchedule and PeriodicTask
        clocked_schedule, _ = ClockedSchedule.objects.get_or_create(clocked_time=scheduled_datetime)
        periodic_task = PeriodicTask.objects.create(
            name=f"{title} - {notification_task.id}",
            task=task_function,
            clocked=clocked_schedule,
            one_off=True,
            args=[notification_task.id],
        )

        # Step 3: Link the PeriodicTask back to the NotificationTask
        notification_task.periodic_task = periodic_task
        notification_task.save()

        return notification_task