import json
import os

# Construir la ruta al archivo JSON desde la ubicación del script
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Directorio raíz del proyecto
json_path = os.path.join(BASE_DIR, "muaytax", "dictionaries", "data", "store_products.json")

def check_duplicate_codes_from_file(file_path):
    """Lee el archivo JSON y busca códigos duplicados."""
    if not os.path.exists(file_path):
        print(f"El archivo {file_path} no existe.")
        return

    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    seen = set()
    duplicates = {}

    for item in data:
        code = item.get("code")
        if code in seen:
            duplicates[code] = duplicates.get(code, 1) + 1  # Contar repeticiones
        seen.add(code)

    if duplicates:
        print("Códigos duplicados encontrados:")
        for code, count in duplicates.items():
            print(f"- Código: {code} → {count} veces")
    else:
        print("No hay códigos duplicados.")

# Ejecutar la función
if __name__ == "__main__":
    check_duplicate_codes_from_file(json_path)
