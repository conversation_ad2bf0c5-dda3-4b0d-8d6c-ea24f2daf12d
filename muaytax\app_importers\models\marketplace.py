from django.db import models
from muaytax.app_marketplaces.models.marketplace import Marketplace
from muaytax.app_importers.choices import MarketplaceImporterStatusChoices

class MarketplaceOrderImporter(models.Model):
    """
    Modelo base para importadores de órdenes de Marketplace.
    Este modelo controla y recoge las acciones de importación de facturas del marketplace.
    Permite al gestor importar órdenes de un marketplace específico para un vendedor.
    Cada importación está asociada a un marketplace y a un vendedor, y registra el estado de la importación,
    """
    marketplace = models.ForeignKey(
        Marketplace,
        on_delete=models.CASCADE,
        related_name="order_importers_marketplace",
        verbose_name="Marketplace",
    )
    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="order_importers_marketplace_seller",
        verbose_name="Vendedor",
    )
    status = models.CharField(
        max_length=50,
        choices=MarketplaceImporterStatusChoices.choices,
        default=MarketplaceImporterStatusChoices.NO_STATUS,
        verbose_name="Estado",
        help_text="Estado de la importación de las órdenes del marketplace."
    )
    start_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Fecha de Inicio",
        help_text="Fecha y hora de inicio de la importación de órdenes."
    )
    total_orders = models.PositiveIntegerField(
        default=0,
        verbose_name="Total de Órdenes",
        help_text="Número total de órdenes importadas desde el marketplace."
    )
    total_orders_created = models.PositiveIntegerField(
        default=0,
        verbose_name="Total de Órdenes Creadas",
        help_text="Número total de órdenes creadas en MuayTax a partir de la importación."
    )
    total_orders_failed = models.PositiveIntegerField(
        default=0,
        verbose_name="Total de Órdenes Fallidas",
        help_text="Número total de órdenes que fallaron durante la importación."
    )
    errors = models.JSONField(
        blank=True,
        null=True,
        verbose_name="Errores",
        help_text="Lista de errores encontrados durante la importación de órdenes."
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Fecha de Creación",
        help_text="Fecha y hora en que se creó la importación."
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Fecha de Actualización",
        help_text="Fecha y hora de la última actualización de la importación."
    )

    class Meta:
        verbose_name = "Importador de Órden de Marketplace"
        verbose_name_plural = "Importador de Órdenes de Marketplace"
        ordering = ['-created_at']

    def __str__(self):
        return f"Importación {self.pk} de {self.marketplace.shopname}"
    
class MarketplaceInvoiceGenerator(models.Model):
    """
    Modelo para gestionar la generación de facturas a partir de órdenes importadas de un marketplace.
    Este modelo permite al gestor generar facturas basadas en las órdenes importadas y asociarlas al marketplace y vendedor correspondientes.
    """
    marketplace = models.ForeignKey(
        Marketplace,
        on_delete=models.CASCADE,
        related_name="invoice_generators_marketplace",
        verbose_name="Marketplace",
        help_text="Marketplace asociado a la generación de facturas."
    )
    seller = models.ForeignKey(
        "sellers.Seller",
        on_delete=models.CASCADE,
        related_name="invoice_generators_marketplace_seller",
        verbose_name="Vendedor",
        help_text="Vendedor asociado a la generación de facturas."
    )
    status = models.CharField(
        max_length=50,
        choices=MarketplaceImporterStatusChoices.choices,
        default=MarketplaceImporterStatusChoices.NO_STATUS,
        verbose_name="Estado",
        help_text="Estado de la generación de facturas."
    )
    orders_processed = models.PositiveIntegerField(
        default=0,
        verbose_name="Órdenes Procesadas",
        help_text="Número de órdenes procesadas para la generación de facturas."
    )
    total_invoices_created = models.PositiveIntegerField(
        default=0,
        verbose_name="Total de Facturas Creadas",
        help_text="Número total de facturas creadas a partir de las órdenes importadas."
    )
    total_invoices_failed = models.PositiveIntegerField(
        default=0,
        verbose_name="Total de Facturas Fallidas",
        help_text="Número total de facturas que fallaron durante la generación."
    )
    errors = models.JSONField(
        blank=True,
        null=True,
        verbose_name="Errores",
        help_text="Lista de errores encontrados durante la generación de facturas."
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Fecha de Creación",
        help_text="Fecha y hora en que se creó la generación de facturas."
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Fecha de Actualización",
        help_text="Fecha y hora de la última actualización de la generación de facturas."
    )

    class Meta:
        verbose_name = "Generador de Facturas del Marketplace"
        verbose_name_plural = "Generadores de Facturas del Marketplace"
        ordering = ['-created_at']

    def __str__(self):
        return f"Generación {self.pk} para {self.marketplace.shopname}"



