import json

from datetime import datetime

from django.db import connection
from django.urls import reverse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponseRedirect, JsonResponse
from django.views.generic import ListView
from django_datatables_view.base_datatable_view import BaseDatatableView

from muaytax.users.models import User
from muaytax.users.permissions import IsManagerRolePermission, IsSellerShortnamePermission
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.dictionaries.models.countries import Country
from muaytax.app_invoices.models.invoice import Invoice

class SellerVatManagementListView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Seller

    def get_template_names(self):
        country = self.kwargs["country"].upper()

        if country == 'ES':
            return ["sellers/seller_list/seller_vat_spain.html"]
        # elif country == 'DE':
        #     return ['sellers/seller_vat-germany.html']
        # elif country == 'IT':
        #     if self.get_period(self.request) == '0A':
        #         return ["sellers/seller_vat-italy_yearly.html"]
        #     else:
        #         return ['sellers/seller_vat-italy.html']
        # elif country == 'FR':
        #     return ['sellers/seller_vat-france.html']
        # elif country == 'GB':
        #     return ['sellers/seller_vat-gb.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        year = self.get_year(self.request)
        period = self.get_period(self.request)
        country_code = self.kwargs["country"].upper()
        country = Country.objects.get(iso_code=country_code)
        sellervat_manager = SellerVat.objects.filter(vat_country__iso_code =country_code, manager_assigned__isnull= False)
        manager_ids = sellervat_manager.values_list('manager_assigned', flat=True)
        managers = User.objects.filter(role='manager', id__in = manager_ids)
        total_pending_invoices = Invoice.objects.filter(
            seller__vat_seller__vat_country__iso_code=country.iso_code,
            seller__vat_seller__is_contracted=True).filter(status="pending").count()

        context['period'] = period
        context['year'] = year
        context['month'] = self.get_month_period(self.request)
        context['country'] = country
        context['total_pending_invoices'] = total_pending_invoices
        context['managers'] = managers
        context['current_user_id'] = self.request.user.pk

        return context

    def get_year(self, request):
        year = None
        year = request.GET.get('year')
        month = datetime.now().month
        if (year is None):
            year = datetime.now().year
            if month == 1:
                year = year - 1
        return year

    def get_period(self, request):
        period = None
        period = request.GET.get('period')
        if (period is None):
            month = datetime.now().month
            if month >= 2 and month <= 4:
                period = "Q1"
            elif month >= 5 and month <= 7:
                period = "Q2"
            elif month >= 8 and month <= 10:
                period = "Q3"
            elif month >= 11 and month <= 12:
                period = "Q4"
            if month == 1:
                period = "Q4"
        return period

    def get_month_period(self, request) -> str:
        month = None
        month = request.GET.get('period')
        if (month is None):
            month = datetime.now().month - 1
            return f'M{month}'
        return month
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
class SellerVatManagementListDT(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), BaseDatatableView):
    def get(self, request, *args, **kwargs):
        country_code = kwargs["country"].upper()
        year = self.request.GET.get('year')
        period = self.request.GET.get('period')
        return self.process_sql(request, year, period, country_code)
    
    def process_sql(self, request, year, period, country_code):
        if country_code == 'ES':
            return self.process_sql_vat_spain(request, year, period)
    
    def process_sql_vat_spain(self, request, year, period):
        sql = "SELECT func_vat_es_cached_list_json(%s, %s);"
        params = (year, period)

        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                result_json = cursor.fetchone()[0]
                result_data = json.loads(result_json)

                response = {
                    'draw': int(request.GET.get('draw', 1)),
                    'data': result_data['data'],
                    'agreed_model_count': result_data['agreed_model_count'],
                    'pending_model_count': result_data['pending_model_count'],
                    'required_model_count': result_data['required_model_count'],
                    'disagreed_model_count': result_data['disagreed_model_count'],
                    'presented_model_count': result_data['presented_model_count'],
                    'total_invoices': result_data['total_pending_invoices'],
                }
                return JsonResponse(response)
        except Exception as e:
            print("Error:", e)
            response = {
                'data': [],
                "error": str(e)
            }
            return JsonResponse(response)