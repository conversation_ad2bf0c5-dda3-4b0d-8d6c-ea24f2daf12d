CREATE OR REPLACE FUNCTION func_vat_es_cached_list_json(
    date_year INTEGER,
    date_period VARCHAR
) RETURNS jsonb AS $$
DECLARE
    first_month DATE;
    last_month DATE;
    result_json jsonb := '[]';
BEGIN
    IF date_period = 'Q1' THEN
        first_month := (date_year || '-01-01')::DATE;
        last_month := (date_year || '-04-01')::DATE;
    ELSIF date_period = 'Q2' THEN
        first_month := (date_year || '-04-01')::DATE;
        last_month := (date_year || '-07-01')::DATE;
    ELSIF date_period = 'Q3' THEN
        first_month := (date_year || '-07-01')::DATE;
        last_month := (date_year || '-10-01')::DATE;
    ELSIF date_period = 'Q4' THEN
        first_month := (date_year || '-10-01')::DATE;
        last_month := ((date_year + 1) || '-01-01')::DATE;
    ELSIF date_period = '0A' THEN
        first_month := (date_year || '-01-01')::DATE;
        last_month := ((date_year + 1) || '-01-01')::DATE;
    END IF;

    -- Main query with aggregation and JSON construction
    SELECT jsonb_build_object(
        'pending_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_184 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'pending' THEN 1 ELSE 0 END)
            ), 0
        ),
        'required_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_184 = 'required' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'required' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'required' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'required' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'required' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'required' THEN 1 ELSE 0 END)
            ), 0
        ),
        'disagreed_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_184 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'disagreed' THEN 1 ELSE 0 END)
            ), 0
        ),
        'agreed_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_184 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'agreed' THEN 1 ELSE 0 END)
            ), 0
        ),
        'presented_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_184 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'presented' THEN 1 ELSE 0 END)
            ), 0
        ),
        'total_pending_invoices', COALESCE(SUM(subq.num_pending_invoices), 0),
        'data', COALESCE(jsonb_agg(
            jsonb_build_object(
                'seller_id', subq.subq_seller_id,
                'seller_name', subq.seller_name,
                'shortname', subq.seller_shortname,
                'email', subq.email,
                'user_name', subq.user_name,
                'last_login', to_char(subq.last_login, 'YYYY-MM-DD HH24:MI:SS'),
                'manager_assigned', subq.manager_assigned,
                'total_invoices_from_period', subq.total_invoices_from_period,
                'num_pending_invoices', subq.num_pending_invoices,
                'percentage_pending_invoices', subq.percentage_pending_invoices,
                'model_184', subq.model_184,
                'model_303', subq.model_303,
                'model_347', subq.model_347,
                'model_349', subq.model_349,
                'model_369', subq.model_369,
                'model_390', subq.model_390,
                'model_min', subq.model_min,
                'model_avg', subq.model_avg,
                'month1', subq.txt_01,
                'month2', subq.txt_02,
                'month3', subq.txt_03,
                'month4', subq.txt_04,
                'month5', subq.txt_05,
                'month6', subq.txt_06,
                'month7', subq.txt_07,
                'month8', subq.txt_08,
                'month9', subq.txt_09,
                'month10', subq.txt_10,
                'month11', subq.txt_11,
                'month12', subq.txt_12,
                'model_json_result', subq.model_json_result,
                'monthly_muaytax_json_invoices', subq.monthly_muaytax_json_invoices
            )
        ), '[]')
    ) INTO result_json

    FROM (
        WITH invs AS (
            SELECT DISTINCT
                inv.seller_id AS invs_seller_id,
                COALESCE(COUNT(inv.id), 0) AS total_invoices_from_period,
                COALESCE(SUM(CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE 0 END), 0) AS num_pending_invoices,
                CASE 
                    WHEN COUNT(inv.id) = 0 THEN 0 
                    ELSE ROUND(COALESCE(SUM(CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE 0 END), 0)::numeric / COUNT(inv.id)::numeric * 100.00, 2)
                END AS percentage_pending_invoices
            FROM invoices_invoice inv
            WHERE 
                (inv.invoice_category_id IS NULL OR inv.invoice_category_id NOT LIKE '%_copy')
                AND (inv.status_id IN ('pending', 'revision-pending') OR (inv.accounting_date >= first_month AND inv.accounting_date < last_month))
            GROUP BY inv.seller_id
        )
        SELECT DISTINCT
            sel.id AS subq_seller_id,
            sel.name AS seller_name, 
            sel.shortname AS seller_shortname, 
            usr.name AS user_name,
            usr.email AS email,
            usr.last_login,
            sv.manager_assigned_id AS manager_assigned,
            list.*,
            COALESCE(invs.total_invoices_from_period, 0) AS total_invoices_from_period, 
            COALESCE(invs.num_pending_invoices, 0) AS num_pending_invoices, 
            COALESCE(invs.percentage_pending_invoices, 0) AS percentage_pending_invoices,
            0 AS model_min,
            0 AS model_avg
        FROM lists_sellervatlistes list
        INNER JOIN sellers_seller sel ON sel.id = list.seller_id
        LEFT JOIN users_user usr ON usr.id = sel.user_id
        LEFT JOIN invs ON invs.invs_seller_id = sel.id
        LEFT JOIN sellers_sellervat sv ON sv.seller_id = sel.id AND sv.vat_country_id ='ES'
        WHERE 
            list.show IS TRUE
            AND list.year = date_year
            AND list.period_id = date_period
            AND (
                sel.legal_entity NOT IN ('sl', 'self-employed')
                OR (
                    sel.legal_entity = 'self-employed-outside'
                    AND sv.vat_country_id = 'ES'
                )
            )
    ) AS subq;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- Example call to the function:
-- SELECT func_vat_es_cached_list_json(2024, 'Q1');
