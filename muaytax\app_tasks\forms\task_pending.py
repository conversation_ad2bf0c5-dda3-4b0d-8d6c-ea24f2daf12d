from django import forms
from muaytax.app_tasks.models.task_pending import TaskPending
from muaytax.users.models import User

class TaskPendingAdminForm(forms.ModelForm):
    user_shortname = forms.CharField(
        label="Nombre Corto del Usuario",
        required=False,
        widget=forms.TextInput(attrs={'readonly': 'readonly'})
    )

    class Meta:
        model = TaskPending
        fields = ['user', 'task_type', 'description', 'created_at', 'notification_type', 'notification_days_before_deadline', 'due_date', 'seen', 'completed']


class TaskPendingForm(forms.ModelForm):
    class Meta:
        model = TaskPending
        fields = ['user', 'task_type', 'description', 'notification_type', 'notification_days_before_deadline', 'due_date']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrar para obtener todos los usuarios con rol 'seller'
        self.fields['user'].queryset = User.objects.filter(role='seller')
        self.fields['user'].label = "Seller"
