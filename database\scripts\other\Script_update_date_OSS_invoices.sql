UPDATE invoices_invoice SET accounting_date = '2024-04-01', status_id = 'revised'
WHERE accounting_date >= '2024-01-01'
AND accounting_date < '2024-04-01'
AND status_id IN ('revised','pending', 'revision-pending')
AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
AND (transaction_type_id IN ('oss', 'oss_refund') OR is_oss IS TRUE)
AND seller_id IN (258, 278, 571, 976, 846)

------------COMPROBAR QUE SELLERS ESTÁN EN LA LISTA DE SELLERS QUE SE ACTUALIZAN---------
SELECT name, first_name, last_name, id, shortname FROM sellers_seller WHERE id IN (258, 278, 571, 976, 846)

-------COMPROBAR CUANTAS FACTURAS SE VEN AFECTADAS POR LA ACTUALIZACIÓN---------
SELECT count(id) FROM  invoices_invoice
WHERE accounting_date >= '2024-01-01'
AND accounting_date < '2024-04-01'
AND status_id IN ('revised','pending', 'revision-pending')
AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
AND (transaction_type_id IN ('oss', 'oss_refund') OR is_oss IS TRUE)
AND seller_id IN (258, 278, 571, 976, 846)