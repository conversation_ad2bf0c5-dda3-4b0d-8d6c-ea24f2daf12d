from django.core.management.base import BaseCommand

from muaytax.app_invoices.models.invoice import Invoice
from muaytax.dictionaries.models.transaction_type import TransactionType


class Command(BaseCommand):
    help = "Change intra-community-expense-cc-700 to intra-community-expense"

    def handle(self, *args, **options):
        # lo cambia
        trans_cc700 = TransactionType.objects.filter(code="intra-community-expense-cc-700").first()
        if trans_cc700:
            invoices = Invoice.objects.filter(transaction_type=trans_cc700)
            # invoices.update(transaction_type=TransactionType.objects.get(code="intra-community-expense"))
            for invoice in invoices:
                invoice.transaction_type = TransactionType.objects.get(code="intra-community-expense")
                invoice.save()
                self.stdout.write(self.style.SUCCESS(f"Successfully changed {invoice} to intra-community-expense"))
            # lo borra
            trans_cc700.delete()
            self.stdout.write(self.style.SUCCESS("Successfully deleted intra-community-expense-cc-700"))
        else:
            self.stdout.write(self.style.SUCCESS("No intra-community-expense-cc-700 found"))
