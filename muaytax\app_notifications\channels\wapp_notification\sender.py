import requests
from os import environ
from dataclasses import dataclass

from muaytax.dictionaries.models import MuaytaxDepartment

@dataclass
class WappNotification:
    base_url: str = 'https://graph.facebook.com/v20.0/{sender_id}/messages'
    auth_token: str = environ.get('WHATSAPP_AUTH_TOKEN')
    template_name: str = 'pending_models'
    language_code: str = 'es_ES'
    sender: MuaytaxDepartment = None
    to_number: str = None
    seller_name: str = None

    def _build_headers(self) -> dict:
        """Builds the headers required for the request."""
        return {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }

    def _build_template_payload(self) -> dict:
        """Builds the payload for the WhatsApp API request."""
        return {
            "messaging_product": "whatsapp",
            "to": self.to_number,
            "type": "template",
            "template": {
                "name": self.template_name,
                "language": {
                    "code": self.language_code
                },
                "components": [
                    {
                        "type": "body",
                        "parameters": [
                            {
                                "type": "text",
                                "text": self.seller_name or "Default Seller Name"  # Fallback if seller is None
                            }
                        ]
                    }
                ]
            }
        }

    def _build_url(self) -> str:
        """Builds the URL for the request."""
        if not self.sender or not self.sender.whatsapp_number_id:
            raise ValueError("El sender debe tener un ID de número de WhatsApp válido.")
        return self.base_url.format(sender_id=self.sender.whatsapp_number_id)

    def send_wapp(self):
        """Send the WhatsApp message by calling the Facebook Graph API."""

        # validate required fields
        if not self.to_number:
            raise ValueError("Número de teléfono de destino debe ser proporcionado.")
        if not self.seller_name:
            raise ValueError("Vendedor debe ser proporcionado.")
        
        url = self._build_url()
        payload = self._build_template_payload()
        headers = self._build_headers()

        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()
        
        except Exception as e:
            return {
                "error": "Request has failed",
                "details": str(e),
                "response": response.text if response.text else None
            }

