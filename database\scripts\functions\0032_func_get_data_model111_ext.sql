DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_get_data_model_111_externo') THEN
        DROP FUNCTION func_get_data_model_111_externo(sellerid INTEGER, period_starts VARCHAR, period_ends VARCHAR);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_get_data_model_111_externo(sellerid INTEGER, period_starts VARCHAR, period_ends VARCHAR)
RETURNS jsonb AS $$
DECLARE
    result_json jsonb := '[]';
    json_object JSONB;
BEGIN

    SELECT jsonb_agg(sub_data) INTO result_json
    FROM(
        WITH workers_merge AS (
            SELECT
                inv.id AS payroll_invoice,
                CONCAT(work.first_name, ' ', work.last_name) AS full_name,
                work.nif_nie,
                adr.address AS worker_address,
                adr.address_zip AS worker_address_zip,
                adr.address_city AS worker_address_city
            FROM invoices_invoice inv
            LEFT JOIN invoices_payrollworkerinvoice inv_work ON inv_work.invoice_id = inv.id
            LEFT JOIN invoices_payrolladministratorinvoice inv_admin ON inv_admin.invoice_id = inv.id
            LEFT JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN workers_worker work ON work.id = COALESCE(inv_work.worker_id, inv_admin.worker_id)
            LEFT JOIN address_address adr ON adr.id = work.address_id
            WHERE 
                status_id = 'revised'
                AND inv.tax_country_id = 'ES'
                AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
                AND inv.invoice_type_id = 'payroll'
                AND con.irpf > 0
                AND con.is_supplied IS NOT true
                AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        )

        SELECT
            sel.id AS seller_id,
            sel.shortname AS seller_shortname,
            inv.id AS invoice_id,
            inv.accounting_date,
            inv.invoice_type_id,
            inv.status_id,
            MAX(
                CASE
                    WHEN cust_info IS NOT NULL then cust_info.cust_name
                    WHEN worker_info is NOT NULL THEN worker_info.full_name
                    WHEN prov_info IS NOT NULL THEN prov_info.prov_name
                END
            ) AS nombre_apellido,
            MAX(
                CASE 
                    WHEN cust_info IS NOT NULL then cust_info.cust_nif_cif_iva
                    WHEN worker_info is NOT NULL THEN worker_info.nif_nie
                    WHEN prov_info IS NOT NULL THEN prov_info.prov_nif_cif_iva
                END
            ) AS nif_cif_iva,
            MAX(
                CASE 
                    WHEN cust_info IS NOT NULL then cust_info.cust_address
                    WHEN worker_info is NOT NULL THEN worker_info.worker_address
                    WHEN prov_info IS NOT NULL THEN prov_info.prov_address
                END
            ) AS address,
            MAX(
                CASE 
                    WHEN cust_info IS NOT NULL then cust_info.cust_address_zip
                    WHEN worker_info is NOT NULL THEN worker_info.worker_address_zip
                    WHEN prov_info IS NOT NULL THEN prov_info.prov_address_zip
                END
            ) AS address_zip,
            MAX(
                CASE 
                    WHEN cust_info IS NOT NULL then cust_info.cust_address_city
                    WHEN worker_info is NOT NULL THEN worker_info.worker_address_city
                    WHEN prov_info IS NOT NULL THEN prov_info.prov_address_city
                END
            ) AS address_city,
            REPLACE(SUM(con.amount_euros * COALESCE(con.quantity, 1))::text, '.', ',') AS total_con_base,
            con.irpf AS irpf,
            ROUND(SUM(con.amount_euros * COALESCE(con.quantity, 1) * con.irpf / 100)::numeric, 2) AS total_retencion_result

        FROM
            sellers_seller sel
        INNER JOIN invoices_invoice inv ON sel.id = inv.seller_id
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id

        -- CLIENTES
        LEFT JOIN(
            SELECT
                cust.id AS cust_id,
                cust.name AS cust_name,
                cust.nif_cif_iva AS cust_nif_cif_iva,
                adr.address AS cust_address,
                adr.address_zip AS cust_address_zip,
                adr.address_city AS cust_address_city
            FROM
                customers_customer cust
            LEFT JOIN address_address adr ON cust.customer_address_id = adr.id
        ) cust_info ON cust_info.cust_id = inv.customer_id

        -- PROVEEDORES
        LEFT JOIN(
            SELECT
                prov.id AS prov_id,
                prov.name AS prov_name,
                prov.nif_cif_iva AS prov_nif_cif_iva,
                adr.address AS prov_address,
                adr.address_zip AS prov_address_zip,
                adr.address_city AS prov_address_city
            FROM
                providers_provider prov
            LEFT JOIN address_address adr ON prov.provider_address_id = adr.id
        ) prov_info ON prov_info.prov_id = inv.provider_id

        LEFT JOIN invoices_payrollworkerinvoice pay_worker ON pay_worker.invoice_id = inv.id
        LEFT JOIN invoices_payrolladministratorinvoice pay_ad ON pay_ad.invoice_id = inv.id

        LEFT JOIN workers_merge worker_info ON worker_info.payroll_invoice = inv.id

        WHERE
        -- 	sel.id = 230
            sel.id = sellerid
            AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
            AND inv.accounting_date >= period_starts::DATE
            AND inv.accounting_date < period_ends::DATE
            AND inv.status_id = 'revised'
            AND tax_country_id = 'ES'
            AND con.irpf > 0
            AND con.is_supplied IS NOT true
            AND inv.account_expenses_id != '621'
            OR (
                sel.id = sellerid
                AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
                AND inv.accounting_date >= period_starts::DATE
                AND inv.accounting_date < period_ends::DATE
                and inv.invoice_type_id = 'payroll' 
                AND inv.status_id = 'revised'
                AND tax_country_id = 'ES'
                AND con.irpf > 0
            )
        GROUP BY
            sel.id,
            inv.id,
            con.irpf
		ORDER BY
			nombre_apellido
            
    ) AS sub_data;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- SELECT func_get_data_model_111_externo(193, '2023-01-01', '2024-04-01');