DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_sellervat_countryFR_list_json') THEN
        -- DROP FUNCTION func_sellervat_countryFR_list_json(date_year INTEGER, date_period VARCHAR);
				DROP FUNCTION func_sellervat_countryFR_list_json(first_date_raw VARCHAR, last_date_raw VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_sellervat_countryFR_list_json(first_date_raw VARCHAR, last_date_raw VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
		first_date DATE;
		last_date DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	
	first_date := TO_DATE(first_date_raw, 'YYYY-MM-DD');
	last_date := TO_DATE(last_date_raw, 'YYYY-MM-DD');
    SELECT jsonb_agg(sub_data) INTO result_json
		FROM (
	
			SELECT DISTINCT 
-- 				subselect.*
-- 			    -- Mondel Min

-- 					-- Mondel Average

-- 				FROM (
					-- id
					sel.id,

					-- seller name
					sel.name AS seller_name,

					-- shortname
					sel.shortname AS seller_shortname,

					-- email
					(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

					-- user name
					(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

					-- gestor asignado
					MAX(sv.manager_assigned_id) AS manager_assigned,

					-- last login
					(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

					-- Num Invoices
					-- COUNT(DISTINCT inv.id) AS num_invoices,

					-- -- Num Pending Invoices
					COALESCE(COUNT(inv.id), 0) AS num_pending_invoices,

					-- -- Percentage Pending Invoices
					-- ROUND(COALESCE(
					-- 	100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
					-- 	0
					-- ), 2) AS percentage_pending_invoices,

					-- Month 1
					BOOL_OR (
						CASE WHEN
						txt.month_1 is True
						THEN true ELSE false END
					) AS month_1,

					-- Month 2
					BOOL_OR (
						CASE WHEN
						txt.month_2 is True
						THEN true ELSE false END
					) AS month_2,

					-- Month 3
					BOOL_OR (
						CASE WHEN
						txt.month_3 is True
						THEN true ELSE false END
					) AS month_3,

					-- Month 4
					BOOL_OR (
						CASE WHEN
						txt.month_4 is True
						THEN true ELSE false END
					) AS month_4,

					-- Month 5
					BOOL_OR (
						CASE WHEN
						txt.month_5 is True
						THEN true ELSE false END
					) AS month_5,

					-- Month 6
					BOOL_OR (
						CASE WHEN
						txt.month_6 is True
						THEN true ELSE false END
					) AS month_6,

					-- Month 7
					BOOL_OR (
						CASE WHEN
						txt.month_7 is True
						THEN true ELSE false END
					) AS month_7,

					-- Month 8
					BOOL_OR (
						CASE WHEN
						txt.month_8 is True
						THEN true ELSE false END
					) AS month_8,

					-- Month 9
					BOOL_OR (
						CASE WHEN
						txt.month_9 is True
						THEN true ELSE false END
					) AS month_9,

					-- Month 10
					BOOL_OR (
						CASE WHEN
						txt.month_10 is True
						THEN true ELSE false END
					) AS month_10,

					-- Month 11
					BOOL_OR (
						CASE WHEN
						txt.month_11 is True
						THEN true ELSE false END
					) AS month_11,

					-- Month 12
					BOOL_OR (
						CASE WHEN
						txt.month_12 is True
						THEN true ELSE false END
					) AS month_12
					
					FROM sellers_seller sel
					LEFT JOIN
            invoices_invoice AS inv ON inv.seller_id = sel.id
            AND (inv.invoice_category_id IS NULL 
                 OR inv.invoice_category_id NOT IN ('expenses_copy', 'sales_copy'))
            AND inv.status_id IN ('pending', 'revision-pending')
					LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
					LEFT JOIN (
						SELECT
						sel.id as seller_id,
						BOOL_OR(CASE WHEN month = 1 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_1,
						BOOL_OR(CASE WHEN month = 2 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_2,
						BOOL_OR(CASE WHEN month = 3 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_3,
						BOOL_OR(CASE WHEN month = 4 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_4,
						BOOL_OR(CASE WHEN month = 5 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_5,
						BOOL_OR(CASE WHEN month = 6 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_6,
						BOOL_OR(CASE WHEN month = 7 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_7,
						BOOL_OR(CASE WHEN month = 8 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_8,
						BOOL_OR(CASE WHEN month = 9 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_9,
						BOOL_OR(CASE WHEN month = 10 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_10,
						BOOL_OR(CASE WHEN month = 11 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_11,
						BOOL_OR(CASE WHEN month = 12 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_12

						FROM sellers_seller sel
						LEFT JOIN importers_amazontxteur txt ON sel.id = txt.seller_id
						WHERE year = EXTRACT(YEAR FROM first_date)
						GROUP BY sel.id
					) AS txt ON sel.id = txt.seller_id
					WHERE (
						sv.vat_country_id = 'FR'  AND (
							(
								sv.activation_date IS NOT NULL AND
								sv.activation_date < last_date AND
								( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_date)
							) OR  (
								sv.activation_date IS NULL AND
								sv.contracting_date IS NOT NULL AND
								sv.contracting_date <= last_date AND
								( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_date)
							)
						)
					)
					GROUP BY sel.id
					ORDER BY sel.id
-- 				) AS subselect
		) AS sub_data;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;

--TEST
-- SELECT func_sellervat_countryFR_list_json('2023-04-01','2023-07-01');