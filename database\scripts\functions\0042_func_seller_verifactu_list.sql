DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_verifactu_seller_list_json') THEN
    DROP FUNCTION func_verifactu_seller_list_json(first_month VARCHAR, last_month VARCHAR, legal_entity VARCHAR);
  END IF;
END $$;

CREATE OR REPLACE FUNCTION func_verifactu_seller_list_json(first_month VARCHAR, last_month VARCHAR, legal_entity VARCHAR)
RETURNS JSONB AS $$
DECLARE
  first_date DATE := first_month::DATE;
  last_date DATE := last_month::DATE;
  entity VARCHAR := legal_entity;
  result JSONB;
BEGIN

  SELECT jsonb_agg(sub_data) INTO result
  FROM (
    SELECT DISTINCT subselect.*
    FROM (
      SELECT DISTINCT 
        sel.id,
        sel.name AS seller_name,
        sel.shortname,
        (SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,
        (SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,
        (SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,
        sel.legal_entity AS legal_entity,

        COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

        ROUND(COALESCE(
          100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
          0
        ), 2) AS percentage_pending_invoices,
        (
            SELECT
              CASE
                WHEN COUNT(*) FILTER (
                  WHERE NOT EXISTS (
                    SELECT 1
                    FROM invoices_verifactuinv inv_veri
                    WHERE inv_veri.invoice_id = inv_check.id
                      AND inv_veri.status_in_verifactu IN ('AceptadoConErrores', 'Correcto')
                  )
                ) > 0 THEN 'missing'
                ELSE 'correct'
              END
            FROM invoices_invoice inv_check
            LEFT JOIN invoices_serialinvoice ser_inv ON inv_check.serial_code_id = ser_inv.id
            WHERE inv_check.seller_id = sel.id
              AND (inv_check.invoice_category_id NOT LIKE '%_copy' OR inv_check.invoice_category_id IS NULL)
              AND (
                (first_date >= '2025-07-29' AND inv_check.accounting_date BETWEEN first_date AND last_date) -- date_verifactu
                OR
                (first_date < '2025-07-29' AND inv_check.accounting_date BETWEEN '2025-07-29' AND last_date) -- date_verifactu
              )
              AND inv_check.accounting_date <= last_date
              AND (inv_check.transaction_type_id NOT LIKE '%-transfer' OR inv_check.transaction_type_id IS NULL)
              AND inv_check.invoice_category_id = 'sales'
              AND inv_check.is_generated_amz IS FALSE
              -- AND inv_check.is_generated IS TRUE -- is_generated_verifactu
              -- AND inv_check.tax_country_id = 'ES' 
              AND inv_check.issuing_country_id = 'ES' -- country_verifactu
              AND inv_check.status_id = 'revised'
              AND ser_inv.is_verifactu IS TRUE -- serial_code_verifactu
        ) AS status_verifactu
      FROM sellers_seller sel
      LEFT JOIN invoices_invoice inv
        ON sel.id = inv.seller_id
        AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
      LEFT JOIN sellers_sellervat sv 
        ON sel.id = sv.seller_id AND sv.vat_country_id = 'ES'
      WHERE 
		(entity = 'all' AND sel.legal_entity IN ('sl', 'self-employed') OR sel.legal_entity = entity)
    AND (
          (
            (sel.contracted_accounting_date < last_date)
            AND
            (sel.contracted_accounting_end_date IS NULL OR (sel.contracted_accounting_end_date >= first_date AND sel.contracted_accounting_end_date <= last_date))
          )
        -- OR (
        --     (sel.oss_date < last_date AND sel.oss_date >= first_date)
        --     AND
        --     (sel.oss_end_date IS NULL OR (sel.oss_end_date >= first_date AND sel.oss_end_date <= last_date))
        -- )
        OR (
            (sv.contracting_date < last_date)
            AND
            (sv.contracting_discontinue IS NULL OR (sv.contracting_discontinue >= first_date AND sv.contracting_discontinue <= last_date))
        )
    )
    AND sel.is_verifactu_active IS TRUE
      GROUP BY sel.id 
      ORDER BY sel.id
    ) AS subselect
  ) AS sub_data;

  RETURN result;

END;
$$ LANGUAGE plpgsql;
--SELECT func_verifactu_seller_list_json('2023-01-01', '2025-12-01', 'all');