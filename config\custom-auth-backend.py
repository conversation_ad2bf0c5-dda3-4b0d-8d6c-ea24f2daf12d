from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend


class CustomBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        UserModel = get_user_model()
        try:
            user = UserModel.objects.filter(username=username).first()
            if user.check_password(password) and self.user_can_authenticate(user):
                 return user

        except:
            return None
               
        return None
