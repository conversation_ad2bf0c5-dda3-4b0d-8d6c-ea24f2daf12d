CREATE OR REPLACE FUNCTION func_get_sellers_347_required(
    date_year INTEGER
) RETURNS jsonb AS $$
DECLARE
    result_json jsonb := '[]';
BEGIN
	WITH filtered_prov AS (
		SELECT
			prov.seller_id,
			prov.id
		FROM providers_provider prov
		LEFT JOIN address_address adr ON adr.id = prov.provider_address_id
		INNER JOIN invoices_invoice inv ON inv.provider_id = prov.id
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		LEFT JOIN (
			SELECT DISTINCT inv_irpf.id AS irpf_invoice_id
			FROM invoices_invoice inv_irpf
			INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
			WHERE
				EXTRACT(YEAR FROM inv_irpf.expedition_date) = date_year
				AND con_irpf.irpf_euros <> 0
				AND con_irpf.is_supplied IS NOT true
		) has_irpf ON has_irpf.irpf_invoice_id = inv.id
		WHERE
			1=1
			AND inv.tax_country_id = 'ES'
			AND EXTRACT(YEAR FROM inv.expedition_date) = date_year
			AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
			AND inv.status_id = 'revised'	
			AND NOT (inv.transaction_type_id LIKE 'intra-community-%'
					OR inv.transaction_type_id LIKE 'import-%'
					OR inv.transaction_type_id LIKE '%-transfer')
			AND has_irpf IS NULL
			AND prov.name NOT ILIKE 'Seguridad Social%'
			AND prov.name NOT ILIKE 'Proveedores Varios%'
			AND con.is_supplied IS NOT true
		GROUP BY prov.id
		HAVING SUM(con.total_euros) > 3005.06
	),
	filtered_cust AS (
		SELECT
			cust.seller_id,
			cust.id
		FROM customers_customer cust
		LEFT JOIN address_address adr ON adr.id = cust.customer_address_id
		INNER JOIN invoices_invoice inv ON inv.customer_id = cust.id
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		LEFT JOIN (
			SELECT DISTINCT
				inv_irpf.id AS irpf_invoice_id
			FROM
				invoices_invoice inv_irpf
			INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
			WHERE
				EXTRACT(YEAR FROM inv_irpf.expedition_date) = date_year
				AND con_irpf.irpf_euros <> 0
				AND con_irpf.is_supplied IS NOT true
		) has_irpf ON has_irpf.irpf_invoice_id = inv.id
		WHERE
			1=1
			AND inv.tax_country_id = 'ES'
			AND EXTRACT(YEAR FROM inv.expedition_date) = date_year
			AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
			AND inv.status_id = 'revised'	
			AND NOT (inv.transaction_type_id LIKE 'intra-community-%'
					OR inv.transaction_type_id LIKE 'import-%'
					OR inv.transaction_type_id LIKE '%-transfer')
			AND has_irpf IS NULL
			AND cust.name NOT ILIKE 'Clientes Particulares%'
			AND cust.name NOT ILIKE 'Clientes Miravia%'
			AND cust.name NOT ILIKE 'Clientes Shopify%'
			AND con.is_supplied IS NOT true
		GROUP BY cust.id
		HAVING SUM(con.total_euros) > 3005.06
	)
	SELECT
		jsonb_agg(
            jsonb_build_object(
                'id', sel.id,
                'shortname', sel.shortname
            )
        ) INTO result_json
	FROM
		sellers_seller sel
	LEFT JOIN (
		SELECT * FROM filtered_prov
		UNION ALL
		SELECT * FROM filtered_cust
	) filtered ON filtered.seller_id = sel.id
	WHERE filtered.id IS NOT NULL;
		
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- SELECT * FROM func_get_sellers_347_required(2025);