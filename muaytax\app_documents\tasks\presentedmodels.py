import json
import logging

from celery import shared_task
from muaytax.celery import app

from django.conf import settings

from muaytax.app_documents.models.presented_model import PresentedModel
from muaytax.app_documents.tasks.controllers.presentedmodels import PendingModelsNotificationHandler
from muaytax.email_notifications.automatic_AEAT import send_model_to_AEAT_auto, send_report_autoAEAT
from muaytax.app_ocr.controller import MuaytaxOCRProcessor
from muaytax.app_ocr.choices import ProcessingDocument
from muaytax.app_ocr.processors import PDFInMemoryProcessor

from muaytax.dictionaries.models import ModelStatus

logger = logging.getLogger(__name__)

@shared_task
def notification_sellers_with_pending_models(notification_task_id):
    handler =  PendingModelsNotificationHandler(notification_task_id)
    result = handler.run()
    return result

@shared_task
def auto_model_toAEAT(pk):
    print("Empieza función de la AEAT")
    if settings.IS_PRODUCTION:
        send_model_to_AEAT_auto(pk)
    print("Acaba función de la AEAT")

@shared_task
def report_AEAT():
    print("Empieza la función de reporte de la AEAT")
    if settings.IS_PRODUCTION:
        send_report_autoAEAT()
    print("Termina la función de reporte de la AEAT")

@app.task(queue='invoice_processing')
def process_presentedmodel_with_ocr(presented_model_pk: int):
    """Processes the presented model with OCR so it can generate the json_pdf."""

    logger.info(f"\r\033[92m**********Procesando el modelo presentado {presented_model_pk} con OCR**********\033[0m")
    def log_and_return(message, level="error"):
        log_function = getattr(logger, level)
        log_function(message)
        return False
    
    try:
        presented_model = PresentedModel.objects.get(pk=presented_model_pk)
    except PresentedModel.DoesNotExist:
        return log_and_return(f"\r\033[91mNo se encontró el modelo presentado con el id {presented_model_pk}\033[0m")

    if not presented_model.file:
        return log_and_return(f"\r\033[91mLa instancia del modelo {presented_model.pk} no tiene archivo\033[0m")
    if not presented_model.file.name.endswith('.pdf'):
        return log_and_return(f"\r\033[91mNo se pudo procesar con OCR el modelo presentado {presented_model.pk} porque no es un archivo PDF\033[0m")
    
    pdf_processor = PDFInMemoryProcessor(presented_model.file)
    if pdf_processor.is_encrypted():
        return log_and_return(f"\r\033[91mEl modelo presentado {presented_model.pk} está encriptado. Se ha parado el procesamiento\033[0m")
    if pdf_processor.has_over_n_pages(20):
        return log_and_return(f"\r\033[91mEl modelo presentado {presented_model.pk} tiene más de 20 páginas. Se ha parado el procesamiento\033[0m")
    
    model_code = presented_model.model.code
    processing_document = ProcessingDocument.get_processing_model(model_code)
    if not processing_document:
        return log_and_return(f"\r\033[91mEl modelo cargado no se encuentra entre los procesables por el OCR\033[0m")
    
    processor = MuaytaxOCRProcessor(processing_document)
    try:
        extracted_text = processor.extract_text(presented_model.file.path)
        json_output = processor.process_extracted_text(extracted_text)

        presented_model.json_pdf = json.dumps(json_output)
        presented_model.processed_by_ocr = True
        presented_model.save()
        
        logger.info(f"\r\033[92m**********Modelo presentado: '{presented_model.pk}' procesado con éxito**********\033[0m")
        return True

    except Exception as e:
        return log_and_return(f"\r\033[91mError al procesar el modelo presentado {presented_model.pk}, por el motivo: {e}\033[0m")

@shared_task
def auto_agree_model(model_pk):
    """Agrees a model after a configured time in the moment of creation"""
    logger.info(f"\r\033[92m**********Función de Auto agree inicia**********\033[0m")
    try:
        presented_model = PresentedModel.objects.get(pk=model_pk)
    except PresentedModel.DoesNotExist:
        return False

    if presented_model.status.code == 'pending':
        presented_model.status = ModelStatus.objects.get(code='agreed')
        presented_model.save()

        logger.info(f"\r\033[92m**********Se ha actualizado el estado del modelo {model_pk} con exito**********\033[0m")
        return True
    return False