DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_sellervat_countryIT_list_json') THEN
        DROP FUNCTION func_sellervat_countryIT_list_json(date_year INTEGER, date_period VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_sellervat_countryIT_list_json(date_year INTEGER, date_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    -- inv_data RECORD;
	first_month DATE;
	last_month DATE;
	last_year DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	IF date_period = 'Q1' THEN 
					first_month := date_year || '-01-01';
					last_month := date_year || '-04-01';
	ELSIF date_period = 'Q2' THEN 
					first_month := date_year || '-04-01';
					last_month := date_year || '-07-01';
	ELSIF date_period = 'Q3' THEN 
					first_month := date_year || '-07-01';
					last_month := date_year || '-10-01';
	ELSIF date_period = 'Q4' THEN 
					first_month := date_year || '-10-01';
					last_month := (date_year + 1) || '-01-01';
				END IF;
				
	last_year = date_year ||'-01-01';

	SELECT jsonb_agg(sub_data) INTO result_json
	FROM (
    -- FOR inv_data IN
		SELECT DISTINCT 
					-- SubSelect
					subselect.*, 

					-- Mondel Min
					(
						CASE 
							WHEN 'required' in (model_lipe, acconto) THEN 0
							WHEN 'warning01' in (model_lipe) THEN 1
							WHEN 'warning02' in (model_lipe) THEN 2
							WHEN 'warning04' in (model_lipe) THEN 3
							WHEN 'disagreed' in (model_lipe, acconto) THEN 4
							WHEN 'agreed' in (model_lipe, acconto) THEN 5
							WHEN 'pending' in (model_lipe, acconto) THEN 6
							WHEN 'presented' in (model_lipe, acconto) THEN 7
							WHEN 'not-required' in (model_lipe, acconto) THEN 8
						ELSE 9
						END 
					) AS model_min,

					-- Model Average
					(
						(
							CASE
								WHEN model_lipe = 'required' THEN 0
								WHEN model_lipe = 'warning01' THEN 1
								WHEN model_lipe = 'warning02' THEN 2
								WHEN model_lipe = 'warning04' THEN 3
								WHEN model_lipe = 'disagreed' THEN 4
								WHEN model_lipe = 'agreed' THEN 5
								WHEN model_lipe = 'pending' THEN 6
								WHEN model_lipe = 'presented' THEN 7
								WHEN model_lipe = 'not-required' THEN 8
								ELSE 9
							END) + (
								CASE
									WHEN acconto = 'required' THEN 0
									WHEN acconto = 'disagreed' THEN 4
									WHEN acconto = 'agreed' THEN 5
									WHEN acconto = 'pending' THEN 6
									WHEN acconto = 'presented' THEN 7
									WHEN acconto = 'not-required' THEN 8
									ELSE 9
							END)
					) * 100 / 9 as model_avg

					FROM 
				(

						SELECT DISTINCT 
								-- id
								sel.id,

								-- seller name
								sel.name AS seller_name,

								-- shortname
								sel.shortname AS shortname,

								-- email
								(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

								-- user name
								(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

								-- gestor asignado
								MAX(sv.manager_assigned_id) AS manager_assigned,

								-- last login
								(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

								-- Num Invoices
								COUNT(DISTINCT inv.id) AS num_invoices,

								-- Num Pending Invoices
								COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

								-- Percentage Pending Invoices
								ROUND(COALESCE(
									100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
									0
								), 2) AS percentage_pending_invoices,
								
								-- Type Representation (sellervat IT)
								(SELECT it_representation_type  FROM sellers_sellervat sv WHERE sv.seller_id = sel.id AND sv.vat_country_id = 'IT' LIMIT 1) AS type_representation,

								-- Cod fiscale (sellervat IT)
								(SELECT codice_fiscale  FROM sellers_sellervat sv WHERE sv.seller_id = sel.id AND sv.vat_country_id = 'IT' LIMIT 1) AS cod_fiscale,

								-- Vat Number (sellervat IT)
								(SELECT vat_number  FROM sellers_sellervat sv WHERE sv.seller_id = sel.id AND sv.vat_country_id = 'IT' LIMIT 1) AS vat_number,

								-- Month 1
								BOOL_OR (
									CASE WHEN
									txt.month_1 is True
									THEN true ELSE false END
								) AS month1,

								-- Month 2
								BOOL_OR (
									CASE WHEN
									txt.month_2 is True
									THEN true ELSE false END
								) AS month2,

								-- Month 3
								BOOL_OR (
									CASE WHEN
									txt.month_3 is True
									THEN true ELSE false END
								) AS month3,

								-- Month 4
								BOOL_OR (
									CASE WHEN
									txt.month_4 is True
									THEN true ELSE false END
								) AS month4,

								-- Month 5
								BOOL_OR (
									CASE WHEN
									txt.month_5 is True
									THEN true ELSE false END
								) AS month5,

								-- Month 6
								BOOL_OR (
									CASE WHEN
									txt.month_6 is True
									THEN true ELSE false END
								) AS month6,

								-- Month 7
								BOOL_OR (
									CASE WHEN
									txt.month_7 is True
									THEN true ELSE false END
								) AS month7,

								-- Month 8
								BOOL_OR (
									CASE WHEN
									txt.month_8 is True
									THEN true ELSE false END
								) AS month8,

								-- Month 9
								BOOL_OR (
									CASE WHEN
									txt.month_9 is True
									THEN true ELSE false END
								) AS month9,

								-- Month 10
								BOOL_OR (
									CASE WHEN
									txt.month_10 is True
									THEN true ELSE false END
								) AS month10,

								-- Month 11
								BOOL_OR (
									CASE WHEN
									txt.month_11 is True
									THEN true ELSE false END
								) AS month11,

								-- Month 12
								BOOL_OR (
									CASE WHEN
									txt.month_12 is True
									THEN true ELSE false END
								) AS month12,

								MAX(
									CASE WHEN pm.model_lipe IS NOT NULL THEN pm.model_lipe
									WHEN pmf.model_lipe IS NOT NULL THEN pmf.model_lipe
									ELSE
										CASE 
									WHEN i.num_inv_null_revised > 0
										THEN 'warning04'
									WHEN 	
											--sv.is_contracted = FALSE
											(
												(
													sv.activation_date IS NULL AND
													sv.contracting_date IS NULL
												) OR
												(
													sv.deactivation_date < first_month AND
													sv.activation_date IS NOT NULL
												) OR
												(
													sv.contracting_date IS NOT NULL AND
													sv.end_contracting_date < first_month
												) OR
												(
													sv.activation_date > last_month
												) OR
												(
													sv.contracting_date > last_month
												)
											) AND sv.vat_country_id = 'IT' AND i.num_inv >0
										THEN 'warning01'
									WHEN i.num_inv > 0 AND NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'IT' AND sv.seller_id = sel.id)
										THEN 'warning02'	
									WHEN sv.vat_country_id = 'IT'  AND (
										(
											sv.activation_date IS NOT NULL AND
											sv.activation_date < last_month AND
											( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month)
										) OR  (
											sv.activation_date IS NULL AND
											sv.contracting_date IS NOT NULL AND
											sv.contracting_date <= last_month AND
											( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_month)
										)
									)
										THEN 'required'
									ELSE 'not-required' END
									END
								) AS model_lipe,
								MAX(
									CASE WHEN pm.acconto IS NOT NULL THEN pm.acconto
									WHEN pmf.acconto IS NOT NULL THEN pmf.acconto
									ELSE
										CASE WHEN sv.vat_country_id = 'IT'  AND (
											(
												sv.activation_date IS NOT NULL AND
												sv.activation_date < last_month AND
												( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month)
											) OR  (
												sv.activation_date IS NULL AND
												sv.contracting_date IS NOT NULL AND
												sv.contracting_date <= last_month AND
												( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_month)
											) 
										) 									
										AND	date_period = 'Q4'  
										-- AND sv.activation_date < last_year 
										AND last_lipe.json_pdf  not like '% "VP6-1": ""%'  
										AND last_lipe.json_pdf  not like '% "VP6-1": "0"%'											
										THEN 'required'
										ELSE 'not-required' 
										END
									END
								) AS acconto
								
								FROM sellers_seller sel
								LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
								LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
								LEFT JOIN (
									SELECT
									sel.id as seller_id,
									BOOL_OR(CASE WHEN month = 1 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_1,
									BOOL_OR(CASE WHEN month = 2 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_2,
									BOOL_OR(CASE WHEN month = 3 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_3,
									BOOL_OR(CASE WHEN month = 4 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_4,
									BOOL_OR(CASE WHEN month = 5 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_5,
									BOOL_OR(CASE WHEN month = 6 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_6,
									BOOL_OR(CASE WHEN month = 7 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_7,
									BOOL_OR(CASE WHEN month = 8 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_8,
									BOOL_OR(CASE WHEN month = 9 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_9,
									BOOL_OR(CASE WHEN month = 10 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_10,
									BOOL_OR(CASE WHEN month = 11 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_11,
									BOOL_OR(CASE WHEN month = 12 AND status_id = 'processed' THEN true ELSE false END) 							
									AS month_12

									FROM sellers_seller sel
									LEFT JOIN importers_amazontxteur txt ON sel.id = txt.seller_id
									WHERE year = date_year
									GROUP BY sel.id
								) AS txt ON sel.id = txt.seller_id
								LEFT JOIN (
									SELECT
									sel.id as seller_id,
									MAX(CASE WHEN pm.model_id::text = 'IT-LIPE' THEN pm.status_id END) as model_lipe,
									MAX(CASE WHEN pm.model_id::text = 'IT-ACCONTO' THEN pm.status_id END) as acconto
									FROM sellers_seller sel
									LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
									WHERE pm.year = date_year AND (pm.period_id = date_period or pm.period_id = '0A') AND pm.country_id = 'IT' 
									GROUP BY sel.id	
								) AS pm ON sel.id = pm.seller_id
								LEFT JOIN (
									SELECT
									sel.id as seller_id,
									MAX(CASE WHEN pmf.model_id::text = 'IT-LIPE' THEN pmf.status_id END) as model_lipe,
									MAX(CASE WHEN pmf.model_id::text = 'IT-ACCONTO' THEN pmf.status_id END) as acconto
									FROM sellers_seller sel
									LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
									WHERE pmf.year = date_year AND (pmf.period_id = date_period or pmf.period_id = '0A') AND pmf.country_id = 'IT' 
									GROUP BY sel.id		
								) AS pmf ON sel.id = pmf.seller_id
								LEFT JOIN(
									SELECT
									sel.id as seller_id,
									MAX(CASE WHEN pm.model_id::text = 'IT-LIPE' THEN pm.json_pdf END) as json_pdf
									FROM sellers_seller sel
									LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
									WHERE
										pm.year = (date_year - 1) 
										AND pm.period_id = 'Q4'
										AND pm.model_id = 'IT-LIPE'
										AND pm.status_id = 'presented'
										AND pm.json_pdf::jsonb ? 'VP6-1'  -- Checkea que exista la clave VP6-1
										AND (
											COALESCE(
												CAST(
													REPLACE(
														REPLACE(
															NULLIF(pm.json_pdf::jsonb ->> 'VP6-1', ''), 
															'.', ''   -- Remueve punto de la cadena
														), 
														',', '.'   -- Reemplaza coma por punto
													) AS NUMERIC
												), 
												0
											) * 0.88 > 103
										)  -- Aquí se hace la validación para que sea mayor o igual a 103.29
									GROUP BY sel.id
								) AS last_lipe ON sel.id = last_lipe.seller_id
								
								LEFT JOIN(
									SELECT
									sel.id as seller_id,
									COUNT(DISTINCT CASE WHEN (i.accounting_date >= first_month AND i.accounting_date < last_month) OR (i.accounting_date IS NULL AND NOT i.status_id = 'discard') THEN i.id END) as num_inv,
									COUNT(DISTINCT CASE WHEN  (i.accounting_date IS NULL AND i.status_id = 'revised') THEN i.id END) as num_inv_null_revised
									FROM sellers_seller sel
									LEFT JOIN invoices_invoice i ON sel.id = i.seller_id
									WHERE tax_country_id = 'IT'
									AND is_txt_amz IS NOT TRUE
									GROUP BY sel.id
								) AS i ON sel.id = i.seller_id
								WHERE 
									--sel.contracted_accounting = False 
									-- AND (sel.legal_entity IS NULL OR sel.legal_entity NOT IN ('sl', 'self-employed'))
									-- AND (
									-- 		(sv.is_contracted = TRUE 
									-- 		AND sv.vat_country_id = 'IT' 
									-- 		AND NOT (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby')
									-- 		AND sv.activation_date < last_month 
									-- 		AND sv.contracting_date < last_month)
									-- 	OR(	sv.is_contracted = FALSE 
									-- 		AND sv.vat_country_id = 'IT' 
									-- 		AND i.num_inv > 0)
									-- )
									(
										sv.vat_country_id = 'IT'  AND (
											(
												sv.activation_date IS NOT NULL AND
												sv.activation_date < last_month AND
												( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month)
											) OR  (
												sv.activation_date IS NULL AND
												sv.contracting_date IS NOT NULL AND
												sv.contracting_date <= last_month AND
												( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_month)
											)
										)
									) OR (
										sv.vat_country_id = 'IT'  AND (
											(
												sv.activation_date IS NOT NULL AND
												sv.activation_date < last_month AND
												( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month)
											) OR  (
												sv.activation_date IS NULL AND
												sv.contracting_date IS NOT NULL AND
												sv.contracting_date <= last_month AND
												( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_month)
											) 
										) 									
										AND	date_period = 'Q4'  
										-- AND sv.activation_date < last_year 
										AND last_lipe.json_pdf IS NOT NULL  
									) OR (
										--sv.is_contracted = FALSE AND
										(
											(
												sv.activation_date IS NULL AND
												sv.contracting_date IS NULL
											) OR
											(
												sv.deactivation_date < first_month AND
												sv.activation_date IS NOT NULL
											) OR
											(
												sv.contracting_date IS NOT NULL AND
												sv.end_contracting_date < first_month
											) OR
											(
												sv.activation_date > last_month
											) OR
											(
												sv.contracting_date > last_month
											)
										) AND
										sv.vat_country_id = 'IT'  AND i.num_inv > 0
									) OR ( i.num_inv > 0 AND ( NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'IT' AND sv.seller_id = sel.id )))
								GROUP BY sel.id
								ORDER BY sel.id
				) AS subselect
	) AS sub_data;
			
		-- 	LOOP
		-- 	result_json := result_json || jsonb_build_object(
		-- 		'seller_id', inv_data.id,
		-- 		'seller_name', inv_data.seller_name,
		-- 		'shortname', inv_data.seller_shortname,
		-- 		'email', inv_data.email,
		-- 		'user_name', inv_data.user_name,
		-- 		'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
		-- 		'num_pending_invoices', inv_data.num_pending_invoices,
		-- 		'percentage_pending_invoices', inv_data.percentage_pending_invoices,
		-- 		'type_representation', inv_data.type_representation,
		-- 		'month1', inv_data.month_1,
		-- 		'month2', inv_data.month_2,
		-- 		'month3', inv_data.month_3,
		-- 		'month4', inv_data.month_4,
		-- 		'month5', inv_data.month_5,
		-- 		'month6', inv_data.month_6,
		-- 		'month7', inv_data.month_7,
		-- 		'month8', inv_data.month_8,
		-- 		'month9', inv_data.month_9,
		-- 		'month10', inv_data.month_10,
		-- 		'month11', inv_data.month_11,
		-- 		'month12', inv_data.month_12,
		-- 		'model_lipe', inv_data.model_lipe,
		-- 		'acconto', inv_data.acconto,
		-- 		'model_min', inv_data.model_min,
		-- 		'model_avg', inv_data.model_avg
		-- 	);
			
		-- END LOOP;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- USE FUNCTION
-- SELECT func_sellervat_countryIT_list_json(2023, 'Q4')