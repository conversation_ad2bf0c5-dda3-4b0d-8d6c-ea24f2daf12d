import os
import re
import json

# Definir la ruta donde están los templates y la carpeta donde se guardará el archivo JSON de salida
template_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'muaytax', 'templates')
output_dir = os.path.dirname(__file__)
output_file = os.path.join(output_dir, 'all_cdns_templates.json')

# Expresión regular para detectar URLs de CDN en los templates
cdn_pattern = re.compile(r'https?://[^\s\'"]+', re.IGNORECASE | re.DOTALL)

# Diccionario para almacenar el mapeo de URLs de CDN
cdn_mapping = {
    'js': {},
    'css': {}
}

# Función para extraer la clave de agrupación (dominio principal) de una URL
def extract_group_key(url):
    match = re.search(r'https?://([^/]+)', url)
    if match:
        domain = match.group(1)
        group_key = domain.split('.')[0]
        return group_key
    return "unknown"

# Función para extraer la extensión de archivo
def get_file_extension(url):
    return url.split('.')[-1].split('?')[0].split('#')[0]  # Obtener la extensión del archivo sin parámetros o anclajes

# Recorrer los archivos de los templates para encontrar y mapear URLs de CDNs
for root, dirs, files in os.walk(template_dir):
    for file in files:
        if file.endswith(".html"):  # Solo se consideran archivos HTML
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                matches = cdn_pattern.findall(content)  # Buscar todas las URLs que coincidan con el patrón de CDN
                for url in matches:
                    file_ext = get_file_extension(url)  # Obtener la extensión del archivo
                    
                    # Determinar si el archivo es JavaScript o CSS
                    if file_ext == 'js':
                        group = 'js'
                    elif file_ext == 'css':
                        group = 'css'
                    else:
                        continue  # Ignorar archivos que no sean .js o .css
                    
                    sub_group = extract_group_key(url)  # Extraer el grupo clave (dominio principal)
                    
                    # Inicializar el subgrupo si no existe en el mapeo
                    if sub_group not in cdn_mapping[group]:
                        cdn_mapping[group][sub_group] = {}
                    
                    # Contar las veces que se encuentra la URL y añadir el texto "templates usados"
                    if url in cdn_mapping[group][sub_group]:
                        cdn_mapping[group][sub_group][url] = f"templates usados {int(cdn_mapping[group][sub_group][url].split(' ')[-1]) + 1}"
                    else:
                        cdn_mapping[group][sub_group][url] = "templates usados 1"

# Guardar el mapeo en un archivo JSON
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(cdn_mapping, f, indent=4)

print(f"Todas las URLs de JS y CSS encontradas y mapeadas en {output_file}")
