// https://dbdiagram.io/d/64f7027c02bd1c4a5efd74a7

Table Dictionaries {
  code varchar [primary key]
  description varchar
  order integer
  created_at timestamp
}


Table address {
  id integer [primary key]
  TOD<PERSON> varchar
  created_at timestamp
}

Table users {
  id integer [primary key]
  username varchar
  TODO varchar
  created_at timestamp
}

Table sellers {
  id integer [primary key]
  user_id integer
  TODO varchar
  created_at timestamp
}

Table seller_vat {
  id integer [primary key]
  seller_id integer
  TODO varchar
  created_at timestamp
}

Table providers {
  id integer [primary key]
  seller_id integer
  address_id intenger
  TODO varchar
  created_at timestamp
}

Table customers {
  id integer [primary key]
  seller_id integer
  address_id intenger
  TODO varchar
  created_at timestamp
}

Table partners {
  id integer [primary key]
  seller_id integer
  address_id intenger
  TODO varchar
  created_at timestamp
}

Table workers {
  id integer [primary key]
  seller_id integer
  address_id intenger
  TODO varchar
  created_at timestamp
}

Table invoices {
  id integer [primary key]
  seller_id integer
  TODO varchar
  created_at timestamp
}

Table invoice_concepts {
  id integer [primary key]
  invoice_id integer
  TODO varchar
  created_at timestamp
}

Table banks {
  id integer [primary key]
  seller_id integer
  TODO varchar
  created_at timestamp
}

Table bank_movements {
  id integer [primary key]
  bank_id integer
  TODO varchar
  created_at timestamp
}

Table bank_reconcilation {
  id integer [primary key]
  movement_id integer
  TODO varchar
  created_at timestamp
}

Table bank_accounting_entries {
  id integer [primary key]
  movement_id integer
  TODO varchar
  created_at timestamp
}

Table documents {
  id integer [primary key]
  seller_id integer
  TODO varchar
  created_at timestamp
}

Table presented_models {
  id integer [primary key]
  seller_id integer
  TODO varchar
  created_at timestamp
}

Table importers_amz_txt_eur {
  id integer [primary key]
  seller_id integer
  TODO varchar
  created_at timestamp
}

Table product_amz {
  id integer [primary key]
  seller_id integer
  TODO varchar
  created_at timestamp
}

Ref: users.id < sellers.user_id
Ref: sellers.id < seller_vat.seller_id
Ref: sellers.id < providers.seller_id
Ref: sellers.id < customers.seller_id
Ref: sellers.id < partners.seller_id
Ref: sellers.id < workers.seller_id
Ref: sellers.id < invoices.seller_id
Ref: invoices.id < invoice_concepts.invoice_id
Ref: sellers.id < banks.seller_id
Ref: banks.seller_id < bank_movements.bank_id
Ref: bank_movements.id < bank_reconcilation.movement_id
Ref: bank_movements.id < bank_accounting_entries.movement_id
Ref: address.id < customers.address_id
Ref: address.id < providers.address_id
Ref: address.id < partners.address_id
Ref: address.id < workers.address_id
Ref: sellers.id < documents.seller_id
Ref: sellers.id < presented_models.seller_id
Ref: sellers.id < importers_amz_txt_eur.seller_id
Ref: sellers.id < product_amz.seller_id

