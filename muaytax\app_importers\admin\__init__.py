from django.contrib import admin

# Import configuracion personalizada
from .amz_txt_eur import AmazonTxtEurAdmin
from .bank_movements import BankMovementsImporterAdmin
from .marketplace_vat_default import MarketplaceVatDefaultAdmin
from .marketplace import MarketplaceOrderImporterAdmin, MarketplaceInvoiceGeneratorAdmin

# Modelos
from ..models import AmazonTxtEur, BankMovementsImporter, MarketplaceOrderImporter, MarketplaceInvoiceGenerator, MarketplaceVatDefault


admin.site.register(AmazonTxtEur, AmazonTxtEurAdmin)
admin.site.register(BankMovementsImporter, BankMovementsImporterAdmin)
admin.site.register(MarketplaceOrderImporter, MarketplaceOrderImporterAdmin)
admin.site.register(MarketplaceInvoiceGenerator, MarketplaceInvoiceGeneratorAdmin)

admin.site.register(MarketplaceVatDefault, MarketplaceVatDefaultAdmin)
