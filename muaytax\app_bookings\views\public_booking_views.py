from rest_framework import generics
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework import status
from django.conf import settings
import uuid

from collections import Counter

from django.views import View
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponseRedirect, Http404, HttpResponse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta, datetime
from django.urls import reverse
from django.conf import settings
from django.db.models import Q, QuerySet
from typing import Optional

from muaytax.app_sellers.models import Seller
from muaytax.app_sellers.serializers import ContactPhoneSerializer
from muaytax.app_bookings.models import Bookings, Absence, GuestUser, Holidays
from muaytax.app_bookings.serializers import HolidaysSerializer
from muaytax.app_bookings.permissions import PublicBookingPermission
from muaytax.app_bookings.utils import *
from muaytax.app_bookings.notification import BookingNotification
from muaytax.dictionaries.models import BookingSubject, Country
from muaytax.dictionaries.serializers import CountrySerializer

from muaytax.calendar_api.bookings import add_to_CAL
from muaytax.utils.mixins import AnonymousRequiredMixin

from api_rest.api_auth.authentications import PublicAuthentication
from api_rest.api_auth.models import PublicAccessCodeToken
from api_rest.generic.response import APIResponse, ResponseError

from muaytax.app_bookings.utils.general import generate_booking_spots, get_first_working_datetime, get_booking_margin_delta, get_managers_consulting
from muaytax.users.models import User

import requests

User = get_user_model()

def validate_recaptcha(recaptcha_response):
    # En desarrollo, permitimos todo
    if settings.DEBUG:
        return True

    recaptcha_secret = settings.RECAPTCHA_PRIVATE_KEY
    data = {
        'secret': recaptcha_secret,
        'response': recaptcha_response
    }
    response = requests.post('https://www.google.com/recaptcha/api/siteverify', data=data)
    result = response.json()
    return result.get('success', False)

class UpcomingHolidaysList(generics.ListAPIView):
    serializer_class = HolidaysSerializer

    def get_queryset(self):
        return Holidays.objects.filter(date__gte=datetime.today()).order_by('date')

class PublicUpcomingHolidaysList(generics.ListAPIView):
    authentication_classes = [] if settings.DEBUG else [PublicAuthentication]
    permission_classes = [] if settings.DEBUG else [PublicBookingPermission]
    serializer_class = HolidaysSerializer

    def get_queryset(self):
        return Holidays.objects.filter(date__gte=datetime.today()).order_by('date')

class BookingsCreatePublicView(AnonymousRequiredMixin, View):
    model = Bookings
    template_name = "bookings/public_booking.html"

    def get(self, request, *args, **kwargs):
        return render(request, self.template_name)
    
    def post(self, request, *args, **kwargs):

        if 'date-submit' in request.POST:
            return self.handle_date_submit(request)
        elif 'comment-submit' in request.POST:
            return self.handle_comment_submit(request)
        elif 'new-info-submit' in request.POST:
            return self.handle_new_info_submit(request)
        elif 'confirm-public-booking' in request.POST:
            recaptcha_response = request.POST.get('g-recaptcha-response')
            if not validate_recaptcha(recaptcha_response):
                return JsonResponse({'status': 'error', 'message': 'Invalid reCAPTCHA. Please try again.'})
            return self.handle_confirm_public_booking(request)
        else:
            print("No action, se ha recargado la página")
            return HttpResponseRedirect(reverse("app_bookings:booking_public_new"))
        
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

    def handle_date_submit(self, request):
        date = request.POST.get('selected_date')
        time = request.POST.get('selected_time')

        if not date or not time:
            return JsonResponse({'status': 'error', 'message': 'Por favor, selecciona una fecha, hora y duración'})

        return JsonResponse({'status': 'okay', 'message': 'Datos completos'})
    
    def handle_comment_submit(self, request):
        comment = request.POST.get('public_comment')
        if not comment:
            return JsonResponse({'status': 'error', 'message': 'Por favor, escribe un comentario'})
        return JsonResponse({'status': 'okay', 'message': 'Comentario enviado'})
    
    def handle_new_info_submit(self, request):
        name = request.POST.get('public_new_name')
        last_name = request.POST.get('public_new_last_name')
        phone_code = request.POST.get('public_new_country_prefix')
        phone = request.POST.get('public_new_phone')
        comment = request.POST.get('public_new_comment')

        missing_fields = []
        if not name:
            missing_fields.append('name')
        if not last_name:
            missing_fields.append('last_name')
        if not phone_code:
            missing_fields.append('phone_code')
        if not phone:
            missing_fields.append('phone')
        if not comment:
            missing_fields.append('comment')

        if missing_fields:
            return JsonResponse({
                'status': 'error',
                'message': 'Por favor, completa todos los campos',
                'missing_fields': missing_fields
            })

        return JsonResponse({'status': 'okay', 'message': 'Datos completos'})

    def handle_confirm_public_booking(self, request):
        
        private_token = request.headers.get('Authorization', '').split(" ")[-1]

        if not private_token:
            return self._unauthorized_response('Acceso no autorizado')
        
        try:
            public_access_token = PublicAccessCodeToken.objects.get(private_token=private_token)
        except PublicAccessCodeToken.DoesNotExist:
            return self._unauthorized_response('Acceso no autorizado')

        if not public_access_token:
            return self._unauthorized_response('Acceso no autorizado')
        
        if not public_access_token.is_active:
            return self._unauthorized_response('El token ha sido desactivado')
        
        try:
            # Extract and validate POST data
            booking_data = self._extract_booking_data(request)

            # Verificar si hay campos faltantes
            if booking_data['missing_fields']:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Por favor, completa todos los campos requeridos',
                    'missing_fields': booking_data['missing_fields']
                })

            # Parse and make datetime aware
            try:
                date_time_str = f"{booking_data['date']} {booking_data['time']}"
                date_time = timezone.make_aware(datetime.strptime(date_time_str, "%Y-%m-%d %H:%M"))
            except ValueError:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Formato de fecha u hora incorrecto',
                    'missing_fields': ['date', 'time']
                })

            if not booking_data['manager']:
                return JsonResponse({
                    'status': 'error',
                    'message': 'No hay un gestor disponible en la fecha y hora seleccionadas',
                    'missing_fields': ['manager']
                })

            # Verificar si ya existe una reserva para el mismo día con el mismo email o teléfono
            existing_booking = None
            if booking_data['shortname']:
                # Si es un seller, verificamos por shortname
                existing_booking = Bookings.objects.filter(
                    seller__shortname=booking_data['shortname'],
                    date__date=date_time.date(),
                    status='pending'
                ).first()
            else:
                # Si es un guest user, verificamos por email o teléfono
                existing_booking = Bookings.objects.filter(
                    Q(guest_user__email=booking_data['email']) | Q(guest_user__phone=booking_data['phone']),
                    date__date=date_time.date(),
                    status='pending',
                    is_guest_user=True
                ).first()

            if existing_booking:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Ya tienes una cita programada para este día. Por favor, elige otro día o contacta con nosotros si necesitas modificar tu cita existente.'
                })

            subject = BookingSubject.objects.get(code="contract-service")
            id_event_cal = str(uuid.uuid4().hex)

            if booking_data['shortname']:
                seller = Seller.objects.filter(shortname=booking_data['shortname']).first()
                seller.contact_phone = booking_data['phone']
                seller.save()

                new_booking = self._create_booking(booking_data['manager'], subject, booking_data, date_time, id_event_cal, seller=seller)
                user_booking = seller
                public = False
            else:
                new_guest_user = self._get_or_create_guest_user(booking_data)
                new_booking = self._create_booking(booking_data['manager'], subject, booking_data, date_time, id_event_cal, guest_user=new_guest_user, is_guest_user=True)
                user_booking = new_guest_user
                public = True

            # deactivate public access token
            public_access_token.is_active = False
            public_access_token.save()

            booking_notification = BookingNotification(booking=new_booking)
            booking_notification.send_email_booking_seller(user_booking=user_booking, public=public)
            booking_notification.send_email_booking_manager(user_booking=user_booking, manager=booking_data['manager'])

            # Add to calendar if in production
            add_to_CAL(new_booking)

            return JsonResponse({'status': 'okay', 'message': 'Cita creada correctamente'})

        except (ValueError, TypeError) as e:
            return JsonResponse({'status': 'error', 'message': 'Fecha u hora no válidas. Por favor, inténtalo de nuevo'})

        except User.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Error al encontrar el gestor. Por favor, inténtalo de nuevo más tarde'})

        except BookingSubject.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Error al encontrar el tema de la cita. Por favor, inténtalo de nuevo más tarde'})

        except Exception as e:
            return JsonResponse({'status': 'error', 'message': 'Ocurrió un error al crear la cita. Por favor, inténtalo de nuevo más tarde'})

    def _unauthorized_response(self, message):
        return JsonResponse({'status': 'error', 'message': message}, status=status.HTTP_401_UNAUTHORIZED)
    
    def _extract_booking_data(self, request):
        """ Extracts the booking data from the POST request """
        managers_ids = request.POST.get('confirmed_public_managers', None)
        manager = None

        if managers_ids:
            try:
                managers_ids = managers_ids.split(',')
                ids = [int(index) for index in managers_ids]
                manager = User.objects.filter(id__in=ids).first() # Solo tomamos el primer gestor disponible. Actualmente solo hay varios managers en consultoría y gest US
            except (ValueError, TypeError):
                # Si hay un error al procesar los IDs de los managers, no asignamos ninguno
                pass

        data = {
            'shortname': request.POST.get('confirmed_seller'),
            'name': request.POST.get('confirmed_new_user_name'),
            'last_name': request.POST.get('confirmed_new_user_last_name'),
            'email': request.POST.get('confirmed_new_user_email'),
            'phone': request.POST.get('confirmed_new_user_phone'),
            'date': request.POST.get('confirmed_public_selected_date'),
            'time': request.POST.get('confirmed_public_selected_time'),
            'duration': request.POST.get('confirmed_public_selected_duration', '1'),  # Valor por defecto 1
            'comment': request.POST.get('confirmed_public_comment'),
            'manager': manager
        }

        # Validar los datos
        missing_fields = []

        if not data['shortname']:
            if not data['name']:
                missing_fields.append('name')
            if not data['last_name']:
                missing_fields.append('last_name')
            if not data['email']:
                missing_fields.append('email')
            elif not self._is_valid_email(data['email']):
                missing_fields.append('email')  # Incluir email si no es válido
        if not data['phone']:
            missing_fields.append('phone')
        if not data['date']:
            missing_fields.append('date')
        if not data['time']:
            missing_fields.append('time')
        if not data['comment']:
            missing_fields.append('comment')
        if not data['manager']:
            missing_fields.append('manager')

        data['missing_fields'] = missing_fields

        return data

    def _is_valid_email(self, email):
        """Validación básica de formato de email"""
        import re
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        return bool(email_pattern.match(email))

    def _get_or_create_guest_user(self, booking_data):
        guest_user, created = GuestUser.objects.get_or_create(
            email=booking_data['email'],
            phone=booking_data['phone'],
            defaults={'name': booking_data['name'], 'last_name': booking_data['last_name']}
        )
        if not created:
            guest_user.name = booking_data['name']
            guest_user.last_name = booking_data['last_name']
            guest_user.save()
        return guest_user

    def _create_booking(self, manager, subject, booking_data, date_time, id_event_cal, seller=None, guest_user=None, is_guest_user=False):
        return Bookings.objects.create(
            seller=seller,
            guest_user=guest_user,
            manager=manager,
            subject=subject,
            topics="",  # No topics for public bookings
            comments=booking_data['comment'],
            date=date_time,
            duration=booking_data['duration'],
            idcal_event=id_event_cal,
            created_by="public",
            is_guest_user=is_guest_user,
        )

class PublicBookingGetFirstAvailable(APIView):
    authentication_classes = [] if settings.DEBUG else [PublicAuthentication]
    permission_classes = [] if settings.DEBUG else [PublicBookingPermission]

    def get(self, request, *args, **kwargs):
        today = timezone.localtime(timezone.now()).date()
        seller_shortname = request.GET.get('shortname', None)
        
        try:
            managers = get_managers_consulting()
            if not managers:
                return Response({'error': 'No hay gestores disponibles para consulta'}, status=status.HTTP_400_BAD_REQUEST)

            first_open_date, first_manager, absence_counter = self._process_first_open_data_and_absences(managers, today)
            common_absences = [date for date, count in absence_counter.items() if count == len(managers)]

            # Search for the first available date
            first_available_date = self._find_first_available_date(first_manager, first_open_date, seller_shortname)

            if not first_available_date:
                return Response({'error': 'No se encontraron fechas disponibles'}, status=status.HTTP_204_NO_CONTENT)

            return Response({
                'availableDay': first_available_date.day,
                'availableMonth': first_available_date.month,
                'availableYear': first_available_date.year,
                'allDayAbsences': common_absences
            })
        except User.DoesNotExist:
            return Response({'error': 'Error al buscar gestores disponibles'}, status=status.HTTP_404_NOT_FOUND)
        except ValueError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            error_detail = f"Error al buscar disponibilidad: {str(e)}"
            return Response({'error': error_detail}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _process_first_open_data_and_absences(self, managers: list, today: datetime) -> tuple:
        """Find the first open date and calculate absence counters for each manager."""
        first_open_date = None
        first_manager = None
        absence_counter = Counter()
        
        for manager in managers:
            try:
                open_date = get_first_working_datetime(manager_username=manager.username, subject="contract-service")

                if not first_open_date or open_date < first_open_date:
                    first_open_date = open_date
                    first_manager = manager

                # Find all-day absences
                absences = Absence.objects.filter(manager__username=manager.username, date__gte=today, is_all_day=True).order_by('date')
                absence_dates = [str(ab.date) for ab in absences]

                absence_counter.update(absence_dates)
            except Exception as e:
                print(f"Error al procesar la disponibilidad del gestor {manager.username}: {str(e)}")
                continue

        # Si después de procesar todos los gestores no tenemos una fecha inicial, lanzar un error
        if first_open_date is None:
            raise ValueError("No se pudo encontrar una fecha inicial disponible con ningún gestor")

        return first_open_date, first_manager, absence_counter
    
    def _find_first_available_date(self, first_manager: object, first_open_date: datetime, seller_shortname: str) -> datetime:
        """Find the first available date based on appointments and holidays."""
        # Verificar que tenemos una fecha inicial válida
        if first_open_date is None:
            return None

        aux_day = first_open_date
        # Limitar la búsqueda a 30 días para evitar bucles infinitos
        max_attempts = 30
        attempts = 0

        while attempts < max_attempts:
            try:
                appointments = self._get_appointments(first_manager, seller_shortname, aux_day)
                horarios_disponibles = generate_booking_spots(
                    appointments,
                    first_manager,
                    aux_day,
                    first_open_date,
                    booking_type='public'
                )

                if horarios_disponibles and True in horarios_disponibles.values():
                    return aux_day
            except Exception as e:
                # Si hay algún error, avanzamos al siguiente día
                print(f"Error al buscar horarios disponibles para {aux_day}: {str(e)}")

            aux_day = self._get_next_day(aux_day)
            attempts += 1

        # Si no se encuentran fechas disponibles después de los intentos, retornar None
        return None

    def _get_appointments(self, first_manager: object, seller_shortname: str, aux_day):
        """Retrieve appointments for the given date."""
        # Para citas públicas, necesitamos un enfoque ligeramente diferente
        # ya que pueden no tener un seller asociado todavía

        # Comprobar si hay un seller asociado con este shortname
        try:
            seller = Seller.objects.get(shortname=seller_shortname)

            # Obtener gestores que ya tienen cita con este vendedor en esta fecha
            managers_with_appointment = Bookings.objects.filter(
                seller=seller,
                date__date=aux_day,
                status="pending"
            ).values_list('manager_id', flat=True)

            # Obtener todos los gestores disponibles para consultas
            all_managers = get_managers_consulting()

            # Filtrar los gestores disponibles (que no tienen cita con este vendedor)
            available_managers = [m for m in all_managers if m.id not in managers_with_appointment]

            # Si no hay gestores disponibles, usamos el gestor por defecto (first_manager)
            manager_to_check = available_managers[0] if available_managers else first_manager

        except Seller.DoesNotExist:
            # Si no existe el seller, usamos el manager por defecto
            manager_to_check = first_manager

        # Devolvemos todas las citas del gestor seleccionado y del vendedor (si existe) para esta fecha
        return Bookings.objects.filter(
            Q(manager=manager_to_check) | Q(seller__shortname=seller_shortname),
            date__date=aux_day, status="pending"
        ).order_by('date')

    def _get_next_day(self, current_day):
        """Find the next valid day (skip weekends and holidays)."""
        if current_day is None:
            # Si no hay día actual, empezar con la fecha de hoy
            current_day = timezone.localtime(timezone.now()).date()
            return current_day

        next_day = current_day + timedelta(days=1)
        while next_day.weekday() in [5, 6] or Holidays.objects.filter(date=next_day).exists():
            next_day += timedelta(days=1)
        return next_day

class PublicBookingGetBookingSpots(APIView):
    authentication_classes = [] if settings.DEBUG else [PublicAuthentication]
    permission_classes = [] if settings.DEBUG else [PublicBookingPermission]

    def get(self, request, *args, **kwargs):
        subject = 'contract-service' # Contract-service is the only subject available for public bookings
        shortname = request.GET.get('shortname', '') # Se recibe seller porque puede que en el enlace público se meta erróneamente gente que ya tiene cuenta
        managers = get_managers_consulting()

        selected_date = self._parse_selected_date(request)
        if not selected_date:
            return Response({'error': 'La fecha es requerida'}, status=status.HTTP_400_BAD_REQUEST)
        
        today = timezone.localtime(timezone.now()).date()
        current_month = today.month

        booking_margin_delta = get_booking_margin_delta(subject, current_month)
        
        first_working_datetime = self._get_first_working_datetime(managers, subject)

        available_appointments = self._find_available_appointments(
            managers,
            shortname,
            selected_date,
            today,
            first_working_datetime,
            booking_margin_delta
        )

        return Response({'time_spots': available_appointments})
    
    def _parse_selected_date(self, request):
        """Parse the selected date from the request."""
        selected_date = request.GET.get('selectedDate')
        if not selected_date:
            return None

        try:
            return datetime.strptime(selected_date, '%Y-%m-%d')  # Convert string to datetime
        except ValueError:
            return None
    
    def _get_first_working_datetime(self, managers: QuerySet, subject: str) -> Optional[datetime]:
        """Get the first working date for any of the managers."""
        first_working_datetime = None
        for manager in managers:
            open_date = get_first_working_datetime(manager_username=manager.username, subject=subject)
            if not first_working_datetime or open_date < first_working_datetime:
                first_working_datetime = open_date
        return first_working_datetime
    
    def _find_available_appointments(self, managers: QuerySet, shortname: str, selected_date, today, first_working_datetime, booking_margin_delta):
        """Find available appointments for each manager."""
        available_appointments = {}

        # Si es un seller existente, verificamos que no tenga cita ese día
        if shortname:
            existing_booking = Bookings.objects.filter(
                seller__shortname=shortname,
                date__date=selected_date,
                status="pending"
            ).exists()
            if existing_booking:
                return []

        # Si es un guest user, verificamos por email o teléfono
        email = self.request.GET.get('email')
        if email:
            existing_booking = Bookings.objects.filter(
                guest_user__email=email,
                date__date=selected_date,
                status="pending",
                is_guest_user=True
            ).exists()
            if existing_booking:
                return []

        for manager in managers:
            free_spots_per_manager = {}

            if self._is_invalid_booking_date(manager, selected_date, today, first_working_datetime, booking_margin_delta):
                continue

            # Solo filtramos por el gestor actual, no por todas las citas del vendedor
            appointments = Bookings.objects.filter(
                manager=manager,
                date__date=selected_date,
                status="pending"
            ).order_by('date')

            free_spots_per_manager = generate_booking_spots(
                appointments,
                manager,
                selected_date,
                first_working_datetime,
                booking_type='public'
            )

            for time, is_available in free_spots_per_manager.items():
                if is_available:
                    if time not in available_appointments:
                        available_appointments[time] = {'is_available': True, 'managers': [manager.pk]}
                    else:
                        available_appointments[time]['managers'].append(manager.pk)

        return [{'time': time, 'is_available': data['is_available'], 'managers': data['managers']} for time, data in sorted(available_appointments.items())]
    
    def _is_invalid_booking_date(self, manager, selected_date, today, first_working_datetime, booking_margin_delta):
        """Check if the selected date is invalid for booking."""
        return (
            selected_date.weekday() in [5, 6] or
            (selected_date.date() <= today and booking_margin_delta == 24) or
            Holidays.objects.filter(date=selected_date.date()).exists() or
            Absence.objects.filter(manager=manager, date=selected_date, is_all_day=True).exists() or
            selected_date.date() < first_working_datetime.date()
        )

class PublicBookingCountryList(APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        countries = Country.objects.all()
        countries = countries.exclude(iso_code="EL")
        unique_countries = CountrySerializer(countries, many=True)

        return Response(unique_countries.data)

class PublicBookingSubmitSellerInfo(APIView):
    authentication_classes = [PublicAuthentication]
    permission_classes = [PublicBookingPermission]

    def post(self, request, *args, **kwargs):
        comment = request.POST.get('public_comment')
        contact_phone_serializer = ContactPhoneSerializer(data=request.data)

        missing_fields = []

        if not contact_phone_serializer.is_valid():
            errors = contact_phone_serializer.errors

            # Extraer los campos específicos que fallan
            if 'contact_phone' in errors:
                missing_fields.append('phone')
            if 'country_code' in errors:
                missing_fields.append('phone_code')

            return ResponseError(
                validation_errors=contact_phone_serializer.errors,
                message="Por favor, revisa que el número de teléfono sea correcto",
                status=status.HTTP_400_BAD_REQUEST,
                code="form_validation_error",
                data={"missing_fields": missing_fields}
            )
        
        if not comment:
            missing_fields.append('comment')
            return ResponseError(
                validation_errors=[{"comment": "Este campo es obligatorio"}],
                message="Por favor, completa todos los campos",
                status=status.HTTP_400_BAD_REQUEST,
                code="form_validation_error",
                data={"missing_fields": missing_fields}
            )
        
        return APIResponse(
            status=status.HTTP_200_OK,
            message="Datos enviados correctamente"
        )

class PublicBookingSuccess(View):
    model = Bookings
    template_name = "bookings/public_booking_success.html"

    def get(self, request, *args, **kwargs):
        return render(request, self.template_name)