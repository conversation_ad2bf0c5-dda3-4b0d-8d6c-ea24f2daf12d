SELECT invoices_invoice.*
FROM invoices_invoice
INNER JOIN customers_customer ON invoices_invoice.customer_id = customers_customer.id
WHERE 
  invoices_invoice.transaction_type_id = 'local-sale'
  AND NOT invoices_invoice.tax_country_id = customers_customer.country_id
  AND invoices_invoice.is_oss = false
  AND invoices_invoice.total_vat_Euros = 0
  AND invoices_invoice.is_txt_amz = true
  AND invoices_invoice.customer_type_id = 'B2B'
  AND invoices_invoice.status_id = 'revised'
  AND EXTRACT(MONTH FROM invoices_invoice.expedition_date) IN (7, 8, 9);


UPDATE invoices_invoice
SET transaction_type_id = 'intra-community-sale'
WHERE id in (
  SELECT invoices_invoice.id
  FROM invoices_invoice
  INNER JOIN customers_customer ON invoices_invoice.customer_id = customers_customer.id
  WHERE invoices_invoice.transaction_type_id = 'local-sale'
    AND NOT invoices_invoice.tax_country_id = customers_customer.country_id
    AND invoices_invoice.is_oss = false
    AND invoices_invoice.total_vat_Euros = 0
    AND invoices_invoice.is_txt_amz = true
    AND invoices_invoice.customer_type_id = 'B2B'
    AND invoices_invoice.status_id = 'revised'
    AND EXTRACT(MONTH FROM invoices_invoice.expedition_date) IN (7, 8, 9)
)