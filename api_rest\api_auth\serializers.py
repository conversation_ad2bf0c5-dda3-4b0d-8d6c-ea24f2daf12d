from rest_framework import serializers
from api_rest.api_auth.models import PublicAccessCodeToken

class GetAccessTokenSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

    def create(self, validated_data):
        email = validated_data['email']
        access_token = PublicAccessCodeToken.generate_token(email=email)
        return access_token
    
class VerifyAccessCodeSerializer(serializers.Serializer):
    access_code = serializers.CharField(max_length=255)
    email = serializers.EmailField()