# admin.py
from django.contrib import admin


class SellerVatMigrationAdmin(admin.ModelAdmin):
    list_display = (
        "seller",
        "get_vat_country",
        "previous_manager_name",
        "previous_manager_start_date",
        "first_tax_declaration_submitted_by_us",
        "created_at",
    )
    list_filter = (
        "seller__country_registration",
        "seller_vat__vat_country",
        "previous_accounting_filed",
        "created_at",
    )
    search_fields = (
        "seller__name",
        "seller__email",
        "seller_vat__vat_number",
        "previous_manager_name",
        "gov_gateway_user_id",
    )
    readonly_fields = ("created_at", "modified_at")

    def get_vat_country(self, obj):
        return obj.seller_vat.vat_country if obj.seller_vat else "-"
        
    get_vat_country.short_description = "País VAT"

    fieldsets = (
        ("Información General", {
            "fields": ("seller", "seller_vat")
        }),
        ("Gestor Anterior", {
            "fields": (
                "previous_manager_name",
                "previous_manager_address",
                "previous_manager_start_date",
                "previous_accounting_filed",
                "last_tax_declaration_date",
                "last_tax_declaration_submitted_by_previous",
            )
        }),
        ("Government Gateway (solo UK)", {
            "fields": (
                "gov_gateway_user_id",
                "gov_gateway_password",
                "gov_gateway_phone_number",
            )
        }),
        ("Declaración por MuayTax", {
            "fields": ("first_tax_declaration_submitted_by_us",)
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at")
        }),
    )
