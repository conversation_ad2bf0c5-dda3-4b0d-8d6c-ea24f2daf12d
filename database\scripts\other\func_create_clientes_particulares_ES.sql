--COMPROBAR SI HAY ALGÚN "CLIENTES PARTICULARES ESPAÑA EN LA BBDD"
-- select count(*) from customers_customer where name = 'Clientes Particulares España'

--COMPROBAR CUANTOS SELLERS VAN A SER AFECTADOS POR LA FUNCIÓN
-- select count(*) from sellers_seller where legal_entity IN ('sl', 'self-employed')

-------------------------------<<FUNCIÓN>>-------------------------------

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_create_clientes_particulares_ES') THEN
        DROP FUNCTION func_create_clientes_particulares_ES();
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_create_clientes_particulares_ES()
RETURNS jsonb AS $$

DECLARE

tmp_key VARCHAR := '';
tmp_value VARCHAR := '';
tmp_json JSONB;

last_cust VARCHAR;
new_cust VARCHAR;
sellers RECORD;
customer RECORD;
json_sellers JSONB;

BEGIN

    json_sellers :=  json_build_object();

    FOR sellers IN (
        SELECT DISTINCT
            id AS sellerid,
            shortname AS short_name
        FROM sellers_seller
        WHERE legal_entity IN ('sl', 'self-employed')
        AND id IN (554, 66, 873, 614)
        ORDER BY id
    )
    LOOP

        --SHORTNAME DEL SELLER
        tmp_value := sellers.short_name;
        tmp_key := sellers.sellerid;
        tmp_json := json_build_object(tmp_key, tmp_value);
        json_sellers := jsonb_concat(json_sellers, tmp_json);

        SELECT customer_number INTO last_cust FROM customers_customer WHERE seller_id = sellers.sellerid  AND customer_number IS NOT NULL ORDER BY customer_number DESC LIMIT 1;
        new_cust := COALESCE(last_cust, '000000')::INTEGER + 1;
        new_cust := LPAD(new_cust::VARCHAR, 6, '0');


        INSERT INTO customers_customer 
				(name, 
				customer_number, 
				account_sales_id, 
				country_id, 
				customer_type_id, 
				seller_id,
				created_at,
				modified_at)
				VALUES
                (
                    'Clientes Particulares',
                    new_cust,
                    '700',
                    'ES',
                    'B2C',
                    sellers.sellerid,
                    CURRENT_TIMESTAMP,
                    CURRENT_TIMESTAMP
                );
    END LOOP;

    -- RETURN JSON
	RETURN json_sellers;

END;
$$ LANGUAGE plpgsql;