from django.db import models
from django.utils.translation import gettext_lazy as _

from django.contrib.auth import get_user_model

User = get_user_model()

class ManageModel(models.Model):
    model = models.ForeignKey(
        "dictionaries.Model",
        on_delete=models.CASCADE,
        related_name="manage_model_model",
        verbose_name="Modelo de la Declaración",
        null=True,
        blank=True,
    )
    legal_entity = models.CharField(
        max_length=50,
        choices=[
            ("self-employed", _("Autónomo")),
            ("sl", _("SL")),
            ("llc", _("LLC")),
            ("other", _("Otro")),
        ],
        blank=True,
        null=True,
        verbose_name="Tipo entidad Jurídica",
    )
    manager = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="manage_model_manager",
        verbose_name="Gestor",
        limit_choices_to={'role': 'manager'}
    )
    created_at = models.DateT<PERSON><PERSON>ield(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)
    class Meta:
        verbose_name = "Gestion de modelo"
        verbose_name_plural = "Gestion de modelos"

    def __str__(self):
        if self.model and self.manager:
            return f"{self.manager.username} gestiona modelo {self.model.code}"
