from django.shortcuts import render
from django.views.generic import View

from muaytax.utils.env_resources import logo_url_blue_letter_muaytax


class LandingAmazonESView(View):
    def get(self, request, *args, **kwargs):
        logo_url = logo_url_blue_letter_muaytax()
        return render(request, 'landing_amz/es.html', {'logo_url': logo_url})

class LandingAmazonENView(View):
    def get(self, request, *args, **kwargs):
        logo_url = logo_url_blue_letter_muaytax()
        return render(request, 'landing_amz/en.html', {'logo_url': logo_url})
