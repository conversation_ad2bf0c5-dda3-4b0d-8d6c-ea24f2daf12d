import json
import logging
from typing import Dict, List, Optional, Tuple

from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils import timezone
from django.views import View
from django.views.generic import ListView, UpdateView
from django_datatables_view.base_datatable_view import BaseDatatableView

from muaytax.app_sellers.models import Seller
from muaytax.app_services.forms.services import ServiceChangeForm
from muaytax.app_services.models import Service
from muaytax.users.permissions import CanReopenFormPermission, IsManagerRolePermission

logger = logging.getLogger(__name__)

class ServiceListView(LoginRequiredMixin, (IsManagerRolePermission), ListView):
  model = Service
  template_name_suffix = "_list"
  slug_url_kwarg = 'shortname'
  slug_field = 'shortname'

  def get_queryset(self):
      seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
      return seller.service_seller.all()

  def get_context_data(self, **kwargs):
      context = super().get_context_data(**kwargs)
      seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
      context["seller"] = seller
      return context

  def handle_no_permission(self):
      return HttpResponseRedirect(reverse("home"))

class ServiceListViewDT(LoginRequiredMixin, (IsManagerRolePermission), BaseDatatableView):
  slug_url_kwarg = 'shortname'
  slug_field = 'shortname'

  model = Service
  columns = [
    'pk',
    'seller',
    'service_name.description',
    'contracting_date',
    'contracting_discontinue',
    'start_contracting_date',
    'end_contracting_date',
    'activation_date',
    'deactivation_date',
    'quantity',
  ]
  order_columns = columns
  max_display_length = 9999999

  def get_initial_queryset(self):
    """Sobrescribir el queryset inicial para filtrar por seller"""
    seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
    return seller.service_seller.all()

  def filter_queryset(self, qs):
    """Aplicar filtros adicionales como búsqueda"""
    # NO sobrescribir el queryset aquí, solo aplicar filtros adicionales
    search = self.request.GET.get('search')
    if search:
      qs = qs.filter(service_name__description__icontains=search)
    return qs


  def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ServiceUpdateView(LoginRequiredMixin, (IsManagerRolePermission), UpdateView):
  model = Service
  form_class = ServiceChangeForm
  template_name_suffix = "_detail"
  slug_url_kwarg = 'shortname'
  slug_field = 'shortname'

  def get_queryset(self):
      seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
      return seller.service_seller.all()

  def get_context_data(self, **kwargs):
      context = super().get_context_data(**kwargs)
      context["seller"] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
      return context

  def get_success_url(self):
      return reverse("app_services:service_list", kwargs={"shortname": self.kwargs["shortname"]})

  def handle_no_permission(self):
      return HttpResponseRedirect(reverse("home"))

class ServiceUpdateAsync(LoginRequiredMixin, (IsManagerRolePermission), UpdateView):

  def post(self, request, *args, **kwargs):
    response = {}
    service = get_object_or_404(Service, pk=request.POST.get('service'))
    form = ServiceChangeForm(request.POST, instance=service)

    if form.is_valid():
      form.save()
      return JsonResponse(response, status=200, safe=False)
    else:
      return JsonResponse(form.errors, safe=False)


class ReopenProcessedFormView(LoginRequiredMixin, CanReopenFormPermission, View):
    """
    Vista mejorada para reabrir formularios procesados desde el panel de gestores.

    Funcionalidades:
    - Verificación de estados de PresentedModel asociado
    - Sincronización de estados entre ProcessedForm y PresentedModel
    - Manejo específico por tipo de formulario (M184, M5472, etc.)
    - Validaciones robustas y manejo de errores
    - Auditoría completa
    """

    FORM_TYPE_HANDLERS = {
        'model_ES-184': '_handle_m184_form_reopening',
        'model_US5472_1120': '_handle_m5472_form_reopening',
    }

    def post(self, request: HttpRequest, *args, **kwargs) -> JsonResponse:
        """Maneja la reapertura de formularios procesados"""

        try:
            # Validar entrada
            form_id = request.POST.get('form_id')
            reason = request.POST.get('reason', '').strip()

            validation_result = self._validate_request(form_id, reason, kwargs.get('shortname'))
            if not validation_result['valid']:
                return JsonResponse({
                    'success': False,
                    'message': validation_result['message']
                }, status=validation_result['status'])

            processed_form = validation_result['processed_form']

            # RESTRICCIÓN UNIVERSAL: Verificar si el modelo está presentado
            if self._is_model_presented(processed_form):
                return JsonResponse({
                    'success': False,
                    'message': 'No se puede reabrir este formulario porque el modelo asociado ya ha sido presentado ante las autoridades fiscales. Los modelos presentados oficialmente no pueden ser modificados.'
                }, status=403)

            # Realizar reapertura con transacción
            with transaction.atomic():
                reopen_result = self._reopen_form_with_validation(processed_form, reason)

                # Log de auditoría
                self._log_reopen_action(request.user, processed_form, reason, reopen_result)

            return JsonResponse({
                'success': True,
                'message': 'Formulario reabierto exitosamente',
                'form_id': form_id,
                'reopened_date': processed_form.re_open_date.strftime('%d/%m/%Y %H:%M'),
                'details': reopen_result.get('details', {})
            })

        except Exception as e:
            logger.error(f"Error reabriendo formulario {form_id}: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'message': f'Error interno: {str(e)}'
            }, status=500)

    def _validate_request(self, form_id: str, reason: str, shortname: str) -> Dict:
        """Valida la petición de reapertura"""

        if not form_id:
            return {
                'valid': False,
                'message': 'ID de formulario requerido',
                'status': 400
            }

        if not reason or len(reason) < 10:
            return {
                'valid': False,
                'message': 'Motivo de reapertura requerido (mínimo 10 caracteres)',
                'status': 400
            }

        try:
            from muaytax.app_documents.models.processed_form import ProcessedForm
            processed_form = ProcessedForm.objects.select_related(
                'seller', 'category_form'
            ).get(id=form_id)
        except ProcessedForm.DoesNotExist:
            return {
                'valid': False,
                'message': 'Formulario no encontrado',
                'status': 404
            }

        # Verificar permisos de gestor sobre el seller
        if processed_form.seller.shortname != shortname:
            return {
                'valid': False,
                'message': 'No tienes permisos para reabrir este formulario',
                'status': 403
            }

        # Verificar que el formulario esté en estado correcto para reapertura
        if not processed_form.is_form_processed:
            return {
                'valid': False,
                'message': 'El formulario no está en estado procesado',
                'status': 400
            }

        return {
            'valid': True,
            'processed_form': processed_form
        }

    def _reopen_form_with_validation(self, processed_form, reason: str) -> Dict:
        """Reabre el formulario con validaciones completas"""

        result = {
            'presented_model_updated': False,
            'seller_vats_updated': [],
            'warnings': [],
            'details': {}
        }

        # 1. Validar y actualizar PresentedModel asociado si existe
        presented_model_result = self._validate_and_update_presented_model(processed_form)
        result.update(presented_model_result)

        # 2. Actualizar ProcessedForm
        processed_form.is_form_processed = False
        processed_form.is_partial_opening = True
        processed_form.re_open_date = timezone.now()
        processed_form.save()

        # 3. Manejo específico por tipo de formulario
        form_type = processed_form.category_form.code
        if form_type in self.FORM_TYPE_HANDLERS:
            handler_name = self.FORM_TYPE_HANDLERS[form_type]
            handler_method = getattr(self, handler_name)
            handler_result = handler_method(processed_form)
            if 'seller_vats_updated' in handler_result:
                result['seller_vats_updated'] = handler_result.pop('seller_vats_updated')
            result['details'].update(handler_result)

        return result

    def _is_model_presented(self, processed_form) -> bool:
        """
        Verifica si el modelo está presentado ante las autoridades fiscales.

        CORRECCIÓN IMPORTANTE:
        - PresentedM184/PresentedM54721120 son modelos contractuales internos (is_processed != presentado)
        - Solo PresentedModel con status.code='presented' indica presentación oficial
        """
        form_type = processed_form.category_form.code
        seller = processed_form.seller
        year = processed_form.year

        try:
            from muaytax.app_documents.models.presented_model import PresentedModel

            # Mapear el tipo de formulario al código del modelo en PresentedModel
            model_code_map = {
                'model_ES-184': 'ES-184',
                'model_US5472_1120': 'US-5472'
            }

            model_code = model_code_map.get(form_type)
            if not model_code:
                # Si no está en el mapeo, intentar extraer del form_type
                model_code = form_type.replace('model_', '').replace('_', '-')

            # Buscar PresentedModel con status='presented'
            # Este es el único indicador real de presentación oficial
            presented_models = PresentedModel.objects.filter(
                seller=seller,
                year=year,
                model__code=model_code,
                status__code='presented'
            )

            is_presented = presented_models.exists()

            if is_presented:
                logger.info(f"BLOQUEO: Modelo {model_code} del seller {seller.shortname} año {year} está PRESENTADO oficialmente")

            return is_presented

        except Exception as e:
            logger.error(f"Error verificando estado presentado para formulario {processed_form.id}: {str(e)}")
            # En caso de error, ser conservador y NO bloquear (False)
            return False

    def _validate_and_update_presented_model(self, processed_form) -> Dict:
        """Valida y actualiza el PresentedModel asociado si existe"""

        result = {
            'presented_model_updated': False,
            'warnings': []
        }

        form_type = processed_form.category_form.code
        seller = processed_form.seller
        year = processed_form.year

        # Buscar PresentedModel asociado según el tipo
        presented_model = None

        if form_type == 'model_ES-184':
            from muaytax.app_sellers.models.seller_184 import PresentedM184
            presented_model = PresentedM184.objects.filter(
                seller=seller, year=year
            ).first()

        elif form_type == 'model_US5472_1120':
            from muaytax.app_documents.models.model_5472_1120 import PresentedM54721120
            presented_model = PresentedM54721120.objects.filter(
                seller=seller, year=year
            ).first()

        # Si existe PresentedModel, validar y actualizar su estado
        if presented_model:
            # Solo revertir si está en estado 'presented' (ya presentado)
            if hasattr(presented_model, 'status'):
                # Para modelos con status de dictionaries
                current_status = presented_model.status.code if hasattr(presented_model.status, 'code') else str(presented_model.status)
                if current_status == 'presented':
                    # Revertir a estado 'agreed' para permitir modificaciones
                    from muaytax.dictionaries.models import ModelStatus
                    agreed_status = ModelStatus.objects.get(code='agreed')
                    presented_model.status = agreed_status
                    presented_model.presentation_date = None  # Limpiar fecha de presentación
                    presented_model.save()
                    result['presented_model_updated'] = True
                    logger.info(f"PresentedModel {presented_model.pk} revertido de 'presented' a 'agreed'")
                else:
                    result['warnings'].append(f'PresentedModel no estaba en estado "presented" (estado actual: {current_status})')

            elif hasattr(presented_model, 'is_processed'):
                # Para modelos con campo booleano is_processed
                if presented_model.is_processed:
                    presented_model.is_processed = False
                    presented_model.save()
                    result['presented_model_updated'] = True
                    logger.info(f"PresentedModel {presented_model.pk} marcado como no procesado")
                else:
                    result['warnings'].append('PresentedModel no estaba marcado como procesado')
        else:
            result['warnings'].append(f'No se encontró PresentedModel asociado para {form_type}')

        return result

    def _handle_m184_form_reopening(self, processed_form) -> Dict:
        """Manejo específico para formularios Modelo 184"""

        result = {
            'partners_validation_cleared': False
        }

        # Para M184, limpiar validaciones específicas de partners si existen
        if processed_form.json_form:
            try:
                form_data = json.loads(processed_form.json_form)

                # Resetear validaciones de partners
                if 'validated_partners' in form_data:
                    form_data['validated_partners'] = []
                    processed_form.json_form = json.dumps(form_data)
                    processed_form.save()
                    result['partners_validation_cleared'] = True

            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Error procesando json_form para M184: {e}")

        return result

    def _handle_m5472_form_reopening(self, processed_form) -> Dict:
        """Manejo específico para formularios Modelo 5472-1120"""

        result = {
            'signature_cleared': False
        }

        # Para M5472, pueden haber firmas electrónicas pendientes
        # Aquí se podría implementar lógica específica si es necesario

        return result

    def _log_reopen_action(self, user, processed_form, reason: str, reopen_result: Dict):
        """Registra la acción de reapertura para auditoría"""

        # Crear mensaje detallado
        details = []
        details.append(f"Motivo: {reason}")

        if reopen_result.get('presented_model_updated'):
            details.append("PresentedModel asociado actualizado")

        if reopen_result.get('warnings'):
            warnings = '; '.join(reopen_result['warnings'])
            details.append(f"Advertencias: {warnings}")

        change_message = f'Formulario reabierto por gestor. {". ".join(details)}'

        # Crear entrada en el log de Django Admin
        from django.contrib.admin.models import CHANGE, LogEntry
        from django.contrib.contenttypes.models import ContentType
        LogEntry.objects.log_action(
            user_id=user.id,
            content_type_id=ContentType.objects.get_for_model(processed_form.__class__).pk,
            object_id=processed_form.id,
            object_repr=str(processed_form),
            action_flag=CHANGE,
            change_message=change_message
        )

        # Log adicional en el sistema de logging
        logger.info(
            f"ProcessedForm {processed_form.pk} reabierto por usuario {user.username}. "
            f"Seller: {processed_form.seller.shortname}, "
            f"Tipo: {processed_form.category_form.code}, "
            f"Año: {processed_form.year}. "
            f"Detalles: {change_message}"
        )
