from muaytax.app_bookings.forms.absence import AbsenceCreateForm
from muaytax.app_bookings.models.absence import Absence

from django.shortcuts import get_object_or_404
from muaytax.users.permissions import IsSellerShortnamePermission, IsSellerRolePermission, IsManagerRolePermission

from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponseRedirect
from django.contrib.auth import get_user_model
from django.urls import reverse


User = get_user_model()

class ManagerAbsenceList(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), ListView):
    model = Absence
    template_name = "bookings/bookings_absence_manager.html"
    slug_url_kwarg = 'username'
    slug_field = 'username'

    def get_queryset(self):
        manager = get_object_or_404(User, username=self.kwargs["username"])
        absences = Absence.objects.filter(manager_id = manager.id).order_by("date", "start_time")
        return absences
    
    # def get_context_data(self, **kwargs):
    #     manager = get_object_or_404(User, username=self.kwargs["username"])
    #     context = super().get_context_data(**kwargs)
    #     context["manager"] = manager
    #     return context
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
class ManagerAbsenceCreate(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), CreateView):
    model = Absence
    template_name = "bookings/bookings_absence_detail.html"
    form_class = AbsenceCreateForm
    slug_url_kwarg = 'username'
    slug_field = 'username'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        manager = get_object_or_404(User, username=self.kwargs["username"])
        kwargs['manager_instance']  = manager
        return kwargs

    def form_valid(self, form):
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse("app_bookings:manager_absence_list", kwargs={"username": self.kwargs["username"]})
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
class ManagerAbsenceUpdate(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Absence
    template_name = "bookings/bookings_absence_detail.html"
    form_class = AbsenceCreateForm
    slug_url_kwarg = 'username'
    slug_field = 'username'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        manager = get_object_or_404(User, username=self.kwargs["username"])
        kwargs['manager_instance']  = manager
        return kwargs

    def form_valid(self, form):
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse("app_bookings:manager_absence_list", kwargs={"username": self.kwargs["username"]})
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
def ManagerAbsenceDelete(request, pk, username):
    print("POST", request)
    absence = Absence.objects.get(pk=pk)
    if absence:
        absence.delete()
    return HttpResponseRedirect(reverse("app_bookings:manager_absence_list", kwargs={"username": username}))
