DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_gestoria_usa') THEN
        DROP FUNCTION func_gestoria_usa(first_date VARCHAR, last_date VARCHAR, model_period VARCHAR);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_gestoria_usa(first_date VARCHAR, last_date VARCHAR, model_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    result_json jsonb := '[]';
BEGIN

SELECT jsonb_agg(sub_data) INTO result_json

FROM (
	SELECT DISTINCT
		sel.id AS seller_id,
		sel.shortname AS shortname,
		sel.name AS seller_name,
		(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,
		(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,
		sel.contracted_accounting_usa_date AS contracted_accounting_usa_date,
		sel.contracted_maintenance_llc_date AS contracted_maintenance_llc_date,
		sel.contracted_maintenance_llc_end_date AS contracted_maintenance_llc_end_date,
		sel.is_llc_premium_direction AS is_llc_premium_direction,
		-- Num Invoices
		COUNT(DISTINCT inv.id) AS num_invoices,

		-- Num Pending Invoices
		COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

		-- Percentage Pending Invoices
		ROUND(COALESCE(
			100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
			0
		), 2) AS percentage_pending_invoices,
		(to_char((SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1), 'YYYY-MM-DD HH24:MI:SS')) AS last_login,
		MAX(pm.model_5472_id) as model_5472_id,
		MAX(pm.model_7004_id) as model_7004_id,
		MAX(pm.model_b15_id) as model_b15_id,
		MAX(
			CASE
				WHEN pm.model_5472 IS NOT null AND pm.model_5472 = 'agreed' AND pm.model_5472_fax IS NOT NULL AND pm.model_5472_fax != '' THEN 'fax_send'
				WHEN pm.model_5472 IS NOT null THEN pm.model_5472
				WHEN pmf.model_5472 IS NOT null THEN pmf.model_5472
			ELSE
				CASE WHEN m54 IS NULL THEN 'not-started'
				ELSE
					CASE
					WHEN m54.model5472_processed AND rec.null_rec > 0 THEN 'warning16'
					WHEN m54.model5472_processed THEN 'processed'
					ELSE 'not-processed' END
				END
			END
		) as model_US,
		MAX(
				CASE
					WHEN pm.model_7004 IS NOT null AND pm.model_7004 = 'agreed' AND pm.model_7004_fax IS NOT NULL AND pm.model_7004_fax != '' THEN 'fax_send'
					WHEN pm.model_7004 IS NOT NULL THEN pm.model_7004
					WHEN pm.model_5472 = 'pending' THEN 'required'
					WHEN pmf.model_7004 IS NOT null THEN pmf.model_7004
				ELSE
					CASE WHEN m54 IS NULL THEN
						CASE WHEN sel.ein IS NULL
							OR addr.address IS NULL
							OR addr.city IS NULL
							OR addr.state IS NULL
							OR addr.zip IS NULL
							OR (sel.establishment_date IS NULL AND sel.incorporation_llc_date IS NULL)
						THEN 'warning15'
						ELSE
							'required'
						END
					ELSE
						CASE WHEN m54.model5472_processed THEN 'not-required'
						ELSE
							CASE WHEN m54.ein IS NULL
								OR m54.address IS NULL
								OR m54.city IS NULL
								OR m54.state IS NULL
								OR m54.zip IS NULL
								OR (m54.incorporation_date IS NULL AND sel.incorporation_llc_date IS NULL)
							THEN 'warning15'
							ELSE
								'required'
							END
						END
					END
				END
		) as model_7004,
		MAX(
			CASE
				WHEN pm.model_b15 IS NOT null AND pm.model_b15 = 'agreed' AND pm.model_b15_fax IS NOT NULL AND pm.model_b15_fax != '' THEN 'fax_send'
				WHEN pm.model_b15 IS NOT NULL THEN pm.model_b15
				WHEN pmf.model_b15 IS NOT null THEN pmf.model_b15
			ELSE
				CASE WHEN ( 
					sel.contracted_maintenance_llc_date < TO_DATE(last_date, 'YYYY-MM-DD') AND 
					(sel.contracted_maintenance_llc_end_date IS NULL OR  sel.contracted_maintenance_llc_end_date >= TO_DATE(first_date, 'YYYY-MM-DD') )
					OR sel.is_b15_contracted IS TRUE
				)
				THEN 
					CASE WHEN b15 IS NULL THEN 'not-started'
					ELSE
						CASE 
						WHEN b15.modelb15_processed AND b15.resp_contacted_bea IS FALSE AND b15.resp_request_bea IS FALSE THEN 'not-wanted'
						WHEN b15.modelb15_processed THEN 'processed'
						ELSE 'not-processed' END
					END
				ELSE 'not-required'
				END
			END
		) as model_b15
	FROM
		sellers_seller sel
	-- INNER JOIN documents_presentedm54721120 m54 ON m54.seller_id = sel.id
 	LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
	LEFT JOIN (
		SELECT DISTINCT
			sel.id as seller_id,
			MAX(CASE WHEN pm.model_id::text = 'US-5472' THEN pm.id END) as model_5472_id,
			MAX(CASE WHEN pm.model_id::text = 'US-5472' THEN pm.status_id END) as model_5472,
			MAX(CASE WHEN pm.model_id::text = 'US-5472' THEN pm.fax_destination_id END) as model_5472_fax,
			MAX(CASE WHEN pm.model_id::text = 'US-7004' THEN pm.id END) as model_7004_id,
			MAX(CASE WHEN pm.model_id::text = 'US-7004' THEN pm.status_id END) as model_7004,
			MAX(CASE WHEN pm.model_id::text = 'US-7004' THEN pm.fax_destination_id END) as model_7004_fax,			
			MAX(CASE WHEN pm.model_id::text = 'US-BE15' THEN pm.id END) as model_b15_id,
			MAX(CASE WHEN pm.model_id::text = 'US-BE15' THEN pm.status_id END) as model_b15,
			MAX(CASE WHEN pm.model_id::text = 'US-BE15' THEN pm.fax_destination_id END) as model_b15_fax
		FROM
			sellers_seller sel
		LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
		WHERE
			pm.year = EXTRACT(YEAR FROM first_date::DATE) AND pm.country_id = 'US'
		GROUP BY
			sel.id
	) AS pm on sel.id = pm.seller_id

	LEFT JOIN (
		SELECT
			sel.id as seller_id,
			MAX(CASE WHEN pmf.model_id::text = 'US-5472' THEN pmf.status_id END) as model_5472,
			MAX(CASE WHEN pmf.model_id::text = 'US-7004' THEN pmf.status_id END) as model_7004,
			MAX(CASE WHEN pmf.model_id::text = 'US-BE15' THEN pmf.status_id END) as model_b15
		FROM
			sellers_seller sel
		LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
		WHERE
			pmf.year = EXTRACT(YEAR FROM first_date::DATE)
			AND pmf.period_id = model_period
			AND pmf.country_id = 'US'
		GROUP BY
			sel.id
	) AS pmf on sel.id = pmf.seller_id

	LEFT JOIN (
		SELECT
			sel.id as seller_id,
			sel.ein AS ein,
			pm54.id AS pm54_id,
			pm54.is_processed as model5472_processed,
			pm54.incorporation_date AS incorporation_date,
			MAX(CASE WHEN addr.address IS NOT NULL THEN addr.address END) as address,
			MAX(CASE WHEN addr.address_city IS NOT NULL THEN addr.address_city END) as city,
			MAX(CASE WHEN addr.address_state IS NOT NULL THEN addr.address_state END) as state,
			MAX(CASE WHEN addr.address_zip IS NOT NULL THEN addr.address_zip END) as zip
		FROM
			sellers_seller sel
		LEFT JOIN documents_presentedm54721120 pm54 ON sel.id = pm54.seller_id
		LEFT JOIN address_address addr ON addr.id = sel.seller_address_id
		WHERE
			pm54.year = EXTRACT(YEAR FROM first_date::DATE)
		GROUP BY
			sel.id,
			pm54.id
			--addr.id
	) AS m54 on sel.id = m54.seller_id

	LEFT JOIN (
		SELECT
			sel.id as seller_id,
			b15.id AS b15_id,
			b15.is_processed as modelb15_processed,
			b15.resp_contacted_bea,
			b15.resp_request_bea
		FROM sellers_seller sel
		LEFT JOIN documents_modelformbe15 b15 ON sel.id = b15.seller_id
		WHERE b15.year = EXTRACT(YEAR FROM first_date::DATE)
		GROUP BY
			sel.id,
			b15.id
	) AS b15 on sel.id = b15.seller_id

	LEFT JOIN (
		SELECT
			sel.id as seller_id,
			sel.ein AS ein,
			MAX(CASE WHEN addr.address IS NOT NULL THEN addr.address END) as address,
			MAX(CASE WHEN addr.address_city IS NOT NULL THEN addr.address_city END) as city,
			MAX(CASE WHEN addr.address_state IS NOT NULL THEN addr.address_state END) as state,
			MAX(CASE WHEN addr.address_zip IS NOT NULL THEN addr.address_zip END) as zip
		FROM sellers_seller sel
		LEFT JOIN address_address addr ON addr.id = sel.seller_address_id
		GROUP BY sel.id
	) AS addr ON addr.seller_id = sel.id
	LEFT JOIN (
		SELECT
			COUNT(DISTINCT rec.id) AS null_rec,
			rec.seller_id AS seller_id
		FROM documents_accountingrecord rec
		LEFT JOIN documents_presentedm54721120 pm54 ON rec.pm5472_1120_id = pm54.id
		WHERE
			(rec.total_currency = 0 OR rec.total_currency IS NULL)
			AND pm54.year = EXTRACT(YEAR FROM first_date::DATE)
		GROUP BY rec.id

	) AS rec ON rec.seller_id =sel.id

	WHERE
		sel.contracted_maintenance_llc_date < TO_DATE(last_date, 'YYYY-MM-DD')
		AND (sel.contracted_maintenance_llc_end_date IS NULL OR  sel.contracted_maintenance_llc_end_date >= TO_DATE(first_date, 'YYYY-MM-DD') )
		OR sel.is_5472_1120_contracted IS TRUE
		OR sel.is_5472_1120_inactive_contracted IS TRUE
		OR sel.is_b15_contracted IS TRUE
		OR sel.contracted_accounting_usa_date IS NOT NULL
	GROUP BY
		sel.id
	ORDER BY sel.id
) AS sub_data;

    IF result_json IS NULL THEN
        result_json := '[]';
    END IF;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- SELECT * FROM func_gestoria_usa('2023-01-01','2024-01-01', '0A');
