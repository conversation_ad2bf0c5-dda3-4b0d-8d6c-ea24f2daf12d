import json
from datetime import datetime

from django.db import IntegrityError, transaction

from django.conf import settings
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse_lazy, reverse
from django.views.generic import View, ListView, CreateView, DeleteView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q

# Para la vista de DataTables
from django_datatables_view.base_datatable_view import BaseDatatableView

# Modelos y Formularios
from muaytax.app_sellers.models.seller_exclusions import ModelExclusion
from muaytax.app_sellers.forms.seller_exclusions import ModelExclusionForm
from muaytax.app_sellers.models.seller import Seller
from muaytax.users.permissions import IsManagerRolePermission

# Para debugging
debug = settings.DEBUG  # Variable de depuración global


# -------------------------------------------------
#  <PERSON> (usa template con listado y contador)
# -------------------------------------------------
class ModelExclusionListView(LoginRequiredMixin, IsManagerRolePermission, ListView):

    model = ModelExclusion
    template_name = "sellers/seller_exclusions_list_manager.html"
    context_object_name = "exclusions"

    def get_queryset(self):
        seller_shortname = self.kwargs.get("shortname")
        queryset = ModelExclusion.objects.filter(seller__shortname=seller_shortname).select_related("customer", "provider")
        print("Queryset data:", queryset)
        print(ModelExclusion.objects.filter(seller__shortname=seller_shortname).exists())
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs.get("shortname"))

        exclusions = ModelExclusion.objects.filter(seller=seller)
        total_exclusions = exclusions.count()
        total_exclusions_customer = exclusions.exclude(customer=None).count()
        total_exclusions_provider = exclusions.exclude(provider=None).count()

        context["debug"] = debug
        context["seller"] = seller
        context["seller_shortname"] = self.kwargs.get("shortname")
        context["labels"] = {
            "EXCLUSIONES TOTALES": "total",
            "EXCLUSIONES TOTALES - CLIENTES": "customer",
            "EXCLUSIONES TOTALES - PROVEEDORES": "provider"
        }
        context["exclusions_counts"] = {
            "total": total_exclusions,
            "customer": total_exclusions_customer,
            "provider": total_exclusions_provider
        }

        return context

# -------------------------------------------------
#  Vista Crear Exclusión (formulario)
# -------------------------------------------------
class ModelExclusionCreateView(LoginRequiredMixin, CreateView):
    model = ModelExclusion
    form_class = ModelExclusionForm
    template_name = "sellers/include/forms/seller_exclusions_form.html"

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["seller"] = get_object_or_404(Seller, shortname=self.kwargs.get("shortname"))
        return kwargs

    def get(self, request, *args, **kwargs):
        """Cargar formulario en el modal usando AJAX."""
        print("[DEBUG] Request GET para cargar el formulario Create - start")

        if request.headers.get("x-requested-with") == "XMLHttpRequest":
            form = self.get_form()
            context = {
                "form": form,
                "seller": get_object_or_404(Seller, shortname=self.kwargs.get("shortname"))
            }

            try:
                html = render(request, self.template_name, context).content.decode("utf-8")
                return JsonResponse({"form_html": html})
            except Exception as e:
                print(f"[ERROR] Problema al renderizar el formulario: {e}")
                return JsonResponse({"error": "Error al cargar el formulario"}, status=500)
            
        

        return super().get(request, *args, **kwargs)

    def form_valid(self, form):
        """Guardar la exclusión y retornar una respuesta JSON."""
        
        self.object = form.save()
        return JsonResponse({"success": True, "message": "Exclusión agregada correctamente"})

    def form_invalid(self, form):
        """Retornar errores en formato JSON si el formulario es inválido."""
        return JsonResponse({"success": False, "errors": form.errors}, status=400)

# -------------------------------------------------
#  Vista Actualizar Exclusión (formulario)
# -------------------------------------------------
class ModelExclusionUpdateView(UpdateView):
        model = ModelExclusion
        form_class = ModelExclusionForm
        template_name = "sellers/include/forms/seller_exclusions_form.html"

        def get_form_kwargs(self):
            kwargs = super().get_form_kwargs()
            exclusion = get_object_or_404(ModelExclusion, id=self.kwargs.get("pk"))
            
            # 🔹 Obtener el seller de la exclusión
            kwargs["seller"] = exclusion.seller
            print(f"[DEBUG] Seller obtenido en UpdateView: {exclusion.seller.shortname}")
            
            kwargs["instance"] = exclusion
            return kwargs

        def get(self, request, *args, **kwargs):
            print("[DEBUG] Request GET para cargar el formulario Update - start")
            # Cargar formulario con datos precargados 
            if request.headers.get("x-requested-with") == "XMLHttpRequest":
                form = self.get_form()
                context = {
                    "form": form,
                    "is_edit": True,
                    "seller": get_object_or_404(Seller, shortname=self.kwargs.get("shortname"))
                }
                # 🔹 Verificar cuántas opciones tiene cada campo antes de renderizar
                print(f"[DEBUG] Opciones en tax_model: {len(form.fields['tax_model'].choices)}")
                print(f"[DEBUG] Opciones en exclusion_type: {len(form.fields['exclusion_type'].choices)}")
                print(f"[DEBUG] Opciones en year: {len(form.fields['year'].choices)}")
                print(f"[DEBUG] Clientes cargados: {form.fields['customer'].queryset.count()}")
                print(f"[DEBUG] Proveedores cargados: {form.fields['provider'].queryset.count()}")
                html = self.render_to_response(context).rendered_content
                return JsonResponse({"form_html": html})
            
            return super().get(request, *args, **kwargs)

        def form_valid(self, form):
            
            self.object = form.save(commit=False)

            # 🔹 Asegurar que `tax_model` no se modifique en la edición
            if self.object.pk:  # Si es una edición
                self.object.tax_model = self.get_object().tax_model

            try:
                self.object.save()
                return JsonResponse({"success": True, "message": "Exclusión actualizada correctamente"})
            except IntegrityError:
                return JsonResponse({"success": False, "errors": {"general": "Ya existe una exclusión con este Modelo y Año."}}, status=400)

        def form_invalid(self, form):
            # Devolver errores en formato JSON 
            return JsonResponse({"success": False, "errors": form.errors}, status=400)
        
# -------------------------------------------------
#  Vista Eliminar Exclusión
# -------------------------------------------------
class ModelExclusionDeleteView(LoginRequiredMixin, IsManagerRolePermission, DeleteView):
    model = ModelExclusion

    def delete(self, request, *args, **kwargs):
        """Eliminar la exclusión y devolver JSON."""
        self.object = self.get_object()
        self.object.delete()
        return JsonResponse({"success": True, "message": "Exclusión eliminada correctamente"})

# --------------------------------------------------------------------------------------------------------------
#  Vista DataTables (provee datos en formato JSON compatible con DataTables usando procesamiento server-side.)
# -------------------------------------------------------------------------------------------------------------
class SellerExclusionsDataTableView(LoginRequiredMixin, IsManagerRolePermission, BaseDatatableView):

    model = ModelExclusion
    columns = ["id", "tax_model", "year", "customer", "provider", "reason", "created_at"]
    # Orden prioritario: created_at DESC, year, tax_model, customer/provider, reason, id
    order_columns = ["-created_at", "-year", "tax_model", "customer", "provider", "reason", "id"]

    def get_initial_queryset(self):
        """Obtiene el queryset inicial filtrado por vendedor."""
        seller_shortname = self.kwargs.get("shortname")
        seller = get_object_or_404(Seller, shortname=seller_shortname)
        return ModelExclusion.objects.filter(seller=seller)

    def filter_queryset(self, qs):
        """Filtra solo por coincidencias de texto en cualquier columna de la tabla."""
        search_value = self.request.GET.get("search[value]", "").strip()
        
        if search_value:
            qs = qs.filter(
                Q(id__icontains=search_value) |
                Q(tax_model__code__icontains=search_value) |
                Q(year__icontains=search_value) |
                Q(customer__name__icontains=search_value) |
                Q(provider__name__icontains=search_value) |
                Q(reason__icontains=search_value) |
                Q(created_at__icontains=search_value)
            )

        return qs

    def prepare_results(self, qs):
        """
        Convierte el queryset en una lista de diccionarios con el formato que DataTables espera (clave "data").
        """
        data = []
        seller_shortname = self.kwargs.get("shortname")

        for exclusion in qs:
            addressee = "—"
            if exclusion.customer:
                addressee = f"Cliente: {exclusion.customer.name}"
            elif exclusion.provider:
                addressee = f"Proveedor: {exclusion.provider.name}"

            item = {
                "id": exclusion.id,
                "tax_model": exclusion.tax_model.description if exclusion.tax_model else "",
                "year": exclusion.year if exclusion.year else "",
                "addressee": addressee,
                "customer": exclusion.customer.name if exclusion.customer else "",
                "provider": exclusion.provider.name if exclusion.provider else "",
                "reason": exclusion.reason if exclusion.reason else "Sin motivo",
                "created_at": exclusion.created_at.strftime("%Y-%m-%d %H:%M:%S") if exclusion.created_at else "",
                "created_at_timestamp": int(exclusion.created_at.timestamp()) if exclusion.created_at else None,
            }
            data.append(item)

        debug and print(f"[DEBUG] JSON enviado a DataTables: {json.dumps(data, indent=2)}")
        return data
