-- Script para insertar series de facturas para el año 2025
-- Este script inserta las series de facturas necesarias para los vendedores que cumplen con los criterios
-- Criterios: SL / Autotnomos con fecha de finalización de contabilidad nula o posterior al 29/07/2025
-- Paises: Contratados (flag) o activados (fecha) antes sin fecha de desactivación o posterior al 29/07/2025

-- Las series a insertar son:
-- VENTAS: RECT-INV-SAL, INV-SAL, PROFORMA-INV-SAL, FV-VERI-RECT-INV-SAL, FV-VERI-INV-SAL
-- GASTOS: RECT-INV-EXP, INV-EXP

DO $$
DECLARE
    sellerid INT;
    countryid CHAR(2);
	date_init DATE := '2025-07-01';
    date_init_sales DATE := '2025-07-01';
	date_init_expenses DATE := '2025-01-01';
    year_serie INT := 2025;
BEGIN
    -- Recorre cada seller de la lista
    FOR sellerid IN 
        SELECT sel.id, sel.shortname
        FROM sellers_seller sel
        WHERE (sel.legal_entity = 'sl' OR sel.legal_entity = 'self-employed')
        AND (sel.contracted_accounting_end_date IS NULL OR sel.contracted_accounting_end_date >= date_init)
    LOOP
        -- Recorre los países
        FOR countryid IN 
			Select Distinct sv.vat_country_id
			FROM sellers_seller sel
			INNER JOIN sellers_sellervat sv ON sv.seller_id = sel.id
			WHERE sel.id = sellerid
			AND sv.vat_country_id IS NOT NULL
			AND (
				sv.is_contracted IS TRUE 
				OR 
				(
					sv.activation_date IS NOT NULL 
					AND sv.deactivation_date IS NULL
				) 
				OR
				(
					sv.activation_date IS NOT NULL 
					AND sv.deactivation_date >= date_init
				)
			)
        LOOP
		
			-- Insertar la serie PROFORMA-INV-SAL
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'PROFORMA-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN				
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'PROFORMA-INV-SAL',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					true,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie RECT-INV-SAL
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'RECT-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'RECT-INV-SAL',  -- Serial Code
					true,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie INV-SAL
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'INV-SAL',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

			-- Insertar la serie FV-VERI-RECT-INV-SAL (Verifactu)
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'FV-VERI-RECT-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'FV-VERI-RECT-INV-SAL',  -- Serial Code
					true,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					true,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie FV-VERI-INV-SAL (Verifactu)
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'FV-VERI-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'FV-VERI-INV-SAL',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					true,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie RECT-INV-EXP
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'RECT-INV-EXP'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'RECT-INV-EXP',  -- Serial Code
					true,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_expenses,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'expenses',  -- invoice_category_id (expenses)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie INV-EXP
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'INV-EXP'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'INV-EXP',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_expenses,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'expenses',  -- invoice_category_id (expenses)
					sellerid  -- seller_id
				);
			END IF;

        END LOOP;
    END LOOP;
END $$;


-- Script para insertar series de facturas para el año 2025 para LLC
-- Este script inserta las series de facturas necesarias para los vendedores que cumplen con los criterios
-- Criterios: Paises: LLC on mantenimiento contratado

-- Las series a insertar son:
-- VENTAS: RECT-INV-SAL (US), INV-SAL (US), PROFORMA-INV-SAL (US)
-- GASTOS: RECT-INV-EXP (US), INV-EXP (US)


DO $$
DECLARE
    sellerid INT;
    countryid CHAR(2) := 'US';
    date_init DATE := '2025-07-01';
    date_init_sales DATE := '2025-07-01';
	date_init_expenses DATE := '2025-01-01';
    year_serie INT := 2025;
BEGIN
    -- Recorre cada seller de la lista
    FOR sellerid IN 
        SELECT sel.id, sel.shortname
        FROM sellers_seller sel
        WHERE (sel.legal_entity = 'llc' OR sel.legal_entity = 'other')
		AND sel.contracted_maintenance_llc_date IS NOT NULL
        AND (sel.contracted_maintenance_llc_end_date IS NULL OR sel.contracted_maintenance_llc_end_date >= date_init)
    LOOP		
		-- Insertar la serie PROFORMA-INV-SAL
		IF NOT EXISTS (
			SELECT 1
			FROM invoices_serialinvoice
			WHERE serial_code = 'PROFORMA-INV-SAL'
			AND year = year_serie
			AND country_id = countryid
			AND seller_id = sellerid
		) THEN				
			INSERT INTO invoices_serialinvoice (
				serial_code, 
				is_rectifying, 
				is_migration, 
				is_proforma, 
				is_verifactu,
				year, 
				last_incremental, 
				start_date, 
				end_date, 
				created_by, 
				created_at, 
				modified_at, 
				country_id, 
				invoice_category_id, 
				seller_id
			)
			VALUES (
				'PROFORMA-INV-SAL',  -- Serial Code
				false,  -- is_rectifying
				false,  -- is_migration
				true,  -- is_proforma
				false,  -- is_verifactu
				year_serie,  -- Año 2025
				0,  -- last_incremental
				date_init_sales,  -- start_date
				NULL,  -- end_date (NULL)
				'app',  -- created_by
				CURRENT_TIMESTAMP,  -- created_at
				CURRENT_TIMESTAMP,  -- modified_at
				countryid,  -- country_id 
				'sales',  -- invoice_category_id (sales)
				sellerid  -- seller_id
			);
		END IF;

		-- Insertar la serie RECT-INV-SAL
		IF NOT EXISTS (
			SELECT 1
			FROM invoices_serialinvoice
			WHERE serial_code = 'RECT-INV-SAL'
			AND year = year_serie
			AND country_id = countryid
			AND seller_id = sellerid
		) THEN	
			INSERT INTO invoices_serialinvoice (
				serial_code, 
				is_rectifying, 
				is_migration, 
				is_proforma, 
				is_verifactu,
				year, 
				last_incremental, 
				start_date, 
				end_date, 
				created_by, 
				created_at, 
				modified_at, 
				country_id, 
				invoice_category_id, 
				seller_id
			)
			VALUES (
				'RECT-INV-SAL',  -- Serial Code
				true,  -- is_rectifying
				false,  -- is_migration
				false,  -- is_proforma
				false,  -- is_verifactu
				year_serie,  -- Año 2025
				0,  -- last_incremental
				date_init_sales,  -- start_date
				NULL,  -- end_date (NULL)
				'app',  -- created_by
				CURRENT_TIMESTAMP,  -- created_at
				CURRENT_TIMESTAMP,  -- modified_at
				countryid,  -- country_id 
				'sales',  -- invoice_category_id (sales)
				sellerid  -- seller_id
			);
		END IF;

		-- Insertar la serie INV-SAL
		IF NOT EXISTS (
			SELECT 1
			FROM invoices_serialinvoice
			WHERE serial_code = 'INV-SAL'
			AND year = year_serie
			AND country_id = countryid
			AND seller_id = sellerid
		) THEN	
			INSERT INTO invoices_serialinvoice (
				serial_code, 
				is_rectifying, 
				is_migration, 
				is_proforma, 
				is_verifactu,
				year, 
				last_incremental, 
				start_date, 
				end_date, 
				created_by, 
				created_at, 
				modified_at, 
				country_id, 
				invoice_category_id, 
				seller_id
			)
			VALUES (
				'INV-SAL',  -- Serial Code
				false,  -- is_rectifying
				false,  -- is_migration
				false,  -- is_proforma
				false,  -- is_verifactu
				year_serie,  -- Año 2025
				0,  -- last_incremental
				date_init_sales,  -- start_date
				NULL,  -- end_date (NULL)
				'app',  -- created_by
				CURRENT_TIMESTAMP,  -- created_at
				CURRENT_TIMESTAMP,  -- modified_at
				countryid,  -- country_id 
				'sales',  -- invoice_category_id (sales)
				sellerid  -- seller_id
			);
		END IF;

		-- Insertar la serie RECT-INV-EXP
		IF NOT EXISTS (
			SELECT 1
			FROM invoices_serialinvoice
			WHERE serial_code = 'RECT-INV-EXP'
			AND year = year_serie
			AND country_id = countryid
			AND seller_id = sellerid
		) THEN	
			INSERT INTO invoices_serialinvoice (
				serial_code, 
				is_rectifying, 
				is_migration, 
				is_proforma, 
				is_verifactu,
				year, 
				last_incremental, 
				start_date, 
				end_date, 
				created_by, 
				created_at, 
				modified_at, 
				country_id, 
				invoice_category_id, 
				seller_id
			)
			VALUES (
				'RECT-INV-EXP',  -- Serial Code
				true,  -- is_rectifying
				false,  -- is_migration
				false,  -- is_proforma
				false,  -- is_verifactu
				year_serie,  -- Año 2025
				0,  -- last_incremental
				date_init_expenses,  -- start_date
				NULL,  -- end_date (NULL)
				'app',  -- created_by
				CURRENT_TIMESTAMP,  -- created_at
				CURRENT_TIMESTAMP,  -- modified_at
				countryid,  -- country_id 
				'expenses',  -- invoice_category_id (expenses)
				sellerid  -- seller_id
			);
		END IF;

		-- Insertar la serie INV-EXP
		IF NOT EXISTS (
			SELECT 1
			FROM invoices_serialinvoice
			WHERE serial_code = 'INV-EXP'
			AND year = year_serie
			AND country_id = countryid
			AND seller_id = sellerid
		) THEN	
			INSERT INTO invoices_serialinvoice (
				serial_code, 
				is_rectifying, 
				is_migration, 
				is_proforma, 
				is_verifactu,
				year, 
				last_incremental, 
				start_date, 
				end_date, 
				created_by, 
				created_at, 
				modified_at, 
				country_id, 
				invoice_category_id, 
				seller_id
			)
			VALUES (
				'INV-EXP',  -- Serial Code
				false,  -- is_rectifying
				false,  -- is_migration
				false,  -- is_proforma
				false,  -- is_verifactu
				year_serie,  -- Año 2025
				0,  -- last_incremental
				date_init_expenses,  -- start_date
				NULL,  -- end_date (NULL)
				'app',  -- created_by
				CURRENT_TIMESTAMP,  -- created_at
				CURRENT_TIMESTAMP,  -- modified_at
				countryid,  -- country_id 
				'expenses',  -- invoice_category_id (expenses)
				sellerid  -- seller_id
			);
		END IF;

    END LOOP;
END $$;


-- Script para insertar series de facturas para el año 2025 Para todos los paises IVA
-- Este script inserta las series de facturas necesarias para los vendedores que cumplen con los criterios
-- Criterios: Paises: Contratados (flag) o activados (fecha) antes sin fecha de desactivación o posterior al 29/07/2025

-- Las series a insertar son:
-- VENTAS: RECT-INV-SAL, INV-SAL, PROFORMA-INV-SAL, FV-VERI-RECT-INV-SAL (ESPAÑA), FV-VERI-INV-SAL (ESPAÑA)
-- GASTOS: RECT-INV-EXP, INV-EXP


DO $$
DECLARE
    sellerid INT;
    countryid CHAR(2);
    date_init DATE := '2025-07-01';
    date_init_sales DATE := '2025-07-01';
	date_init_expenses DATE := '2025-01-01';
    year_serie INT := 2025;
BEGIN
    -- Recorre cada seller de la lista
    FOR sellerid IN 
        SELECT sel.id, sel.shortname
        FROM sellers_seller sel
        -- WHERE (sel.legal_entity = 'sl' OR sel.legal_entity = 'self-employed')
        -- AND (sel.contracted_accounting_end_date IS NULL OR sel.contracted_accounting_end_date >= date_init)
    LOOP
        -- Recorre los países
        FOR countryid IN 
			Select Distinct sv.vat_country_id
			FROM sellers_seller sel
			INNER JOIN sellers_sellervat sv ON sv.seller_id = sel.id
			WHERE sel.id = sellerid
			AND sv.vat_country_id IS NOT NULL
			AND (
				sv.is_contracted IS TRUE 
				OR 
				(
					sv.activation_date IS NOT NULL 
					AND sv.deactivation_date IS NULL
				) 
				OR
				(
					sv.activation_date IS NOT NULL 
					AND sv.deactivation_date >= date_init
				)
			)
        LOOP
		
			-- Insertar la serie PROFORMA-INV-SAL
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'PROFORMA-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN				
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'PROFORMA-INV-SAL',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					true,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie RECT-INV-SAL
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'RECT-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'RECT-INV-SAL',  -- Serial Code
					true,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie INV-SAL
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'INV-SAL'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'INV-SAL',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

			-- Insertar la serie FV-VERI-RECT-INV-SAL (Verifactu)
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'FV-VERI-RECT-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
				AND country_id = 'ES'
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'FV-VERI-RECT-INV-SAL',  -- Serial Code
					true,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					true,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie FV-VERI-INV-SAL (Verifactu)
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'FV-VERI-INV-SAL'
                AND year = year_serie
                AND country_id = countryid
				AND country_id = 'ES'
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'FV-VERI-INV-SAL',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					true,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_sales,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'sales',  -- invoice_category_id (sales)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie RECT-INV-EXP
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'RECT-INV-EXP'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'RECT-INV-EXP',  -- Serial Code
					true,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_expenses,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'expenses',  -- invoice_category_id (expenses)
					sellerid  -- seller_id
				);
			END IF;

            -- Insertar la serie INV-EXP
			IF NOT EXISTS (
                SELECT 1
                FROM invoices_serialinvoice
                WHERE serial_code = 'INV-EXP'
                AND year = year_serie
                AND country_id = countryid
                AND seller_id = sellerid
            ) THEN	
				INSERT INTO invoices_serialinvoice (
					serial_code, 
					is_rectifying, 
					is_migration, 
					is_proforma, 
					is_verifactu,
					year, 
					last_incremental, 
					start_date, 
					end_date, 
					created_by, 
					created_at, 
					modified_at, 
					country_id, 
					invoice_category_id, 
					seller_id
				)
				VALUES (
					'INV-EXP',  -- Serial Code
					false,  -- is_rectifying
					false,  -- is_migration
					false,  -- is_proforma
					false,  -- is_verifactu
					year_serie,  -- Año 2025
					0,  -- last_incremental
					date_init_expenses,  -- start_date
					NULL,  -- end_date (NULL)
					'app',  -- created_by
					CURRENT_TIMESTAMP,  -- created_at
					CURRENT_TIMESTAMP,  -- modified_at
					countryid,  -- country_id 
					'expenses',  -- invoice_category_id (expenses)
					sellerid  -- seller_id
				);
			END IF;

        END LOOP;
    END LOOP;
END $$;


-- SERIES ANTIGUAS DE GASTOS: 2021 - 2024
DO $$
DECLARE
    sellerid INT;
    countryid CHAR(2);
	years INT[] := ARRAY[2021, 2022, 2023, 2024];
    year_serie INT;
    date_init DATE;
	date_end DATE;
	i INT;
BEGIN
    -- Recorre cada seller de la lista
    FOR sellerid IN 
        SELECT sel.id, sel.shortname
        FROM sellers_seller sel
    LOOP

		FOR i IN 1 .. array_length(years, 1) 
		LOOP
            year_serie := years[i];
            date_init := TO_DATE(year_serie || '-01-01', 'YYYY-MM-DD');
            date_end := TO_DATE(year_serie || '-12-31', 'YYYY-MM-DD');

			-- Recorre los países
			FOR countryid IN 
				Select Distinct sv.vat_country_id
				FROM sellers_seller sel
				INNER JOIN sellers_sellervat sv ON sv.seller_id = sel.id
				WHERE sel.id = sellerid
				AND sv.vat_country_id IS NOT NULL
				AND (
					sv.is_contracted IS TRUE 
					OR 
					(
						sv.activation_date IS NOT NULL 
						AND sv.deactivation_date IS NULL
					) 
					OR
					(
						sv.activation_date IS NOT NULL 
						AND sv.deactivation_date >= date_init
					)
				)
			LOOP
				-- Insertar la serie RECT-INV-EXP
				IF NOT EXISTS (
					SELECT 1
					FROM invoices_serialinvoice
					WHERE serial_code = 'RECT-INV-EXP'
					AND year = year_serie
					AND country_id = countryid
					AND seller_id = sellerid
				) THEN	
					INSERT INTO invoices_serialinvoice (
						serial_code, 
						is_rectifying, 
						is_migration, 
						is_proforma, 
						is_verifactu,
						year, 
						last_incremental, 
						start_date, 
						end_date, 
						created_by, 
						created_at, 
						modified_at, 
						country_id, 
						invoice_category_id, 
						seller_id
					)
					VALUES (
						'RECT-INV-EXP',  -- Serial Code
						true,  -- is_rectifying
						false,  -- is_migration
						false,  -- is_proforma
						false,  -- is_verifactu
						year_serie,  -- Año 2025
						0,  -- last_incremental
						date_init,  -- start_date
						date_end,  -- end_date (NULL)
						'app',  -- created_by
						CURRENT_TIMESTAMP,  -- created_at
						CURRENT_TIMESTAMP,  -- modified_at
						countryid,  -- country_id 
						'expenses',  -- invoice_category_id (expenses)
						sellerid  -- seller_id
					);
				END IF;

				-- Insertar la serie INV-EXP
				IF NOT EXISTS (
					SELECT 1
					FROM invoices_serialinvoice
					WHERE serial_code = 'INV-EXP'
					AND year = year_serie
					AND country_id = countryid
					AND seller_id = sellerid
				) THEN	
					INSERT INTO invoices_serialinvoice (
						serial_code, 
						is_rectifying, 
						is_migration, 
						is_proforma, 
						is_verifactu,
						year, 
						last_incremental, 
						start_date, 
						end_date, 
						created_by, 
						created_at, 
						modified_at, 
						country_id, 
						invoice_category_id, 
						seller_id
					)
					VALUES (
						'INV-EXP',  -- Serial Code
						false,  -- is_rectifying
						false,  -- is_migration
						false,  -- is_proforma
						false,  -- is_verifactu
						year_serie,  -- Año 2025
						0,  -- last_incremental
						date_init,  -- start_date
						date_end,  -- end_date (NULL)
						'app',  -- created_by
						CURRENT_TIMESTAMP,  -- created_at
						CURRENT_TIMESTAMP,  -- modified_at
						countryid,  -- country_id 
						'expenses',  -- invoice_category_id (expenses)
						sellerid  -- seller_id
					);
				END IF;
			END LOOP;
        END LOOP;
    END LOOP;
END $$;



--- Script para recorrer facturas de los sellers y actualizar el campo serial_code y serial_number
DO $$
DECLARE
    sellerid INT;
	invoiceid INT;
    countryid CHAR(2);
	is_rectifying_inv BOOLEAN;
	is_oss_inv BOOLEAN;
	serialid INT;
	serialnumber INT;
	year_serie INT := 2025;    
    date_init DATE := '2025-07-01';
	qty_invoices_seller INT := 0;
	qty_invoices INT := 0;
BEGIN
    -- Recorre cada seller de la lista
    FOR sellerid IN 
        SELECT DISTINCT sel.id, sel.shortname
        FROM sellers_seller sel
		INNER JOIN invoices_invoice inv ON inv.seller_id = sel.id
		WHERE inv.status_id = 'revised'
		AND inv.invoice_category_id = 'sales'
		AND inv.expedition_date >= '2025-07-01'
    LOOP
		-- Recorre las facturas del seller
		FOR invoiceid, countryid, is_rectifying_inv, is_oss_inv IN 
			SELECT inv.id, inv.tax_country_id, inv.is_rectifying, inv.is_oss
			FROM invoices_invoice inv
			WHERE inv.seller_id = sellerid
			AND inv.status_id = 'revised'
			AND inv.invoice_category_id = 'sales'
			AND inv.expedition_date >= '2025-07-01'
			AND inv.serial_code_id IS NULL
			AND inv.serial_number IS NULL
			ORDER BY inv.expedition_date ASC
			LIMIT 1000
    	LOOP
			qty_invoices_seller := qty_invoices_seller + 1;
			qty_invoices := qty_invoices + 1;
			if is_rectifying_inv IS NULL then
				is_rectifying_inv := false;
			end if;
			if is_oss_inv IS NULL then
				is_oss_inv := false;
			end if;
			if is_oss_inv then
				countryid := 'ES';
			end if;

			FOR serialid IN 
				SELECT s.id
				FROM invoices_serialinvoice s
				WHERE s.seller_id = sellerid 
				AND s.year = year_serie
				AND s.country_id = countryid
				AND s.is_rectifying = is_rectifying_inv
				AND s.invoice_category_id = 'sales'
				AND s.serial_code IN ('INV-SAL', 'RECT-INV-SAL')
				LIMIT 1
			LOOP
				-- Obtener el número de serie asociado al serial_code
				SELECT COALESCE(MAX(inv.serial_number), 0) + 1 -- Si es nulo asignarle 0
				INTO serialnumber
				FROM invoices_invoice inv
				WHERE inv.seller_id = sellerid
				AND inv.serial_code_id = serialid
				LIMIT 1;

				-- Actualizar el campo serial_code y serial_number de la factura
				UPDATE invoices_invoice
				SET serial_code_id = serialid, serial_number = serialnumber
				WHERE id = invoiceid;
			END LOOP;

    	END LOOP;
		-- PRINTEAR: SE HAN ACTUALIZADO X CANTIDAD DE REGISTROS
		RAISE NOTICE 'Seller ID: %, Updated invoices: %', sellerid, qty_invoices_seller;		
    END LOOP;
	RAISE NOTICE 'Se han actualizado un total de % facturas.', qty_invoices;
END $$;