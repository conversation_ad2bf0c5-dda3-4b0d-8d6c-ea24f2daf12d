log:
  level: INFO

entryPoints:
  web:
    # http
    address: ":80"
    http:
      # https://docs.traefik.io/routing/entrypoints/#entrypoint
      redirections:
        entryPoint:
          to: web-secure

  web-secure:
    # https
    address: ":443"

  flower:
    address: ":5556"
    http:
      tls:
        certResolver: letsencrypt

certificatesResolvers:
  letsencrypt:
    # https://docs.traefik.io/master/https/acme/#lets-encrypt
    acme:
      email: "<EMAIL>"
      storage: /etc/traefik/acme/acme.json
      # https://docs.traefik.io/master/https/acme/#httpchallenge
      httpChallenge:
        entryPoint: web

http:
  routers:

    web-secure-router:
      rule: "Host(`dev.muaytax.com`)"
      # rule: "Host(`app.muaytax.com`,`dev.muaytax.com`,`demo-app.muaytax.com`,`demo.muaytax.com`)"
      entryPoints:
        - web-secure
      middlewares:
        - csrf
      service: nginx
      tls:
        # https://docs.traefik.io/master/routing/routers/#certresolver
        certResolver: letsencrypt
      priority: 20  # El numero mas alto se evalua primero

    flower-router:
      rule: "Host(`dev.muaytax.com`)" # Puedes usar un subdominio diferente o un path con `PathPrefix`
      entryPoints:
        - flower
      service: flower
      tls:
        certResolver: letsencrypt
      priority: 10  # El numero mas alto se evalua primero



  middlewares:
    csrf:
      # https://docs.traefik.io/master/middlewares/headers/#hostsproxyheaders
      # https://docs.djangoproject.com/en/dev/ref/csrf/#ajax
      headers:
        hostsProxyHeaders: ["X-CSRFToken"]

  services:
    nginx:
      loadBalancer:
        servers:
          - url: http://nginx:80

    flower:
      loadBalancer:
        servers:
          - url: http://flower:5555 # Asumiendo que Flower está corriendo en este puerto dentro del contenedor

providers:
  # https://docs.traefik.io/master/providers/file/
  file:
    filename: /etc/traefik/traefik.yml
    watch: true
