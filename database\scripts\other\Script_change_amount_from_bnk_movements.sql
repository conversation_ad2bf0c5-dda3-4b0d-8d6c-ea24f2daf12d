--NUMERO TOTAL DE REGISTROS AFECTADOS POR LA CONSULTA
SELECT
-- 	sel.name,
-- 	bmv.concept,
-- 	bmv.amount_euros,
-- 	bmv.amount,
-- 	bnk.bank_name
count(bmv.id)
FROM banks_movement bmv
INNER JOIN banks_bank bnk ON bnk.id = bmv.bank_id
INNER JOIN sellers_seller sel ON sel.id = bnk.bank_seller_id
WHERE sel.id = 66
AND bnk.bank_name like 'AMEX ERROR%'

--------------------------------UPDATE QUERY-------------------------------------------

UPDATE banks_movement bmv
SET 
    amount = amount * (-1),
    amount_euros = amount_euros * (-1)
FROM banks_bank bnk
INNER JOIN sellers_seller sel ON sel.id = bnk.bank_seller_id
WHERE 
    bmv.bank_id = bnk.id
    AND sel.id = 66
    AND bnk.bank_name like 'AMEX ERROR%';
