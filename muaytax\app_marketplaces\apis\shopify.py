import json
import time, os, requests, datetime
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from muaytax.app_marketplaces.apis.utils import encode_state
from muaytax.app_marketplaces.register import MarketplaceRegistry
from muaytax.app_marketplaces.models.order import MarketplaceOrder

ORDER_GRAPHQL_QUERY = """
    order(id: "{order_id}") {{
        id
        name
        createdAt
        currencyCode
        currentShippingPriceSet {{
            presentmentMoney {{ amount }} 
            shopMoney {{ amount }}
        }}
        currentTotalPriceSet {{
            presentmentMoney {{ amount currencyCode }}
            shopMoney {{ amount }}
        }}
        originalTotalPriceSet {{
            presentmentMoney {{ amount currencyCode }}
            shopMoney {{ amount }}
        }}
        discountCodes
        taxesIncluded
        taxLines {{
            rate
            title
            priceSet {{
                presentmentMoney {{ amount }}
                shopMoney {{ amount }}
            }}
        }}
        displayFinancialStatus
        displayFulfillmentStatus
        billingAddress {{name firstName lastName company address1 city zip province country countryCodeV2 }}
        shippingAddress {{ countryCodeV2 }}
        shippingLines(first: 40) {{
            edges {{
                node {{
                    id
                    code
                    taxLines {{
                        rate
                        title
                        priceSet {{
                            presentmentMoney {{ amount }}
                            shopMoney {{ amount }}
                        }}
                    }}
                    discountedPriceSet {{
                        presentmentMoney {{ amount }}
                        shopMoney {{ amount }}
                    }}
                }}
            }}
        }}
        fulfillmentOrders(first: 50) {{
            edges {{
                node {{
                    id
                    status
                    deliveryMethod {{
                        methodType
                        serviceCode
                        presentedName
                    }}
                    assignedLocation {{
                        location {{ id }}
                    }}
                    lineItems(first: 200) {{
                        edges {{
                            node {{
                                lineItem {{
                                    id
                                    name
                                    title
                                    quantity
                                    taxable
                                    taxLines {{
                                        rate
                                        title
                                        priceSet {{
                                            presentmentMoney {{ amount }}
                                            shopMoney {{ amount }}
                                        }}
                                    }}
                                    discountedTotalSet (withCodeDiscounts: true) {{
                                        presentmentMoney {{ amount }}
                                        shopMoney {{ amount }}
                                    }}
                                    discountedUnitPriceAfterAllDiscountsSet {{
                                        presentmentMoney {{ amount }}
                                        shopMoney {{ amount }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                    fulfillments (first: 50) {{
                        edges {{
                            node {{  id }}
                        }}
                    }}
                }}
            }}
        }}
        refunds {{
            createdAt
            refundLineItems(first: 20) {{
                edges {{
                    node {{
                        id
                        lineItem {{ id }}
                        priceSet {{
                            presentmentMoney {{ amount }}
                            shopMoney {{ amount }}
                        }}
                        quantity
                        restockType
                        subtotalSet {{
                            presentmentMoney {{ amount }}
                            shopMoney {{ amount }}
                        }}
                        totalTaxSet {{
                            presentmentMoney {{ amount }}
                            shopMoney {{ amount }}
                        }}
                    }}
                }}
            }}
            refundShippingLines(first:20) {{
                edges {{
                    node {{
                        id
                        shippingLine {{
                            id
                            title
                            taxLines {{
                                rate
                                title
                            }}
                        }}
                        subtotalAmountSet {{
                            presentmentMoney {{ amount }}
                            shopMoney {{ amount }}
                        }}
                        taxAmountSet {{
                            presentmentMoney {{ amount }}
                            shopMoney {{ amount }}
                        }}
                    }}
                }}
            }}
        }}
    }}
    """

class ShopifyApiService:
    def __init__(self, shortname=None, access_token=None):
        self.client_id = os.environ.get('SHOPIFY_CLIENT_ID')
        self.client_secret = os.environ.get('SHOPIFY_CLIENT_SECRET')
        self.redirect_uri = os.environ.get('SHOPIFY_REDIRECT_URI')
        self.shortname = shortname
        self.access_token = access_token

        self.graphql_endpoint = None
        self.headers = None

    def set_url_and_headers(self, shop_name: str) -> None:
        """
        Configura la URL y los encabezados para las solicitudes a la API de Shopify.
        Esta función se utiliza para establecer el punto final de GraphQL y los encabezados necesarios.
        """
        if not self.access_token:
            raise ValueError("El token de acceso es obligatorio.")
        
        if not shop_name:
            raise ValueError("El nombre de la tienda es obligatorio.")
        
        self.graphql_endpoint = f"https://{shop_name}/admin/api/2025-01/graphql.json"
        self.headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": self.access_token,
        }

    def get_shopify_auth_url(self, shop_name: str, shop_title: str) -> str:
        """
        Genera la URL de autorización para Shopify.
        Esta URL se utiliza para redirigir al usuario a la página de autorización de Shopify.
        """
        nounce = encode_state(self.shortname, shop_title) # type: ignore
        auth_url = (
            f"https://{shop_name}/admin/oauth/authorize"
            f"?client_id={self.client_id}"
            f"&scope=read_products,write_products,read_orders,read_customers,read_locations,read_assigned_fulfillment_orders,read_merchant_managed_fulfillment_orders,read_shipping"
            f"&redirect_uri={self.redirect_uri}"
            f"&state={nounce}"
        )

        return auth_url
    
    def get_shopify_access_token(self, shop_name: str, code: str) -> str:
        """
        Cambia el código de autorización por un token de acceso. Pero devuelve todo el JSON respuesta.
        Esto es útil para obtener información adicional sobre el token de acceso.
        Este token se utiliza para autenticar las solicitudes a la API de Shopify.
        """
        token_url = f"https://{shop_name}/admin/oauth/access_token"

        payload = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'code': code
        }

        response = requests.post(token_url, json=payload, verify=os.environ.get('PATH_VERIFY_CA'))

        if response.status_code == 200:
            print("Shopify Access token obtained successfully.")
            return response.json().get('access_token')
        
        raise ValueError(
            f"Failed to obtain access token from Shopify. Status code: {response.status_code}, "
            f"Response: {response.text}"
        )
    
    def get_shopify_locations(self, shop_name: str) -> dict:
        """
        Obtiene las ubicaciones (warehouses) de Shopify.
        Este método utiliza el token de acceso para autenticar la solicitud a la API de Shopify.
        """
        if not self.access_token:
            raise ValueError("El token de acceso es obligatorio.")
        
        if not shop_name:
            raise ValueError("El nombre de la tienda es obligatorio.")
        
        locations_url = f"https://{shop_name}/admin/api/2025-01/locations.json"
        headers = {
            'X-Shopify-Access-Token': self.access_token,
            'Content-Type': 'application/json'
        }

        response = requests.get(locations_url, headers=headers, verify=os.environ.get('PATH_VERIFY_CA'))

        if response.status_code == 200:
            print("Shopify locations obtained successfully.")
            return response.json().get('locations')
        
        raise ValueError(
            f"Failed to obtain locations from Shopify. Status code: {response.status_code}, "
            f"Response: {response.text}"
        )

    def register_shopify_webhooks(self, shop_name: str):
        webhook_url = os.environ.get('SHOPIFY_API_WEBHOOK_URL')
        # webhook_url = "https://eef7-178-237-238-71.ngrok-free.app/marketplaces/shopify/api/webhooks/"
        
        webhook_topics = [
            ("ORDERS_PAID", ["id", "admin_graphql_api_id"]),
            ("REFUNDS_CREATE", ["id", "order_id"]),
            # ("FULFILLMENTS_CREATE", ["id"])
        ]

        try:
            for topic, fields in webhook_topics:
                webhook = self._post_graphql_webhook(
                    topic=topic,
                    shop_name=shop_name,
                    webhook_url=webhook_url,
                    include_fields=fields
                )

                print(f"Shopify webhook {topic} registrado: {webhook['id']}.")
        except Exception as e:
            print(f"Error al registrar webhooks de Shopify: {e}")
            # Aquí podrías manejar el error de manera más específica o registrar el error en un sistema de logs

    def _post_graphql_webhook(self, topic, shop_name: str,  webhook_url: str, include_fields=None) -> dict:
        self.set_url_and_headers(shop_name)
        if not self.graphql_endpoint:
            raise Exception(f'Error al obtener la url de Graphql')
        
        graphql_query = """
            mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookUrl: URL!, $fields: [String!]) {
                webhookSubscriptionCreate(
                    topic: $topic,
                    webhookSubscription: {
                        callbackUrl: $webhookUrl,
                        format: JSON,
                        includeFields: $fields
                    }
                ) {
                    webhookSubscription {
                    id
                    topic
                    endpoint {
                        __typename
                        ... on WebhookHttpEndpoint {
                            callbackUrl
                        }
                    }
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
            """
        
        variables = {
            "topic": topic,
            "webhookUrl": webhook_url,
            "fields": include_fields or []
        }

        verify_ssl = os.environ.get('PATH_VERIFY_CA', True)
        response = requests.post(
            self.graphql_endpoint,
            headers=self.headers,
            json={"query": graphql_query, "variables": variables},
            verify=verify_ssl
        )

        result = response.json()
        if "errors" in result or result.get("data", {}).get("webhookSubscriptionCreate", {}).get("userErrors"):
            raise Exception(f"Error al crear webhook para {topic}: {json.dumps(result, indent=2)}")

        return result["data"]["webhookSubscriptionCreate"]["webhookSubscription"] 

    def delete_all_shopify_webhooks(self, shop_name: str):
        """
        Elimina todos los webhooks registrados en Shopify para la tienda especificada.
        Esta función utiliza el token de acceso para autenticar la solicitud a la API de Shopify.
        """
        if not self.access_token:
            raise ValueError("El token de acceso es obligatorio.")
        
        if not shop_name:
            raise ValueError("El nombre de la tienda es obligatorio.")
        
        headers = {
            'X-Shopify-Access-Token': self.access_token,
            'Content-Type': 'application/json'
        }
        veryfy_ssl = os.environ.get('PATH_VERIFY_CA', True)

        # Obtener todos los webhooks
        response = requests.get(
            f"https://{shop_name}/admin/api/2025-01/webhooks.json",
            headers=headers,
            verify=veryfy_ssl
        )

        if response.status_code != 200:
            raise ValueError(
                f"Fallo al obtener los webhooks de Shopify. "
                f"Status code: {response.status_code}, "
                f"Response: {response.text}"
            )
        
        webhooks = response.json().get('webhooks', [])
        
        # Eliminar cada webhook
        for webhook in webhooks:
            webhook_id = webhook['id']
            delete_response = requests.delete(
                f"https://{shop_name}/admin/api/2025-01/webhooks/{webhook_id}.json",
                headers=headers,
                verify=veryfy_ssl
            )
            
            if delete_response.status_code != 200:
                raise ValueError(
                    f"Fallo al eliminar el webhook con ID {webhook_id}. "
                    f"Status code: {delete_response.status_code}, Response: {delete_response.text}"
                )
        
        print("All Shopify webhooks deleted successfully.")

    def get_marketplace_orders(self, shop_name: str, **kwargs) -> list:
        """
        Obtiene los pedidos de Shopify usando GraphQL con paginación.
        Esta versión divide la obtención en dos fases: 
        1. Obtiene las IDs y metadatos de las órdenes.
        2. Realiza consultas por lote para obtener el detalle completo.
        """
        
        if not self.access_token:
            raise ValueError("El token de acceso es obligatorio.")
        if not shop_name:
            raise ValueError("El nombre de la tienda es obligatorio.")
        
        start_date = kwargs.get('start_date')
        if not start_date:
            raise ValueError("La fecha de inicio es obligatoria.")
        if isinstance(start_date, (datetime.date, datetime.datetime)):
            start_date = start_date.isoformat()

        financial_status = kwargs.get('financial_status', 'paid,refunded,partially_refunded')
        # fulfillment_status = kwargs.get('fulfillment_status', 'fulfilled')

        self.set_url_and_headers(shop_name)

        imported_orders = self.get_orders_in_bulk(start_date, financial_status)

        # Se obtiene la lista de IDs de órdenes existentes para evitar hacer consultas de ordenes existentes
        existing_ids = set(
            MarketplaceOrder.objects.filter(
                marketplace__shopname=shop_name,
                seller__shortname=self.shortname
            ).values_list('order_id', flat=True)
        )
        order_ids = [order for order in imported_orders if order not in existing_ids]

        #  Segunda fase: Obtener detalles completos de las órdenes
        orders = self.fetch_order_details_by_alias(order_ids)

        return orders

    def get_orders_in_bulk(self, start_date: str, financial_status: str) -> list:
        """
        Inicia una operación de consulta por lotes para obtener pedidos desde una fecha específica.
        Esta función utiliza la API GraphQL de Shopify para iniciar una operación de consulta por lotes.
        """
        # 1. Iniciar la operación de consulta por lotes
        self.start_bulk_orders_query(start_date, financial_status)

        # 2. Esperar a que la operación de consulta por lotes finalice
        download_url = self.wait_for_bulk_completion()

        # 3. Descargar los IDs
        return self.download_bulk_ids(download_url)

    def start_bulk_orders_query(self, start_date, financial_status) -> dict:
        """ 
        Inicia una operación de consulta por lotes para obtener los IDs de las órdenes creadas desde una fecha específica.
        Esta función construye una consulta GraphQL para obtener los IDs de las órdenes
        """
        orders_query = f"""
        {{
            orders(query: "created_at:>={start_date} financial_status:{financial_status}") {{
                edges {{
                    node {{
                        id
                    }}
                }}
            }}
        }}
        """

        mutation = {
            "query": f"""
                mutation {{
                    bulkOperationRunQuery(
                        query: {json.dumps(orders_query)}
                    ) {{
                        bulkOperation {{ id status }}
                        userErrors {{ field message }}
                    }}
                }}
            """
        }
        verify_ssl = os.environ.get('PATH_VERIFY_CA', True)

        if not self.graphql_endpoint:
            raise ValueError("GraphQL endpoint no está configurado. Por favor, configúralo primero.")
        
        response = requests.post(
            self.graphql_endpoint,
            headers=self.headers,
            json=mutation,
            verify=verify_ssl
        )

        if response.status_code != 200:
            raise ValueError(f"Error al iniciar la operación de consulta por lotes: {response.text}")
        
        data = response.json()

        user_errors = data.get("data", {}).get("bulkOperationRunQuery", {}).get("userErrors", [])
        if user_errors:
            raise ValueError(f"La petición de consulta por lotes falló: {user_errors}")

        return data
    
    def wait_for_bulk_completion(self, timeout=180, interval=5) -> str:
        """
        Espera a que la operación de consulta por lotes se complete.
        Esta función consulta periódicamente el estado de la operación de consulta por lotes
        hasta que se complete o falle, o hasta que se alcance el tiempo de espera.
        """
        start_time = time.time()
        query = {
            "query": """
            {
                currentBulkOperation {
                    id
                    status
                    url
                    errorCode
                    createdAt
                    completedAt
                    objectCount
                    fileSize
                }
            }
            """
        }
        verify_ssl = os.environ.get('PATH_VERIFY_CA', True)
        if not self.graphql_endpoint:
            raise ValueError("GraphQL endpoint no está configurado. Por favor, configúralo primero.")
        
        while True:
            response = requests.post(
                self.graphql_endpoint,
                headers=self.headers,
                json=query,
                verify=verify_ssl
            )
            data = response.json().get("data", {}).get("currentBulkOperation", {})
            status = data.get("status")
            if status == "COMPLETED":
                return data.get("url")
            if status in ("FAILED", "CANCELED"):
                raise RuntimeError(f"Bulk operation failed: {status}")
            if time.time() - start_time > timeout:
                raise TimeoutError("Bulk operation timeout.")
            time.sleep(interval)

    def download_bulk_ids(self, download_url: str) -> list:
        """
        Descarga los IDs de las órdenes desde la URL proporcionada por la operación de consulta por lotes.
        Esta función realiza una solicitud GET a la URL de descarga y procesa el archivo CSV resultante.
        """
        response = requests.get(
            download_url,
            verify=os.environ.get('PATH_VERIFY_CA')
        )

        if response.status_code != 200:
            raise ValueError(f"Error al descargar los IDs: {response.text}")

        # Procesar el CSV
        lines = response.text.strip().splitlines()
        return [json.loads(line).get('id') for line in lines if line.strip()]

    def fetch_order_details_by_alias(self, order_ids: list[str], initial_batch_size: int = 2) -> list[dict]:
        """
        Recupera detalles completos de órdenes de Shopify en lotes usando GraphQL y alias.
        """
        detailed_orders = []
        i = 0

        while i < len(order_ids):
            batch_size = initial_batch_size
            while batch_size > 0:
                batch = order_ids[i:i + batch_size]
                query_body = self._build_batched_order_query(batch)

                try:
                    result = self._post_graphql(query_body)
                    data = result.get("data", {})
                    errors = result.get("errors", [])
                    if any("MAX_COST_EXCEEDED" in err.get("extensions", "").get("code", "") for err in errors):
                            print(f"[Aviso] Coste de query excedido para batch_size={batch_size}. Reduciendo...")
                            batch_size -= 1
                            time.sleep(0.5)
                            continue

                    for key in data:
                        order_data = data[key]
                        if order_data:
                            detailed_orders.append(order_data)

                    i += batch_size  # avanzar solo si éxito
                    break  # salir del loop interno (no reducir más el batch)
                except Exception as e:
                    print(f"[Error] Otro error al consultar órdenes: {e}")
                    break

            time.sleep(0.3)

        return detailed_orders

    def fetch_single_order_details(self, order_id: str) -> dict:
        """
        Recupera los detalles de una orden específica de Shopify usando GraphQL.
        Esta función construye una consulta GraphQL para obtener los detalles de la orden.
        """
        # for testign raise a bad request 
        query = f'query {{\n{ORDER_GRAPHQL_QUERY.format(order_id=order_id)}\n}}'
        result = self._post_graphql(query)

        data = result.get("data", {}).get("order")
        if not data:
            raise ValueError(f"No se encontraron datos para la orden {order_id}")

        return data

    def _build_batched_order_query(self, order_ids: list[str]) -> str:
        """
        Construye una consulta GraphQL para obtener detalles de órdenes en lotes.
        Utiliza alias para cada orden para evitar conflictos en la consulta.
        """
        fragments = []
        for idx, oid in enumerate(order_ids):
            alias = f"order{idx}"
            fragments.append(f"""
                {alias}: {ORDER_GRAPHQL_QUERY.format(order_id=oid)}
            """)

        return f"query {{\n{''.join(fragments)}\n}}"
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_fixed(2),
        retry=retry_if_exception_type((requests.exceptions.RequestException,))
    )
    def _post_graphql(self, query):
        """
        Realiza una consulta GraphQL para obtener los detalles de una orden específica.
        Esta función maneja la solicitud y devuelve el resultado.
        """
        if not self.graphql_endpoint:
            raise ValueError("GraphQL endpoint no está configurado. Por favor, configúralo primero.")
        
        verify_ssl = os.environ.get('PATH_VERIFY_CA', True)

        response = requests.post(
            self.graphql_endpoint,
            headers=self.headers,
            json={"query": query},
            verify=verify_ssl
        )

        response.raise_for_status()

        return response.json()

MarketplaceRegistry.register('shopify', service=ShopifyApiService)