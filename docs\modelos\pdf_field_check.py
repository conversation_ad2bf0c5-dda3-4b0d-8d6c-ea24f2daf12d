import os

from pypdf import PdfReader

# ➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖
PDF_NAME = "FR-MANDAT-REPRESENTATION-VAT-289A-MUAYFRANCE"
# ➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖➖

################# CODE #####################
current_dir = os.path.dirname(os.path.abspath(__file__))
pdf_path = os.path.join(current_dir, "../../muaytax/static/assets/pdf", f"{PDF_NAME}.pdf")

try:
    reader = PdfReader(pdf_path)

    print("\n" + "="*50)
    print("📄 ANÁLISIS DE CAMPOS DEL PDF")
    print("="*50)

    # Campos de texto
    text_fields = reader.get_form_text_fields()
    print("\n📝 CAMPOS DE TEXTO DETECTADOS:")
    print("-"*30)
    if text_fields:
        for field in text_fields:
            print(f"  • {field}")
    else:
        print("  No se encontraron campos de texto")

    # Campos de firma
    print("\n✍️ CAMPOS DE FIRMA DETECTADOS:")
    print("-"*30)
    signature_found = False
    for page in reader.pages:
        if '/Annots' in page:
            for annot in page['/Annots']:
                obj = annot.get_object()
                if obj.get('/FT') == '/Sig' or '/Sig' in str(obj):
                    field_name = obj.get('/T', 'Sin nombre')
                    print(f"  • {field_name}")
                    if field_name == 'client_signature':
                        signature_found = True
                        print("    ✓ ¡Campo client_signature encontrado!")

    # Todos los campos
    print("\n📋 RESUMEN DE TODOS LOS CAMPOS:")
    print("-"*30)
    if reader.get_fields():
        for field_name, field_value in reader.get_fields().items():
            print(f"  • {field_name}: {field_value}")
            if field_name == 'client_signature' and not signature_found:
                print("    ✓ ¡Campo client_signature encontrado!")
    else:
        print("  No se encontraron campos en el formulario")

    print("\n" + "="*50)

except FileNotFoundError:
    print("\n❌ ERROR: El archivo no se encontró")
    print(f"   Ruta intentada: {pdf_path}")
except Exception as e:
    print("\n❌ ERROR: Fallo al procesar el PDF")
    print(f"   Detalles: {str(e)}")
