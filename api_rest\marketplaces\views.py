from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.generics import ListAPIView
from django.utils.translation import gettext as _

from muaytax.app_importers.models import MarketplaceOrderImporter, MarketplaceInvoiceGenerator
from muaytax.app_importers.serializers.marketplace import MarketplaceOrderImporterSerializer

class GetMarketplaceOrderImportsView(ListAPIView):
    serializer_class = MarketplaceOrderImporterSerializer
    permission_classes = [IsAuthenticated]
    # permission_classes = []  # Descomentar si no se requiere autenticación. Para pruebas.

    def get_queryset(self):
        marketplace_id = self.kwargs.get('pk')
        return MarketplaceOrderImporter.objects.filter(
            marketplace__id=marketplace_id
        ).order_by('-created_at')
    
class GetMarketplaceOrderActivitiesView(APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        marketplace_id = self.kwargs.get('pk')

        imports = MarketplaceOrderImporter.objects.filter(
            marketplace_id=marketplace_id
        ).order_by('-created_at')[:5]

        generations = MarketplaceInvoiceGenerator.objects.filter(
            marketplace_id=marketplace_id
        ).order_by('-created_at')[:5]

        combined = sorted(
            list(imports) + list(generations),
            key=lambda x: x.created_at,
            reverse=True
        )

        data = [
            {
                "id": obj.pk,
                "status": obj.status,
                "created_at": obj.created_at,
                "start_date": obj.start_date if isinstance(obj, MarketplaceOrderImporter) and obj.start_date else None,
                "type": "import" if isinstance(obj, MarketplaceOrderImporter) else "generation",
                "total_success": obj.total_orders_created if isinstance(obj, MarketplaceOrderImporter) else obj.total_invoices_created,
                "total_failed": obj.total_orders_failed if isinstance(obj, MarketplaceOrderImporter) else obj.total_invoices_failed,
            }
            for obj in combined
        ]

        return Response(data)