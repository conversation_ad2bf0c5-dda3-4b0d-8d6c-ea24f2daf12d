"""
WSGI config for muaytax project.
"""
import os
import sys
from pathlib import Path

from django.core.wsgi import get_wsgi_application

# Path to the root directory of the project
ROOT_DIR = Path(__file__).resolve(strict=True).parent.parent
sys.path.append(str(ROOT_DIR) + '/muaytax')

# Get Enviroment
environment = os.environ.get('DJANGO_ENV', 'local')

# Set DJANGO_SETTINGS_MODULE
if environment == 'local':
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
elif environment == 'dev':
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.develop")
elif environment == 'prod':
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.production")

# Get Application
application = get_wsgi_application()

# Apply WSGI middleware here.
# from helloworld.wsgi import HelloWorldApplication
# application = HelloWorldApplication(application)
