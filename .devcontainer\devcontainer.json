{"name": "<PERSON><PERSON><PERSON> Dev Container", "dockerComposeFile": ["../light-local.yml"], "service": "django", "workspaceFolder": "/app", "remoteUser": "root", "shutdownAction": "stopCompose", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"version": "latest", "moby": false}, "ghcr.io/devcontainers/features/common-utils:2": {"installZsh": "false", "configureZshAsDefaultShell": "false"}}, "mounts": ["source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind", "source=${localEnv:USERPROFILE}\\.ssh,target=/root/.ssh,type=bind,consistency=cached"], "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "ms-python.pylint", "ms-python.flake8", "ms-python.black-formatter", "ms-python.mypy-type-checker", "njpwerner.autodocstring", "batisteo.vscode-django", "davidanson.vscode-markdownlint", "tamasfe.even-better-toml", "usernamehw.errorlens"], "settings": {"python.analysis.typeCheckingMode": "basic", "python.analysis.diagnosticSeverityOverrides": {"reportIndentationError": "error"}, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "editor.formatOnSave": true, "editor.rulers": [120], "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}}}, "postCreateCommand": "pip install -U pylint pylint-django black flake8 mypy django-stubs djangorestframework-stubs && mkdir -p ~/.ssh && chmod 700 ~/.ssh && cp /app/.devcontainer/ssh_config ~/.ssh/config && chmod 600 ~/.ssh/config && if [ -f ~/.ssh/known_hosts ]; then chmod 644 ~/.ssh/known_hosts; fi && git config --global --add safe.directory /app"}