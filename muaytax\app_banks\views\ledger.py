import csv
import io
import json
from decimal import Decimal

from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.db import models
from django.db.models import Sum, Q
from django.db.models.functions import Coalesce
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.utils.dateparse import parse_date
from django.views.generic import TemplateView, View

from muaytax.app_banks.models import Entry
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models import AccountingAccount


def get_account_movements(account_code, start_date, end_date, seller_pk, filters=None):
    """
    Obtiene los movimientos de una cuenta contable en un rango de fechas,
    aplicando filtros adicionales si se proporcionan.

    Args:
        filters: Diccionario con filtros adicionales con la siguiente estructura:
        {
            'text': {'type': '+' o '-', 'value': 'texto', 'column': 'columna'},
            'amount': {'type': '>' o '<', 'value': 'importe', 'column': 'columna'}
        }
    """
    # Consulta base
    query = Entry.objects.filter(
        entry_accounting_account=account_code,
        entry_date__range=[start_date, end_date],
        entry_seller_id=seller_pk,
    )

    # Aplicar filtros si existen
    if filters:
        # Filtro de texto
        if filters.get('text', {}).get('type') and filters.get('text', {}).get('value'):
            text_type = filters['text']['type']
            text_value = filters['text']['value']
            text_column = filters['text']['column']

            # Construir condición según columna
            if text_column == 'all':
                # Buscar en todas las columnas de texto
                if text_type == '+':  # Contiene
                    text_filter = (
                        Q(entry_concept__icontains=text_value) |
                        Q(entry_document__icontains=text_value) |
                        Q(entry_num__icontains=text_value)
                    )
                else:  # No contiene
                    text_filter = (
                        ~Q(entry_concept__icontains=text_value) &
                        ~Q(entry_document__icontains=text_value) &
                        ~Q(entry_num__icontains=text_value)
                    )
            else:
                # Mapeo de nombres de columnas a campos del modelo
                column_mapping = {
                    'date': 'entry_date__icontains',
                    'num': 'entry_num__icontains',
                    'document': 'entry_document__icontains',
                    'concept': 'entry_concept__icontains',
                }

                field_name = column_mapping.get(text_column)
                if field_name:
                    if text_type == '+':  # Contiene
                        text_filter = Q(**{field_name: text_value})
                    else:  # No contiene
                        text_filter = ~Q(**{field_name: text_value})
                else:
                    text_filter = Q()  # Filtro vacío si la columna no es válida

            query = query.filter(text_filter)

        # Filtro de cantidad
        if filters.get('amount', {}).get('type') and filters.get('amount', {}).get('value', '').strip():
            amount_type = filters['amount']['type']
            try:
                amount_value = Decimal(filters['amount']['value'])
                amount_column = filters['amount']['column']

                # Mapeo de nombres de columnas a campos del modelo
                column_mapping = {
                    'debit': 'entry_debit',
                    'credit': 'entry_credit',
                    # Para saldo, necesitamos una lógica especial
                }

                # Para saldo, tendríamos que filtrar después de calcular el saldo en memoria
                if amount_column != 'balance':
                    field_name = column_mapping.get(amount_column)
                    if field_name:
                        if amount_type == '>':  # Mayor que
                            amount_filter = Q(**{f"{field_name}__gt": amount_value})
                        else:  # Menor que
                            amount_filter = Q(**{f"{field_name}__lt": amount_value})

                        query = query.filter(amount_filter)
            except (ValueError, TypeError):
                # Si hay un error al convertir el valor a Decimal, ignoramos el filtro
                pass

    return query.order_by("entry_date", "entry_num")


def get_previous_balance(account_code, start_date, seller_pk):
    """
    Calcula el saldo anterior a la fecha de inicio para una cuenta contable.
    """
    entries = Entry.objects.filter(
        entry_accounting_account=account_code,
        entry_date__lt=start_date,
        entry_seller_id=seller_pk,
    )

    result = entries.aggregate(
        total_debit=models.Sum("entry_debit") or 0,
        total_credit=models.Sum("entry_credit") or 0,
    )

    total_debit = result["total_debit"] or Decimal("0")
    total_credit = result["total_credit"] or Decimal("0")

    return total_debit - total_credit


class LedgerListView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    """
    Vista para mostrar la página principal del Libro Mayor.
    Muestra solo las cuentas contables que el vendedor ha usado.
    """

    template_name = "banks/ledger_list.html"
    permission_required = "app_banks.view_entry"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=kwargs["shortname"])
        context["seller"] = seller

        # Obtener las cuentas únicas usadas por el vendedor
        used_accounts = (
            Entry.objects.filter(
                entry_seller=seller, entry_accounting_account__isnull=False
            )
            .values_list("entry_accounting_account", flat=True)
            .distinct()
        )

        # Obtener los detalles de las cuentas usadas
        context["accounts"] = AccountingAccount.objects.filter(
            code__in=used_accounts
        ).order_by("code")

        return context


class LedgerDataView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """
    Vista optimizada para proporcionar datos al DataTable de movimientos contables.
    Implementa paginación server-side y optimización de consultas.
    """

    permission_required = "app_banks.view_entry"

    def post(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=kwargs["shortname"])

        # Obtener y validar parámetros de paginación
        draw = int(request.POST.get("draw", 1))
        # Aunque actualmente no aplicamos la paginación en el servidor,
        # conservamos estas variables para uso futuro
        # start = int(request.POST.get("start", 0))
        # length = int(request.POST.get("length", 50))

        # Permitir recibir varias cuentas (array o string)
        account = (
            request.POST.getlist("account[]")
            or request.POST.getlist("account")
            or request.POST.get("account")
        )
        if isinstance(account, str):
            account = [account]
        if not account or not isinstance(account, list) or not any(account):
            return JsonResponse(
                {
                    "error": "Faltan parámetros requeridos o son inválidos",
                    "draw": draw,
                    "recordsTotal": 0,
                    "recordsFiltered": 0,
                    "data": [],
                },
                status=400,
            )

        start_date_str = request.POST.get("start_date")
        end_date_str = request.POST.get("end_date")

        # Validar fechas
        param_start_date_ok = bool(start_date_str and start_date_str.strip())
        param_end_date_ok = bool(end_date_str and end_date_str.strip())
        if not all([param_start_date_ok, param_end_date_ok]):
            return JsonResponse(
                {
                    "error": "Faltan parámetros requeridos o son inválidos",
                    "draw": draw,
                    "recordsTotal": 0,
                    "recordsFiltered": 0,
                    "data": [],
                },
                status=400,
            )

        # Convertir fechas
        try:

            def convert_date_format(date_str):
                if not date_str:
                    return None
                parts = date_str.split("/")
                if len(parts) != 3:
                    return None
                day, month, year = parts
                return f"{year}-{month}-{day}"

            start_date_iso = convert_date_format(start_date_str)
            end_date_iso = convert_date_format(end_date_str)
            if start_date_iso is None or end_date_iso is None:
                raise ValueError("Formato de fecha inválido")
            start_date = parse_date(start_date_iso)
            end_date = parse_date(end_date_iso)
            if not start_date or not end_date:
                raise ValueError("Fechas inválidas (nulas)")
        except ValueError:
            return JsonResponse(
                {
                    "error": "Formato de fecha inválido",
                    "draw": draw,
                    "recordsTotal": 0,
                    "recordsFiltered": 0,
                    "data": [],
                },
                status=400,
            )

        # Obtener parámetros de filtro
        filters = self._extract_filters_from_request(request)

        # Preparar la respuesta
        all_data = []
        grand_total_debit = Decimal("0")
        grand_total_credit = Decimal("0")
        grand_final_balance = Decimal("0")
        total_records = 0

        # Obtener información de cuentas primero para evitar N+1 queries
        try:
            account_info = {
                acc.code: f"{acc.code} - {acc.description}"
                for acc in AccountingAccount.objects.filter(code__in=account)
            }
        except Exception:
            account_info = {acc: acc for acc in account}

        # Procesar cada cuenta seleccionada
        for acc in account:
            if not acc:
                continue

            # Calcular saldo anterior
            previous_balance = get_previous_balance(acc, start_date, seller.pk)

            # Obtener movimientos con los filtros aplicados
            entries_qs = get_account_movements(acc, start_date, end_date, seller.pk, filters)

            # Para el filtro de balance, necesitamos procesar los datos en memoria
            if filters and filters.get('amount', {}).get('column') == 'balance' and filters.get('amount', {}).get('type') and filters.get('amount', {}).get('value', '').strip():
                try:
                    # Primero obtenemos todos los movimientos sin filtro de balance
                    all_entries = list(entries_qs)
                    filtered_entries = []
                    current_balance = previous_balance
                    amount_type = filters['amount']['type']
                    amount_value = Decimal(filters['amount']['value'])

                    # Calculamos el saldo acumulado y aplicamos el filtro
                    for entry in all_entries:
                        entry_debit = entry.entry_debit or Decimal("0")
                        entry_credit = entry.entry_credit or Decimal("0")
                        current_balance += entry_debit - entry_credit

                        # Aplicar filtro según tipo
                        if (amount_type == '>' and current_balance > amount_value) or \
                           (amount_type == '<' and current_balance < amount_value):
                            filtered_entries.append(entry)

                    # Usar los movimientos filtrados
                    entries = filtered_entries
                    total_entries = len(filtered_entries)

                    # Recalcular totales para los movimientos filtrados
                    total_debit = sum((entry.entry_debit or Decimal("0")) for entry in filtered_entries)
                    total_credit = sum((entry.entry_credit or Decimal("0")) for entry in filtered_entries)
                except (ValueError, TypeError):
                    # Si hay error en la conversión, usamos todos los movimientos
                    entries = list(entries_qs)
                    total_entries = len(entries)
                    # Calcular totales con suma en memoria
                    total_debit = sum((entry.entry_debit or Decimal("0")) for entry in entries)
                    total_credit = sum((entry.entry_credit or Decimal("0")) for entry in entries)
            else:
                # Calcular totales con agregación
                totals = entries_qs.aggregate(
                    total_debit=Coalesce(Sum("entry_debit"), Decimal("0")),
                    total_credit=Coalesce(Sum("entry_credit"), Decimal("0")),
                )
                total_debit = totals["total_debit"]
                total_credit = totals["total_credit"]

                # Paginación de los movimientos (implementada en memoria para mantener saldos acumulados)
                entries = list(entries_qs)
                total_entries = len(entries)

            final_balance = previous_balance + total_debit - total_credit

            # Actualizar totales globales
            grand_total_debit += total_debit
            grand_total_credit += total_credit
            grand_final_balance += final_balance

            total_records += total_entries

            # Calcular saldo acumulado (aún necesario para mostrar el balance en cada línea)
            current_balance = previous_balance
            account_data = []

            for entry in entries:
                entry_debit = entry.entry_debit or Decimal("0")
                entry_credit = entry.entry_credit or Decimal("0")
                current_balance += entry_debit - entry_credit

                account_data.append(
                    {
                        "account": acc,
                        "date": (
                            entry.entry_date.strftime("%d/%m/%Y")
                            if entry.entry_date
                            else ""
                        ),
                        "concept": entry.entry_concept or "",
                        "debit": float(entry_debit),
                        "credit": float(entry_credit),
                        "balance": float(current_balance),
                        "document": entry.entry_document or "",
                        "num": entry.entry_num or "",
                    }
                )

            # Añadir información de la cuenta al resultado
            acc_name = account_info.get(acc, acc)
            all_data.append(
                {
                    "account": acc,
                    "account_name": acc_name,
                    "previous_balance": float(previous_balance),
                    "total_debit": float(total_debit),
                    "total_credit": float(total_credit),
                    "final_balance": float(final_balance),
                    "entries": account_data,
                    "total_entries": total_entries,
                }
            )

        response = {
            "draw": draw,
            "recordsTotal": total_records,
            "recordsFiltered": total_records,
            "data": all_data,
            "grand_total_debit": float(grand_total_debit),
            "grand_total_credit": float(grand_total_credit),
            "grand_final_balance": float(grand_final_balance),
        }
        return JsonResponse(response)

    def _extract_filters_from_request(self, request):
        """
        Extrae y valida los parámetros de filtro de la solicitud.
        """
        filters = {}

        # Filtro de texto
        text_filter_type = request.POST.get("text_filter_type")
        text_filter_value = request.POST.get("text_filter_value")
        text_filter_column = request.POST.get("text_filter_column", "all")

        if text_filter_type and text_filter_value:
            filters["text"] = {
                "type": text_filter_type,
                "value": text_filter_value,
                "column": text_filter_column
            }

        # Filtro de cantidad
        amount_filter_type = request.POST.get("amount_filter_type")
        amount_filter_value = request.POST.get("amount_filter_value")
        amount_filter_column = request.POST.get("amount_filter_column", "debit")

        if amount_filter_type and amount_filter_value:
            filters["amount"] = {
                "type": amount_filter_type,
                "value": amount_filter_value,
                "column": amount_filter_column
            }

        return filters if filters else None


class LedgerExportView(LoginRequiredMixin, PermissionRequiredMixin, View):
    """
    Vista para exportar datos del Libro Mayor en CSV o PDF.
    """

    permission_required = "app_banks.view_entry"

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=kwargs["shortname"])

        # Obtener y validar parámetros
        # Soportar múltiples cuentas (pueden venir como array o como string separado por comas)
        account_param = request.GET.get("account")
        if not account_param:
            return JsonResponse({"error": "Faltan parámetros requeridos"}, status=400)

        # Convertir a lista si viene como string
        if isinstance(account_param, str):
            # Si es un string que parece un array en formato JSON, intentar parsearlo
            if account_param.startswith("[") and account_param.endswith("]"):
                try:
                    account = json.loads(account_param)
                except json.JSONDecodeError:
                    # Si falla el parsing JSON, verificar si es un string con comas
                    if "," in account_param:
                        account = [acc.strip() for acc in account_param.split(",")]
                    else:
                        account = [account_param]
            # Si es un string con comas
            elif "," in account_param:
                account = [acc.strip() for acc in account_param.split(",")]
            # Si es un código de cuenta único
            else:
                account = [account_param]
        else:
            account = account_param

        # Validar que haya al menos una cuenta
        if not account or not isinstance(account, list) or not any(account):
            return JsonResponse({"error": "Cuenta contable no válida"}, status=400)

        start_date_str = request.GET.get("start_date")  # Formato DD/MM/YYYY
        end_date_str = request.GET.get("end_date")  # Formato DD/MM/YYYY
        export_format = request.GET.get("format", "csv")

        # Validar que todos los parámetros necesarios estén presentes
        if not all([start_date_str, end_date_str]):
            return JsonResponse({"error": "Faltan parámetros requeridos"}, status=400)

        # Convertir fechas de DD/MM/YYYY a YYYY-MM-DD antes de parsear
        try:

            def convert_date_format(date_str):
                if not date_str:
                    return None
                parts = date_str.split("/")
                if len(parts) != 3:
                    return None
                day, month, year = parts
                return f"{year}-{month}-{day}"  # formato YYYY-MM-DD

            start_date_iso = convert_date_format(start_date_str)
            end_date_iso = convert_date_format(end_date_str)

            # Verificar que las fechas ISO no sean None antes de parsear
            if start_date_iso is None or end_date_iso is None:
                raise ValueError("Formato de fecha inválido (conversión fallida)")

            start_date = parse_date(start_date_iso)  # Parsear YYYY-MM-DD
            end_date = parse_date(end_date_iso)  # Parsear YYYY-MM-DD

            if not start_date or not end_date:
                raise ValueError("Fechas inválidas (parse_date retornó None)")
        except ValueError as e:
            # Puedes añadir un log aquí si quieres ver el error exacto en la consola
            print(f"Error convirtiendo fechas en LedgerExportView: {e}")
            return JsonResponse({"error": "Formato de fecha inválido"}, status=400)

        # Obtener filtros
        filters = self._extract_filters_from_request(request)

        try:
            # Obtener información de cuentas
            try:
                accounts_info = {
                    acc.code: acc
                    for acc in AccountingAccount.objects.filter(code__in=account)
                }
            except AccountingAccount.DoesNotExist:
                return JsonResponse(
                    {"error": "Cuenta contable no encontrada"}, status=404
                )

            # Verificar que todas las cuentas solicitadas existan
            missing_accounts = [acc for acc in account if acc not in accounts_info]
            if missing_accounts:
                return JsonResponse(
                    {
                        "error": f"Cuentas contables no encontradas: {', '.join(missing_accounts)}"
                    },
                    status=404,
                )

            if export_format == "csv":
                # Generar CSV para todas las cuentas
                return self._generate_csv_multicuenta(
                    account,
                    accounts_info,
                    start_date,
                    end_date,
                    seller,
                    filters
                )
            elif export_format == "pdf":
                # Por ahora, solo soportamos CSV
                return JsonResponse(
                    {"error": "Exportación PDF no implementada aún"}, status=400
                )
            else:
                return JsonResponse(
                    {"error": "Formato de exportación no soportado"}, status=400
                )

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)

    def _extract_filters_from_request(self, request):
        """
        Extrae y valida los parámetros de filtro de la solicitud GET.
        """
        filters = {}

        # Filtro de texto
        text_filter_type = request.GET.get("text_filter_type")
        text_filter_value = request.GET.get("text_filter_value")
        text_filter_column = request.GET.get("text_filter_column", "all")

        if text_filter_type and text_filter_value:
            filters["text"] = {
                "type": text_filter_type,
                "value": text_filter_value,
                "column": text_filter_column
            }

        # Filtro de cantidad
        amount_filter_type = request.GET.get("amount_filter_type")
        amount_filter_value = request.GET.get("amount_filter_value")
        amount_filter_column = request.GET.get("amount_filter_column", "debit")

        if amount_filter_type and amount_filter_value:
            filters["amount"] = {
                "type": amount_filter_type,
                "value": amount_filter_value,
                "column": amount_filter_column
            }

        return filters if filters else None

    def _generate_csv_multicuenta(
        self, account_codes, accounts_info, start_date, end_date, seller, filters=None
    ):
        """
        Genera un archivo CSV con los datos del Libro Mayor para múltiples cuentas.
        """
        # Crear archivo en memoria
        output = io.StringIO()
        writer = csv.writer(output, delimiter=";")  # Usar punto y coma como delimitador

        # Cabecera general
        writer.writerow(["LIBRO MAYOR"])
        writer.writerow(["Empresa:", seller.name])
        writer.writerow(
            [
                "Período:",
                f"Del {start_date.strftime('%d/%m/%Y')} al {end_date.strftime('%d/%m/%Y')}",
            ]
        )

        # Añadir información de filtros si existen
        if filters:
            writer.writerow([])
            writer.writerow(["FILTROS APLICADOS:"])

            if filters.get('text'):
                tipo = "+ Contiene" if filters['text']['type'] == '+' else "- No contiene"
                columna = "Todas" if filters['text']['column'] == 'all' else {
                    'date': 'Fecha',
                    'num': 'Nº Asiento',
                    'document': 'Documento',
                    'concept': 'Concepto'
                }.get(filters['text']['column'], filters['text']['column'])

                writer.writerow([
                    "Texto:",
                    f"{tipo} '{filters['text']['value']}' en {columna}"
                ])

            if filters.get('amount'):
                tipo = "Mayor que" if filters['amount']['type'] == '>' else "Menor que"
                columna = {
                    'debit': 'Debe',
                    'credit': 'Haber',
                    'balance': 'Saldo'
                }.get(filters['amount']['column'], filters['amount']['column'])

                writer.writerow([
                    "Cantidad:",
                    f"{tipo} {filters['amount']['value']} en {columna}"
                ])

        writer.writerow([])

        # Para cada cuenta, generar su sección
        grand_total_debit = Decimal("0")
        grand_total_credit = Decimal("0")

        for acc_code in account_codes:
            if not acc_code:
                continue

            account_info = accounts_info.get(acc_code)

            if not account_info:
                writer.writerow([f"ADVERTENCIA: Cuenta {acc_code} no encontrada"])
                writer.writerow([])
                continue

            # Calcular saldo anterior
            previous_balance = get_previous_balance(acc_code, start_date, seller.pk)

            # Obtener movimientos con filtros aplicados
            entries_qs = get_account_movements(acc_code, start_date, end_date, seller.pk, filters)

            # Para el filtro de balance, necesitamos procesar los datos en memoria
            if filters and filters.get('amount', {}).get('column') == 'balance' and filters.get('amount', {}).get('type') and filters.get('amount', {}).get('value', '').strip():
                try:
                    # Primero obtenemos todos los movimientos sin filtro de balance
                    all_entries = list(entries_qs)
                    filtered_entries = []
                    entries_balance = previous_balance
                    amount_type = filters['amount']['type']
                    amount_value = Decimal(filters['amount']['value'])

                    # Calculamos el saldo acumulado y aplicamos el filtro
                    for entry in all_entries:
                        entry_debit = entry.entry_debit or Decimal("0")
                        entry_credit = entry.entry_credit or Decimal("0")
                        entries_balance += entry_debit - entry_credit

                        # Aplicar filtro según tipo
                        if (amount_type == '>' and entries_balance > amount_value) or \
                           (amount_type == '<' and entries_balance < amount_value):
                            filtered_entries.append(entry)

                    # Usar los movimientos filtrados
                    entries = filtered_entries
                except (ValueError, TypeError):
                    # Si hay error en la conversión, usamos todos los movimientos
                    entries = list(entries_qs)

            else:
                entries = list(entries_qs)

            # Cabecera de la cuenta
            writer.writerow([f"CUENTA: {acc_code} - {account_info.description}"])
            writer.writerow(
                ["Saldo anterior:", str(previous_balance).replace(".", ",")]
            )
            writer.writerow([])

            # Cabecera de la tabla
            writer.writerow(
                [
                    "Fecha",
                    "Nº Asiento",
                    "Documento",
                    "Concepto",
                    "Debe",
                    "Haber",
                    "Saldo",
                ]
            )

            # Contenido
            current_balance = previous_balance
            # Escribir saldo anterior como primera fila de datos
            writer.writerow(
                [
                    "",  # Fecha
                    "",  # Nº Asiento
                    "",  # Documento
                    "Saldo anterior",
                    (
                        str(previous_balance).replace(".", ",")
                        if previous_balance >= 0
                        else "0,00"
                    ),  # Debe
                    (
                        str(-previous_balance).replace(".", ",")
                        if previous_balance < 0
                        else "0,00"
                    ),  # Haber
                    str(current_balance).replace(".", ","),  # Saldo
                ]
            )

            total_debit = Decimal("0")
            total_credit = Decimal("0")

            for entry in entries:
                entry_debit = entry.entry_debit or Decimal("0")
                entry_credit = entry.entry_credit or Decimal("0")
                current_balance += entry_debit - entry_credit
                total_debit += entry_debit
                total_credit += entry_credit

                writer.writerow(
                    [
                        (
                            entry.entry_date.strftime("%d/%m/%Y")
                            if entry.entry_date
                            else ""
                        ),
                        entry.entry_num or "",
                        entry.entry_document or "",
                        entry.entry_concept or "",
                        str(entry_debit).replace(
                            ".", ","
                        ),  # Reemplazar punto por coma para formato español
                        str(entry_credit).replace(
                            ".", ","
                        ),  # Reemplazar punto por coma
                        str(current_balance).replace(
                            ".", ","
                        ),  # Reemplazar punto por coma
                    ]
                )

            # Añadir totales de la cuenta
            writer.writerow([])
            writer.writerow(
                [
                    "TOTAL CUENTA:",
                    "",
                    "",
                    "",
                    str(total_debit).replace(".", ","),
                    str(total_credit).replace(".", ","),
                    str(current_balance).replace(".", ","),
                ]
            )
            writer.writerow([])
            writer.writerow([])  # Línea en blanco entre cuentas

            # Acumular para el total general
            grand_total_debit += total_debit
            grand_total_credit += total_credit

        # Añadir total general de todas las cuentas
        writer.writerow(
            [
                "TOTAL GENERAL:",
                "",
                "",
                "",
                str(grand_total_debit).replace(".", ","),
                str(grand_total_credit).replace(".", ","),
                "",
            ]
        )
        writer.writerow([])

        # Cerrar y preparar respuesta
        output.seek(0)

        # Añadir BOM para UTF-8 y especificar charset
        response = HttpResponse(
            output.getvalue(), content_type="text/csv; charset=utf-8-sig"
        )
        response["Content-Disposition"] = (
            f'attachment; filename="libro_mayor_multiple_'
            f'{start_date.strftime("%Y%m%d")}_{end_date.strftime("%Y%m%d")}.csv"'
        )

        return response

    def _generate_csv(
        self, entries, account_info, previous_balance, seller, start_date, end_date
    ):
        """
        Genera un archivo CSV con los datos del Libro Mayor para una única cuenta.
        """
        # Crear archivo en memoria
        output = io.StringIO()
        writer = csv.writer(output, delimiter=";")  # Usar punto y coma como delimitador

        # Cabecera general
        writer.writerow(["LIBRO MAYOR"])
        writer.writerow(["Empresa:", seller.name])
        writer.writerow(
            ["Cuenta:", f"{account_info.code} - {account_info.description}"]
        )
        writer.writerow(
            [
                "Período:",
                f"Del {start_date.strftime('%d/%m/%Y')} al {end_date.strftime('%d/%m/%Y')}",
            ]
        )
        writer.writerow([])
        writer.writerow(["Saldo anterior:", str(previous_balance).replace(".", ",")])
        writer.writerow([])

        # Cabecera de la tabla
        writer.writerow(
            ["Fecha", "Nº Asiento", "Documento", "Concepto", "Debe", "Haber", "Saldo"]
        )

        # Contenido
        current_balance = previous_balance
        # Escribir saldo anterior como primera fila de datos
        writer.writerow(
            [
                "",  # Fecha
                "",  # Nº Asiento
                "",  # Documento
                "Saldo anterior",
                (
                    str(previous_balance).replace(".", ",")
                    if previous_balance >= 0
                    else "0,00"
                ),  # Debe
                (
                    str(-previous_balance).replace(".", ",")
                    if previous_balance < 0
                    else "0,00"
                ),  # Haber
                str(current_balance).replace(".", ","),  # Saldo
            ]
        )
        for entry in entries:
            entry_debit = entry.entry_debit or Decimal("0")
            entry_credit = entry.entry_credit or Decimal("0")
            current_balance += entry_debit - entry_credit

            writer.writerow(
                [
                    entry.entry_date.strftime("%d/%m/%Y") if entry.entry_date else "",
                    entry.entry_num or "",
                    entry.entry_document or "",
                    entry.entry_concept or "",
                    str(entry_debit).replace(
                        ".", ","
                    ),  # Reemplazar punto por coma para formato español
                    str(entry_credit).replace(".", ","),  # Reemplazar punto por coma
                    str(current_balance).replace(".", ","),  # Reemplazar punto por coma
                ]
            )

        # Cerrar y preparar respuesta
        output.seek(0)

        # Añadir BOM para UTF-8 y especificar charset
        response = HttpResponse(
            output.getvalue(), content_type="text/csv; charset=utf-8-sig"
        )
        response["Content-Disposition"] = (
            f'attachment; filename="libro_mayor_{account_info.code}_'
            f'{start_date.strftime("%Y%m%d")}_{end_date.strftime("%Y%m%d")}.csv"'
        )

        return response
