import os
import sys

# Agregar la ruta del script al sys.path si es necesario
sys.path.append(os.path.abspath("docs/script_store_products"))

# Importar la función desde el script
from duplicate_code import check_duplicate_codes_from_file

# Definir la ruta del JSON manualmente
json_path = os.path.join("muaytax", "dictionaries", "data", "store_products.json")

# Ejecutar la función
check_duplicate_codes_from_file(json_path)


# Para buscar en la shell ejecutando el script (check_duplicate_codes_from_file)