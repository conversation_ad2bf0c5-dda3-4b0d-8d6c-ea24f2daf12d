-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_369' ) THEN
	  DROP FUNCTION func_calc_model_es_369(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_369(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE
    short_name VARCHAR := '';
    tmp_key VARCHAR := '';
    tmp_value VARCHAR := '';
    OP_TXT VARCHAR := '01';
    OP NUMERIC := 1;
    T6PAG NUMERIC := 2;
    TOTAL NUMERIC := 0;
    resultado_total VARCHAR := NULL;
    table4 RECORD;
    table6 RECORD;
    table8 RECORD;
    errors RECORD;
    tmp_json JSONB;
	json_result JSONB;
    json_operators JSONB;
BEGIN
    -- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

    json_operators :=  json_build_object();

    -- ERRORS
    OP := 1;
    FOR errors IN (
        (
            SELECT DISTINCT
            inv.departure_country_id,
            inv.tax_country_id,
            ROUND(con.vat::numeric,2) as vat,
            vr.vat_rates,
            ROUND(SUM(
                CASE WHEN inv.is_txt_amz = True
                THEN con.amount_euros
                ELSE con.amount_euros * con.quantity
                END
            )::numeric,2) as amount_euros,
            ROUND(SUM(
                CASE WHEN inv.is_txt_amz = True
                THEN con.amount_euros * con.vat / 100
                ELSE con.amount_euros * con.quantity * con.vat / 100
                END
            )::numeric,2) as vat_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            INNER JOIN sellers_seller sel ON sel.id = inv.seller_id
            INNER JOIN dictionaries_vatrates vr ON (vr.country_code = inv.tax_country_id AND NOT con.vat::text::real = ANY(string_to_array(vr.vat_rates, ', ')::real[]))
            WHERE sel.id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND inv.status_id = 'revised'
            AND inv.transaction_type_id NOT LIKE 'transfer-%'
            AND inv.transaction_type_id in ('oss', 'oss-refund')
            AND (inv.is_generated_amz = False OR inv.is_generated_amz IS NULL)
            AND con.vat > 0
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND con.is_supplied IS NOT true
            GROUP BY inv.departure_country_id, inv.tax_country_id, con.vat, vr.vat_rates
        )
        UNION
        (
            SELECT DISTINCT
            inv.departure_country_id,
            inv.tax_country_id,
            ROUND(con.vat::numeric,2) as vat,
            vr.vat_rates,
            ROUND(SUM(
                CASE WHEN inv.is_txt_amz = True
                THEN con.amount_euros
                ELSE con.amount_euros * con.quantity
                END
            )::numeric,2) as amount_euros,
            ROUND(SUM(
                CASE WHEN inv.is_txt_amz = True
                THEN con.amount_euros * con.vat / 100
                ELSE con.amount_euros * con.quantity * con.vat / 100
                END
            )::numeric,2) as vat_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            INNER JOIN sellers_seller sel ON sel.id = inv.seller_id
            INNER JOIN dictionaries_vatrates vr ON (vr.country_code = inv.tax_country_id)
            WHERE sel.id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND inv.status_id = 'revised'
            AND inv.transaction_type_id NOT LIKE 'transfer-%'
            AND inv.transaction_type_id in ('oss', 'oss-refund')
            AND inv.departure_country_id IS NULL
            AND (inv.is_generated_amz = False OR inv.is_generated_amz IS NULL)
            AND con.vat > 0
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND con.is_supplied IS NOT true
            GROUP BY inv.departure_country_id, inv.tax_country_id, vat, vr.vat_rates
        )
    )
    LOOP
        -- ERROR
        IF errors.departure_country_id IS NULL THEN
            tmp_value := tmp_value || (
                'ERROR: No se encuentra Departure Country para el siguiente Tax Country:' || errors.tax_country_id ||
                ' y IVA: ' || errors.vat || '%'
            ) || '. ';
        ELSE
			IF NOT errors.vat::text::real = ANY(string_to_array(errors.vat_rates, ', ')::real[])  THEN
                tmp_value := tmp_value || (
                    'ERROR: Tiene Facturas con un IVA invalido para el siguiente Tax Country:' || errors.tax_country_id ||
                    ' y IVA: ' || errors.vat || '%' ||
                    ' y Departure Country: ' || errors.departure_country_id || ''
                ) || '. ';
            ELSE
                tmp_value := tmp_value || (
                    'ERROR: Hay un problema con:' ||
                    ' Tax Country: ' || errors.tax_country_id ||
                    ' Departure Country: ' || errors.departure_country_id ||
                    ' y IVA: ' || errors.vat || '%' || '  VAT_RATES: ' || errors.vat_rates
                ) || '. ';
            END IF;
        END IF;
    END LOOP;

    IF tmp_value != '' THEN
        tmp_key := 'ERRORS';
        tmp_json := json_build_object(tmp_key, tmp_value);
        json_operators := jsonb_concat(json_operators, tmp_json);
        tmp_value := '';
    END IF;

    -- TABLE 4
    OP := 1;
    FOR table4 IN (
		SELECT DISTINCT subq.*
		FROM (
			SELECT DISTINCT
			inv.departure_country_id,
			inv.tax_country_id,
			ROUND(con.vat::numeric,2) as vat,
			ROUND(SUM(
				CASE WHEN inv.is_txt_amz = True
				THEN con.amount_euros
				ELSE con.amount_euros * con.quantity
				END
			)::numeric,2) as amount_euros,
			ROUND(SUM(
				CASE WHEN inv.is_txt_amz = True
				THEN con.amount_euros * con.vat / 100
				ELSE con.amount_euros * con.quantity * con.vat / 100
				END
			)::numeric,2) as vat_euros
			FROM invoices_invoice inv
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			INNER JOIN sellers_seller sel ON sel.id = inv.seller_id
			INNER JOIN dictionaries_vatrates vr ON (vr.country_code = inv.tax_country_id AND con.vat::text::real = ANY(string_to_array(vr.vat_rates, ', ')::real[]))
			WHERE sel.id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND inv.status_id = 'revised'
			AND inv.transaction_type_id NOT LIKE 'transfer-%'
			AND inv.transaction_type_id in ('oss', 'oss-refund')
			AND (inv.departure_country_id = 'ES')
			AND (inv.is_generated_amz = False OR inv.is_generated_amz IS NULL)
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
			AND con.vat > 0
			AND con.is_supplied IS NOT true
			AND inv.tax_country_id != 'GB' -- BORRADO porque no es parte de la UE y no entra en el régimen OSS/IOSS.
			AND inv.tax_country_id != 'MC' -- BORRADO porque no es parte de la UE y no entra en el régimen OSS/IOSS.
			AND inv.tax_country_id != 'ES' -- BORRADO porque son ventas desde España a otros países, no a sí misma.
			GROUP BY inv.departure_country_id, inv.tax_country_id, con.vat
			ORDER BY inv.departure_country_id, inv.tax_country_id, vat
		) AS subq WHERE subq.amount_euros > 0 AND subq.vat_euros > 0
    )
    LOOP

        IF table4.departure_country_id IS NOT NULL THEN
            IF table4.amount_euros > 0 AND table4.vat_euros > 0 THEN
                -- COUNTRY
                tmp_value := CASE WHEN table4.tax_country_id = 'GR' THEN 'EL' ELSE table4.tax_country_id END; -- IF COUNTRY IS GREECE (GR) THEN CHANGE TO GREECE (EL)
                tmp_key := 'doc_1' || '_EM4_' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- VAT RATE
                tmp_value := table4.vat;
                tmp_key := 'doc_1' || '_IVA4_' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- AMOUNT
                tmp_value := table4.amount_euros;
                tmp_key := 'doc_1' || '_BI4_' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- VAT EUROS
                tmp_value := table4.vat_euros;
                tmp_key := 'doc_1' || '_CIVA4_' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);
            END IF;
        ELSE
            -- COUNTRY
            tmp_value := 'ERROR';
            tmp_key := 'doc_1' || '_EM4_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);
        END IF;

        OP := OP + 1;
    END LOOP;


    -- TABLE 6
    OP := 1;
    T6PAG := 2;
    FOR table6 IN (
		SELECT DISTINCT subq.*
		FROM (
			SELECT DISTINCT
			inv.departure_country_id,
			sv.vat_number,
			inv.tax_country_id,
			ROUND(con.vat::numeric,2) as vat,
			ROUND(SUM(
				CASE WHEN inv.is_txt_amz = True
				THEN con.amount_euros
				ELSE con.amount_euros * con.quantity
				END
			)::numeric,2) as amount_euros,
			ROUND(SUM(
				CASE WHEN inv.is_txt_amz = True
				THEN con.amount_euros * con.vat / 100
				ELSE con.amount_euros * con.quantity * con.vat / 100
				END
			)::numeric,2) as vat_euros
			FROM invoices_invoice inv
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			INNER JOIN sellers_seller sel ON sel.id = inv.seller_id
			LEFT JOIN sellers_sellervat sv ON (sv.seller_id = sel.id AND sv.vat_country_id = inv.departure_country_id)
			INNER JOIN dictionaries_vatrates vr ON (vr.country_code = inv.tax_country_id AND con.vat::text::real = ANY(string_to_array(vr.vat_rates, ', ')::real[]))
			WHERE sel.id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND inv.status_id = 'revised'
			AND inv.transaction_type_id NOT LIKE 'transfer-%'
			AND inv.transaction_type_id in ('oss', 'oss-refund')
			AND inv.departure_country_id IS NOT NULL
			AND inv.departure_country_id != 'ES'
			AND inv.departure_country_id != 'GB'
			AND inv.departure_country_id != 'MC'
			AND inv.tax_country_id != 'GB'
            AND inv.tax_country_id != 'MC'
			AND (inv.is_generated_amz = False OR inv.is_generated_amz IS NULL)
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
			AND con.vat > 0
			AND con.is_supplied IS NOT true
			GROUP BY inv.departure_country_id, inv.tax_country_id, con.vat, sv.vat_number
			ORDER BY inv.departure_country_id, inv.tax_country_id, vat desc
		) AS subq WHERE subq.amount_euros > 0 AND subq.vat_euros > 0
    )
    LOOP
        IF table6.amount_euros > 0 AND table6.amount_euros > 0 THEN
            IF OP < 10 THEN
                OP_TXT := '0' || OP::text;
            else
                OP_TXT := OP::text;
            end if;

            -- DEPARTURE COUNTRY
            tmp_value := CASE WHEN table6.departure_country_id = 'GR' THEN 'EL' ELSE table6.departure_country_id END; -- IF COUNTRY IS GREECE (GR) THEN CHANGE TO GREECE (EL)
            tmp_key := 'doc_' || T6PAG::text || '_CP_envio_' || OP_TXT;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- VAT NUMBER (antes que no se quitaba el prefijo)
            -- tmp_value := CASE WHEN table6.vat_number IS NULL THEN '' ELSE table6.vat_number END;

            -- VAT NUMBER sin prefijo del país
            tmp_value := CASE
                WHEN table6.vat_number IS NULL THEN ''
                WHEN table6.vat_number LIKE table6.departure_country_id || '%'
                THEN RIGHT(table6.vat_number, LENGTH(table6.vat_number) - LENGTH(table6.departure_country_id))
                ELSE table6.vat_number
            END;
            tmp_key := 'doc_' || T6PAG::text || '_NIVA_if_' || OP_TXT;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- TAX COUNTRY
            tmp_value := CASE WHEN table6.tax_country_id = 'GR' THEN 'EL' ELSE table6.tax_country_id END; -- IF COUNTRY IS GREECE (GR) THEN CHANGE TO GREECE (EL)
            tmp_key := 'doc_' || T6PAG::text || '_EM_consumo_' || OP_TXT;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- VAT
            tmp_value := table6.vat;
            tmp_key := 'doc_' || T6PAG::text || '_IVA_pag4_' || OP_TXT;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- AMOUNT
            tmp_value := table6.amount_euros;
            tmp_key := 'doc_' || T6PAG::text || '_BI_pag4_' || OP_TXT;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- VAT EUROS
            tmp_value := table6.vat_euros;
            tmp_key := 'doc_' || T6PAG::text || '_Cuota_pag4_' || OP_TXT;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            OP := OP + 1;
            IF OP > 28 THEN
                OP := 1;
                T6PAG := T6PAG + 1;
            END IF;
        END IF;
    END LOOP;

    IF OP = 1 AND T6PAG != 2 THEN
        T6PAG := T6PAG -1;
    END IF;

    -- TABLE 8
    OP := 1;
    FOR table8 IN (
        SELECT DISTINCT
        country,
        ROUND(SUM(
            CASE WHEN (q.departure_country_id IS NULL OR (q.departure_country_id = 'ES' AND q.tax_country_id != 'ES'))
            THEN q.vat_euros ELSE 0 END
        )::numeric,2) as table4,
        ROUND(SUM(
            CASE WHEN (q.departure_country_id IS NOT NULL AND q.departure_country_id != 'ES')
            THEN q.vat_euros ELSE 0 END
        )::numeric,2) as table6,
        ROUND(SUM(
            CASE 
                WHEN (q.departure_country_id IS NULL OR (q.departure_country_id = 'ES' AND q.tax_country_id != 'ES'))
                THEN q.vat_euros ELSE 0 
            END +
            CASE 
                WHEN (q.departure_country_id IS NOT NULL AND q.departure_country_id != 'ES')
                THEN q.vat_euros ELSE 0 
            END
        )::numeric, 2) as total
        FROM (
            SELECT DISTINCT
            inv.tax_country_id as country
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            INNER JOIN sellers_seller sel ON sel.id = inv.seller_id
            INNER JOIN dictionaries_vatrates vr ON (vr.country_code = inv.tax_country_id AND con.vat::text::real = ANY(string_to_array(vr.vat_rates, ', ')::real[]))
            WHERE sel.id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND inv.status_id = 'revised'
            AND inv.transaction_type_id NOT LIKE 'transfer-%'
            AND inv.transaction_type_id in ('oss', 'oss-refund')
            AND (inv.departure_country_id = 'ES' OR inv.departure_country_id IS NULL)
            AND inv.tax_country_id != 'GB'
            AND inv.tax_country_id != 'MC'
            AND (inv.is_generated_amz = False OR inv.is_generated_amz IS NULL)
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND con.vat > 0
            AND con.is_supplied IS NOT true
            GROUP BY inv.tax_country_id
            UNION
            SELECT DISTINCT
            inv.tax_country_id as country
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            INNER JOIN sellers_seller sel ON sel.id = inv.seller_id
            WHERE sel.id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND inv.status_id = 'revised'
            AND inv.transaction_type_id NOT LIKE 'transfer-%'
            AND inv.transaction_type_id in ('oss', 'oss-refund')
            AND inv.departure_country_id IS NOT NULL
            AND inv.departure_country_id != 'ES'
            AND inv.tax_country_id != 'GB'
            AND inv.tax_country_id != 'MC'
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND con.vat > 0
            AND con.is_supplied IS NOT true
            GROUP BY inv.tax_country_id
            UNION
            SELECT DISTINCT 'TOTAL' as country
        ) as countries,
        (
            SELECT DISTINCT subq.*
            FROM (
                SELECT DISTINCT
                inv.departure_country_id,
                inv.tax_country_id,
                ROUND(SUM(
                    CASE WHEN inv.is_txt_amz = True
                    THEN con.amount_euros * con.vat / 100
                    ELSE con.amount_euros * con.quantity * con.vat / 100
                    END
                )::numeric,2) as vat_euros
                FROM invoices_invoice inv
                INNER JOIN invoices_concept con ON con.invoice_id = inv.id
                INNER JOIN sellers_seller sel ON sel.id = inv.seller_id
                WHERE sel.id = sellerid
                AND EXTRACT(YEAR FROM accounting_date) = date_year
                AND EXTRACT(MONTH FROM accounting_date) >= month_min
                AND EXTRACT(MONTH FROM accounting_date) <= month_max
                AND inv.status_id = 'revised'
                AND inv.transaction_type_id NOT LIKE 'transfer-%'
                AND inv.transaction_type_id in ('oss', 'oss-refund')
                AND inv.departure_country_id IS NOT NULL
                AND inv.tax_country_id != 'GB'
                AND inv.tax_country_id != 'MC'
                AND (inv.is_generated_amz = False OR inv.is_generated_amz IS NULL)
                AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
                AND con.vat > 0
                AND con.is_supplied IS NOT true
                GROUP BY inv.departure_country_id, inv.tax_country_id
                ORDER BY inv.departure_country_id, inv.tax_country_id
            ) as subq WHERE subq.vat_euros >  0
        ) as q
        WHERE countries.country = q.tax_country_id OR countries.country = 'TOTAL'
        GROUP BY countries.country
    )
    LOOP
        IF table8.country = 'TOTAL' THEN
            TOTAL := table8.total;
        ELSE
            -- COUNTRY
            tmp_value := CASE WHEN table8.country = 'GR' THEN 'EL' ELSE table8.country END; -- IF COUNTRY IS GREECE (GR) THEN CHANGE TO GREECE (EL)
            tmp_key := 'doc_6' || '_EM8_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- APT 4
            tmp_value := table8.table4;
            tmp_key := 'doc_6' || '_entrega_bienes_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- APT 3 + APT 4
            tmp_value := table8.table4;
            tmp_key := 'doc_6' || '_suma3y4_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- APT 6
            tmp_value := table8.table6;
            tmp_key := 'doc_6' || '_entrega_bienesII_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- APT 5 + APT 6
            tmp_value := table8.table6;
            tmp_key := 'doc_6' || '_suma5y6_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- TOTAL CUOTAS
            tmp_value := table8.total;
            tmp_key := 'doc_6' || '_tot_cuotas_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- TOTAL CUOTAS
            tmp_value := table8.total;
            tmp_key := 'doc_6' || '_resultado_EM_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            -- TOTAL CUOTAS
            tmp_value := table8.total;
            tmp_key := 'doc_6' || '_ingresar_ES_' || OP::text;
            tmp_json := json_build_object(tmp_key, tmp_value);
            json_operators := jsonb_concat(json_operators, tmp_json);

            OP := OP + 1;
        END IF;

        -- TOTAL
        resultado_total := TOTAL::text;
        tmp_value := TOTAL::text;
        tmp_key := 'doc_6_resultado_total';
        tmp_json := json_build_object(tmp_key, tmp_value);
        json_operators := jsonb_concat(json_operators, tmp_json);

    END LOOP;


    -- MAKE JSON
	json_result := json_build_object(
        'shortname', short_name,
        'T6PAG', T6PAG
    );

    IF resultado_total IS NULL THEN
        json_result := jsonb_concat( json_result, json_build_object('doc_6_resultado_total', '0')::jsonb );
    END IF;

	IF json_operators IS NOT NULL THEN
        json_result := jsonb_concat( json_result, json_operators );
    END IF;

	-- RETURN JSON
	RETURN json_result::varchar;
END;
$$ LANGUAGE plpgsql;


-- USAR LA FUNCION
-- SELECT func_calc_model_es_369(241, 2023, 01, 09);
-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_369(INTEGER,INTEGER,INTEGER,INTEGER);