from django.core.exceptions import ValidationError

class UppercasePasswordValidator:
    """
    Validate that the password has at least 1 uppercase letter.
    """

    def validate(self, password, user=None):
        if not any(char.isupper() for char in password):
            raise ValidationError(
                ("La contraseña debe contener al menos 1 letra mayúscula."),
                code='password_no_upper',
            )

    def get_help_text(self):
        return ("Su contraseña debe contener al menos 1 letra mayúscula.")
    
class LowercasePasswordValidator:
    """
    Validate that the password has at least 1 lowercase letter.
    """

    def validate(self, password, user=None):
        if not any(char.islower() for char in password):
            raise ValidationError(
                ("La contraseña debe contener al menos 1 letra minúscula."),
                code='password_no_lower',
            )

    def get_help_text(self):
        return ("Su contraseña debe contener al menos 1 letra minúscula.")
    
class ExistNumberValidator:
    """
    Validate that the password has at least 1 digit.
    """

    def validate(self, password, user=None):
        if not any(char.isdigit() for char in password):
            raise ValidationError(
                ("La contraseña debe contener al menos 1 número."),
                code='password_no_digit',
            )

    def get_help_text(self):
        return ("Su contraseña debe contener al menos 1 número.")
    
class ExistSpecialCharValidator:
    """
    Validate that the password has at least 1 special character.
    """

    def validate(self, password, user=None):
        if not any(not char.isalnum() for char in password):
            raise ValidationError(
                ("La contraseña debe contener al menos 1 caracter especial."),
                code='password_no_special_char',
            )

    def get_help_text(self):
        return ("Su contraseña debe contener al menos 1 caracter especial.")
    