import os
import re
import json

# Definir la ruta base del directorio del script
base_dir = os.path.dirname(__file__)

# Definir la ruta base del directorio del proyecto 'muaytax'
project_base_dir = os.path.join(base_dir, '..', '..', 'muaytax')

# Definir el directorio donde se encuentran los archivos de plantillas HTML
templates_dir = os.path.join(project_base_dir, 'templates')

# Definir el directorio donde se encuentran los archivos Python
python_dir = os.path.join(project_base_dir, '')

# Ruta para guardar el archivo JSON con las API keys encontradas
output_file = os.path.join(base_dir, 'api_keys_found.json')

# Expresión regular actualizada para detectar claves API en variables que contengan 'key'
api_key_pattern = re.compile(r'\b(\w*apikey\w*)\s*=\s*["\']([a-zA-Z0-9_\-]{20,})["\']', re.IGNORECASE)

# Expresión regular para detectar comentarios en Python
python_comment_pattern = re.compile(r'#.*?$|""".*?"""|\'\'\'.*?\'\'\'', re.DOTALL | re.MULTILINE)

# Expresión regular para detectar comentarios en HTML
html_comment_pattern = re.compile(r'<!--.*?-->', re.DOTALL)

# Diccionario para almacenar las API keys encontradas y los archivos en los que aparecen
api_keys_found = {}

# Función para limpiar el contenido de comentarios
def remove_comments(content, file_extension):
    if file_extension == '.py':
        # Eliminar comentarios en archivos Python
        content = python_comment_pattern.sub('', content)
    elif file_extension == '.html':
        # Eliminar comentarios en archivos HTML
        content = html_comment_pattern.sub('', content)
    return content

# Función para buscar API keys en un archivo y almacenar los resultados en el diccionario
def search_api_keys_in_file(file_path):
    # Determinar la extensión del archivo para aplicar la limpieza de comentarios adecuada
    file_extension = os.path.splitext(file_path)[1]
    
    # Abrir el archivo en modo lectura con codificación UTF-8
    with open(file_path, 'r', encoding='utf-8') as f:
        # Leer el contenido completo del archivo
        content = f.read()
        # Eliminar comentarios del contenido
        content = remove_comments(content, file_extension)
        # Buscar todas las coincidencias de la expresión regular en el contenido del archivo
        for match in api_key_pattern.finditer(content):
            var_name, key_value = match.groups()
            # Si la clave API aún no está en el diccionario, agregarla con un diccionario vacío
            if key_value not in api_keys_found:
                api_keys_found[key_value] = {'files': [], 'variables': []}
            # Construir la ruta relativa a 'muaytax'
            relative_path = os.path.relpath(file_path, project_base_dir)
            # Agregar la ruta del archivo a la lista de archivos que contienen esta clave API
            api_keys_found[key_value]['files'].append(relative_path)
            # Agregar el nombre de la variable que contiene la clave API
            if var_name not in api_keys_found[key_value]['variables']:
                api_keys_found[key_value]['variables'].append(var_name)

# Recorrer los archivos en el directorio de plantillas para buscar API keys
for root, dirs, files in os.walk(templates_dir):
    for file in files:
        # Procesar solo archivos con extensión .html
        if file.endswith(".html"):
            file_path = os.path.join(root, file)
            search_api_keys_in_file(file_path)

# Recorrer los archivos en el directorio de código Python para buscar API keys
for root, dirs, files in os.walk(python_dir):
    for file in files:
        # Procesar solo archivos con extensión .py
        if file.endswith(".py"):
            file_path = os.path.join(root, file)
            search_api_keys_in_file(file_path)

# Guardar los resultados en un archivo JSON
with open(output_file, 'w', encoding='utf-8') as f:
    # Volcar el diccionario api_keys_found en el archivo JSON con una indentación de 4 espacios
    json.dump(api_keys_found, f, indent=4)

# Imprimir mensaje indicando que el proceso ha finalizado y mostrar la ruta del archivo JSON generado
print(f"Proceso completado. API keys encontradas guardadas en {output_file}")
