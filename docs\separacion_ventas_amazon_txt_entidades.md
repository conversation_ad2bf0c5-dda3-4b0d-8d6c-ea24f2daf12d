# Separación de Ventas Amazon TXT Entre Entidades Jurídicas

## Descripción del Proceso

Este documento describe cómo realizar la separación de ventas de un archivo TXT de Amazon ya procesado entre diferentes entidades jurídicas (por ejemplo, de LLC a SL) basándose en rangos de fechas específicos.

## Caso de Uso Típico

Un cliente cambia su estructura empresarial durante un mes (por ejemplo, de LLC a SL) y necesita separar las ventas del archivo TXT ya procesado para que las facturas se asocien correctamente a cada entidad según las fechas correspondientes.

## Información Necesaria Antes de Empezar

1. **Archivo TXT original**: Nombre del archivo y su ID en la base de datos
2. **Entidad original**: ID del seller actual (donde están todas las facturas)
3. **Entidad destino**: ID del seller al que se moverán las facturas
4. **Rango de fechas**: Fechas de corte para la separación

## Estructura de Base de Datos Relevante

### Tablas Principales

- `importers_amazontxteur`: Registros de archivos TXT importados
- `invoices_invoice`: Facturas generadas desde el TXT
- `sellers_seller`: Información de vendedores/entidades

### Relaciones Clave

- `invoices_invoice.amz_txt_eur_id` → `importers_amazontxteur.id`
- `invoices_invoice.seller_id` → `sellers_seller.id` ⚠️ **CRÍTICO**
- `importers_amazontxteur.seller_id` → `sellers_seller.id`

### ⚠️ IMPORTANTE: Doble Relación Vendedor-Factura

Las facturas tienen **DOS campos** que las vinculan con vendedores:
1. **Indirecta**: `invoices_invoice.amz_txt_eur_id` → `importers_amazontxteur.seller_id`
2. **Directa**: `invoices_invoice.seller_id`

**La vista de facturas filtra por `invoices_invoice.seller_id` directamente**, por lo que ambos campos deben estar actualizados.

## Proceso Paso a Paso

### Paso 1: Identificación y Verificación

#### 1.1. Localizar el archivo TXT en base de datos

```sql
-- Buscar el archivo TXT específico
SELECT
    id,
    file,
    amz_id,
    month,
    year,
    status_id,
    seller_id,
    created_at
FROM importers_amazontxteur
WHERE amz_id = '[AMAZON_ID]'
    AND month = [MES]
    AND file LIKE '%[NOMBRE_ARCHIVO]%';
```

#### 1.2. Verificar la distribución de fechas

```sql
-- Ver distribución de fechas de las facturas
SELECT
    accounting_date,
    COUNT(*) as numero_de_facturas
FROM invoices_invoice
WHERE amz_txt_eur_id = [ID_IMPORTADOR_ORIGINAL]
GROUP BY accounting_date
ORDER BY accounting_date;
```

#### 1.3. Contar facturas para cada entidad

```sql
-- Facturas que se moverán a la nueva entidad (desde fecha de corte)
SELECT COUNT(*)
FROM invoices_invoice
WHERE amz_txt_eur_id = [ID_IMPORTADOR_ORIGINAL]
  AND accounting_date >= '[FECHA_CORTE]';

-- Facturas que permanecerán en la entidad original (hasta fecha de corte)
SELECT COUNT(*)
FROM invoices_invoice
WHERE amz_txt_eur_id = [ID_IMPORTADOR_ORIGINAL]
  AND accounting_date < '[FECHA_CORTE]';
```

#### 1.4. Verificar IDs de vendedores

```sql
-- Confirmar nombres y IDs de las entidades
SELECT id, name, shortname
FROM sellers_seller
WHERE id IN ([ID_SELLER_ORIGINAL], [ID_SELLER_DESTINO]);
```

### Paso 2: Creación del Nuevo Registro de Importación

#### 2.1. Crear registro duplicado para la nueva entidad

```sql
-- Crear copia del registro de importación para nueva entidad
INSERT INTO importers_amazontxteur (
    file, amz_id, month, year, status_id, seller_id, error_message, is_email_send, created_at, modified_at
)
SELECT
    file, amz_id, month, year, status_id, [ID_SELLER_DESTINO] AS seller_id,
    'Copia del TXT ID [ID_ORIGINAL] para [NOMBRE_ENTIDAD_DESTINO]' AS error_message,
    is_email_send, NOW(), NOW()
FROM importers_amazontxteur
WHERE id = [ID_IMPORTADOR_ORIGINAL]
RETURNING id;
```

**⚠️ IMPORTANTE**: Anota el ID devuelto, será el `[NUEVO_ID_IMPORTADOR]`

#### 2.2. Verificar creación del nuevo registro

```sql
-- Comprobar el nuevo registro
SELECT * FROM importers_amazontxteur WHERE id = [NUEVO_ID_IMPORTADOR];
```

### Paso 3: Reasignación de Facturas ⚠️ CRÍTICO

#### 3.1. Verificar seller_id actual antes del cambio

```sql
-- Ver el seller_id actual de las facturas (ANTES del update)
SELECT
    seller_id,
    amz_txt_eur_id,
    COUNT(*) as facturas
FROM invoices_invoice
WHERE amz_txt_eur_id = [ID_IMPORTADOR_ORIGINAL]
GROUP BY seller_id, amz_txt_eur_id;
```

#### 3.2. Actualización masiva combinada (amz_txt_eur_id + seller_id)

```sql
-- CRÍTICO: Mover facturas al nuevo importador Y cambiar seller_id en una sola operación
UPDATE invoices_invoice
SET
    amz_txt_eur_id = [NUEVO_ID_IMPORTADOR],
    seller_id = [ID_SELLER_DESTINO]
WHERE amz_txt_eur_id = [ID_IMPORTADOR_ORIGINAL]
  AND accounting_date >= '[FECHA_CORTE]';
```

### Paso 4: Verificación Final Completa

#### 4.1. Verificar la separación final

```sql
-- Ver el estado final de ambos importadores
SELECT
    seller_id,
    amz_txt_eur_id,
    COUNT(*) as facturas
FROM invoices_invoice
WHERE amz_txt_eur_id IN ([ID_IMPORTADOR_ORIGINAL], [NUEVO_ID_IMPORTADOR])
GROUP BY seller_id, amz_txt_eur_id
ORDER BY amz_txt_eur_id;
```

**Resultado esperado:**
- `([ID_SELLER_ORIGINAL], [ID_IMPORTADOR_ORIGINAL], [NUM_FACTURAS_ENTIDAD_ORIGINAL])`
- `([ID_SELLER_DESTINO], [NUEVO_ID_IMPORTADOR], [NUM_FACTURAS_ENTIDAD_DESTINO])` ← ✅ Ambos campos correctos

#### 4.2. Verificar distribución de fechas final

```sql
-- Verificar que las fechas están correctamente separadas
SELECT
    accounting_date,
    COUNT(*) as facturas,
    amz_txt_eur_id
FROM invoices_invoice
WHERE amz_txt_eur_id IN ([ID_IMPORTADOR_ORIGINAL], [NUEVO_ID_IMPORTADOR])
GROUP BY accounting_date, amz_txt_eur_id
ORDER BY accounting_date, amz_txt_eur_id;
```

#### 4.3. Muestra de facturas movidas

```sql
-- Comprobar algunas facturas movidas con sus sellers
SELECT
    i.id,
    i.reference,
    i.accounting_date,
    i.amz_txt_eur_id,
    i.seller_id,
    s.name as seller_name
FROM invoices_invoice i
JOIN sellers_seller s ON i.seller_id = s.id
WHERE i.amz_txt_eur_id = [NUEVO_ID_IMPORTADOR]
ORDER BY i.accounting_date ASC
LIMIT 10;
```

## Verificación en la Interfaz Web

### Dónde Ver Los Cambios

#### 1. Lista de Importadores Amazon TXT
- **Entidad Original**: `/sellers/[shortname_original]/AmazonTxtEur/`
- **Entidad Destino**: `/sellers/[shortname_destino]/AmazonTxtEur/`

Ambas entidades deberían mostrar su archivo TXT correspondiente.

#### 2. Lista de Facturas
- **Entidad Original**: `/sellers/[shortname_original]/invoices/`
- **Entidad Destino**: `/sellers/[shortname_destino]/invoices/`

#### 3. Búsqueda de Facturas Específicas
En la lista de facturas, usar el campo de búsqueda con:
- Número de referencia de la factura
- Nombre del archivo TXT (ej: `685289020242_1.txt`)
- ASIN del producto



### Consultas Ejecutadas
```sql
-- 1. Localizar archivo
SELECT * FROM importers_amazontxteur WHERE amz_id = 'ACOVKJ6DOEFKM' AND month = 5;
-- Resultado: ID 16852

-- 2. Crear nuevo importador
INSERT INTO importers_amazontxteur (...) SELECT ... WHERE id = 16852 RETURNING id;
-- Resultado: ID 17010

-- 3. Actualización combinada (amz_txt_eur_id + seller_id)
UPDATE invoices_invoice
SET amz_txt_eur_id = 17010, seller_id = 2909
WHERE amz_txt_eur_id = 16852 AND accounting_date >= '2025-05-15';
-- Afectadas: 1230 facturas (ambos campos actualizados)
```

### Resultado Final
- **LLC (16852)**: 649 facturas (01-14 mayo), seller_id = 387
- **SL (17010)**: 1230 facturas (15-31 mayo), seller_id = 2909

