import uuid

from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
from django.http import HttpResponseRedirect, JsonResponse, HttpResponseBadRequest
from django.shortcuts import get_object_or_404, redirect, render
from django.views import View
from django.views.generic import CreateView
from django.views.decorators.csrf import csrf_exempt
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.utils.decorators import method_decorator
from urllib.parse import urlencode

from muaytax.app_sellers.models.seller import Seller
from muaytax.app_marketplaces.apis import ShopifyApiService, verify_hmac_signature, decode_state, validate_shopname
from muaytax.app_marketplaces.models.marketplace import Marketplace, MarketplaceLocation
from muaytax.app_marketplaces.models.wizard import MarketplaceWizardDraft
from muaytax.app_marketplaces.forms.marketplace import MarketplaceChangeForm
from muaytax.dictionaries.models.marketplaces import Marketplaces
from muaytax.dictionaries.models.countries import Country
from muaytax.users.permissions import IsSellerShortnamePermission, IsSellerRolePermission

from muaytax.app_marketplaces.tasks.shopify import process_shopify_webhook_async

class ShopifyAddStore(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), CreateView):
    model = Marketplace
    template_name = "marketplaces/shopify/shopify_add_store.html"
    context_object_name = "marketplace"
    form_class = MarketplaceChangeForm
    success_url = "/sellers/<shortname>/marketplaces/"

    def get(self, request, *args, **kwargs):
        """
        Vista para crear una nueva tienda de Shopify.
        Se crea un ID de wizard si no existe y se establece como cookie.
        Si el paso es "success", significa que la tienda se ha creado correctament y la petición viene de la vista de callback de Shopify.
        Con lo cual no se establece la cookie.
        """
        is_success = request.GET.get("step") == "success"
        response = super().get(request, *args, **kwargs)

        if is_success:
            return response
        
        # Si el paso no es "success", se crea un nuevo ID de wizard y se establece como cookie.
        wizard_id, should_set_cookie = MarketplaceWizardDraft.get_or_create_draft_token(request)

        if should_set_cookie:
            response.set_cookie(
                "_mtxMkpWizShop",
                wizard_id,
                max_age=60*15,  # 15 minutos
                # httponly=True,
                secure=True,
                samesite="Lax",
            )

        return response

    def post(self, request, *args, **kwargs) -> JsonResponse:
        """Vista para crear una nueva tienda de Shopify."""
        shortname = self.kwargs.get("shortname")
        seller = get_object_or_404(Seller, shortname=shortname)

        shop_title = request.POST.get("shop_title", "")
        shopname = request.POST.get("shopname", "").strip()
        oss_selection = request.POST.get("oss_selection") == "yes"
        oss_start_date = request.POST.get("oss_start_date")
        oss_country = request.POST.get("oss_country")
        wizard_id = request.COOKIES.get("_mtxMkpWizShop")

        if not wizard_id or not self.__check_wizard_expiration(wizard_id):
            return JsonResponse({"error": "La sesión ha expirado. Esta página se recargará.", "code": "session_expired"}, status=400)
        
        if not shopname:
            return JsonResponse({"error": "El nombre de la tienda es obligatorio.", "code": "shopname_required"}, status=400)
        
        if not shopname.endswith(".myshopify.com"):
            return JsonResponse({"error": "El nombre de la tienda debe terminar en .myshopify.com.", "code": "shopname_invalid"}, status=400)

        if not validate_shopname(shopname):
            return JsonResponse({"error": "El nombre de la tienda no es válido.", "code": "shopname_invalid"}, status=400)

        existing_marketplace = Marketplace.objects.filter(seller=seller, shopname=shopname).first()
        if existing_marketplace:
            if existing_marketplace.is_active:
                return JsonResponse({
                    "error": _("Ya tienes una tienda de Shopify activa con ese nombre."),
                    "code": "existing_active_marketplace"
                    },status=400)
                    
            return JsonResponse({
                "error": _("Ya tienes una tienda de Shopify inactiva con ese nombre."),
                "code": "existing_inactive_marketplace"
                },status=400)
        
        shopify_dict = Marketplaces.objects.filter(code="shopify").first()
        if not shopify_dict:
            return JsonResponse(
                {
                    "error": "No se ha encontrado el marketplace de Shopify en los diccionarios.",
                    "code": "marketplace_not_found"
                }, status=400)

        with transaction.atomic():
            seller.oss = oss_selection
            seller.oss_date = oss_start_date if oss_selection else None # type: ignore
            seller.oss_country = Country.objects.filter(iso_code=oss_country).first() if oss_country else None # type: ignore
            seller.save(update_fields=["oss", "oss_date", "oss_country"])

        base_url = reverse("app_marketplaces:shopify_api_auth", args=[shortname, shopname])
        query_string = urlencode({"shop_title": shop_title})
        redirect_url = f"{base_url}?{query_string}"
        return JsonResponse({
            "code": "success",
            "redirect_url": redirect_url,
            "message": _("La petición es exitosa y se redirigirá a la autorización de la API de Shopify.")
        })

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        seller_vats = seller.vat_seller.all()
        countries = Country.objects.all().order_by("name")

        context["seller_vats"] = seller_vats
        context["seller"] = seller
        context["countries"] = countries
        return context

    def __check_wizard_expiration(self, wizard_id: str) -> bool:
        """
        Verifica si el wizard ha expirado.
        Si el ID del wizard no es válido o no existe, se considera que ha expirado.
        """
        if not wizard_id:
            return False

        try:
            draft = MarketplaceWizardDraft.objects.get(
                wizard_id=uuid.UUID(wizard_id),
                seller=self.request.user.seller,
                is_completed=False,
            )
            return not draft.is_expired
        except (MarketplaceWizardDraft.DoesNotExist, ValueError):
            return False

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ShopifyAuthView(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), View):

    def get(self, request, *args, **kwargs):
        shopname = self.kwargs["shopname"]
        shortname = self.kwargs["shortname"]
        shop_title = request.GET.get("shop_title", "")

        if not shopname or not shortname:
            return JsonResponse({"error": "Faltan parámetros"}, status=400)
        if not validate_shopname(shopname):
            return JsonResponse({"error": "El nombre de la tienda no es válido."}, status=400)

        ShopifyService = ShopifyApiService(shortname=shortname)
        auth_url = ShopifyService.get_shopify_auth_url(shopname, shop_title)

        return redirect(auth_url)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ShopifyCallbackView(View):
    def get(self, request, *args, **kwargs):
        query_params = request.GET.dict()
        hmac_received  = query_params.pop('hmac', None)
        wizard_cookie = request.COOKIES.get("_mtxMkpWizShop")

        if not hmac_received or not verify_hmac_signature(query_params, hmac_received):
            return self.handle_failed_authentication(request)
        
        shop = query_params.get("shop")
        code = query_params.get("code")
        state = query_params.get("state")

        if not state or not shop or not code:
            return self.handle_failed_authentication(request)
        
        state_data = decode_state(state)
        if not state_data:
            return self.handle_failed_authentication(request)
        
        shop_title = state_data.get("shop_title", "")
        shortname = state_data.get("shortname")
        seller = get_object_or_404(Seller, shortname=shortname)

        try:
            client = ShopifyApiService()
            access_token = client.get_shopify_access_token(shop, code)
        except ValueError:
            return self.handle_failed_authentication(request)
        
        shopify_marketplace_type  = Marketplaces.objects.filter(code="shopify").first()
        if not shopify_marketplace_type:
            return self.handle_failed_authentication(request)

        try:
            with transaction.atomic():
                existing = (
                    Marketplace.objects.select_for_update()
                    .filter(marketplace_type=shopify_marketplace_type, shopname=shop, seller=seller)
                    .first()
                )
                if existing:
                    return self.handle_failed_authentication(request)
                
                marketplace  = Marketplace.objects.create(
                    name=shop_title,
                    shopname=shop,
                    marketplace_type=shopify_marketplace_type,
                    seller=seller,
                    token=access_token,
                    is_active=True,
                )
            self.create_shopify_locations(marketplace, access_token, shop)
            self.register_shopify_webhooks(shop, access_token)
            
        except Exception:
            return self.handle_failed_authentication(request)
            

        if wizard_cookie:
            try: 
                draft = MarketplaceWizardDraft.objects.get(wizard_id=uuid.UUID(wizard_cookie))
                draft.is_completed = True
                draft.save(update_fields=["is_completed"])
            except (MarketplaceWizardDraft.DoesNotExist, ValueError):
                pass

        return HttpResponseRedirect(
            reverse("app_marketplaces:shopify_new_store", args=[shortname])
            + "?" + urlencode({"step": "success"})
        )
    
    def create_shopify_locations(self, marketplace_instance: Marketplace, access_token: str, shopname: str) -> None:
        """
        Obtiene y crea las ubicaciones del marketplace de Shopify.
        """
        service = ShopifyApiService(access_token=access_token)
        locations = service.get_shopify_locations(shopname)

        if not locations:
            raise ValueError("No se encontraron ubicaciones de Shopify.")

        for location in locations:
            country = Country.objects.filter(iso_code=location["country_code"]).first() if location.get("country_code") else None
            MarketplaceLocation.objects.update_or_create(
                marketplace=marketplace_instance,
                location_id=location["id"],
                defaults={
                    "name": location["name"],
                    "location_country": country,
                    "is_active": location.get("active", True)
                }
            )
        
        # return JsonResponse({"message": "Ubicaciones creadas correctamente."}, status=200)

    def register_shopify_webhooks(self, shop_name: str, access_token: str) -> None:
        """
        Registra los webhooks necesarios para la tienda de Shopify.
        """
        service = ShopifyApiService(access_token=access_token)
        service.register_shopify_webhooks(shop_name=shop_name)

    def handle_failed_authentication(self, request):
        """
        Maneja la autenticación fallida de Shopify.
        Redirige al usuario a una vista de error.
        """
        if request.user.is_authenticated:
            shortname = getattr(getattr(request.user, "seller", None), "shortname", None)
            return HttpResponseRedirect(reverse("app_marketplaces:shopify_auth_error", args=[shortname]))
        
        return HttpResponseBadRequest(
            _("Ha ocurrido un error. La petición falló o no tienes permisos para acceder a esta página.")
        )

class ShopifyAuthErrorView(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), View):
    """
    Vista para manejar errores de autenticación de Shopify.
    Esta vista se utiliza para redirigir al usuario a una página de error si la autenticación falla.
    """
    template_name = "marketplaces/shopify/shopify_auth_error.html"

    def get(self, request, *args, **kwargs):
        shortname = kwargs.get("shortname")
        seller = get_object_or_404(Seller, shortname=shortname)

        return self.render_error_page(request, seller)
    
    def render_error_page(self, request, seller):
        """
        Renderiza la página de error de autenticación de Shopify.
        """
        context = {
            "seller": seller,
            "error_message": _("Error de autenticación de Shopify.")
        }
        return render(request, self.template_name, context)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

@method_decorator(csrf_exempt, name='dispatch')
class ShopifyWebhookView(View):

    def post(self, request, *args, **kwargs):
        """
        Maneja los webhooks de Shopify. Encola el procesamiento del webhook para evitar bloqueos.
        Se espera que el cuerpo del webhook sea JSON y se envíe en la cabecera "X-Shopify-Topic".
        """
        topic = request.headers.get("X-Shopify-Topic")
        shop_domain = request.headers.get("X-Shopify-Shop-Domain")
        raw_body = request.body

        print(f"\033[92m\r\n[SH-WEBHOOK]: Recibiendo: {topic} para la tienda {shop_domain}\033[0m")
        # Encolamos la solicitud y respondemos inmediatamente
        process_shopify_webhook_async.delay(
            topic=topic,
            domain=shop_domain,
            raw_body=raw_body.decode("utf-8"),
            headers=dict(request.headers)
        )

        return JsonResponse({
            "status": "accepted",
            "message": f"Webhook {topic} encolado para la tienda {shop_domain}"
        }, status=202)