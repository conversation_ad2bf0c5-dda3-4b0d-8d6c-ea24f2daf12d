  --CHECK PARA COMPROBAR SI AUN QUEDAN FACTURAS CON BOOLEANOS A NULL

SELECT COUNT(*) FROM invoices_invoice 
WHERE is_txt_amz IS NULL 
OR is_api_shopify IS NULL
OR is_api_miravia IS NULL
OR is_generated IS NULL
OR is_generated_amz IS NULL
OR is_rectifying IS NULL
OR is_reconciled IS NULL
OR is_oss IS NULL
OR is_eqtax IS NULL
OR out_of_time IS NULL

------------------------------------------------------------------------------------------------------------------

--FUNCIÓN PARA ACTUALIZAR REGISTROS CON UN LÍMITE DE UPDATES
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_update_null_bool_invoices') THEN
        DROP FUNCTION func_update_null_bool_invoices(inv_limit INTEGER);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_update_null_bool_invoices(inv_limit INTEGER)
RETURNS VOID AS $$

DECLARE

null_bool_inv RECORD;

BEGIN 
    FOR null_bool_inv IN (
      SELECT DISTINCT *
      FROM invoices_invoice 
      WHERE
        (is_txt_amz IS NULL) OR
        (is_api_shopify IS NULL) OR
        (is_api_miravia IS NULL) OR
        (is_generated IS NULL) OR
        (is_generated_amz IS NULL) OR
        (is_rectifying IS NULL) OR
        (is_reconciled IS NULL) OR
        (is_oss IS NULL) OR
        (is_eqtax IS NULL) OR
        (out_of_time IS NULL)
        LIMIT inv_limit
    )
    LOOP
      UPDATE invoices_invoice 
      SET
        is_txt_amz = CASE WHEN is_txt_amz IS NULL THEN False ELSE is_txt_amz END,
        is_api_shopify = CASE WHEN is_api_shopify IS NULL THEN False ELSE is_api_shopify END,
        is_api_miravia = CASE WHEN is_api_miravia IS NULL THEN False ELSE is_api_miravia END,
        is_generated = CASE WHEN is_generated IS NULL THEN False ELSE is_generated END,
        is_generated_amz = CASE WHEN is_generated_amz IS NULL THEN False ELSE is_generated_amz END,
        is_rectifying = CASE WHEN is_rectifying IS NULL THEN False ELSE is_rectifying END,
        is_reconciled = CASE WHEN is_reconciled IS NULL THEN False ELSE is_reconciled END,
        is_oss = CASE WHEN is_oss IS NULL THEN False ELSE is_oss END,
        is_eqtax = CASE WHEN is_eqtax IS NULL THEN False ELSE is_eqtax END,
        out_of_time = CASE WHEN out_of_time IS NULL THEN False ELSE out_of_time END
      WHERE
        id = null_bool_inv.id;
    END LOOP;

END;
$$ LANGUAGE plpgsql;

--SELECT func_update_null_bool_invoices(100000);

-----------------------------------------------------------------------------------------------------------------------

-- Al intentar hacer la migración es probable que la consola nos devuelva un error tipo:
-- "psycopg2.errors.ObjectInUse: cannot ALTER TABLE "invoices_invoice" because it has pending trigger events"

-- Ocurre cuando intentas realizar una operación de modificación (ALTER TABLE) en una tabla en PostgreSQL, 
-- pero hay eventos de desencadenadores (triggers) pendientes asociados a esa tabla

-- POSIBLE SOLUCIÓN DESACTIVAR TEMPORALMENTE LOS TRIGGERS DE ESA TABLA
ALTER TABLE "invoices_invoice" DISABLE TRIGGER ALL;

-- << HACER LA MIGRACIÓN >>

--REACTIVAR LOS TRIGGERS
ALTER TABLE "invoices_invoice" ENABLE TRIGGER ALL;

--LISTAR LOS TRIGGERS ASOCIADOS A LA TABLA
SELECT tgname AS trigger_name, tgrelid::regclass AS table_name, tgenabled
FROM pg_trigger
WHERE tgrelid = 'invoices_invoice'::regclass;

-- Si la columna "tgenabled" devuelve O es que el trigger está habilitado
-- Si la columna "tgenabled" devuelve D es que el trigger está deshabilitado

