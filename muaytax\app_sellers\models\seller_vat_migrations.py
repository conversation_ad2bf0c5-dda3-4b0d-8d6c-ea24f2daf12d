# Importaciones de Django
from django.db import models

# Import de terceros
from phonenumber_field.modelfields import PhoneNumberField

# Imoportaciones locales - Modelos
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_address.models.address import Address
from muaytax.dictionaries.models.countries import Country


class SellerVatMigration(models.Model):
    """
    Información completa de migración del servicio VAT de un Seller,
    incluyendo gestor anterior, acceso Government Gateway (UK) y declaraciones pendientes.
    """

    seller = models.ForeignKey(
        Seller,
        on_delete=models.CASCADE,
        related_name="vat_migrations",
        verbose_name="Vendedor"
    )

    seller_vat = models.ForeignKey(  
        SellerVat,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="migration_info",
        verbose_name="Registro VAT asociado",
        help_text="Si el servicio está relacionado con IVA, se vincula con el registro de SellerVat."
    )

    # Información del gestor anterior
    previous_manager_name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Nombre del gestor anterior"
    )

    previous_manager_address = models.ForeignKey(
        "address.Address",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        related_name="seller_vat_migration_previous_manager_address",
        verbose_name="Dirección del Gestor Anterior"
    )

    previous_manager_start_date = models.DateField(
        blank=True,
        null=True,
        verbose_name="Fecha en la que inicio con el gestor anterior"
    )

    previous_accounting_filed = models.BooleanField(
        blank=True,
        null=True,
        verbose_name="¿Se presentó la contabilidad del año anterior?"
    )

    last_tax_declaration_date = models.DateField(
        blank=True,
        null=True,
        verbose_name="fecha de última declaracion precentada por el gestor anterior"
    )

    last_tax_declaration_submitted_by_previous = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Última declaración presentada por el gestor anterior"
    )

    # Acceso Government Gateway (solo UK)
    gov_gateway_user_id = models.CharField(
        max_length=12,
        blank=True,
        null=True,
        verbose_name="Government Gateway User ID",
        help_text="Debe contener exactamente 12 dígitos numéricos."
    )

    gov_gateway_password = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Contraseña de acceso Gateway"
    )

    gov_gateway_phone_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="gateway_phone_country",
        verbose_name="Prefijo Telefónico de la Cuenta Gateway",
        help_text="Prefijo del número de teléfono asociado a la cuenta Gateway. Por ejemplo, '44' para Reino Unido."
    )

    gov_gateway_phone_number = PhoneNumberField(
        blank=True,
        null=True,
        verbose_name="Teléfono asociado a la cuenta Gateway",
        help_text="Número de teléfono asociado a la cuenta Gateway, sin el prefijo del país. Por ejemplo, '1234567890' para un número en Reino Unido."
    )

    # Declaración a presentar por MuayTax
    first_tax_declaration_submitted_by_us = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Primera declaración a presentar por MuayTax"
    )

    created_at = models.DateTimeField(
        auto_now_add=True
    )

    modified_at = models.DateTimeField(
        auto_now=True
    )

    class Meta:
        verbose_name = "Migración VAT"
        verbose_name_plural = "Migraciones VAT"

    def __str__(self):
        if self.seller_vat:
            return f"{self.seller} – {self.seller_vat.vat_country} (Migración VAT)"
        return f"{self.seller} – Migración VAT"
