from django.conf import settings
#from muaytax.users.api.views import RegisterView
from rest_framework.routers import DefaultRouter, SimpleRouter

from muaytax.app_sellers.api.views import SellersViewSet
# from muaytax.users.api.views import UserViewSet
#from muaytax.users.api.views import CreateUserViewSet

if settings.DEBUG:
    router = DefaultRouter()
else:
    router = SimpleRouter()

# router.register("users", UserViewSet)
#router.register("createusers", CreateUserViewSet)
#router.register("register/", RegisterView)


app_name = "api"
urlpatterns = router.urls
