SELECT sel.id as seller_id, sel.shortname, 'PROV' as type, prov.id, prov.provider_number as number
FROM sellers_seller sel
LEFT JOIN providers_provider prov ON prov.seller_id = sel.id
WHERE LENGTH(prov.provider_number) > 6 AND prov.provider_number NOT LIKE 'PROV-%'
UNION
SELECT sel.id as seller_id, sel.shortname, 'CUST' as type, cust.id, cust.customer_number as number
FROM sellers_seller sel
LEFT JOIN customers_customer cust ON cust.seller_id = sel.id
WHERE LENGTH(cust.customer_number) > 6 AND cust.customer_number NOT LIKE 'CUST-%'
ORDER BY seller_id, type, number;