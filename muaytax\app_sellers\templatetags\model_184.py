from datetime import date

from crispy_forms.templatetags.crispy_forms_filters import as_crispy_field
from django import template

from muaytax.app_services.models import Service

register = template.Library()


@register.simple_tag
def m184_is_in_date():
    return date.today() >= date(2023, 1, 2)

@register.simple_tag(takes_context=True)
def m184_is_contracted(context):
    user = context['user'].seller
    serv_184 = Service.objects.filter(seller=user, service_name__code='model_184')
    return serv_184

# OLD CODE
@register.simple_tag(takes_context=True)
def m184_processed(context):
    seller = context['user'].seller
    is_184_processed = seller.seller_m184.filter(
        year=date.today().year - 1,
        is_processed=True
    ).exists()

    return is_184_processed


@register.filter(name='custom_as_crispy_field')
def custom_as_crispy_field(value):
    return as_crispy_field(value, field_class='date-events')
