# General
# ------------------------------------------------------------------------------
DEBUG=True
USE_DOCKER=yes
IPYTHONDIR=/app/.ipython

# D<PERSON><PERSON>-Abble Template
# ------------------------------------------------------------------------------
ASSETS_ROOT=/static/assets

# DEEPL_API_KEY
# ------------------------------------------------------------------------------
DEEPL_API_KEY=a67c0a80-2043-472f-abe0-56dde3eaa952

# FAXPLUS
# ------------------------------------------------------------------------------
FAXPLUS_API_KEY=alohi_pat_StBT7rdKDCAweNk7iZjHXF_iUuB7THbOk7JjVxT5gCgLOMpQfbx2wZAWGl4kpJY4qSu2VoTJJr65aRUBfp
FAXPLUS_SENDING_NUMBER=+19177088651
FAXPLUS_RECEIVING_NUMBER=+16469076027 # TEST NUMBER
FAXPLUS_RECEIVING_NUMBER_US_5472=+16469076027 # TEST NUMBER
FAXPLUS_RECEIVING_NUMBER_US_7004=+16469076027 # TEST NUMBER
FAXPLUS_RECEIVING_NUMBER_US_BE15=+16469076027 # TEST NUMBER

# AEAT(HACIENDA)
# ------------------------------------------------------------------------------
URL_AEAT=https://prewww1.aeat.es/wlpl/PFTW-PICW/PresBasicaDos # TEST ENVIRONMENT
URL_AEAT_VALIDATION=https://prewww2.aeat.es/wlpl/PFTW-PICW/ServValiDos # TEST ENVIRONMENT DRAFT
PATH_CERTIFICATE=/app/config/certs/cert_digital.pfx
PASSWORD_CERTIFICATE=imYHPn7dQdJhgXLRWsKnBtQ0SoxbFrtFJ9cDuODN
PATH_VERIFY_CA=/etc/ssl/certs/ca-certificates.crt
# JWT CONFIG
MUAYTAX_JWT_SECRET_KEY=87d49146-b767-4a25-bb62-a5b03a39434a
MUAYTAX_JWT_ISSUER=muaytax.com

#RECAPTCHA
RECAPTCHA_SITE_KEY=6LfJYu0pAAAAAKPdF8tKrwHZvVTJJwpdfx9sqN7x
RECAPTCHA_SECRET_KEY=6LfJYu0pAAAAAA-5T5UElD8PfWpgz0HhPIQHuK-D

# CURRENCY_CONVERTER
CURRENCY_API_KEY=EDLuGDGqlAndVEluSmjE528FkzVy3mcmnIveNO2G

# ZOHO SIGN
ZOHO_SIGN_CLIENT_ID=1000.I9M5YF1WB8RPZNMUWBDTG0WETMSKZO
ZOHO_SIGN_CLIENT_SECRET=0118a447a071183f9a20a35576f0ff0fd1a12c9fc9
ZOHO_SIGN_TEMP_REFRESH_TOKEN=**********************************************************************

# ENCRYPTION KEY CA
ENCRYPTION_KEY=UpdNjmNssr2hBUUPcq5CW6HvnJ412BcCZPP_YOMAqu0=

# MARKETPLACE KEY DETAIL
SHOP_AUTH_MARETPLACE=1df9a9fc1895d564f2ac373fb7a4b6d0

# INVOICES_API_KEY | VIEW
INVOICES_MINDEE_CLIENT_KEY=8065472b91110e9930fa2a78892af32b

# INVOICES_GELOCATOR_GOOGLEV3_KEY=AIzaSyBm14Z4hoEkX82hc67UBs1vbNKsA1iIeRU
INVOICES_GELOCATOR_GOOGLEV3_KEY=AIzaSyAWwRwaJaKqgUYTvMYQXTltpXRa0fZzjPs

# INVOICE_API_KEY | TASKS
TASKS_MINDEE_CLIENT_KEY=8065472b91110e9930fa2a78892af32b

# TASKS_GELOCATOR_GOOGLEV3_KEY=AIzaSyBm14Z4hoEkX82hc67UBs1vbNKsA1iIeRU
GELOCATOR_GOOGLEV3_KEY=AIzaSyAWwRwaJaKqgUYTvMYQXTltpXRa0fZzjPs

# WHATSAPP API
WHATSAPP_AUTH_TOKEN=EAACDJaID38QBO3Fzr52su6WWhRkNWuuN48LfHkbPMFq8WdWvhNGqRlCWJKtZAa2y6bubbiZAuQT2jyHHEETaiSZBAiHZAVCGpmGvT9m1IVAhncmgqtazvQncyQZCBGlPX27X1azZCTiWgO6oGMkTMTjZC8ka9YksUA5o53Lb4G1LeUqiAZBlPC028ZBnRewGGIAqZA3K1PROdHPigOLPa80btQQa0NfUgH0iqRKf0ZD

# TWILIO
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=9ddf056d150e09c1467ea4b22c9b9045

# HMRC
HMRC_CLIENT_ID=3NEz2c8a3mjeT0DephNyMAUbTJHp
HMRC_CLIENT_SECRET=569dbd71-6797-4e6e-8d6d-143e7ed0b7e7
HMRC_REDIRECT_URI=http://localhost:8000/hmrc/grant-access/callback/
HMRC_URL=https://test-www.tax.service.gov.uk
HMRC_API_URL=https://test-api.service.hmrc.gov.uk
HMRC_TEST_API_URL=https://test-api.service.hmrc.gov.uk

# GOOGLE LOGIN
GOOGLE_CLIENT_ID=*************-ngbckpso8i8gp83jio7uokc7vq9o88d4.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# OPENAI
# OPENAI_API_KEY=***********************************************************************************************
OPENAI_API_KEY=********************************************************************************************************************************************************************

# AWS TEXTRACT
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=a+rY+5MHXz+51FUMAxv3UCrD9SFf9RACWckdsFUK
AWS_REGION_NAME=eu-west-2
# S3_BUCKET_NAME=textract-console-eu-west-2-5fec6ccf-136a-4842-bb08-56a65e501ba8
S3_BUCKET_NAME=muaytax-ocr-textract-temp-bucket

# VERIFACTU
VERIFACTU_SOAP_URL=https://prewww1.aeat.es/wlpl/TIKE-CONT/ws/SistemaFacturacion/VerifactuSOAP # TEST ENVIRONMENT
VERIFACTU_QR_VALIDATOR_URL=https://prewww2.aeat.es/wlpl/TIKE-CONT/ValidarQR? # TEST ENVIRONMENT

# SHOPIFY
# APP NAME = Muaytapp
SHOPIFY_CLIENT_ID=dd5a6665a9801d8e974f3298fc273004
SHOPIFY_CLIENT_SECRET=2bd3b5396b600e692c81b6adbb723f5d
SHOPIFY_REDIRECT_URI=http://localhost:8000/marketplaces/shopify/api/auth/callback/
SHOPIFY_API_WEBHOOK_URL=https://eef7-***********-71.ngrok-free.app/marketplaces/shopify/api/webhooks/ # PARA TESTEAR EN LOCAL HAY QUE CONFIGURAR UN TUNNEL CON NGROK Y MODIFICAR ESTA URL

# MIRAVIA
MIRAVIA_CLIENT_ID=510596
MIRAVIA_CLIENT_SECRET=7TsJC2By7mJRm1lEw5AZxzicxz0dFo6Q
MIRAVIA_REDIRECT_URI=https://dev.muaytax.com/miravia-callback/

# Email Configuration
DJANGO_EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# ENVIROMENT
ENVIRONMENT=local
