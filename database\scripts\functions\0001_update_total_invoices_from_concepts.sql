-- Eliminar la función si existe
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_update_total_invoices_from_concepts' ) THEN
	  DROP FUNCTION func_update_total_invoices_from_concepts;
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION
CREATE OR REPLACE FUNCTION func_update_total_invoices_from_concepts(startdate DATE, enddate DATE)
RETURNS varchar AS $$
DECLARE
    counter INTEGER := 0;
BEGIN
	-- Actualizar el totales de las facturas
	UPDATE invoices_invoice AS inv
	SET 
		-- SET EUROS
		total_amount_euros = subquery.concept_total_amount_euros,
		total_vat_euros = subquery.concept_total_vat_euros,
		total_eqtax_euros = subquery.concept_total_eqtax_euros,
		total_irpf_euros = subquery.concept_total_irpf_euros,
		total_euros = subquery.concept_total_euros,
		
		-- SET CURRENCY
		total_amount_currency = subquery.concept_total_amount_currency,
		total_vat_currency = subquery.concept_total_vat_currency,
		total_eqtax_currency = subquery.concept_total_eqtax_currency,
		total_irpf_currency = subquery.concept_total_irpf_currency,
		total_currency = subquery.concept_total_currency
	FROM 
	(
		SELECT 
			-- ID
			inv.id, 

			-- AMOUNT
			-- ROUND(inv.total_amount_euros::numeric,2) as invoice_total_amount_euros,	
			-- ROUND(inv.total_amount_currency::numeric,2) as invoice_total_amount_currency,
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.amount_euros ELSE con.amount_euros * con.quantity END)::numeric,2) as concept_total_amount_euros,			
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.amount_currency ELSE con.amount_currency * con.quantity END)::numeric,2) as concept_total_amount_currency,

			-- VAT
			-- ROUND(inv.total_vat_euros::numeric,2) as invoice_total_vat_euros,
			-- ROUND(inv.total_vat_currency::numeric,2) as invoice_total_vat_currency,	
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.vat_euros ELSE con.vat_euros * con.quantity END)::numeric,2) as concept_total_vat_euros,
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.vat_currency ELSE con.vat_currency * con.quantity END)::numeric,2) as concept_total_vat_currency,

			-- EQTAX
			-- ROUND(inv.total_eqtax_euros::numeric,2) as invoice_total_eqtax_euros, 
			-- ROUND(inv.total_eqtax_currency::numeric,2) as invoice_total_eqtax_currency, 
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.eqtax_euros ELSE con.eqtax_euros * con.quantity END)::numeric,2) as concept_total_eqtax_euros,
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.eqtax_currency ELSE con.eqtax_currency * con.quantity END)::numeric,2) as concept_total_eqtax_currency,

			-- IRPF
			-- ROUND(inv.total_irpf_euros::numeric,2) as invoice_total_irpf_euros,
			-- ROUND(inv.total_irpf_currency::numeric,2) as invoice_total_irpf_currency,
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.irpf_euros ELSE con.irpf_euros * con.quantity END)::numeric,2) as concept_total_irpf_euros,
			ROUND(SUM(CASE WHEN inv.is_txt_amz = True THEN con.irpf_currency ELSE con.irpf_currency * con.quantity END)::numeric,2) as concept_total_irpf_currency,

			-- TOTAL
			-- ROUND(inv.total_euros::numeric,2) as invoice_total_euros,
			-- ROUND(inv.total_currency::numeric,2) as invoice_total_currency,
			ROUND(SUM(con.total_euros)::numeric,2) as concept_total_euros,	
			ROUND(SUM(con.total_currency)::numeric,2) as concept_total_currency

		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		-- INNER JOIN sellers_seller sel ON inv.seller_id = sel.id
		WHERE inv.status_id = 'revised' 	
		AND inv.accounting_date >= startdate
		AND inv.accounting_date < enddate
		-- AND inv.transaction_type_id NOT LIKE '%-transfer'
		-- AND sel.shortname = 'azglobal21sl'
		-- AND inv.invoice_category_id = 'sales' AND inv.invoice_type_id = 'sales' 
		GROUP BY inv.id
	) AS subquery
	WHERE inv.id = subquery.id;

	 -- Obtener la cantidad de filas actualizadas y asignarla a la variable counter
  	GET DIAGNOSTICS counter = ROW_COUNT;

 	 -- Devolver la cantidad de filas actualizadas
    RETURN 'Updated ' || counter || ' invoices';
END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION
-- SELECT func_update_total_invoices_from_concepts('2023-01-01', '2023-04-01');