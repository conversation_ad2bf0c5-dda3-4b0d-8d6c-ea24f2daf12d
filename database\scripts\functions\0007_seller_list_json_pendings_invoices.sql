DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_seller_list_json_pending_invoices') THEN
        DROP FUNCTION func_seller_list_json_pending_invoices(date_year INTEGER);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_seller_list_json_pending_invoices(date_year INTEGER)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
    seller_name VARCHAR;
    seller_email VARCHAR;
    seller_user_name VARCHAR;
    seller_shortname VARCHAR;
    seller_last_login timestamp with time zone;
    seller_num_invoices NUMERIC;
    seller_num_pending_invoices NUMERIC;
    seller_percentaje_pending NUMERIC;

    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
    FOR inv_data IN
        SELECT
            s.id,
            s.name AS seller_name,
            s.shortname AS seller_shortname,
            u.email AS seller_email,
            u.name AS user_name,
            u.last_login AS user_last_login,
            COUNT(isl.id) AS num_invoices,
            COUNT(CASE WHEN isl.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) AS num_pending_invoices,
            ROUND(COALESCE(
                100.0 * COUNT(CASE WHEN isl.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(isl.id), 0),
                0
            ), 2) AS percentage_pending_invoices
        FROM
            sellers_seller AS s
            LEFT JOIN invoices_invoice AS isl ON (s.id = isl.seller_id AND (isl.invoice_category_id NOT LIKE '%_copy' OR isl.invoice_category_id IS NULL ))
            LEFT JOIN users_user AS u ON s.user_id = u.id
        GROUP BY s.id, s.name, s.shortname, u.email, u.name, u.last_login

    LOOP
        seller_name := inv_data.seller_name;
        seller_email := inv_data.seller_email;
        seller_user_name := inv_data.user_name;
        seller_shortname := inv_data.seller_shortname;
        seller_last_login := to_char(inv_data.user_last_login, 'YYYY-MM-DD HH24:MI:SS');
        seller_num_invoices := inv_data.num_invoices;
        seller_num_pending_invoices := inv_data.num_pending_invoices;
        seller_percentaje_pending := inv_data.percentage_pending_invoices;

        result_json := result_json || jsonb_build_object(
            'name', seller_name,
            'email', seller_email,
            'user_name', seller_user_name,
            'shortname', seller_shortname,
            'last_login',  to_char(inv_data.user_last_login, 'YYYY-MM-DD HH24:MI:SS'),
            'num_invoices', seller_num_invoices,
            'num_pending_invoices', seller_num_pending_invoices,
            'percentage_pending', seller_percentaje_pending
        );
    END LOOP;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;