import os
import uuid

from datetime import datetime
from django.db import transaction
from django.views.generic import View
from django.shortcuts import get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponseBadRequest, JsonResponse

from muaytax.app_sellers.models import Seller
from muaytax.users.permissions import IsSellerShortnamePermission

from muaytax.app_invoices.models import Invoice
from muaytax.app_invoices.tasks import process_invoices_with_ocr
from muaytax.app_invoices.forms.invoice import InvoiceCreateForm
from muaytax.app_invoices.generic.response import InvoiceResponseHandler

from muaytax.app_ocr.processors import PDFInMemoryProcessor

from muaytax.dictionaries.models import InvoiceCategory, InvoiceType

MAX_FILENAME_LENGTH = 255

class InvoiceUploderProcessView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    form_class = InvoiceCreateForm

    def post(self, request, *args, **kwargs):
        user_role = request.user.role
        seller = get_object_or_404(Seller, shortname=kwargs['shortname'])
        files = request.FILES.getlist('files')
        iae_id = request.POST.get('iae')
        document_type = request.GET.get('invoice_type', 'invoice')
        
        default_date = datetime(datetime.now().year, 12, 31)

        if not files:
            return InvoiceResponseHandler.error(code='no_files_provided')
        if not iae_id:
            return InvoiceResponseHandler.error(code='iae_not_provided')

        form_data = {'seller': seller, 'iae': iae_id, 'expedition_date': default_date}

        if document_type == 'payroll':
            form_data['invoice_type'] = InvoiceType.objects.get(code='payroll')
            form_data['invoice_category'] = InvoiceCategory.objects.get(code='expenses')

        common_form = self.form_class(data=form_data)
        if not common_form.is_valid():
            return InvoiceResponseHandler.error(code='invalid_form', errors=common_form.errors)

        if user_role != "manager":
            if not self.validate_invoice_limit(seller, files):
                return InvoiceResponseHandler.error(
                    code='invoice_limit_exceeded',
                    errors=['Se ha excedido el límite de facturas para este mes'],
                    status=413
                )

        invoice_creation_data = self.process_invoice_creation(files, form_data)

        try:
            with transaction.atomic():
                created_invoices = Invoice.objects.bulk_create(invoice_creation_data['to_ocr'] + invoice_creation_data['not_ocr'] )

            # send the invoices to the OCR processor using Celery
            if created_invoices and document_type == 'invoice':
                invoices_to_ocr_pks = [inv.pk for inv in invoice_creation_data['to_ocr']]
                transaction.on_commit(lambda: process_invoices_with_ocr.delay(invoices_to_ocr_pks))

            # Enviar la respuesta al cliente
            return self.build_response(created_invoices, invoice_creation_data['failed'], files)
        
        except Exception as e:
            print(f"Error al guardar las facturas en la base de datos: {e}")
            return InvoiceResponseHandler.error(code='invoices_not_saved', errors=[str(e)])
    
    def validate_invoice_limit(self, seller: object, files: list) -> bool:
        """Valida que el límite de facturas no sea excedido"""
        current_date = datetime.now()
        files_uploaded = len(files)
        invoice_limit = seller.get_total_limit_invoices

        if invoice_limit < 0:
            return True
        
        total_invoices = Invoice.objects.filter(
            seller=seller,
            created_at__year=current_date.year,
            created_at__month=current_date.month,
        ).exclude(invoice_category__code__icontains='_copy').exclude(is_txt_amz=True).exclude(is_generated=True).count()

        return total_invoices + files_uploaded <= invoice_limit

    def process_invoice_creation(self, files: list, form_data: dict) -> dict:
        """
        Procesa la creación de instancias de facturas solo con el campo file e IAE
        El resto de campos en blanco o default para ser llenados después por el OCR
        """
        invoices = {
            'to_ocr': [],
            'not_ocr': [],
            'failed': []
        }

        for inv_file in files:

            self._process_file_name(inv_file)
            current_form_data = form_data.copy()
            current_form_data['name'] = inv_file.name

            form = self.form_class(data=current_form_data, files={'file': inv_file})
            if not form.is_valid():
                invoices['failed'].append({
                    'file': inv_file.name,
                    'error': form.errors.get('file', ['Unknown error'])
                })
                continue

            invoice_instance = form.save(commit=False)

            if inv_file.content_type == 'application/pdf' and not self._validate_pdf(inv_file, invoices['failed']):
                invoices['not_ocr'].append(invoice_instance)
            else:
                invoices['to_ocr'].append(invoice_instance)


        return invoices

    def build_response(self, created_invoices: list , failed_invoices: list, files: list) -> JsonResponse:
        """Construye la respuesta final para la API o la vista"""

        created_invoice_data = [
            {'id': inv.pk, 'file': inv.file.name.split('/')[-1]} for inv in created_invoices
        ]

        data = {
            'total_processed': len(files),
            'invoices_created': len(created_invoices),
            'invoices_failed': len(failed_invoices),
            'invoices': {
                'created': created_invoice_data,
                'failed': failed_invoices or []
            }
        }

        if failed_invoices and created_invoices:
            return InvoiceResponseHandler.partial_success(
                data=data,
                message="Algunas facturas no se han podido procesar."
            )
        
        if failed_invoices:
            return InvoiceResponseHandler.error(code='invoices_not_created', errors=failed_invoices)
        
        return InvoiceResponseHandler.created(data=data, message="Facturas procesadas exitosamente.")

    def _validate_pdf(self, inv_file, failed_invoices):
        """Valida que el archivo PDF sea válido"""
        pdf_processor = PDFInMemoryProcessor(inv_file)
        if pdf_processor.is_encrypted():
            failed_invoices.append({
                'file': inv_file.name,
                'error': 'El archivo PDF está encriptado y no puede ser procesado. No se ha creado la factura.'
            })
            return False
        
        if pdf_processor.has_over_n_pages(10):
            failed_invoices.append({
                'file': inv_file.name,
                'error': 'El archivo PDF tiene más de 10 páginas'
            })
            return False
        
        return True

    def _process_file_name(self, inv_file):
        """
        Procesa el nombre del archivo para garantizar que siempre sea aceptado, truncándolo cuando sea necesario
        """
        safe_max_length = 100 # 255 es un valor inseguro, se ha reducido a 100 para evitar problemas. Hablado con Cristian.
        unique_id = uuid.uuid4().hex[:8]
        base_name, ext = os.path.splitext(inv_file.name)
        
        if not ext:
            ext = '.unknown'
        
        available_space = safe_max_length - len(ext) - len(unique_id) - 1
        
        if len(base_name) > available_space:
            base_name = base_name[:available_space]
        
        new_file_name = f"{base_name}_{unique_id}{ext}"
        inv_file.name = new_file_name
        
        return new_file_name

    def handle_no_permission(self):
        return HttpResponseBadRequest("No Permission")