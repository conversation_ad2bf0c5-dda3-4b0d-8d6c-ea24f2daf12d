from celery import shared_task
from datetime import timedelta
from django.utils import timezone
from muaytax.app_sellers.models import SellerVat
from muaytax.app_marketplaces.models import Marketplace, MarketplaceWizardDraft
from muaytax.app_marketplaces.webhooks.shopify.dispatcher import ShopifyWebhookDispatcher

@shared_task
def delete_expired_shopify_drafts():
    print(f"\r\033[92m**********Eliminando borradores de Shopify expirados**********\033[0m")
    delete_threshold = timezone.now() - timedelta(minutes=16) # 15 minutos porque en el modelo se establece a 15 minutos en la propiedad is_expired
    # Filtrar los borradores que no están completados y han expirado
    expired_drafts = MarketplaceWizardDraft.objects.filter(
        is_completed=False,
        created_at__lt=delete_threshold,
    )

    if not expired_drafts.exists():
        print(f"\r\033[92mNo hay borradores de Shopify expirados para eliminar.\033[0m")
        return False

    for draft in expired_drafts:
        try:
            vat_ids = draft.wizard_data.get("seller_vat_ids", [])
            print(f"\r\033[92mVAT_IDS: {vat_ids}\033[0m")
            if vat_ids:
                SellerVat.objects.filter(id__in=vat_ids).delete()
            draft.delete()
            print(f"\r\033[92mSe ha eliminado el borrador de Shopify: {str(draft.wizard_id)[:6]}... con {len(vat_ids)} IDs de IVA\033[0m")
        except Exception as e:
            print(f"\r\033[91mError al eliminar el borrador de Shopify: {e}\033[0m")

    print(f"\r\033[92m**********Se han eliminado borradores de Shopify expirados**********\033[0m")

    return True

@shared_task(queue='invoice_processing')
def process_shopify_webhook_async(topic: str, domain: str, raw_body: str, headers: dict):
    print(f"\033[92m\r\n[SH-WEBHOOK]: Procesando webhook para el dominio {domain} y topic {topic}\033[0m")
    try:
        store = Marketplace.objects.get(shopname=domain)
        dispatcher = ShopifyWebhookDispatcher(
            topic=topic,
            domain=domain,
            raw_body=raw_body.encode("utf-8"),
            headers=headers,
            store=store
        )
        return dispatcher.dispatch()
    except Exception as e:
        # Aquí puedes loggear el error a un sistema de monitoreo como Sentry
        return f"\033[91mError en procesamiento async del webhook: {e}\033[0m"
