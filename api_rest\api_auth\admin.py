from django.contrib import admin

from api_rest.api_auth.models import PublicAccessCodeToken

class PublicAccessCodeTokenAdmin(admin.ModelAdmin):
    list_display = ('email', 'date_expired', 'code', 'is_active')
    search_fields = ('email', 'code')
    list_filter = ('is_active', 'date_expired')
    readonly_fields = ('private_token',)

admin.site.register(PublicAccessCodeToken, PublicAccessCodeTokenAdmin)