from django.core.management.base import BaseCommand

from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.dictionaries.models.economic_activity import EconomicActivity


class Command(BaseCommand):
    help = "Copy epigraph data from seller to contracted sellervat."

    def handle(self, *args, **options):
        sellers = Seller.objects.all()
        for seller in sellers:
            seller_iae_list = seller.seller_iae.all()
            sellervats = seller.vat_seller.filter(is_contracted=True)
            for svat in sellervats:
                for iae in seller_iae_list:
                    new_iae = EconomicActivity.objects.filter(
                        code__startswith=svat.vat_country.iso_code,
                        code__endswith=iae.code,
                    ).first()
                    if new_iae:
                        activity, created = SellerVatActivity.objects.get_or_create(sellervat=svat,
                                                                                    sellervat_activity_iae=new_iae)
                        if created:
                            self.stdout.write(
                                self.style.SUCCESS(
                                    'Created epigraph for seller: %s with vatseller %s and epigraph: %s\n' % (
                                        seller, svat, new_iae)
                                )
                            )
