/* =======================================================
  EXCLUSIVOS PARA MÓDULOS:  
    >  country_migrations (formIVA)
    >  country_documents (formIVA)
======================================================= */

/* Línea verde base para los tabs */
.form-iva-nav-tabs {
  position: relative;
  border-bottom: 1px solid #00AD65;
  display: flex;
  flex-wrap: wrap;
}

/* Tabs: estilo base */
.form-iva-nav-tabs .nav-link {
  border: 1px solid transparent;
  border-radius: 20px 20px 0 0;
  background-color: #F7FAFF;
  color: #000;
  font-weight: 500;
  padding: 0.6rem 1.2rem;
  margin-right: 2px;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Tab activo: se sobrepone al borde inferior */
.form-iva-nav-tabs .nav-link.active {
  background-color: #D9FFEE;
  border-color: #00AD65 #00AD65 transparent #00AD65;
  z-index: 2;
}

/* Hover en tabs inactivos: leve verde claro */
.form-iva-nav-tabs .nav-link:not(.active):hover {
  background-color: #E3FCEF; /* tono verdoso leve */
  color: #000;
}

/* Hover en tab activo: leve oscurecimiento del verde */
.form-iva-nav-tabs .nav-link.active:hover {
  background-color: #BFF6DD; /* verde ligeramente más oscuro que #D9FFEE */
}

@keyframes pulse-tab {
  0% { background-color: #D9FFEE; }
  50% { background-color: #BFF6DD; }
  100% { background-color: #D9FFEE; }
}

.form-iva-nav-tabs .nav-link.active:hover {
  animation: pulse-tab 1.5s ease-in-out infinite;
}

/* Tab activo: línea verde debajo */
.form-iva-nav-tabs .nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #D9FFEE; /* Igual que el fondo del tab activo */
  z-index: 3;
}

/* Tab hover: cambia el fondo y el color del texto */
.tab-pane.form-iva-tab-pane {
  border: none; /* eliminar borde */
  background-color: transparent; /* igual al fondo del body/content */
  padding: 1.5rem;
  border-radius: 0;
  box-shadow: none; /* elimina sombra*/
}

/* Tab hover: cambia el fondo y el color del texto */
#migrationTabContent,
#documentsTabContent {
  box-shadow: none !important;
  filter: none !important;
  background-color: transparent !important;
  border: none !important;
}

[id^="check-migration-icon-"]:focus,
[id^="check-documents-icon-"]:focus {
  outline: none !important;
}

.form-iva-nav-tabs .nav-link:focus {
  outline: none !important;
}

.form-iva-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  font-size: 0.85rem;
  z-index: 5;
  cursor: pointer;
  user-select: none;
}

.form-iva-arrow.left {
  left: -50px; /* ajusta según ancho de tu layout */
}

.form-iva-arrow.right {
  right: -50px;
}

.arrow-icon {
  font-size: 2rem;
  font-weight: bold;
  color: #00AD65;
  transition: transform 0.3s ease;
}

.arrow-icon:hover {
  transform: scale(1.15);
}

.arrow-label {
  margin-top: 0.25rem;
  color: #333;
}

.flag-icon-small {
  width: 16px;
  height: auto;
  vertical-align: middle;
  margin-left: 6px;
}

/* === Estilo general para todos los campos multifile === */
.formIVA-multifile-upload-wrapper {
  border: 2px solid #9bb4d1;
  border-radius: 10px;
  padding: 0.6rem 1rem;
  font-size: 1rem;
  color: #333;
  background-color: #fff;
  cursor: pointer;
  width: 100%;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.formIVA-multifile-upload-wrapper:hover {
  border-color: #6fa4e8;
}

.formIVA-multifile-upload-wrapper:focus-within {
  box-shadow: 0 0 0 0.18rem rgba(86, 154, 231, 0.2);
  border-color: #569ae7;
}

/* Placeholder dentro del recuadro */
.placeholder-text {
  color: #999;
}

/* Estilo para la lista de archivos cargados */
.selected-file-pill {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid #9bb4d1;
  border-radius: 8px;
  background-color: #f8f9fc;
  margin-bottom: 4px;
  font-size: 0.95rem;
}

.selected-file-pill span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90%;
}

.selected-file-pill button {
  border: none;
  background: transparent;
  color: #dc3545;
  cursor: pointer;
}

.business_proof_icon_wrapper.delete_icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
}

.business_proof_icon_wrapper.delete_icon:hover {
    transform: scale(1.1);
}

.icon_business_proof_delete_file {
    display: block;
    height: 28px;
    width: 28px;
}

.formIVA-multifile-upload-wrapper.disabled {
    pointer-events: none;
    background-color: #f8d7da; /* rojo muy suave */
    border: 2px dashed #dc3545;
    opacity: 0.7;
    cursor: not-allowed;
}

.formIVA-multifile-upload-wrapper.disabled .placeholder-text {
    color: #dc3545;
    font-weight: bold;
}

textarea.error-coment,
input.error-coment {
    border: 1px solid red !important;
}

.input-error-border {
    border: 1px solid #dc3545 !important;
    border-radius: 4px;
    padding: 8px;
}


/* =======================================================
  EXCLUSIVOS PARA MÓDULOS:  
    >  Partners (formIVA)
======================================================= */

/* Estilos generales */
.missing-data { color: red; font-weight: bold; }

/* Estilos de la tabla */
.table-partners {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 2px solid #CBD5E0;
  border-radius: 10px;
  overflow: hidden;
}

.table-partners thead tr {
  background-color: #F2F6FF;
  height: 36px;
}

.table-partners thead th {
  text-align: left;
  padding: 8px 12px;
  border-bottom: 2px solid #CBD5E0;
  font-weight: 600;
  white-space: nowrap;
}

.table-partners tbody tr {
    height: 40px;
}

.table-partners tbody td {
  padding: 6px 12px;
  border-bottom: 2px solid #CBD5E0;
  white-space: nowrap;
  font-weight: 500;
}

/* Estilos específicos de las columnas */
.table-partners .col-name { 
  width: 70%;
  min-width: 100px;
  text-align: center;
  white-space: nowrap;
  padding: 6px 6px;
}

.table-partners .col-icons { 
  width: 10%; 
  text-align: center; 
}
.table-partners .col-buttons {
  width: 15%;
  text-align: right;
  padding-right: 60px; /* Asegura alineación con margen derecho */
}
/* Efecto hover en filas de la tabla */
.table-partners tbody tr:hover {
  background-color: #F8FAFC; /* Un gris claro para resaltar la fila */
  transition: background-color 0.2s ease-in-out;
}

/* Contenedor de iconos */
.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

/* Botones */
.btn-dark-blue, .btn-green, .btn-dark-gray {
  height: 36px;
  padding: 6px 12px;
  min-width: 80px;
  border-radius: 50px;
  font-weight: 700;
  border: solid 2px;
  transition: all 0.2s ease;
}

.btn-dark-blue {
  background-color: var(--color-black-blue);
  color: white;
  border-color: var(--color-black-blue);
}
.btn-dark-blue:hover { background-color: white !important; color: var(--color-black-blue) !important; }

.btn-green {
  background-color: var(--color-green-dark);
  color: white;
  border-color: var(--color-green-dark);
}
.btn-green:hover { background-color: white !important; color: var(--color-green-dark) !important; }

.btn-dark-gray {
  background-color: var(--color-black-gray);
  color: white;
  border-color: var(--color-black-gray);
}
.btn-dark-gray:hover { background-color: white !important; color: var(--color-black-gray) !important; border-color: var(--color-black-gray);}

/* Estilos del botón "Editar" */
.edit-partner-btn {
  height: 30px;
  min-width: 30px;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Columna del checkbox circular */
.table-partners .col-radio {
  width: 40px;
  padding-left: 10px;
  text-align: center;
  vertical-align: middle;
}

#addPartnerModal .custom-close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  color: #000;
  opacity: 0.6;
  cursor: pointer;
}

#addPartnerModal .custom-close-button:hover {
  opacity: 1;
}

input.readonly-checkbox {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.8;
}
/* =======================================================
  EXCLUSIVOS PARA MÓDULOS:  
    >  Summary (formIVA)
======================================================= */

#top-summary-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

#top-summary-row > .col-5 {
  display: flex;
}

#top-summary-row .card {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

#top-summary-row .card-body {
  flex-grow: 1;
}

#country-cards-row {
  display: flex;
  flex-wrap: wrap;
}

#country-cards-row > .col-md-6 {
  display: flex;
}

#country-cards-row .card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

#country-cards-row .card-body {
  flex-grow: 1;
}

.card-header.bg-light {
  background-color: #f6f9fe !important;
  padding: 0.8rem 1.2rem !important;  /* más estrecho */
  border-bottom: none !important;
}

/* =======================================================
  EXCLUSIVOS PARA MÓDULOS:  
    >  Company (formIVA)
======================================================= */

/* === Wrapper General === */
.amazon_icon_wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px;
    margin: 0 3px;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease, background-color 0.2s ease;
}

/* ==== Icono de editar ==== */
.amazon_icon_wrapper.edit_icon {
    border: 2px solid #031549;
    background-color: rgba(3, 21, 73, 0.05);
}

.amazon_icon_wrapper.edit_icon:hover {
    transform: scale(1.1);
    background-color: rgba(3, 21, 73, 0.12);
}

.amazon_icon_wrapper.edit_icon:hover .icon_amazon_edit_token path {
    stroke: #007bff;
}

/* ==== Icono de eliminar ==== */
.amazon_icon_wrapper.delete_icon {
    border: 2px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.05);
}

.amazon_icon_wrapper.delete_icon:hover {
    transform: scale(1.1);
    background-color: rgba(220, 53, 69, 0.15);
}

.amazon_icon_wrapper.delete_icon:hover .icon_amazon_delete_token path {
    stroke: #dc3545;
}


#merchant_token_table_wrapper table td,
#merchant_token_table_wrapper table th {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    vertical-align: middle !important; /* Alinea bien el contenido verticalmente */
}

#merchant_token_table_wrapper table {
    font-size: 0.875rem; /* Opcional: más pequeño */
}

.amazon_icon_wrapper svg {
    max-height: 20px;
}

.table td.truncate-cell {
    max-width: 180px;         /* ajusta según preferencia */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}
