import random
import jwt
from datetime import timedelta
from secrets import token_hex
from django.conf import settings


from django.db import models
from django.utils import timezone
from muaytax.utils.mixins import CustomTimeStampedModel

class PublicAccessCodeToken(CustomTimeStampedModel):
    email = models.EmailField()
    date_expired = models.DateTimeField(help_text="Tiempo de expiración del código usado para obtener el token de acceso")
    code = models.Char<PERSON>ield(max_length=6)
    private_token = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True, help_text="Indica si el token de acceso está activo")


    def is_expired(self):
        return self.date_expired < timezone.now()
    
    @classmethod
    def generate_token(cls, email=None):

        existing_token = cls.objects.filter(email=email, is_active=True).first()
        if existing_token:
            existing_token.delete()

        expiration_time = timezone.now() + timedelta(hours=1)
        toke_id = token_hex(8)

        payload = {
            "id": toke_id,
            "email": email,
            "exp": expiration_time.timestamp(),
            "iss": settings.SIMPLE_JWT['ISSUER']
        }

        jwt_token = jwt.encode(payload, settings.SIMPLE_JWT['SIGNING_KEY'], algorithm='HS256')

        public_access_token = PublicAccessCodeToken(
            email=email,
            created_at=timezone.now(),
            date_expired=timezone.now() + timedelta(minutes=5),
            private_token=jwt_token,
            code=("%s" % random.randint(1, 999999)).zfill(6)
        )

        public_access_token.save()
        return public_access_token
    
    def update_date_expired(self):
        self.date_expired = timezone.now()
        self.save()

    class Meta:
        db_table = "api_auth_public_access_code_token"
        verbose_name = "Token para acceso público"
        verbose_name_plural = "Tokens para acceso público"

    def __str__(self):
        return f'Public-Access-Token {self.id} para {self.email}'