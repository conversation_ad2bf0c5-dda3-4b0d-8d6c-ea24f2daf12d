import json
import requests
from datetime import datetime
from django.utils import timezone
import os

from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import  HttpResponseRedirect, JsonResponse
from django.urls import reverse
from django.views import View
from django.views.generic import ListView

from muaytax.app_hmrc.controller import HmrcApiService
from muaytax.app_hmrc.generic.response import ErrorResponseHMRC, HMRCException
from muaytax.app_hmrc.models import HMRCAuthorization, HMRCToken

from muaytax.app_sellers.models import Seller
from muaytax.app_sellers.models import SellerVat
from muaytax.app_documents.models import PresentedModel
from muaytax.app_documents.controllers import UpdateModelDirectPresentation

from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission, SellerHasGBVatNumber


class HmrcDashboardView(LoginRequiredMixin, (IsManagerRolePermission and SellerHasGBVatNumber), ListView):
    model = Seller
    template_name = "hmrc/dashboard-hmrc.html"

    def get_template_names(self):
        names = super().get_template_names()
        seller = Seller.objects.get(shortname=self.kwargs.get("shortname"))

        hmrc_access = HMRCAuthorization.objects.filter(seller=seller).first()
        if not hmrc_access or not hmrc_access.is_access_granted:
            names = ["hmrc/dashboard-hmrc-locked.html"]
        return names

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        seller = Seller.objects.get(shortname=self.kwargs.get("shortname"))
        context["seller"] = seller

        vrn = SellerVat.objects.filter(seller=seller, vat_country__iso_code='GB').first()
        if vrn:
            context["vat_number"] = vrn
            context["vrn"] = self.get_clean_vrn(vrn.vat_number)

        context["auth_hmrc"] = HMRCAuthorization.objects.filter(seller=seller).first()

        vat_return_models = PresentedModel.objects.filter(
            seller=seller,
            model__code='GB-VAT-PROOF',
            status__code__in=['pending', 'agreed']
        )
        context["vat_return_models"] = vat_return_models

        return context
    
    def get_clean_vrn(self, vrn: str) -> str:
        return vrn.replace(' ', '').replace('GB', '').upper()
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
    
class SendVatReturnView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    """View to send the VAT return to HMRC."""

    def post(self, request, *args, **kwargs):
        if not self.is_confirmation_valid(request):
            return ErrorResponseHMRC(
                code="CONFIRMATION_MISSING",
                title="Falta confirmación",
                message="La casilla de confirmación es obligatoria.",
                status=403
            )
        
        seller, presentedmodel, vrn = self.get_seller_vrn_and_model(kwargs)
        if not seller or not presentedmodel or not vrn:
            return ErrorResponseHMRC(
                code="BAD_REQUEST",
                title="Error en la petición",
                message="Uno o más parámetros no son válidos.",
                status=400
            )
        
        vendor_ip = self._get_vendor_ip()
        fraud_prev_headers = self._get_fraud_prevention_headers(request, vendor_ip)
        hmrc = HmrcApiService(
            shortname=seller.shortname,
            fph=fraud_prev_headers
        )

        # if not hmrc.validate_fraud_prevention_headers(fraud_prev_headers):
        #     return ErrorResponseHMRC(
        #         code="INVALID_HEADERS",
        #         title="Cabeceras inválidas",
        #         message="La petición no ha pasado la validación de cabeceras. Contacte con IT.",
        #         status=400
        #     )
        
        auth_hmrc = HMRCAuthorization.objects.filter(seller=seller).first()
        if not self.is_authorization_valid(auth_hmrc):
            return ErrorResponseHMRC(
                code="AUTHORIZATION_ERROR",
                title="Error de autorización",
                message="El acceso a HMRC no está autorizado o ha expirado. Comuníquese con IT para renovar el acceso.",
                status=400
            )
        
        # get a valid access token. The token is refreshed if it is expired, and the new token is saved in the database.
        try:
            access_token = hmrc.get_valid_access_token(auth_hmrc)
        except ValueError as e:
            errors = e.args[0]
            return ErrorResponseHMRC(
                code=errors['code'],
                title=errors['title'],
                message=errors['message'],
                status=errors['status']
            )
        
        try:
            vat_data = self.prepare_vat_return_data(presentedmodel)
            # vat_data = {
            #     'periodKey': '22A1',
            #     'vatDueSales': '0.00',
            #     'vatDueAcquisitions': '0.00',
            #     'totalVatDue': '0.00',
            #     'vatReclaimedCurrPeriod': '0.00',
            #     'netVatDue': '0.00',
            #     'totalValueSalesExVAT': '0.00',
            #     'totalValuePurchasesExVAT': '0.0',
            #     'totalValueGoodsSuppliedExVAT': '0',
            #     'totalAcquisitionsExVAT': '0',
            #     'finalised': True
            # }

        except ValueError as e:
            errors = e.args[0]
            return ErrorResponseHMRC(
                code="JSON_PDF_ERROR",
                title="Error en el JSON PDF",
                message=errors,
                status=400
            )

        try:
            hmrc_response = hmrc.vat_return(access_token, vat_data, vrn)
        except HMRCException as e:
            error_data = e.error_data
            return JsonResponse(
                error_data,
                status=400
            )
        
        presentedmodel.presentation_response = hmrc_response
        presentedmodel.save()
        json_pdf_updated = self._update_json_pdf(
            model=presentedmodel,
            hmrc_response=hmrc_response,
            vrn=vrn,
            period_key=vat_data['periodKey'],
        )

        UpdateModel = UpdateModelDirectPresentation(presentedmodel, json_pdf_updated)
        UpdateModel.update()
        
        return JsonResponse({
            "file_path": presentedmodel.get_file_url(),
        }, status=200)
    
    def is_confirmation_valid(self, request):
        """Check if the confirmation checkbox is checked."""
        return request.POST.get('confirmation') == "true"
    
    def is_authorization_valid(self, auth_hmrc: HMRCToken):
        """Check if HMRC authorization is valid."""
        if os.environ.get('ENVIRONMENT') != 'production':
            return True
        return auth_hmrc and auth_hmrc.is_access_granted
    
    def get_seller_vrn_and_model(self, kwargs) -> tuple:
        try:
            seller = Seller.objects.get(shortname=kwargs.get('shortname'))
            model = PresentedModel.objects.get(id=kwargs.get('model_id'))

            vat_number = SellerVat.objects.filter(seller=seller, vat_country__iso_code='GB').first()
            if not vat_number:
                return None, None, None

            vrn = vat_number.vat_number.replace('GB', '') if vat_number.vat_number.startswith('GB') else vat_number.vat_number
            return seller, model, vrn

        except Seller.DoesNotExist or PresentedModel.DoesNotExist:
            return None, None, None

    def prepare_vat_return_data(self, model: PresentedModel) -> dict:
        """Prepare VAT return data to send to HMRC."""

        if not model.json_pdf:
            raise ValueError("El modelo no tiene un JSON válido.")
        
        try:
            json_pdf = json.loads(model.json_pdf)

            float_fields = {
                'BOX_1': 'vatDueSales',
                'BOX_2': 'vatDueAcquisitions',
                'BOX_3': 'totalVatDue',
                'BOX_4': 'vatReclaimedCurrPeriod',
                'BOX_5': 'netVatDue'
            }

            int_fields = {
                'BOX_6': 'totalValueSalesExVAT',
                'BOX_7': 'totalValuePurchasesExVAT',
                'BOX_8': 'totalValueGoodsSuppliedExVAT',
                'BOX_9': 'totalAcquisitionsExVAT'
            }

            date_fields = {
                'DATE_FROM': 'date_from',
                'DATE_TO': 'date_to'
            }

            vat_data = {}

            def convert_value(value, to_type, json_key):
                try:
                    if to_type == 'float':
                        return abs(float(value.replace(',', '.')))
                    elif to_type == 'int':
                        return abs(int(value))
                except ValueError:
                    raise ValueError(f"Valor inválido en el JSON PDF para {json_key}, se esperaba un {to_type}.")

            # Proccess float fields
            for json_key, vat_key in float_fields.items():
                value = json_pdf.get(json_key)
                if value is None:
                    raise ValueError(f"Falta un campo requerido en el JSON PDF para {json_key}.")
                vat_data[vat_key] = convert_value(value, 'float', json_key)

            # Process int fields
            for json_key, vat_key in int_fields.items():
                value = json_pdf.get(json_key)
                if value is None:
                    raise ValueError(f"Falta un campo requerido en el JSON PDF para {json_key}.")
                vat_data[vat_key] = convert_value(value, 'int', json_key)

            vat_data.update({
                'periodKey': json_pdf.get('PERIOD_KEY', ''),
                'finalised': True
            })

            return vat_data

        except ValueError as e:
            raise ValueError(e.args[0])
        
        except json.JSONDecodeError:
            raise ValueError("El JSON PDF no es válido.")
        
        except KeyError:
            raise ValueError("El JSON PDF no contiene todos los campos necesarios.")
        
        except Exception as e:
            raise ValueError("Ha ocurrido un error al procesar el JSON PDF.")

    def _get_fraud_prevention_headers(self, request, vendor_ip):
        """Extract fraud prevention headers from the request."""
        client_ip = request.META.get('HTTP_GOV_CLIENT_PUBLIC_IP', '')

        headers = {}
        headers['Gov-Client-Connection-Method'] = 'WEB_APP_VIA_SERVER'
        headers['Gov-Client-Browser-JS-User-Agent'] = request.META.get('HTTP_USER_AGENT', '')
        headers['Gov-Client-Device-id'] = request.META.get('HTTP_GOV_CLIENT_DEVICE_ID', '')
        headers['Gov-Client-Public-IP'] = client_ip
        headers['Gov-Client-Public-IP-Timestamp'] = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        # headers['Gov-Client-Public-Port'] = request.META.get('SERVER_PORT', '')
        headers['Gov-Client-Public-Port'] = str(self._get_random_port_number())
        headers['Gov-Client-Screens'] = request.META.get('HTTP_GOV_CLIENT_SCREENS', '')
        headers['Gov-Client-Timezone'] = request.META.get('HTTP_GOV_CLIENT_TIMEZONE', '')
        headers['Gov-Client-User-IDs'] = f'username={request.user.username}'
        headers['Gov-Client-Window-Size'] = request.META.get('HTTP_GOV_CLIENT_WINDOW_SIZE', '')
        headers['Gov-Vendor-Forwarded'] = f'by={vendor_ip}&for={client_ip}'
        headers['Gov-Vendor-Product-Name'] = 'Muaytapp'
        headers['Gov-Vendor-Public-Ip'] = vendor_ip
        headers['Gov-Vendor-Version'] = 'muaytapp=1.0.0'
        # headers['gov-client-multi-factor'] = 'type=AUTH_CODE&timestamp=2024-09-22T13%3A23Z&unique-reference=fc4b5fd6816f75a7c81fc8eaa9499d6a299bd803397166e8c4cf9280b801d62c'
        # headers['Gov-Vendor-License-IDs'] = 'muaytapp=8D7963490527D33716835EE7C195516D5E562E03B224E9B359836466EE40CDE1'

        return headers
    
    def _get_random_port_number(self):
        """Get a random port number between 1 and 65535."""
        import random
        random_port = random.randint(1, 65535)
        if random_port == 443 or random_port == 80:
            random_port = random_port - 1

        return random_port

    def _get_vendor_ip(self):
        """Get the vendor public IP."""
        try:
            response = requests.get('https://api.ipify.org?format=json')
            response.raise_for_status()
            public_ip = response.json().get('ip')
            return public_ip
        except requests.exceptions.RequestException:
            return None

    def _update_json_pdf(self, model: PresentedModel, hmrc_response: dict, *args, **kwargs)-> dict:
        """Update the JSON PDF in the model with the response from HMRC."""
        old_json = json.loads(model.json_pdf)
        seller_address = model.seller.seller_address.address if model.seller.seller_address else ''

        period_key = kwargs.get('period_key', '')

        processing_date = datetime.strptime(hmrc_response.get("processingDate", ""), "%Y-%m-%dT%H:%M:%S.%fZ")
        
        vrn = kwargs.get('vrn', '')

        full_json = {
            "PERIOD_KEY": period_key,
            "CORRELATION_ID": hmrc_response.get("X-CorrelationId", ""),
            "RECEIPT_ID": hmrc_response.get("Receipt-Id", ""),
            "RECEIPT_TIMESTAMP": processing_date.strftime("%d/%m/%Y, %H:%M:%S"),
            "PROCESSING_DATE": processing_date.strftime("%d/%m/%Y, %H:%M:%S"),
            "PAYMENT_ID": hmrc_response.get("paymentIndicator", ""),
            "FORM_BUNDLE_NUMBER": hmrc_response.get("formBundleNumber", ""),
            "VRN": vrn,
            "MODE": "Accounting",
            "SUBMITTED": "An agent",
            "BUSINESS_ADDRESS": f'{seller_address}',
            "SCHEME_TYPE": "Standard",
            "GENERATED_AT": timezone.now().strftime("%d/%m/%Y, %H:%M:%S"),
        }

        updated_json = {**old_json, **full_json}

        return updated_json