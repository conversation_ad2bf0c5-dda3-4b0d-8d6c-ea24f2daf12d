from django.db import models
from muaytax.utils.mixins import CustomTimeStampedModel

class GuestUser(CustomTimeStampedModel):
    name = models.CharField(
        blank=True,
        null=True,
        max_length=255,
        verbose_name="Nombres"
    )

    last_name = models.Char<PERSON>ield(
        blank=True,
        null=True,
        max_length=255,
        verbose_name="Apellidos"
    )
    
    email = models.EmailField(
        blank=True,
        null=True,
        max_length=255,
        verbose_name="Correo electrónico"
    )
    
    phone = models.CharField(
        blank=True,
        null=True,
        max_length=255,
        verbose_name="Teléfono"
    )
    
    class Meta:
        verbose_name = "Usuario invitado para citas"
        verbose_name_plural = "Usuarios invitados para cita"
    
    def __str__(self):
        return self.get_full_name
    
    @property
    def get_full_name(self):
        last_name = self.last_name if self.last_name else ""
        return f"{self.name} {last_name}"