DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_invoicelist_sellervatFR') THEN
    DROP FUNCTION func_invoicelist_sellervatFR(first_date_raw VARCHAR, last_date_raw VARCHAR, manager_assigned INT, search VARCHAR);
  END IF;
END $$;

CREATE OR REPLACE FUNCTION func_invoicelist_sellervatFR(
    first_date_raw VARCHAR,
    last_date_raw VARCHAR,
    manager_assigned INT,
    search VARCHAR
)
RETURNS jsonb AS $$
DECLARE
    result_json jsonb := '[]';
    first_date DATE;
    last_date DATE;
BEGIN
    first_date := TO_DATE(first_date_raw, 'YYYY-MM-DD');
    last_date := TO_DATE(last_date_raw, 'YYYY-MM-DD');

    SELECT jsonb_agg(sub_data) INTO result_json
    FROM (
        SELECT 
            UPPER(sel.name) AS BUSINESS_NAME,

            -- EXPORTS COMMOM (export-sale, export-refund)
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('export-sale', 'export-refund') 
                      AND inv.tax_country_id = 'FR'
                      AND inv.invoice_category_id = 'sales'
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('export-sale', 'export-refund') 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros 
                  ELSE 0 
                END)::NUMERIC, 0), 2
            ) AS EXPORTS,

            -- B2B INTRACOMMUNITY (intra-community-sale, intra-community-refund)
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('intra-community-sale', 'intra-community-refund') 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('intra-community-sale', 'intra-community-refund') 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros 
                  ELSE 0 
              END)::NUMERIC, 0), 2
            ) AS B2B_INTRACOM,

            -- FRANCE HT (20%) AMOUNT VAT (local-sale, local-refund)
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 20 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 20 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros 
                  ELSE 0 
              END)::NUMERIC, 0), 2
            ) AS FRANCE_HT_20,

            -- FRANCE HT (5.5%) AMOUNT VAT (local-sale, local-refund)
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 5.5 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 5.5 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE
                      AND inv.status_id = 'revised'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros 
                  ELSE 0 
              END)::NUMERIC, 0), 2
            ) AS FRANCE_HT_5_5,

            -- FRANCE HT (0%) AMOUNT VAT (local-sale, local-refund)
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 0 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE 
                      AND inv.status_id = 'revised'
                      AND (inv.customer_id IS NULL OR cus.country_id IS NULL OR cus.country_id != 'FR' OR cus.customer_type_id IS NULL OR cus.customer_type_id != 'B2B')
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 0 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE 
                      AND inv.status_id = 'revised'
                      AND (inv.customer_id IS NULL OR cus.country_id IS NULL OR cus.country_id != 'FR' OR cus.customer_type_id IS NULL OR cus.customer_type_id != 'B2B')
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros 
                  ELSE 0 
              END)::NUMERIC, 0), 2
            ) AS FRANCE_HT_0,

            -- ART_194_EU: FRANCE 0% VAT (local-sale, local-refund)
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 0 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE 
                      AND inv.status_id = 'revised'
                      AND inv.customer_id IS NOT NULL
                      AND cus.country_id = 'FR'
                      AND cus.customer_type_id = 'B2B'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.transaction_type_id IN ('local-sale', 'local-refund') 
                      AND con.vat = 0 
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE 
                      AND inv.status_id = 'revised'
                      AND inv.customer_id IS NOT NULL
                      AND cus.country_id = 'FR'
                      AND cus.customer_type_id = 'B2B'
                      AND (inv.tax_responsibility_id IS NULL OR inv.tax_responsibility_id != 'marketplace')
                  THEN con.amount_euros 
                  ELSE 0 
              END)::NUMERIC, 0), 2
            ) AS ART_194_EU,

            -- AMZ_TAX_RESP:  B.I de todas las ventas en las que amazon sea el responsable 
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE 
                      AND inv.status_id = 'revised'
                      AND inv.tax_responsibility_id = 'marketplace'
                      AND inv.transaction_type_id NOT IN ('export-sale', 'export-refund')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE 
                      AND inv.status_id = 'revised'
                      AND inv.tax_responsibility_id = 'marketplace'
                      AND inv.transaction_type_id NOT IN ('export-sale', 'export-refund')
                  THEN con.amount_euros 
                  ELSE 0 
              END)::NUMERIC, 0), 2
            ) AS AMZ_TAX_RESP,

            -- AMZ_TAX_RESP_EXPORT:  B.I de todas las ventas en las que amazon sea el responsable 
            ROUND(
              COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS NOT TRUE 
                      AND inv.status_id = 'revised'
                      AND inv.tax_responsibility_id = 'marketplace'
                      AND inv.transaction_type_id IN ('export-sale', 'export-refund')
                  THEN con.amount_euros * con.quantity 
                  ELSE 0 
              END)::NUMERIC, 0) 
              + COALESCE(SUM(CASE 
                  WHEN inv.accounting_date >= first_date 
                      AND inv.accounting_date < last_date
                      AND inv.tax_country_id = 'FR' 
                      AND inv.invoice_category_id = 'sales' 
                      AND inv.is_generated_amz IS NOT TRUE 
                      AND inv.is_txt_amz IS TRUE 
                      AND inv.status_id = 'revised'
                      AND inv.tax_responsibility_id = 'marketplace'
                      AND inv.transaction_type_id IN ('export-sale', 'export-refund')
                  THEN con.amount_euros 
                  ELSE 0 
              END)::NUMERIC, 0), 2
            ) AS AMZ_TAX_RESP_EXPORT

        FROM 
            sellers_seller sel
        LEFT JOIN 
            sellers_sellervat sv ON sel.id = sv.seller_id
        INNER JOIN 
            users_user usr ON sel.user_id = usr.id
        LEFT JOIN 
            invoices_invoice inv ON sel.id = inv.seller_id
        LEFT JOIN 
            invoices_concept con ON inv.id = con.invoice_id
        LEFT JOIN
            customers_customer cus ON inv.customer_id = cus.id

        WHERE (
						sv.vat_country_id = 'FR'  AND (
							(
								sv.activation_date IS NOT NULL AND
								sv.activation_date < last_date AND
								( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_date)
							) OR  (
								sv.activation_date IS NULL AND
								sv.contracting_date IS NOT NULL AND
								sv.contracting_date <= last_date AND
								( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_date)
							)
						)
            AND con.is_supplied IS NOT true
            AND (manager_assigned = 0 OR sv.manager_assigned_id = manager_assigned)
            AND (
                search = '' OR
                sel.name ILIKE '%' || search || '%' OR
                usr.name ILIKE '%' || search || '%' OR
                usr.email ILIKE '%' || search || '%'
            )
					)
        
        GROUP BY 
            sel.name

    ) AS sub_data;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

--TEST
-- SELECT * FROM func_invoicelist_sellervatFR('2023-04-01', '2023-07-01',0, '');