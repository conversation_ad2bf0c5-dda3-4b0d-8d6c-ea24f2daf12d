import uuid

from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
from django.http import HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.views import View
from django.views.generic import CreateView
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from urllib.parse import urlencode

from muaytax.app_sellers.models.seller import Seller
from muaytax.app_marketplaces.apis import ShopifyApiService, verify_hmac_signature, decode_state, validate_shopname
from muaytax.app_marketplaces.models.marketplace import Marketplace
from muaytax.app_marketplaces.models.wizard import MarketplaceWizardDraft
from muaytax.app_marketplaces.forms.marketplace import MarketplaceChangeForm
from muaytax.dictionaries.models.marketplaces import Marketplaces
from muaytax.users.permissions import IsSellerShortnamePermission, IsSellerRolePermission

class MiraviaAddStore(LoginRequiredMixin, (IsSellerShortnamePermission and IsSellerRolePermission), CreateView):
    model = Marketplace
    template_name = "marketplaces/miravia/miravia_add_store.html"
    context_object_name = "marketplace"
    form_class = MarketplaceChangeForm
    success_url = "/sellers/<shortname>/marketplaces/"

    def get(self, request, *args, **kwargs):
        """
        Vista para crear una nueva tienda de Miravia.
        Se crea un ID de wizard si no existe y se establece como cookie.
        Si el paso es "success", significa que la tienda se ha creado correctament y la petición viene de la vista de callback de Miravia.
        Con lo cual no se establece la cookie.
        """
        is_success = request.GET.get("step") == "success"
        response = super().get(request, *args, **kwargs)

        if is_success:
            return response
        
        # Si el paso no es "success", se crea un nuevo ID de wizard y se establece como cookie.
        wizard_id, should_set_cookie = MarketplaceWizardDraft.get_or_create_draft_token(request)

        if should_set_cookie:
            response.set_cookie(
                "_mtxMkpWizShop",
                wizard_id,
                max_age=60*15,  # 15 minutos
                # httponly=True,
                secure=True,
                samesite="Lax",
            )

        return response

    def post(self, request, *args, **kwargs) -> JsonResponse:
        """Vista para crear una nueva tienda de Miravia."""
        shortname = self.kwargs["shortname"]
        shopname = request.POST.get("shopname").strip()
        oss_selection = request.POST.get("oss_selection") == "yes"
        oss_start_date = request.POST.get("oss_start_date")

        wizard_id = request.COOKIES.get("_mtxMkpWizShop")
        if not wizard_id or not self.__check_wizard_expiration(wizard_id):
            return JsonResponse({"error": "La sesión ha expirado. Esta página se recargará.", "code": "session_expired"}, status=400)
        
        if not shopname:
            return JsonResponse({"error": "El nombre de la tienda es obligatorio.", "code": "shopname_required"}, status=400)
        
        if not shopname.endswith(".miravia.com"):
            return JsonResponse({"error": "El nombre de la tienda debe terminar en .miravia.com.", "code": "shopname_invalid"}, status=400)

        if not validate_shopname(shopname):
            return JsonResponse({"error": "El nombre de la tienda no es válido.", "code": "shopname_invalid"}, status=400)

        seller = get_object_or_404(Seller, shortname=shortname)

        existing_marketplace = Marketplace.objects.filter(seller=seller, shopname=shopname).first()
        if existing_marketplace:
            if existing_marketplace.is_active:
                return JsonResponse({
                    "error": _("Ya tienes una tienda de Shopify activa con ese nombre."),
                    "code": "existing_active_marketplace"
                    },status=400)
                    
            return JsonResponse({
                "error": _("Ya tienes una tienda de Shopify inactiva con ese nombre."),
                "code": "existing_inactive_marketplace"
                },status=400)
        
        shopify_marketplace = Marketplaces.objects.filter(code="shopify").first()
        if not shopify_marketplace:
            return JsonResponse(
                {
                    "error": "No se ha encontrado el marketplace de Shopify en los diccionarios.",
                    "code": "marketplace_not_found"
                }, status=400)

        with transaction.atomic():
            seller.oss = oss_selection
            seller.oss_date = oss_start_date if oss_selection else None
            seller.save(update_fields=["oss", "oss_date"])

        redirect_url = reverse("app_marketplaces:shopify_api_auth", args=[shortname, shopname])

        return JsonResponse({
            "code": "success",
            "redirect_url": redirect_url,
            "message": _("La petición es exitosa y se redirigirá a la autorización de la API de Shopify.")
        })

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        seller_vats = seller.vat_seller.all()

        context["seller_vats"] = seller_vats
        context["seller"] = seller
        return context

    def __check_wizard_expiration(self, wizard_id: str) -> bool:
        """
        Verifica si el wizard ha expirado.
        Si el ID del wizard no es válido o no existe, se considera que ha expirado.
        """
        if not wizard_id:
            return False

        try:
            draft = MarketplaceWizardDraft.objects.get(
                wizard_id=uuid.UUID(wizard_id),
                seller=self.request.user.seller,
                is_completed=False,
            )
            return not draft.is_expired
        except (MarketplaceWizardDraft.DoesNotExist, ValueError):
            return False

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))