from datetime import datetime
from muaytax.dictionaries.models import MuaytaxDepartment
from muaytax.app_notifications.channels.email_notification.sender import EmailMessageNotification
from muaytax.app_notifications.channels.call_notification.caller import TwilioCallNotification
from muaytax.app_notifications.channels.sms_notification.sender import TwilioSMSNotification
from muaytax.app_notifications.channels.wapp_notification.sender import WappNotification
from muaytax.utils.env_resources import logo_url_head_muaytax, logo_url_head_amzvat
from muaytax.email_notifications.utils import get_mail_signature_info_muaytax


class NotificationMethodFactory:
    @staticmethod
    def get_method_class(method: str):
        methods = {
            'email': EmailNotificationMethod,
            # 'sms': SMSNotificationMethod,
            # 'wapp': WhatsAppNotificationMethod,
            # 'call': CallNotificationMethod
        }
        return methods.get(method, None)
    
    @staticmethod
    def initiate_method(method: str, seller=None, sender_code=None):
        if method:
            return method(seller=seller, sender_code=sender_code)
        return None
    

class EmailNotificationMethod(EmailMessageNotification):
    def __init__(self, seller=None, sender_code=None):
        super().__init__()
        self.to_email = seller.user.email
        self.from_email = "<EMAIL>"
        self.set_context(seller=seller)
        self.sender = MuaytaxDepartment.objects.get(code=sender_code)

        try:
            if self.sender is not None:
                self.from_email = self.sender.email
                self.set_context(seller=seller, email=self.sender.email)
            else:
                
                    if seller.legal_entity in ['sl', 'self-employed'] and seller.contracted_accounting_date is not None and seller.contracted_accounting_date <= datetime.now().date():
                        self.from_email = "<EMAIL>"
                        self.set_context(seller=seller, email='<EMAIL>')

                    if seller.legal_entity not in ['sl', 'self-employed']:
                        self.from_email = "<EMAIL>"
                        self.set_context(seller=seller, email='<EMAIL>')
        except Exception as e:
            print(f"Error al obtener email de la gestoria en notificacion de modelos pendientes: {e}")

        # PASAMOS EL LOGO AL CONTEXTO
        logo=logo_url_head_muaytax()
        self.set_context(logo_head_muaytax=logo) 

    def send_notification(self):
        # OBTENEMOS LA INFORMACIÓN DE LA FIRMA DEL EMAIL
        mail_signature_details = get_mail_signature_info_muaytax()
        logo_head_muaytax = logo_url_head_muaytax()
        logo_head_amzvat = logo_url_head_amzvat()

        self.template_name = "emails/notifications/pending_models_notification.html"
        self.subject = "MUAYTAX - Modelos pendientes de aprobación"
        self.text_content = "Tienes uno o más modelos pendientes de aprobar. Por favor, revisa tu cuenta para más detalles."
        self.reply_to = self.sender.email
        # AÑADIR EL LOGO Y BCC AL CONTEXTO
        self.bcc = ['<EMAIL>', '<EMAIL>']

        # IMPORTANTE: Reestablecemos el contexto, asegurándonos de que el logo y el remitente estén correctamente establecidos
        self.set_context(
            sender=self.sender,
            mail_signature=mail_signature_details,
            logo_url_head_muaytax=logo_head_muaytax,
            logo_url_head_amzvat=logo_head_amzvat
        )

        self.send_email()
        
class CallNotificationMethod(TwilioCallNotification):
    def __init__(self, seller=None, sender_code=None):
        super().__init__()
        self.sender = MuaytaxDepartment.objects.get(code=sender_code)
        self.to_number = str(seller.contact_phone)
        self.seller_name = seller.first_name or seller.name

    def send_notification(self):
        self.send_call()

class SMSNotificationMethod(TwilioSMSNotification):
    def __init__(self, seller=None, sender_code=None):
        super().__init__()
        self.sender = MuaytaxDepartment.objects.get(code=sender_code)
        self.to_number = str(seller.contact_phone)
        self.seller_name = seller.first_name or seller.name

    def send_notification(self):
        self.send_sms()

class WhatsAppNotificationMethod(WappNotification):
    def __init__(self, seller=None, sender_code=None):
        super().__init__()
        self.sender = MuaytaxDepartment.objects.get(code=sender_code)
        self.to_number = str(seller.contact_phone)
        self.seller_name = seller.first_name or seller.name

    def send_notification(self):
        result = self.send_wapp()

        if "error" in result:
            raise Exception(f"Error: {result}")