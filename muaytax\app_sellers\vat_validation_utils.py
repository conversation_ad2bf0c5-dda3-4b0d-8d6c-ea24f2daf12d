# Biblioteca estándar Py
import re

# De terceros
from zeep import Client # Servicio api VIES
from zeep.exceptions import Fault # Servicio api VIES

# Patrones de validación de VAT por país
VAT_PATTERNS = {
    'AT': r'^ATU[0-9]{8}$',  
    'BE': r'^BE0[0-9]{9}$',  
    'BG': r'^BG[0-9]{9,10}$',  
    'CY': r'^CY[0-9]{8}[A-Z]$',  
    'CZ': r'^CZ[0-9]{8,10}$',  
    'DE': r'^DE[0-9]{9}$',  
    'DK': r'^DK[0-9]{8}$',  
    'EE': r'^EE[0-9]{9}$',  
    'EL': r'^EL[0-9]{9}$',  
    'ES': r'^ES[A-Z0-9][0-9]{7}[A-Z0-9]$',  
    'FI': r'^FI[0-9]{8}$',  
    'FR': r'^FR[A-Z0-9]{2}[0-9]{9}$',  
    'HR': r'^HR[0-9]{11}$',  
    'HU': r'^HU[0-9]{8}$',  
    'IE': r'^IE[0-9]{7}[A-W][0-9]{1}[A-Z]{1}|IE[0-9][A-Z][0-9]{5}[A-Z]|IE[0-9A-Z*+]{7}[A-Z]{1,2}$',
    'IT': r'^IT[0-9]{11}$',  
    'LT': r'^LT([0-9]{9}|[0-9]{12})$',  
    'LU': r'^LU[0-9]{8}$',  
    'LV': r'^LV[0-9]{11}$',  
    'MT': r'^MT[0-9]{8}$',  
    'NL': r'^NL[0-9]{9}B[0-9]{2}$',  
    'PL': r'^PL[0-9]{10}$',  
    'PT': r'^PT[0-9]{9}$',  
    'RO': r'^RO[0-9]{2,10}$',  
    'SE': r'^SE[0-9]{12}$',  
    'SI': r'^SI[0-9]{8}$',  
    'SK': r'^SK[0-9]{10}$',  
}

def validate_vat_number_by_country(iso_code, vat_number):
    """
    Valida el número de VAT según el código ISO del país.
    
    :param iso_code: Código ISO del país.
    :param vat_number: Número de VAT a validar.
    :return: True si el número de VAT es válido según el patrón del país, False en caso contrario.
    """
    pattern = VAT_PATTERNS.get(iso_code)

    # Si se proporciona el código de país, limpiar el número del IVA
    # if vat_number[:2].isalpha():
    #     vat_number_complete = vat_number
    # else:
    #     vat_number_complete = iso_code + vat_number

    vat_number_complete = vat_number if vat_number[:2] == iso_code else iso_code + vat_number
    if pattern is not None and re.match(pattern, vat_number_complete):
        print(f"validate_vat_number_by_country: True (patrón válido para {iso_code})")
        return True
    else:
        print(f"validate_vat_number_by_country: False (patrón no válido para {iso_code})")
        return False

def check_vat_vies(vat_number, country_code=None):
    vat_number = vat_number.upper()
    
    # Si no se proporciona country_code, extraerlo del vat_number
    if not country_code:
        if vat_number[:2].isalpha():
            country_code = vat_number[:2]
            vat_number_only = vat_number[2:]
        else:
            print(f'No se proporcionó un código de país válido para {vat_number}.')
            return False
    else:
        country_code = country_code.upper()
        vat_number_only = vat_number[2:] if vat_number[:2] == country_code else vat_number

    # Cliente SOAP para VIES
    client = Client('https://ec.europa.eu/taxation_customs/vies/checkVatService.wsdl')

    try:
        result = client.service.checkVat(countryCode=country_code, vatNumber=vat_number_only)
        
        # Manejo de errores en la fecha
        if getattr(result, 'requestDate', None) is None:
            print(f"Advertencia: `requestDate` es None en la respuesta de VIES para {vat_number}.")
        
        print(f'******************************** {result}')
        
        return getattr(result, 'valid', False)
    
    except Exception as e:
        print(f'Error en check_vat_vies: {e}')
        return False

def is_valid_spanish_vat(vat_number):
    # Extrae el NIE/NIF sin el prefijo ES si existe
    tax_id = vat_number[2:] if vat_number.startswith('ES') else vat_number
    
    # NIF provisionales | comienzan con ("K", "L" o "M")
    if re.match(r'^[KLM][0-9]{7}[A-Z]$', tax_id):
        return True  # No necesita otras validaciones
    elif re.match(r'^[0-9]{8}[A-Z]$', tax_id):
        return is_valid_nif(tax_id)
    elif re.match(r'^[XYZ][0-9]{7}[A-Z]$', tax_id):  # VALIDACIÓN DE NIE
        return is_valid_nie(tax_id)
    elif re.match(r'^[A-HJ-W][0-9]{7}[A-Z0-9]$', tax_id):
        return is_valid_cif(tax_id)
    else:
        return False

def is_valid_nif(nif):
    """Valida un NIF español usando módulo 23"""
    letters = "TRWAGMYFPDXBNJZSQVHLCKE"
    number = int(nif[:-1])  # Extrae los 8 números
    letter = nif[-1]  # Extrae la letra final
    return letters[number % 23] == letter

def is_valid_nie(nie):
    """Convierte NIE a NIF y valida con la misma fórmula"""
    if not re.match(r'^[XYZ][0-9]{7}[A-Z]$', nie):
        return False  # Formato incorrecto

    # Convertir la letra inicial a número
    conversion = {'X': '0', 'Y': '1', 'Z': '2'}
    nie_as_nif = conversion[nie[0]] + nie[1:]  # Reemplaza la letra por el número correspondiente
    return is_valid_nif(nie_as_nif)

def is_valid_cif(cif):
    """Valida un CIF español"""
    control = cif[-1]
    digits = cif[1:-1]
    sum_even = sum(int(digit) for digit in digits[1::2]) 
    sum_odd = sum(sum(divmod(int(digit) * 2, 10)) for digit in digits[0::2])
    checksum = (10 - (sum_even + sum_odd) % 10) % 10
    
    if control.isdigit():
        return checksum == int(control)
    else:
        return control == 'JABCDEFGHI'[checksum]
