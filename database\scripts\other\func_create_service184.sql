--QUERY PARA SABER EL NUMERO DE REGISTROS AFECTADOS
-- SELECT  sel.id, sel.shortname, m184.year
-- FROM sellers_presentedm184 m184
-- INNER JOIN sellers_seller sel ON sel.id = m184.seller_id
-- LEFT JOIN (
--     SELECT m184_inner.seller_id, m184_inner.year
--     FROM sellers_presentedm184 m184_inner
--     INNER JOIN sellers_seller sel_inner ON sel_inner.id = m184_inner.seller_id
--     INNER JOIN services_service serv ON serv.seller_id = sel_inner.id
--     WHERE serv.service_name_id = 'model_184'
-- ) AS filter ON filter.seller_id = sel.id AND filter.year = m184.year
-- WHERE filter.seller_id IS NULL;



DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_create_service184') THEN
        DROP FUNCTION func_create_service184(seller_limit INTEGER);
    END IF;
END $$;


CREATE OR REPLACE FUNCTION func_create_service184(seller_limit INTEGER)
RETURNS jsonb AS $$

DECLARE
m184instantes RECORD;

tmp_key VARCHAR := '';
tmp_value VARCHAR := '';
tmp_json JSONB;
json_sellers JSONB := '{}'::JSONB; 


BEGIN

  --Busca todas las instancias de Form184 que no tengan un servicio 184 de ese año asociado
  FOR m184instantes IN (
    SELECT  sel.id AS sel_id, sel.shortname AS short_name, m184.year AS m184_year
	FROM sellers_presentedm184 m184
	INNER JOIN sellers_seller sel ON sel.id = m184.seller_id
	LEFT JOIN (
	    SELECT m184_inner.seller_id, m184_inner.year
	    FROM sellers_presentedm184 m184_inner
	    INNER JOIN sellers_seller sel_inner ON sel_inner.id = m184_inner.seller_id
	    INNER JOIN services_service serv ON serv.seller_id = sel_inner.id
	    WHERE serv.service_name_id = 'model_184' AND serv.year = m184_inner.year
	) AS filter ON filter.seller_id = sel.id AND filter.year = m184.year
	WHERE filter.seller_id IS NULL
	LIMIT seller_limit 
  )
  LOOP

        --SHORTNAME DEL SELLER
        tmp_value := m184instantes.m184_year;
        tmp_key := m184instantes.short_name;
        tmp_json := json_build_object(tmp_key, tmp_value);
        json_sellers := jsonb_concat(json_sellers, tmp_json);

        --Inserta las instancias de Form184 que no tengan un servicio 184 de ese año asociado
        INSERT INTO services_service (
            seller_id,
            service_name_id,
            year,
            created_at,
            modified_at
        ) VALUES(
          m184instantes.sel_id,
          'model_184',
          m184instantes.m184_year,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        );
    
  END LOOP;


  -- RETURN JSON con los seller afectados y qué años del 184 pertenecen a cada uno
	RETURN json_sellers;

END;
$$ LANGUAGE plpgsql;


--SELECT func_create_service184(1);


-------------------------------------------------------------------------------------------------
--------------------CREAR SERVICIO 184 A PARTIR DE UN PROCESO DE VENTA---------------------------


--QUERY PARA SABER EL NUMERO DE REGISTROS AFECTADOS
-- SELECT 
--     sel.id, 
--     sel.name, 
--     sel.shortname, 
--     pc.product_name, 
--     TO_CHAR(pc.contracting_date, 'DD/MM/YYYY') AS formatted_date
-- FROM 
--     sellers_sellerprocess pc 
-- INNER JOIN 
--     sellers_seller sel ON sel.id = pc.seller_id
-- LEFT JOIN (
--     SELECT serv.seller_id
--     FROM services_service serv
--     INNER JOIN sellers_seller sel_inner ON sel_inner.id = serv.seller_id
--     WHERE serv.service_name_id = 'model_184' AND serv.year = '2024'
-- ) AS filter ON filter.seller_id = sel.id
-- WHERE 
--     filter.seller_id IS NULL
--     AND product_id = '8953'
--     AND sel.id NOT IN ('251');


DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_create_service184_from_process') THEN
        DROP FUNCTION func_create_service184_from_process(seller_limit INTEGER);
    END IF;
END $$;


CREATE OR REPLACE FUNCTION func_create_service184_from_process(seller_limit INTEGER)
RETURNS jsonb AS $$

DECLARE
m184process RECORD;

tmp_key VARCHAR := '';
tmp_value VARCHAR := '';
tmp_json JSONB;
json_sellers JSONB := '{}'::JSONB; 


BEGIN

  --Busca todas las instancias de Form184 que no tengan un servicio 184 de ese año asociado
  FOR m184process IN (
    SELECT 
        sel.id as sel_id, 
        sel.name, 
        sel.shortname, 
        pc.product_name,
        pc.contracting_date
    FROM 
        sellers_sellerprocess pc 
    INNER JOIN 
        sellers_seller sel ON sel.id = pc.seller_id
    LEFT JOIN (
        SELECT serv.seller_id
        FROM services_service serv
        INNER JOIN sellers_seller sel_inner ON sel_inner.id = serv.seller_id
        WHERE serv.service_name_id = 'model_184' AND serv.year = '2024'
    ) AS filter ON filter.seller_id = sel.id
    WHERE 
        filter.seller_id IS NULL
        AND product_id = '8953'
        AND sel.id NOT IN ('251')
	LIMIT seller_limit 
  )
  LOOP

        --SHORTNAME DEL SELLER
        tmp_value := m184process.product_name;
        tmp_key := m184process.shortname;
        tmp_json := json_build_object(tmp_key, tmp_value);
        json_sellers := jsonb_concat(json_sellers, tmp_json);

        --Inserta las instancias de Form184 que no tengan un servicio 184 de ese año asociado
        INSERT INTO services_service (
            seller_id,
            service_name_id,
            year,
            contracting_date,
            quantity,
            created_at,
            modified_at
        ) VALUES(
          m184process.sel_id,
          'model_184',
          '2024',
          m184process.contracting_date,
          1,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        );
    
  END LOOP;


  -- RETURN JSON con los seller afectados y qué años del 184 pertenecen a cada uno
	RETURN json_sellers;

END;
$$ LANGUAGE plpgsql;


--SELECT func_create_service184_from_process(1);



