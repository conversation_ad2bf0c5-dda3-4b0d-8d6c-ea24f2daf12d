import json
import os
import tempfile
from datetime import datetime
from typing import Optional
from urllib.parse import urlparse

import pandas as pd # type: ignore
import pdfkit # type: ignore
from dateutil.relativedelta import relativedelta # type: ignore
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.mail import EmailMultiAlternatives
from django.db import connection
from django.db.models import Case, F, FloatField, Q, Sum, When
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import format_html
from django.urls import reverse

from muaytax.app_invoices.models import Invoice
from muaytax.app_sellers.notification import SellerNotification
from muaytax.dictionaries.models import Country
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.dictionaries.models.store_products import StoreProduct
from muaytax.utils.env_resources import logo_url_head_muaytax, get_environment
from muaytax.dictionaries.models.product_service import ProductService
from muaytax.app_sellers.constants import PRODUCTS_SERVICES_CHOICES, ENTITY_TYPE_CHOICES, BOOLEAN_DISPLAY_VALUES, REVIEW_GROUPED_FIELDS_BY_COUNTRY


DOCUMENT_TYPE_JSON_PATH = "muaytax/dictionaries/data/document_type.json" # Ruta al archivo JSON de tipos de documentos

LIMIT_X_ENTITY = {
    'llc': 40,
    'self-employed': 50,
    'sl': 70
}

def debug_print(*args, **kwargs):
    """Imprime mensajes solo si la variable global debug es True."""
    if get_environment() == "local":
        print(*args, **kwargs)

def update_limit_invoice(seller):
    seller.limit_invoice = get_limit_invoice(seller)
    seller.save()

def get_limit_invoice(seller):
    # if seller.user.date_joined.date() >= datetime(2024, 1, 1).date():
    #     limit = 0
    #     total_vats = seller.vat_seller.filter(is_contracted=True).count()
    #     is_contracted = seller.contracted_accounting
    #
    #     is_cond = all(seller.legal_entity != string for string in [None, 'llc', 'other', ''])
    #
    #     if total_vats > 0:
    #         if is_contracted and is_cond:
    #             limit = LIMIT_X_ENTITY[seller.legal_entity]
    #         limit = limit + total_vats * LIMIT_X_ENTITY['llc']
    #     else:
    #         if is_cond:
    #             limit = LIMIT_X_ENTITY[seller.legal_entity]
    #     return limit
    return seller.limit_invoice

def get_providers_model347(seller, accounting_year):
    sql = "SELECT func_gets_providers_mod347(%s, %s);"
    params = (seller.id, accounting_year)
    sql_result = ""
    result_list = []
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql, params)
            row = cursor.fetchone()
            while row is not None:
                sql_result = row[0]
                if sql_result == None:
                    return result_list
                result_list = json.loads(sql_result)
                row = cursor.fetchone()
            # if row:
            #     sql_result = row[0]
            #     result_list = json.loads(sql_result)

    except Exception as e:
        debug_print("ERROR_P: ", e)

    return result_list

def get_customers_model347(seller, accounting_year):
    sql = "SELECT func_gets_customers_mod347(%s, %s);"
    params = (seller.id, accounting_year)
    sql_result = ""
    result_list = []
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql, params)
            row = cursor.fetchone()
            while row is not None:
                sql_result = row[0]
                if sql_result == None:
                    return result_list
                result_list = json.loads(sql_result)
                row = cursor.fetchone()

    except Exception as e:
        debug_print("ERROR_C: ", e)

    return result_list

def get_total_sum_in_periods_model_347(declarado, seller, accounting_year):
    total_sum_periods = {}
    total_year = 0

    for i in range(1, 13, 3):
        start_date = datetime(int(accounting_year), i, 1)
        end_date = start_date + relativedelta(months=2, day=31)

        total_inv = Invoice.objects.filter(
            Q(provider__id=declarado['id']) | Q(customer__id=declarado['id']),
            seller=seller,
            expedition_date__range=[start_date, end_date],
            status__code='revised',
            tax_country__iso_code='ES'
        ).exclude(
            Q(transaction_type__code__contains='intra-community') |
            Q(transaction_type__code__contains='import') |
            Q(transaction_type__code__contains='transfer') |
            Q(transaction_type__code__contains='copy')
        ).aggregate(
            total_concepts_inv=Sum(
                Case(
                    When(
                        concept__irpf_euros=0,
                        then=F('concept__total_euros')
                    ),
                    default=0,
                    output_field=FloatField()
                )
            )
        )['total_concepts_inv']

        total_inv = round(total_inv, 2) if total_inv is not None else 0
        total_sum_periods[f't{i // 3 + 1}'] = total_inv
        total_year += total_inv

    total_sum_periods['total'] = round(total_year, 2)
    return total_sum_periods

def get_model347_carts(seller, declarados, accounting_year):
    """
    Declarados debe venir en formato lista, NO en queryset
    Esto es porque viene la union de los queries de custumers y providers
    """

    template_path = 'documents/cartas/carta_model347.html'
    today = timezone.now()

    temp_dir = tempfile.mkdtemp()
    pdf_files = []
    for index, declarado in enumerate(declarados):
        if declarado['country'] == 'ES':
            clave = declarado['clave']
            declarado['country'] = Country.objects.get(iso_code=declarado['country']).name if declarado[
                'country'] else None
            # yearly_values = get_total_sum_in_periods_model_347(declarado, seller, accounting_year)
            name_declarado = declarado['name'].replace(' ', '').lower()
            name_declarado = name_declarado.replace(',', '').replace('.', '')
            file_name = f"carta_{index + 1}_modelo347_{name_declarado}_{clave.replace(' ', '').replace('-', '_')}.pdf"

            # Datos para rellenar el template
            data = {
                "seller": seller,
                "client": declarado,
                "today": today,
                "year": accounting_year,
                # "yearly_values": yearly_values,
                "clave": clave,
            }

            # Renderizar el template con los datos
            rendered_template = render_to_string(template_path, data)

            # Ruta donde se guardará el PDF generado
            output_path = os.path.join(temp_dir, file_name)

            # Generar el PDF
            pdfkit.from_string(rendered_template, output_path)
            pdf_files.append(output_path)

    return pdf_files

def add_totals_per_period(declarados, seller, year):
    for declarado in declarados:
        total_invoices = get_total_sum_in_periods_model_347(declarado, seller, year)
        declarado.update({
            "t1": total_invoices['t1'],
            "t2": total_invoices['t2'],
            "t3": total_invoices['t3'],
            "t4": total_invoices['t4']
        })
    return declarados

def send_email_oss(sellervat):
    # si tiene la oss contratada y tiene el numero de iva
    if sellervat.seller.oss and sellervat.vat_number:
        # envia un email a gestoria
        email = '<EMAIL>'
        seller = sellervat.seller.name
        country = sellervat.vat_country.name
        iva_number = sellervat.vat_number
        fiscal_number = sellervat.steuernummer or sellervat.siret

        message = render_to_string("emails/sellervat_iva_oss_contracted.html", {
            'seller': seller,
            'country': country,
            'iva_number': iva_number,
            'fiscal_number': fiscal_number,
            'logo_head_muaytax': logo_url_head_muaytax()
        })
        subject = 'MUAYTAX - Aviso de nuevo IVA'
        from_email = '<EMAIL>'
        to_email = [email]
        reply_to = [email]
        html_content = message
        text_content = 'Aviso de nuevo IVA'
        bcc_email = ['<EMAIL>']
        email_iva_oss = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
        email_iva_oss.attach_alternative(html_content, "text/html")
        email_iva_oss.send()

def send_end_service_email(instance, changes_in_date, service_name, email_type="low"):
    debug_print(f'typof de email_tyepe es {type(email_type)}')
    email_services_type = 'Alta' if email_type == "high" else 'Baja'
    debug_print(f'******************email_services_type: {email_services_type}')
    subject = f'MUAYTAX - {email_services_type} de Servicios'
    from_email = '<EMAIL>'
    to_email = ['<EMAIL>']

    if not settings.IS_PRODUCTION:
        to_email = ['<EMAIL>']

    # Diccionario de equivalencias legal_entity
    legal_entity_map = {
        "self-employed": "Autónomo",
        "sl": "SL",
        "llc": "LLC",
        "other": "Otro",
    }

    # Obtener información básica de `seller` y `legal_entity`
    seller = instance.seller if hasattr(instance, 'seller') else instance
    legal_entity = seller.legal_entity if hasattr(seller, 'legal_entity') else ''

    # Usar el nombre completo si está disponible, si no, el `shortname`
    full_name = f"{seller.first_name} {seller.last_name}" if seller.first_name and seller.last_name else seller.shortname

    # Cabecera del correo según el tipo de operación
    header = f"{email_services_type} de Servicios"

    # Obtener país de IVA si existe
    vat_country = getattr(instance, 'vat_country', None)
    vat_country_name = vat_country.name if vat_country else None

    # Determinar si es una desactivación masiva
    is_deactivation = service_name == "Servicios desactivados"

    if email_services_type == "Alta":
        message_plain = (
            f"Desde el departamento de Gestoría se han realizado los siguientes cambios en las fechas de alta de los servicios contratados "
            f"para el cliente {full_name} con la empresa {seller} y entidad {legal_entity}:\n"
        )
    else:  # Caso de Baja
        message_plain = (
            f"Desde el departamento de Gestoría se han realizado los siguientes cambios en las fechas de baja de los servicios contratados "
            f"para el cliente {full_name} con la empresa {seller} y entidad {legal_entity}:\n"
        )

    if is_deactivation: # Caso de desactivación masiva de todos los servicios

        changes_by_country = {}
        for change in changes_in_date:
            country = change.get('country', vat_country_name)
            if country not in changes_by_country:
                changes_by_country[country] = []
            changes_by_country[country].append(change)

        # Generar mensaje agrupado por modelo y pais si existe
        for country, changes in changes_by_country.items():
            if country is None:
                group_header = "Servicios del Seller"
            elif country == 'not-assigned':
                group_header = "Otros servicios"
            else:
                group_header = f"Servicios del país IVA {country}"

            message_plain += f"\n- {group_header}:\n"
            for change in changes:
                new_date = change.get('new_date')
                message_plain += f"  - {change['label']}: a {new_date} (new)\n"

        debug_print(f'--------------->>>changes_by_country : {changes_by_country}')
        changes = changes_by_country

    else: # Caso de baja manual desde Seller o SellerVat

        group_header = f"Servicios del país IVA {vat_country_name}" if vat_country_name else "Servicios del Seller"
        message_plain += f"\n- {group_header}:\n"

        for change in changes_in_date:
            previous_date = change.get('previous_date')
            new_date = change.get('new_date')
            if previous_date:
                message_plain += f"  - {change['label']}: de {previous_date} a {new_date} (act)\n"
            else:
                message_plain += f"  - {change['label']}: a {new_date} (new)\n"
        debug_print(f'--------------->>>changes_in_date : {changes_in_date}')
        changes = changes_in_date

    # Convertir `legal_entity` a su equivalente legible
    legal_entity_display = legal_entity_map.get(legal_entity, '')
    debug_print(f'--------------->>>legal_entity_display : {legal_entity_display}')

    # Preparar datos para el template
    template_data = {
        'header': header,
        'changes': changes,
        'service_name': service_name,
        'full_name': full_name,
        'seller': seller,
        'legal_entity': legal_entity_display,
        'vat_country': vat_country_name,
        'logo_head_muaytax': logo_url_head_muaytax(),
        'is_deactivation': is_deactivation,
        'email_services_type': email_services_type
    }

    debug_print(f"Valores pasados al template 'end_date_services.html'::\n {json.dumps(template_data, default=str, indent=4)}")

    # Renderizar el template con los datos preparados
    html_content = render_to_string('emails/end_date_services.html', template_data)

    # Configurar y enviar el correo
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email = EmailMultiAlternatives(subject, message_plain, from_email, to_email, bcc=bcc_email)
    email.attach_alternative(html_content, "text/html")
    email.send()

def update_send_email_for_limit_invoice(seller: object) -> None:
    """Actualiza el campo send_email_limit_invoice del SELLER y envía un correo si se cumple la condición."""
    # Calcular el número total de facturas asociadas a ese vendedor en el mes actual
    current_date = datetime.now()
    total_invoices = Invoice.objects.filter(
        seller_id=seller.id,
        created_at__year=current_date.year,
        created_at__month=current_date.month,
    ).exclude(
        Q(invoice_category__code__icontains='_copy') | Q(is_txt_amz=True) | Q(is_generated=True)
    ).count()

    # Obtener el límite de facturas del vendedor
    invoices_limit = seller.limit_invoice
    calculo_porciento = int((total_invoices / invoices_limit) * 100)

    email_sent = seller.send_email_limit_invoice

    # Verificar si el porcentaje de facturas está por encima del límite y el campo send_email_limit_invoice aún no se ha establecido en True
    # Cambiar el campo booleano en el modelo Seller solo la primera vez que supere el 90%
    if calculo_porciento >= 90 and not email_sent:
        seller.send_email_limit_invoice = True
        notification = SellerNotification(seller)
        notification.max_invoice_limit_notification()
    elif calculo_porciento < 90 and email_sent:
        seller.send_email_limit_invoice = False

    seller._skip_seller_signal = True
    seller.save()

def process_excel_files_and_update_product_data(instance, excel_fields, current_product_data):
    """
    Procesa los archivos Excel asociados a un objeto y actualiza el campo product_data.

    :param instance: Instancia del modelo que contiene los campos Excel.
    :param excel_fields: Diccionario donde las claves son nombres de tiendas y los valores son campos FileField.
    :param current_product_data: JSON actual del campo product_data.
    :return: JSON actualizado de product_data.
    """
    for store, excel_file in excel_fields.items():
        if excel_file:
            try:
                # Leer el archivo Excel
                df = pd.read_excel(excel_file)
                if df.empty:
                    continue  # Saltar archivos vacíos

                # Extraer IDs y calcular ventas
                product_ids = df.iloc[:, 0].tolist()
                sales_data = {}

                # Preparar todos los IDs para procesamiento
                processed_ids = []
                for product_id in product_ids:
                    # Normalizar el ID: asegurar que siempre tenga prefijo según formato actual
                    if isinstance(product_id, str) and product_id.startswith(f"{store[:3].upper()}-"):
                        prefixed_id = product_id
                    else:
                        prefixed_id = f"{store[:3].upper()}-{product_id}"

                    processed_ids.append(prefixed_id)
                    sales_data[prefixed_id] = sales_data.get(prefixed_id, 0) + 1

                # Consultar todos los productos de una vez para evitar múltiples consultas a DB
                # Solo consultamos los productos que necesitamos (los del archivo actual)
                store_products = {
                    prod.code: prod.description
                    for prod in StoreProduct.objects.filter(
                        code__in=processed_ids,
                        shop=store
                    )
                }

                # Validar y agregar datos al JSON de product_data
                if store not in current_product_data:
                    current_product_data[store] = []

                existing_ids = {p['id'] for p in current_product_data[store]}  # IDs ya existentes
                for product_id, ventas in sales_data.items():
                    # Obtener nombre del producto del cache creado
                    product_name = store_products.get(product_id)

                    # Asignar valor por defecto si no se encuentra el producto
                    if not product_name:
                        product_name = f"Producto {product_id} -- "

                    if product_id in existing_ids:
                        # Actualizar ventas del producto existente
                        for existing_product in current_product_data[store]:
                            if existing_product['id'] == product_id:
                                existing_product['ventas'] += ventas
                                break
                    else:
                        # Agregar nuevo producto si no existe en los datos actuales
                        current_product_data[store].append({
                            'id': product_id,
                            'name': product_name,
                            'ventas': ventas
                        })

            except Exception as e:
                raise ValidationError(f"Error procesando el archivo para la tienda '{store}': {str(e)}")

            # Limpiar el campo del archivo una vez procesado
            setattr(instance, f"{store}_excel", None)

    return current_product_data

def generate_product_data_table(product_data):
    """
    Genera una tabla HTML para mostrar los datos de productos.
    :param product_data: JSON con los datos de productos agrupados por tienda.
    :return: HTML seguro para renderizar en el admin.
    """
    if product_data:
        html_content = '<div style="background-color: black; color: white; padding: 10px; border-radius: 5px; max-width: 100%; overflow-x: auto;">'
        for store, products in product_data.items():
            html_content += f'<h3 style="color: white; text-align: center;">{store}</h3>'
            html_content += '''
            <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                <thead>
                    <tr style="background-color: #333; color: white;">
                        <th style="padding: 8px; border: 1px solid #555;">ID Producto</th>
                        <th style="padding: 8px; border: 1px solid #555;">Nombre</th>
                        <th style="padding: 8px; border: 1px solid #555;">Cantidad</th>
                    </tr>
                </thead>
                <tbody>
            '''
            for product in products:
                html_content += f'''
                <tr>
                    <td style="padding: 8px; border: 1px solid #555; text-align: center;">{product['id']}</td>
                    <td style="padding: 8px; border: 1px solid #555; text-align: left;">{product['name']}</td>
                    <td style="padding: 8px; border: 1px solid #555; text-align: center;">{product['ventas']}</td>
                </tr>
                '''
            html_content += '</tbody></table>'
        html_content += '</div>'
        return format_html(html_content)

    return "No hay datos disponibles"

# ===== Funciones utilitarias de Formularios IVA ======

# Función para generar un mapeo de campos de documentos 
def generate_document_field_mapping(iso_country_codes=None, module="full", num_amazon_tokens=0):
    """ Genera un mapeo de campos de documentos para los países especificados. """

    if isinstance(iso_country_codes, str):
        raise TypeError("El parámetro 'iso_country_codes' debe ser una lista, no un string.")

    # 🔹 Bloques base
    mappings = {
        "company_info": {
            "country_incorporation_tax_number": "DOC-CountryIncorporationTaxNumber",
            "bank_certificate": "DOC-BankCertificate",
            "incorporation_entity_certificate": "DOC-IncorporationEntityCertificate",
            "entity_extract_updated": "DOC-EntityExtractUpdated",
        },
        "migration": {},
        "country_documents": {},
    }
    
    # 🔁 Añadir dinámicamente los campos para tokens de Amazon
    if module in ("company_info", "full") and num_amazon_tokens > 0:
        for i in range(1, num_amazon_tokens + 1):
            mappings["company_info"][f"screenshot_token_{i}"] = f"screenshot_token_{i}"
            mappings["company_info"][f"screenshot_shop_{i}"] = f"screenshot_shop_{i}"

    # 🔁 Rellenar dinámicamente si corresponde
    if iso_country_codes and module in ("migration", "country_documents", "full"):
        for iso in iso_country_codes:
            upper_iso = iso.upper()

            if module in ("migration", "full"):
                mappings["migration"][f"vat_number_certificate_{iso}"] = f"{upper_iso}-CERTIFICATEVATNUMBER"
                if upper_iso == "DE":
                    mappings["migration"][f"steuernummer_certificate_{iso}"] = f"{upper_iso}-CERTIFICATESTEURNUMMER"

            if module in ("country_documents", "full"):
                if upper_iso == "DE":
                    mappings["country_documents"].update({
                        f"ecommerce_proof_screenshot_{iso}": f"{upper_iso}-ECOMMERCEPROOF",
                        f"german_3pl_contract_{iso}": f"{upper_iso}-3PLCONTRACT",
                        f"admin_tax_residency_certificate_{iso}": f"{upper_iso}-ADMINRESIDENCY",
                        f"amazon_business_agreement_{iso}": f"{upper_iso}-BUSINESSAGREEMENT",
                    })
                elif upper_iso == "GB":
                    mappings["country_documents"].update({
                        f"proof_of_address_1_{iso}": f"{upper_iso}-PROOFOFADDRESS-1",
                        f"proof_of_address_2_{iso}": f"{upper_iso}-PROOFOFADDRESS-2",
                    })
                elif upper_iso == "IE":
                    mappings["country_documents"].update({
                        f"irish_3pl_contract_{iso}": f"{upper_iso}-3PLCONTRACT",
                        f"business_operating_proof_1_{iso}": f"{upper_iso}-OPERATINGPROOF-1",
                        f"business_operating_proof_2_{iso}": f"{upper_iso}-OPERATINGPROOF-2",
                        f"business_operating_proof_3_{iso}": f"{upper_iso}-OPERATINGPROOF-3",
                        f"business_operating_proof_4_{iso}": f"{upper_iso}-OPERATINGPROOF-4",
                        f"business_operating_proof_5_{iso}": f"{upper_iso}-OPERATINGPROOF-5",
                    })

    # 🔹 Construir full_mapping sumando todos los bloques
    full_mapping = {}
    for group in mappings.values():
        full_mapping.update(group)

    # 🔁 Resultado
    result = {"full_mapping": full_mapping}

    if module == "full":
        result.update(mappings)
    else:
        result[module] = mappings.get(module, {})

    return result

# Función para obtener o crear un tipo de documento
def get_or_create_document_type(code: str, country_name: str, suffix: str = "") -> dict:
    description = f"Documentos actuales del IVA ({country_name}){suffix}"
    obj, created = DocumentType.objects.get_or_create(
        code=code,
        defaults={"description": description}
    )

    # Solo se modifica el archivo document_type.json si estamos en entorno "local"
    if get_environment() == "local":
        try:
            if os.path.exists(DOCUMENT_TYPE_JSON_PATH):
                with open(DOCUMENT_TYPE_JSON_PATH, "r", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                os.makedirs(os.path.dirname(DOCUMENT_TYPE_JSON_PATH), exist_ok=True)
                data = []

            if isinstance(data, list):
                if not any(entry.get("code") == code for entry in data):
                    data.append({"code": code, "description": description})
                    with open(DOCUMENT_TYPE_JSON_PATH, "w", encoding="utf-8") as f:
                        json.dump(data, f, indent=4, ensure_ascii=False)
            else:
                debug_print("⚠️ El archivo document_type.json no es una lista")

        except Exception as e:
            debug_print(f"❌ Error al actualizar document_type.json: {e}")

    return {"code": code, "description": description, "created": created}

# NOTE: NO se usa la funcion get_document_code_for_field en el código actual, pero se deja aquí por si se necesita en el futuro.
# Función para obtener el código de documento según el campo y bloque
def get_document_code_for_field(field_name, block_key):
        if "migration_info_" in block_key:
            iso = block_key.split("_")[-1].upper()
            if field_name.startswith("vat_number_certificate") or field_name == "steuernummer_certificate":
                return f"{iso}-CERTIFICATEVATNUMBER"
        elif "documents_by_country_" in block_key:
            iso = block_key.split("_")[-1].upper()
            mapping_by_iso = {
                "DE": {
                    "merchant_token_screenshot": f"{iso}-MERCHANTTOKEN",
                    "ecommerce_proof_screenshot": f"{iso}-ECOMMERCEPROOF",
                    "german_3pl_contract": f"{iso}-3PLCONTRACT",
                    "admin_tax_residency_certificate": f"{iso}-ADMINRESIDENCY",
                },
                "GB": {
                    "uk_proof_of_address_1": f"{iso}-PROOFOFADDRESS-1",
                    "uk_proof_of_address_2": f"{iso}-PROOFOFADDRESS-2",
                },
            }
            return mapping_by_iso.get(iso, {}).get(field_name)

        elif block_key == "company_info_form":
            company_mapping = {
                "country_incorporation_tax_number": "DOC-CountryIncorporationTaxNumber",
                "bank_certificate": "DOC-BankCertificate",
                "incorporation_entity_certificate": "DOC-IncorporationEntityCertificate",
                "entity_extract_updated": "DOC-EntityExtractUpdated",
                "amazon_vies_screenshot": "DOC-AmazonViesScreenshot",
            }
            return company_mapping.get(field_name)
        
        return None
        
# Función para obtener los datos de documentos existentes según un mapeo
def get_documents_block_data(doc_mapping, seller, seller_vat=None, shortname=None):
    """
    Dado un mapeo {campo: código_documento}, retorna un dict con los datos de los documentos existentes.
    Si se pasa `shortname`, se devuelve una URL segura.
    """
    from muaytax.app_documents.models.document import Document

    result = {}

    for field_name, doc_code in doc_mapping.items():
        filters = {"seller": seller, "documentType__code": doc_code}
        if seller_vat:
            filters["sellerVat"] = seller_vat

        doc = Document.objects.filter(**filters).first()

        if doc and doc.file:
            file_name = doc.file.name.split("/")[-1]
            secure_url = (
                build_secure_file_url(doc.file.url, shortname)
                if shortname else doc.file.url
            )
            result[field_name] = {
                "value": file_name,
                "file": secure_url,
            }
        else:
            result[field_name] = {"value": "", "file": ""}

    return result

# Función para obtener los datos de documentos existentes por socio
def get_documents_block_data_by_partner(partner_id, doc_mapping, shortname=None) -> dict:
    """
    Dado un partner_id y un mapeo {campo_clave: codigo_documento}, devuelve un dict con los documentos encontrados.
    Si se pasa `shortname`, construye URLs seguras en lugar de las reales del sistema de archivos.
    """
    from muaytax.app_documents.models.document import Document

    result = {}

    for field_name, doc_code in doc_mapping.items():
        filters = {
            "partner": partner_id,
            "documentType__code": doc_code,
        }

        doc = Document.objects.filter(**filters).first()

        if doc and doc.file:
            file_name = doc.file.name.split("/")[-1]
            secure_url = (
                build_secure_file_url(doc.file.url, shortname)
                if shortname else doc.file.url
            )
            result[field_name] = {
                "value": file_name,
                "file": secure_url,
            }
        else:
            result[field_name] = {"value": "", "file": ""}

    return result

# Función para obtener los campos visibles por bloque en un formulario JSON
def get_visible_fields_by_block(json_form, iso_country_codes=None):
    """
    Retorna un diccionario con los campos efectivamente visibles en cada bloque del formulario,
    extraídos desde el json_form. Se puede usar directamente para el sistema de validación del gestor.
    """
    if not isinstance(json_form, dict):
        return {}

    visible_fields = {}
    doc_mappings = generate_document_field_mapping(iso_country_codes or [])
    full_document_mapping = doc_mappings["full_mapping"]

    for block_key, block_data in json_form.items():
        if isinstance(block_data, dict):
            visible_fields[block_key] = []

            for field_key, field_val in block_data.items():
                # Campos simples visibles
                if isinstance(field_val, (str, dict, int, float, bool)):
                    visible_fields[block_key].append(field_key)

                # Manejo especial: tokens Amazon (estructura tipo lista)
                if block_key == "company_info_form" and field_key == "amazon_merchant_tokens" and isinstance(field_val, list):
                    for entry in field_val:
                        idx = str(entry.get("id", "")).strip()
                        if not idx:
                            continue
                        visible_fields[block_key].extend([
                            f"token_{idx}",
                            f"screenshot_token_{idx}",
                            f"screenshot_shop_{idx}",
                        ])

    return visible_fields

# Función para asignar la URL y nombre del archivo a un campo de documento
def set_document_file_info(field_content, seller, seller_vat, doc_type):
    """ Asigna la URL y nombre del archivo si existe un documento previo coincidente, o limpia los valores si no existe """

    from muaytax.app_documents.models.document import Document

    value = field_content.get("value", "")
    if value:
        try:
            doc = Document.objects.filter(
                seller=seller,
                sellerVat=seller_vat,
                documentType=doc_type,
                file__icontains=value
            ).first()
            if doc:
                field_content["file"] = doc.file.url
                field_content["value"] = doc.file.name.split("/")[-1]
                debug_print(f"📂 Documento recuperado de DB: {field_content['value']} → {field_content['file']}")
                return
        except Exception as e:
            debug_print(f"⚠️ Error buscando doc: {e}")
    field_content["file"] = ""
    field_content["value"] = ""
    debug_print("📂 Documento no encontrado, se dejan campos vacíos")

# Función para enviar un correo de notificación al cambiar la fecha de inicio de cobro
def send_payment_date_change_email(seller, service_name, field_name, previous_date, new_date, country=None):
    """
    Envía notificación cuando se actualiza o elimina una fecha de inicio de cobro

    Args:
        seller: Instancia del Seller
        service_name: Nombre del servicio (ej: "Contabilidad ES")
        field_name: Nombre del campo actualizado (ej: "contracted_accounting_payment_date")
        previous_date: Fecha anterior (puede ser None)
        new_date: Nueva fecha (None indica eliminación de la fecha de pago)
        country: País IVA si aplica (para servicios externos)
    """
    try:
        subject = 'MUAYTAX - Cambio en Fecha de Inicio de Cobro'
        from_email = '<EMAIL>'
        to_email = ['<EMAIL>']

        if not settings.IS_PRODUCTION:
            to_email = ['<EMAIL>']

        # Diccionario de equivalencias legal_entity
        legal_entity_map = {
            "self-employed": "Autónomo",
            "sl": "SL",
            "llc": "LLC",
            "other": "Otro",
        }

        # Obtener información básica del seller
        legal_entity = seller.legal_entity if hasattr(seller, 'legal_entity') else ''
        legal_entity_display = legal_entity_map.get(legal_entity, legal_entity)

        # Usar el nombre completo si está disponible, si no, el shortname
        full_name = f"{seller.first_name} {seller.last_name}" if seller.first_name and seller.last_name else seller.shortname

        # Preparar el mensaje de texto plano
        # Convertir fechas a objetos date si son strings
        if isinstance(previous_date, str):
            previous_date = datetime.strptime(previous_date, '%Y-%m-%d').date()
        if isinstance(new_date, str):
            new_date = datetime.strptime(new_date, '%Y-%m-%d').date()

        # Manejar caso de eliminación de fecha de pago
        if new_date is None:
            change_description = f"eliminada (anteriormente: {previous_date.strftime('%d/%m/%Y')})"
            action_type = "eliminada"
        elif previous_date:
            change_description = f"de {previous_date.strftime('%d/%m/%Y')} a {new_date.strftime('%d/%m/%Y')}"
            action_type = "actualizado"
        else:
            change_description = f"a {new_date.strftime('%d/%m/%Y')}"
            action_type = "establecido"

        country_info = f" ({country})" if country else ""

        # Ajustar mensaje según el tipo de acción
        if action_type == "eliminada":
            message_plain = (
                f"Se ha eliminado la fecha de inicio de cobro para el servicio '{service_name}{country_info}' "
                f"del cliente {full_name} con la empresa {seller} y entidad {legal_entity_display}.\n\n"
                f"La fecha de inicio de cobro ha sido {change_description}\n"
                f"Campo técnico: {field_name}\n\n"
                f"Este es un correo automático generado desde la plataforma."
            )
        else:
            message_plain = (
                f"Se ha {action_type} la fecha de inicio de cobro para el servicio '{service_name}{country_info}' "
                f"del cliente {full_name} con la empresa {seller} y entidad {legal_entity_display}.\n\n"
                f"Cambio realizado: {change_description}\n"
                f"Campo técnico: {field_name}\n\n"
                f"Este es un correo automático generado desde la plataforma."
            )

        # Preparar datos para el template
        template_data = {
            'seller_name': full_name,
            'seller_shortname': seller.shortname,
            'seller_email': seller.user.email,
            'legal_entity': legal_entity_display,
            'service_name': service_name,
            'field_name': field_name,
            'previous_date': previous_date,
            'new_date': new_date,
            'country': country,
            'action_type': action_type,
            'change_description': change_description,
            'logo_head_muaytax': logo_url_head_muaytax(),
        }

        # Renderizar el template con los datos preparados
        html_content = render_to_string('emails/payment_date_change_notification.html', template_data)

        # Configurar y enviar el correo
        bcc_email = ['<EMAIL>', '<EMAIL>']
        email = EmailMultiAlternatives(subject, message_plain, from_email, to_email, bcc=bcc_email)
        email.attach_alternative(html_content, "text/html")
        email.send()

        #debug_print(f'[PAYMENT_DATE_EMAIL] Email enviado correctamente para {service_name}')

    except Exception as e:
        debug_print(f'[PAYMENT_DATE_EMAIL] Error enviando email: {e}')

# Función para generar contexto de tooltip a partir de campos de contexto
def generate_tooltip_context_from_context_fields(fields_dict):
    return {
        f"label_{key}": value["info_tooltip"]
        for key, value in fields_dict.items()
        if "info_tooltip" in value
    }

# NOTE: solo para Debug Borrar al terminal y eliminar todos los usos Función para depurar datos JSON
def debug_json_data(data, title="", debug=False):

    debug_print("\n" + ("🧩 " + title if title else "🧩 INSPECCIÓN DE DATOS (JSON)"))

    # 🔄 Si es str y parece un JSON, intentar decodificar
    if isinstance(data, str):
        try:
            data = json.loads(data)
            debug_print("📦 Cadena JSON decodificada correctamente")
        except json.JSONDecodeError:
            debug_print("❌ No se pudo decodificar la cadena como JSON")
            debug_print(f"🔚 Fin de la inspección (valor original): {data}")
            return

    # 🔁 Si es lista, iterar por elementos
    if isinstance(data, list):
        debug_print(f"📚 Lista de {len(data)} elementos")
        for idx, item in enumerate(data, 1):
            debug_print(f"\n   🧷 Elemento #{idx}")
            if isinstance(item, dict):
                debug_json_data(item, debug=debug)  # Recursivo
            else:
                debug_print(f"     ⚠️ No es dict: {item} (tipo: {type(item).__name__})")
        debug_print("🔚 Fin de la inspección de la lista")
        return

    # 📘 Si no es dict (ya no es str ni list)
    if not isinstance(data, dict):
        debug_print(f"⚠️ Tipo de dato no compatible: {repr(data)} (tipo: {type(data).__name__})")
        return

    # 📂 Si es dict, inspeccionar como antes
    for key, value in data.items():
        # Intentar decodificar valores str que parezcan JSON
        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                if isinstance(parsed, dict):
                    debug_print(f"🔹 {key} (bloque JSON serializado):")
                    debug_json_data(parsed, debug=debug)  # Recursivo
                    continue
                elif isinstance(parsed, list):
                    debug_print(f"🔹 {key} (lista JSON serializada):")
                    debug_json_data(parsed, debug=debug)  # Recursivo
                    continue
            except (json.JSONDecodeError, TypeError):
                pass

        if isinstance(value, dict) and set(value.keys()) >= {"value", "file"}:
            debug_print(f"📂 {key} (documento):")
            debug_print(f"     ├─ value: {value.get('value')}")
            debug_print(f"     └─ file : {value.get('file')}")

        elif isinstance(value, dict):
            debug_print(f"🔸 {key} (subestructura):")
            for subkey, subval in value.items():
                tipo = type(subval).__name__
                debug_print(f"     └── {subkey}: {subval} (tipo: {tipo})")

        else:
            tipo = type(value).__name__
            debug_print(f"• {key}: {value} (tipo: {tipo})")

# Función para obtener el número nacional           
def get_phone_national_from_db(contact_phone: Optional[str], phone_country, seller_address) -> str:
    """
    Extrae el número nacional desde contact_phone si se puede verificar que
    comienza con el prefijo del país (phone_country o seller_address.address_country).

    Args:
        contact_phone (str): Teléfono completo, con prefijo.
        phone_country (Country or None): País asociado al teléfono.
        seller_address (Address or None): Dirección del vendedor (para fallback).

    Returns:
        str: Número nacional (sin prefijo), o cadena vacía si no es posible extraerlo.
    """
    if not contact_phone:
        return ""

    # Paso 1: Determinar país (prioriza phone_country, luego dirección)
    country = phone_country or (seller_address.address_country if seller_address else None)
    if not country or not country.phoneprefix:
        return ""

    # Paso 2: Verificar si el número comienza con el prefijo y extraer parte nacional
    full_phone = contact_phone.strip()
    prefix = country.phoneprefix.strip()

    if full_phone.startswith(prefix):
        return full_phone.removeprefix(prefix).strip()
    return ""

# Función para aplicar plantillas dinámicas a códigos ISO y nombres de países
def apply_dynamic_templates(dynamic_templates: dict, iso_codes: list, country_names: dict) -> dict:
    """
    Devuelve un dict plano con los labels personalizados por país.
    """
    result = {}
    for template_key, func in dynamic_templates.items():
        for iso in iso_codes:
            country_name = country_names.get(iso, iso)
            final_key = template_key.format(iso=iso)
            result[final_key] = func(iso, country_name)
    return result

# Función para construir una URL segura para un documento
def build_secure_file_url(file_path: str, shortname: str) -> str:
    """
    Construye la URL segura para un documento basado en el nombre del archivo.
    """
    if not file_path or not shortname:
        return ""
    
    filename = file_path.split("/")[-1]      # ej. "002_plFsxqL.pdf"
    file_id = filename.split(".")[0]         # ej. "002_plFsxqL"

    # Asegúrate de que el nombre coincide con el de la ruta en urls.py
    return reverse("app_sellers:serve_secure_document", kwargs={"shortname": shortname, "file_id": file_id})

# Función para limpiar valores None en un diccionario plano
def clean_none_values(d):
    """
    Reemplaza todos los valores None en el diccionario plano por strings vacíos
    (excepto para objetos o campos que son diccionarios anidados como documentos o direcciones).
    """
    return {
        k: "" if v is None or v == "None" else v
        for k, v in d.items()
    }

# Función para validar la estructura de un formulario IVA
def validate_iva_form_structure(iva_form_data):
    for block_key, block_data in iva_form_data.items():
        if isinstance(block_data, dict):
            for field, content in block_data.items():
                if isinstance(content, dict):
                    if "value" not in content or "file" not in content:
                        debug_print(f"⚠️ Campo malformado en {block_key} → {field}: {content}")
                elif isinstance(content, str):
                    debug_print(f"⚠️ Campo string no válido en {block_key} → {field}: {content}")

# Función para obtener el valor de prioridad de un JSON
def get_priority_value(json_data, key, fallback):
    val = json_data.get(key)
    return val if val not in [None, ""] else fallback

# Función para obtener los datos de la empresa desde JSON o DB
def get_company_info_data(seller, json_company: dict, use_json_data: bool = False) -> dict:
    from muaytax.app_documents.models.document import Document

    # Campos prioritarios desde JSON o DB
    entity_name = get_priority_value(json_company, "entity_name", seller.name or "")
    legal_entity = get_priority_value(json_company, "legal_entity", seller.legal_entity or "")
    country_registration = get_priority_value(
        json_company, "country_registration",
        getattr(seller.country_registration, "iso_code", "")
    )
    nif_registration = get_priority_value(json_company, "nif_registration", seller.nif_registration or "")

    # Producto y servicio
    products_and_services_code = get_priority_value(json_company, "products_and_services", seller.products_and_services_id or "")
    products_and_services_obj = ProductService.objects.filter(code=products_and_services_code).first() if products_and_services_code else None
    products_and_services = {
        "code": products_and_services_code,
        "description": products_and_services_obj.description if products_and_services_obj else products_and_services_code
    }

    desc_products_services = get_priority_value(json_company, "desc_products_services", seller.desc_products_services or "")
    desc_main_activity = get_priority_value(json_company, "desc_main_activity", seller.desc_main_activity or "")
    activity_type = get_priority_value(json_company, "activity_type", seller.activity_type or "")
    amazon_sell = get_priority_value(json_company, "amazon_sell", str(seller.amazon_sell))
    has_amazon_account = get_priority_value(
        json_company,
        "has_amazon_account",
        str(seller.has_amazon_account or "")
    )
    if has_amazon_account not in ["True", "False"]:
        has_amazon_account = ""

    # Verifica que el valor desde DB sea lista real (por si se guardó mal como string)
    raw_tokens = seller.amazon_merchant_tokens
    if isinstance(raw_tokens, str):
        try:
            raw_tokens = json.loads(raw_tokens)
        except Exception:
            raw_tokens = []

    amazon_merchant_tokens = get_priority_value(
        json_company,
        "amazon_merchant_tokens",
        raw_tokens or []
    ) or []
    
    

    # Entity type
    entity_type = get_priority_value(json_company, "entity_type", "")
    if not entity_type:
        if legal_entity in ["self-employed", "self-employed-outside"]:
            entity_type = "self_employed"
        elif legal_entity in ["sl", "llc", "other"]:
            entity_type = "company"

    # Teléfono
    contact_phone = get_priority_value(json_company, "contact_phone", str(seller.contact_phone or "").strip())
    phone_country = get_priority_value(json_company, "phone_country", getattr(seller.phone_country, "iso_code", "").strip())
    phone_country_obj = seller.phone_country

    phone_national = get_priority_value(
        json_company,
        "phone_national",
        get_phone_national_from_db(
            contact_phone=contact_phone,
            phone_country=phone_country_obj,
            seller_address=seller.seller_address
        )
    )

    # Dirección
    json_address = json_company.get("company_address", {})
    seller_address = seller.seller_address
    company_address = {
        "address": get_priority_value(json_address, "address", seller_address.address if seller_address else ""),
        "address_zip": get_priority_value(json_address, "address_zip", seller_address.address_zip if seller_address else ""),
        "address_city": get_priority_value(json_address, "address_city", seller_address.address_city if seller_address else ""),
        "address_state": get_priority_value(json_address, "address_state", seller_address.address_state if seller_address else ""),
        "address_country": get_priority_value(json_address, "address_country", getattr(seller_address.address_country, "iso_code", "") if seller_address else ""),
    }

    # Construcción del bloque final
    company_info_data = {
        "entity_name": entity_name,
        "legal_entity": legal_entity,
        "country_registration": country_registration,
        "nif_registration": nif_registration,
        "products_and_services": products_and_services,
        "desc_products_services": desc_products_services,
        "desc_main_activity": desc_main_activity,
        "phone_country": phone_country,
        "contact_phone": contact_phone,
        "activity_type": activity_type,
        "amazon_sell": amazon_sell,
        "has_amazon_account": has_amazon_account,
        "amazon_merchant_tokens": amazon_merchant_tokens,
        "entity_type": entity_type,
        "phone_national": phone_national,
        "company_address": company_address,
    }
    
    # Calcular cuántos registros de Amazon hay para generar los tipos dinámicos
    num_amazon_tokens = len(amazon_merchant_tokens or [])

    # Añadir documentos (siempre desde DB)
    company_docs = generate_document_field_mapping(
        module="company_info",
        num_amazon_tokens=num_amazon_tokens
    )["company_info"]
    doc_data = get_documents_block_data(company_docs, seller, shortname=seller.shortname)
    company_info_data.update(doc_data)
    
    updated_merchant_tokens = []
    for idx, entry in enumerate(amazon_merchant_tokens, start=1):
        token = entry.get("token", "")
        token_key = f"screenshot_token_{idx}"
        shop_key = f"screenshot_shop_{idx}"

        entry["screenshot_token"] = {
            "value": entry.get("screenshot_token", {}).get("value", ""),
            "file": company_info_data.get(token_key, {}).get("file", ""),
        }

        entry["screenshot_shop"] = {
            "value": entry.get("screenshot_shop", {}).get("value", ""),
            "file": company_info_data.get(shop_key, {}).get("file", ""),
        }

        updated_merchant_tokens.append({
            "id": idx,
            "token": token,
            "screenshot_token": entry["screenshot_token"],
            "screenshot_shop": entry["screenshot_shop"],
        })

    company_info_data["amazon_merchant_tokens"] = updated_merchant_tokens
    
    debug_print("💡 RAW DB value:", seller.amazon_merchant_tokens)
    debug_print("🧩 Usado como fallback:", amazon_merchant_tokens)
    
    return company_info_data

# Función para construir datos de revisión desde un bloque de datos
def build_review_data_from_block(data_block, visible_fields_block, field_labels, block_key="", ordering=None, manager_validation_data=None):
    """
    Construye una lista de diccionarios legibles para revisión en template,
    con agrupación lógica por campo principal.
    """
    review_data = []
    processed_fields = set()

    # === Agrupaciones definidas manualmente ===
    group_mapping = {
        # Dirección
        "company_address-address": [
            "company_address-address",
            "company_address-address_zip",
            "company_address-address_city",
            "company_address-address_state",
            "company_address-address_country",
        ],
        # Teléfono
        "phone_country": ["phone_country", "phone_national"],
        # Actividad
        "activity_type": [
            "activity_type",
            "products_and_services",
            "desc_products_services",
            "desc_main_activity",
        ],
    }
    
    # === Añadir grupo dinámico para amazon_merchant_tokens (has_amazon_account)
    merchant_tokens = data_block.get("amazon_merchant_tokens", [])
    group_mapping["has_amazon_account"] = []

    for entry in merchant_tokens:
        idx = str(entry.get("id", "")).strip()
        if idx:
            group_mapping["has_amazon_account"].extend([
                f"token_{idx}",              # ← el token de texto
                f"screenshot_token_{idx}",   # ← el archivo del token
                f"screenshot_shop_{idx}",    # ← la captura de la tienda
            ])

    # Invertir el mapping para saber a qué grupo pertenece cada campo
    field_to_group = {}
    for group_id, fields in group_mapping.items():
        for f in fields:
            field_to_group[f] = group_id

    for field_id in visible_fields_block:
        if field_id in processed_fields:
            continue

        val = data_block.get(field_id, "")
        value = val.get("value") if isinstance(val, dict) else val
        file = val.get("file") if isinstance(val, dict) else ""

        # Especial: país de constitución
        if field_id == "country_registration":
            country = Country.objects.filter(iso_code=value).first()
            value = country.name if country else value

        # Especial: entity_type
        if field_id == "entity_type":
            value = dict(ENTITY_TYPE_CHOICES).get(value, value)

        # Especial: activity_type
        if field_id == "activity_type":
            value = dict(PRODUCTS_SERVICES_CHOICES).get(value, value)

        # Especial: amazon_sell
        if field_id == "amazon_sell":
            value = dict(BOOLEAN_DISPLAY_VALUES).get(value, "_____")

        # Especial: products_and_services
        if field_id == "products_and_services" and isinstance(val, dict):
            code = val.get("code", "").strip()
            desc = val.get("description", "").strip()
            value = f"{code} - {desc}".strip(" -")

        # Especial: teléfono (combinar prefijo + número nacional)
        if field_id == "phone_country":
            phone_iso = value
            phone_prefix = ""
            try:
                country = Country.objects.get(iso_code=phone_iso)
                phone_prefix = country.phoneprefix or ""
            except Country.DoesNotExist:
                phone_prefix = ""

            phone_national = data_block.get("phone_national", "")
            if isinstance(phone_national, dict):
                phone_national = phone_national.get("value", "")

            value = f"({phone_prefix})-{phone_national}".strip()


        group_id = field_to_group.get(field_id, field_id)  # Si no está en grupo → se agrupa por sí mismo

        # Determinar tipo (misma lógica que en migración/documentos)
        if field_id == group_id:
            if file:
                field_type = "document"
            else:
                field_type = "editable"
        else:
            if file:
                field_type = "document_no_editable"
            else:
                field_type = "no_editable"
                
        validation = {}
        if manager_validation_data and block_key:
            validation = manager_validation_data.get(block_key, {}).get(field_id, {})

        review_data.append({
            "field_id": field_id,
            "label": field_labels.get(field_id, field_id.replace("_", " ").title()),
            "value": value or "",
            "file": file or "",
            "type": field_type,
            "full_id": f"{block_key}-{field_id}",
            "validation": validation,
            "group": group_id,
        })

        processed_fields.add(field_id)

    # Ordenar si se proporciona
    if ordering:
        review_data.sort(
            key=lambda x: ordering.index(x["field_id"]) if x["field_id"] in ordering else 999
        )

    return review_data

# NOTE: NO se usa - Función para asignar un grupo temático a cada campo
def get_field_group(field_id):
    """
    Asigna un grupo temático a cada campo para estructurar visualmente el bloque.
    """
    if field_id.startswith("company_address"):
        return "Dirección"
    elif field_id in {"phone_country", "phone_national", "phone_full"}:
        return "Teléfono"
    elif field_id in {
        "entity_name", "country_registration", "entity_type",
        "nif_registration", "desc_main_activity", "desc_products_services",
        "amazon_sell"
    }:
        return "General"
    elif field_id in {"products_and_services", "activity_type"}:
        return "Actividad"
    elif field_id in {
        "country_incorporation_tax_number", "bank_certificate",
        "incorporation_entity_certificate", "entity_extract_updated",
        "amazon_vies_screenshot"
    }:
        return "Documentos"
    return "Otros"

# Función para construir los datos de migración por país
def get_migration_info_data(iso, vat, json_data, migration_instance, seller_shortname):
    """
    Construye un diccionario de datos para migración por país, con prioridad entre JSON y BD.
    """
    debug_print(f"🔍 Construyendo datos de migración para {iso}...con el json {json_data}")

    # Dirección del gestor anterior (solo Francia)
    address_data_prefixed = {}
    if iso == "FR":
        address_model = migration_instance.previous_manager_address if migration_instance else None
        prefix = f"previous_manager_address_{iso}"

        address_data_prefixed = {
            f"{prefix}-address": get_priority_value(json_data, f"{prefix}-address", address_model.address if address_model else ""),
            f"{prefix}-address_city": get_priority_value(json_data, f"{prefix}-address_city", address_model.address_city if address_model else ""),
            f"{prefix}-address_state": get_priority_value(json_data, f"{prefix}-address_state", address_model.address_state if address_model else ""),
            f"{prefix}-address_zip": get_priority_value(json_data, f"{prefix}-address_zip", address_model.address_zip if address_model else ""),
            f"{prefix}-address_country": get_priority_value(json_data, f"{prefix}-address_country", address_model.address_country.iso_code if address_model and address_model.address_country else ""),
        }

    # Campos comunes para todos
    data = {
        f"last_tax_declaration_submitted_by_previous_{iso}": get_priority_value(json_data, f"last_tax_declaration_submitted_by_previous_{iso}", getattr(migration_instance, "last_tax_declaration_submitted_by_previous", "")),
        f"last_tax_declaration_date_{iso}": get_priority_value(json_data, f"last_tax_declaration_date_{iso}", migration_instance.last_tax_declaration_date.strftime("%Y-%m-%d") if migration_instance and migration_instance.last_tax_declaration_date else ""),
        f"first_tax_declaration_submitted_by_us_{iso}": get_priority_value(json_data, f"first_tax_declaration_submitted_by_us_{iso}", getattr(migration_instance, "first_tax_declaration_submitted_by_us", "")),
        f"vat_number_{iso}": get_priority_value(json_data, f"vat_number_{iso}", vat.vat_number),
        f"vat_number_certificate_{iso}": get_priority_value(json_data, f"vat_number_certificate_{iso}", ""),
    }

    # Campos específicos por país
    if iso == "FR":
        data.update({
            f"siret_{iso}": get_priority_value(json_data, f"siret_{iso}", getattr(vat, "siret", "")),
            f"previous_manager_name_{iso}": get_priority_value(json_data, f"previous_manager_name_{iso}", getattr(migration_instance, "previous_manager_name", "")),
            f"previous_manager_start_date_{iso}": get_priority_value(json_data, f"previous_manager_start_date_{iso}", migration_instance.previous_manager_start_date.strftime("%Y-%m-%d") if migration_instance and migration_instance.previous_manager_start_date else ""),
        })

    elif iso == "DE":
        data.update({
            f"steuernummer_{iso}": get_priority_value(json_data, f"steuernummer_{iso}", getattr(vat, "steuernummer", "")),
            f"steuernummer_certificate_{iso}": get_priority_value(json_data, f"steuernummer_certificate_{iso}", ""),
        })

    elif iso == "IT":
        data.update({
            f"codice_fiscale_{iso}": get_priority_value(json_data, f"codice_fiscale_{iso}", getattr(vat, "codice_fiscale", "")),
        })

    elif iso == "GB":
        data.update({
            f"gov_gateway_user_id_{iso}": get_priority_value(json_data, f"gov_gateway_user_id_{iso}", getattr(migration_instance, "gov_gateway_user_id", "")),
            f"gov_gateway_password_{iso}": get_priority_value(json_data, f"gov_gateway_password_{iso}", getattr(migration_instance, "gov_gateway_password", "")),
            f"gov_gateway_phone_number_{iso}": get_priority_value(json_data, f"gov_gateway_phone_number_{iso}", getattr(migration_instance, "gov_gateway_phone_number", "")),
            f"gov_gateway_phone_country_{iso}": get_priority_value(json_data, f"gov_gateway_phone_country_{iso}", getattr(getattr(migration_instance, "gov_gateway_phone_country", None), "iso_code", "GB")),
        })

    # Dirección del gestor anterior
    data.update(address_data_prefixed)

    # Documentos
    document_mappings = generate_document_field_mapping([iso], module="migration")["migration"]
    doc_data = get_documents_block_data(document_mappings, seller=vat.seller, seller_vat=vat, shortname=seller_shortname)
    data.update(doc_data)

    # Limpiar valores None
    data = clean_none_values(data)

    return data

# Función para obtener los documentos extras por país a partir de la DB y el JSON del formulario IVA
def get_documents_by_country_data(iso, vat, json_data, seller_shortname):
    seller = vat.seller
    data_db = {}

    # === Documentos base ===
    document_mappings = generate_document_field_mapping([iso], module="country_documents")["country_documents"]
    doc_data = get_documents_block_data(document_mappings, seller=seller, seller_vat=vat, shortname=seller_shortname)

    # === País: Alemania ===
    if iso == "DE":
        # Mostrar 3PL y ecommerce solo si NO vende en Amazon o vende sin cuenta
        show_3pl_and_ecommerce = (seller.amazon_sell is False) or (seller.amazon_sell and not seller.has_amazon_account)

        if not show_3pl_and_ecommerce:
            doc_data.pop(f"german_3pl_contract_{iso}", None)
            doc_data.pop(f"ecommerce_proof_screenshot_{iso}", None)

        # Fiscalmente transparente
        fiscal_answer_db = "True" if seller.is_fiscally_transparent else "False"
        fiscal_answer = get_priority_value(json_data, f"is_fiscally_transparent_{iso}", fiscal_answer_db)
        data_db[f"is_fiscally_transparent_{iso}"] = fiscal_answer
        if fiscal_answer != "True":
            doc_data.pop(f"admin_tax_residency_certificate_{iso}", None)

        # Nunca mostrar certificado de Amazon Business
        doc_data.pop(f"amazon_business_certificate_{iso}", None)

    # === País: Irlanda ===
    elif iso == "IE":
        amazon_sell = str(seller.amazon_sell) if seller.amazon_sell is not None else json_data.get("amazon_sell", "")
        has_amazon_account = str(seller.has_amazon_account) if seller.has_amazon_account is not None else json_data.get("has_amazon_account", "")

        # --- Mostrar enlaces solo si tiene cuenta Amazon y vende en Amazon
        show_links = amazon_sell == "True" and has_amazon_account == "True"
        if show_links:
            amazon_links_db = vat.amazon_store_links or ""
            amazon_links = get_priority_value(json_data, f"amazon_store_links_{iso}", amazon_links_db)
            if amazon_links:  # ✅ solo si hay contenido
                data_db[f"amazon_store_links_{iso}"] = amazon_links
            else:
                doc_data.pop(f"amazon_store_links_{iso}", None)
        else:
            doc_data.pop(f"amazon_store_links_{iso}", None)

        # --- Mostrar contrato 3PL solo si NO aplica Amazon completo
        show_3pl = (amazon_sell == "True" and has_amazon_account != "True") or amazon_sell == "False"
        if not show_3pl:
            doc_data.pop(f"irish_3pl_contract_{iso}", None)

        # --- Agrupar archivos multifile: business_operating_proof_{i}_{iso}
        proof_keys = sorted(
            [key for key in doc_data if key.startswith("business_operating_proof_") and key.endswith(f"_{iso}")],
            key=lambda k: int(k.split("_")[3])  # extrae el índice
        )

        business_files = []
        for key in proof_keys:
            file_info = doc_data.pop(key, None)
            if file_info and file_info.get("file"):
                file_info["field_key"] = key
                business_files.append(file_info)

        if business_files:
            data_db[f"business_operating_proof_{iso}"] = business_files

    # === Resultado final combinado
    data_db.update(doc_data)
    return clean_none_values(data_db)

# Función para generar un diccionario de etiquetas a partir de campos de entrada
def generate_label_dict_from_fields(input_fields: dict, iso_code: str, *label_dicts) -> dict:
    """
    A partir de un diccionario de datos (input_fields) con claves reales, genera un nuevo diccionario con etiquetas legibles si la clave coincide con una plantilla en uno o varios diccionarios
    """
    result = {}
    for real_field in input_fields:
        for label_dict in label_dicts:
            for key_template, label_or_lambda in label_dict.items():
                expected_key = key_template.replace("{iso}", iso_code)
                if expected_key == real_field:
                    label = label_or_lambda(iso_code) if callable(label_or_lambda) else label_or_lambda
                    result[real_field] = label
                    break  # Ya lo encontramos, no seguir en otros
    return result

# Función para extraer subclaves por prefijo
def extract_subkeys_by_prefix(data_dict, type_keys):
    """
    Extrae y combina en un solo dict todas las subclaves de los bloques que comienzan con un prefijo dado o una lista de ellos.
    """
    if isinstance(type_keys, str):
        type_keys = [type_keys]

    result = {}
    for key in data_dict:
        if any(key.startswith(prefix) for prefix in type_keys):
            block = data_dict.get(key, {})
            if isinstance(block, dict):
                result.update(block)
    return result

# NOTE: [No se esta usando ya] Función para resolver un diccionario de etiquetas dinámicas
def resolve_dynamic_labels(dicts, variable_name: str, value: str) -> dict:
    """
    Recibe uno o varios diccionarios de etiquetas con claves dinámicas (como '{iso}') y devuelve un único diccionario con las claves estaticas y valores evaluados.
    """
    if not isinstance(dicts, list):
        dicts = [dicts]

    placeholder = f"{{{variable_name}}}"
    resolved = {}

    for label_dict in dicts:
        for raw_key, func in label_dict.items():
            key = raw_key.replace(placeholder, value)
            resolved[key] = func(value)

    debug_print(f"Diccionario resuelto con {variable_name}={value}: {resolved}")
    return resolved

# Función para resolver campos agrupados por país y bloque
def resolve_grouped_fields(iso: str, block_key: str, visible_fields_list: list) -> dict:
    """
    Devuelve los grupos aplicables al país y bloque indicado, si su campo principal o al menos un hijo está visible.
    """
    all_groups = REVIEW_GROUPED_FIELDS_BY_COUNTRY(iso)
    group_map = {}

    for group_id, children in all_groups.items():
        if group_id in visible_fields_list or any(child in visible_fields_list for child in children):
            group_map[group_id] = children

    return group_map

# Determina qué bloques del formulario deben reabrirse según las validaciones.
def determine_reopen_blocks(manager_validation_data, seller_vats, relevant_countries_processed):
    """
    Determina qué bloques del formulario deben reabrirse según las validaciones.

    Reglas:
    - Si hay errores en los campos principales de 'company_info_form' ("entity_name", "country_registration"),
      se considera apertura GENERAL (todos los bloques se abren).
    - Si no, se considera apertura PARCIAL:
        - Solo los bloques con campos inválidos se abren.
        - Si el bloque es por país (e.g., migration_info_FR), se marca ese país como no enviado (`form_submitted=False`).

    Parámetros:
    - manager_validation_data: dict con las validaciones del gestor por bloque.
    - seller_vats: queryset de SellerVat activos.
    - relevant_countries_processed: lista de códigos ISO de países involucrados en la validación.

    Retorna:
    - editable_blocks: set con los bloques que deben permitirse editar en el front.
    """
    editable_blocks = set()
    main_company_fields = {"entity_name", "country_registration"}

    company_errors = {
        field for field, data in manager_validation_data.get("company_info_form", {}).items()
        if field in main_company_fields and data.get("status") == "incorrecto"
    }

    if company_errors:
        # Apertura general: todos los bloques
        editable_blocks.update(manager_validation_data.keys())
    else:
        # Apertura parcial: solo los bloques con errores pendientes
        for block_key, fields in manager_validation_data.items():
            if any(f.get("status") == "incorrecto" and f.get("pending") is True for f in fields.values()):
                editable_blocks.add(block_key)

                # Si es bloque de país y país es relevante ➜ desmarcar form_submitted
                iso = extract_iso_from_block(block_key)
                if iso and iso in relevant_countries_processed:
                    seller_vat = seller_vats.filter(vat_country__iso_code=iso).first()
                    if seller_vat:
                        seller_vat.form_submitted = False
                        seller_vat.save(update_fields=["form_submitted"])

    return editable_blocks

def merge_validated_block(block_key, form_block_fields, previous_validation_block):
    """
    Fusiona campos nuevos al bloque de validación sin sobrescribir campos existentes, ni eliminar claves especiales como 'group' o 'completed'.
    """
    result = previous_validation_block.copy() if isinstance(previous_validation_block, dict) else {}

    # Paso 1: detectar campos secundarios de grupo (que no se deben agregar)
    excluded_members = set()
    for field_id, field_data in result.items():
        if isinstance(field_data, dict) and field_data.get("pending") and field_data.get("status") == "incorrecto":
            group = field_data.get("group_members", [])
            if group:
                excluded_members.update(group)
                excluded_members.discard(field_id)  # conservar el principal

    # Paso 2: añadir nuevos campos
    for field_id in form_block_fields:
        if field_id in excluded_members:
            continue  # campo cubierto por grupo principal con error

        if field_id in result:
            continue  # ya validado, mantener intacto

        result[field_id] = {
            "status": "correcto",
            "comment": "",
            "pending": False
        }

    return result

def inject_special_dependent_fields(rep, grp_dict, iso):
    """
    Inyecta campos ocultos dependientes en grp_dict[rep] si no existen ya.
    Solo aplica a documentos especiales por país (como DE).
    """
    if iso != "DE":
        return

    special_dependencies = {
        "amazon_account_exists_DE": ["merchant_token_screenshot_DE", "ecommerce_proof_screenshot_DE"],
        "is_fiscally_transparent_DE": ["admin_tax_residency_certificate_DE"]
    }

    if rep in special_dependencies:
        grp_dict.setdefault(rep, [])
        for dep in special_dependencies[rep]:
            if dep not in grp_dict[rep]:
                grp_dict[rep].append(dep)

def save_amazon_tokens_to_seller(request, seller, company_info_data):
    from muaytax.app_documents.models.document import Document
    from muaytax.dictionaries.models.document_type import DocumentType

    debug_print("📦 Iniciando guardado de Amazon Merchant Tokens")

    amazon_tokens = company_info_data.get("amazon_merchant_tokens", [])
    previous_tokens = seller.amazon_merchant_tokens or []
    saved_token_list = []

    # Indexar los tokens previos por ID
    previous_by_id = {str(entry.get("id")): entry for entry in previous_tokens}

    for token_entry in amazon_tokens:
        token_id = str(token_entry.get("id"))
        token_value = token_entry.get("token", "").strip()

        token_field = f"screenshot_token_{token_id}"
        shop_field = f"screenshot_shop_{token_id}"

        uploaded_token_file = request.FILES.get(token_field)
        uploaded_shop_file = request.FILES.get(shop_field)

        debug_print(f"\n🔹 Procesando token ID: {token_id}")
        debug_print(f"   🔸 Token: {token_value}")
        debug_print(f"   🔍 request.FILES[{token_field}]: {uploaded_token_file.name if uploaded_token_file else '❌ No encontrado'}")
        debug_print(f"   🔍 request.FILES[{shop_field}]: {uploaded_shop_file.name if uploaded_shop_file else '❌ No encontrado'}")

        # Si hay ambos archivos, actualizamos los documentos
        if uploaded_token_file and uploaded_shop_file:
            doc_type_token, _ = DocumentType.objects.get_or_create(
                code=token_field,
                defaults={"description": f"Captura del token Amazon (Tienda #{token_id})"}
            )
            doc_token, _ = Document.objects.update_or_create(
                seller=seller,
                documentType=doc_type_token,
                sellerVat=None,
                defaults={"file": uploaded_token_file}
            )

            doc_type_shop, _ = DocumentType.objects.get_or_create(
                code=shop_field,
                defaults={"description": f"Captura de tienda con nombre entidad (Tienda #{token_id})"}
            )
            doc_shop, _ = Document.objects.update_or_create(
                seller=seller,
                documentType=doc_type_shop,
                sellerVat=None,
                defaults={"file": uploaded_shop_file}
            )

            screenshot_token = {
                "value": uploaded_token_file.name,
                "file": doc_token.file.url
            }
            screenshot_shop = {
                "value": uploaded_shop_file.name,
                "file": doc_shop.file.url
            }

            debug_print("✅ Archivos guardados correctamente")

        else:
            # Si no hay ambos archivos → conservar lo anterior
            previous_entry = previous_by_id.get(token_id, {})
            screenshot_token = previous_entry.get("screenshot_token", {"value": "", "file": ""})
            screenshot_shop = previous_entry.get("screenshot_shop", {"value": "", "file": ""})

            debug_print("⚠️ Faltan archivos → se mantienen versiones anteriores.")

        # Guardar token actualizado con capturas nuevas o anteriores
        saved_token_list.append({
            "id": token_id,
            "token": token_value,
            "screenshot_token": screenshot_token,
            "screenshot_shop": screenshot_shop
        })

    # Guardar en el modelo Seller y en el JSON
    seller.amazon_merchant_tokens = saved_token_list
    seller.save(update_fields=["amazon_merchant_tokens"])

    company_info_data["amazon_merchant_tokens"] = saved_token_list

    debug_print("\n✅ Guardado finalizado. Campo amazon_merchant_tokens actualizado.")
    debug_print(f"📥 Valor guardado en Seller:\n{saved_token_list}")

def cleanup_unused_amazon_token_documents(seller, company_data):
    """
    Elimina documentos tipo screenshot_token_{id} o screenshot_shop_{id}
    que ya no estén presentes en el JSON actual.
    """
    from muaytax.app_documents.models import Document

    # Paso 1: IDs válidos según el JSON actual
    valid_amazon_token_ids = {
        str(entry["id"]) for entry in company_data.get("amazon_merchant_tokens", [])
        if isinstance(entry, dict) and "id" in entry
    }

    # Paso 2: construir el universo de nombres de campos posibles
    all_token_fields = {
        f"screenshot_token_{i}" for i in range(1, 50)
    }.union({
        f"screenshot_shop_{i}" for i in range(1, 50)
    })

    # Paso 3: campos válidos que sí se deben conservar
    valid_fields = {
        f"screenshot_token_{i}" for i in valid_amazon_token_ids
    }.union({
        f"screenshot_shop_{i}" for i in valid_amazon_token_ids
    })

    # Paso 4: eliminar los documentos obsoletos
    Document.objects.filter(
        seller=seller,
        sellerVat__isnull=True,  # Solo company_info
        documentType__code__in=all_token_fields
    ).exclude(
        documentType__code__in=valid_fields
    ).delete()

def update_amazon_store_links(seller, iva_form_json):
    """
    Actualiza el campo `amazon_store_links` de cada SellerVat correspondiente según el contenido
    del bloque `documents_by_country_{iso}` en el JSON.
    """
    from muaytax.app_sellers.models import SellerVat

    for key, data in iva_form_json.items():
        if not key.startswith("documents_by_country_"):
            continue

        iso = key.split("documents_by_country_")[-1]
        raw_links = data.get(f"amazon_store_links_{iso}", "").strip()

        # Normalizar como lista: dividir por líneas no vacías
        links_list = [line.strip() for line in raw_links.splitlines() if line.strip()]
        normalized_links_text = "\n".join(links_list)

        try:
            seller_vat = SellerVat.objects.get(seller=seller, vat_country__iso_code=iso)
            seller_vat.amazon_store_links = normalized_links_text
            seller_vat.save(update_fields=["amazon_store_links"])
        except SellerVat.DoesNotExist:
            # Si no hay un SellerVat correspondiente, se ignora
            continue

def cleanup_unused_business_proof_documents(seller, form_data, iso_country_codes, field_to_document_type):
    from muaytax.app_documents.models.document import Document
    for iso in iso_country_codes:
        block_key = f"documents_by_country_{iso}"
        block_data = form_data.get(block_key, {})
        if not isinstance(block_data, dict):
            continue

        # Campos válidos (nombres reales de los campos del JSON)
        valid_field_names = [f"business_operating_proof_{i}" for i in range(1, 6)]
        valid_field_keys = [f"{field}_{iso}" for field in valid_field_names]

        # Códigos de documento válidos en BD
        valid_document_codes = {
            field_to_document_type.get(field_key)
            for field_key in valid_field_keys
            if field_to_document_type.get(field_key)
        }

        # Campos presentes en el JSON
        present_keys = set(block_data.keys())
        present_codes = {
            field_to_document_type.get(f"{key}_{iso}")
            for key in present_keys
            if f"{key}_{iso}" in field_to_document_type
        }

        # Documentos que deben eliminarse
        codes_to_delete = valid_document_codes - present_codes

        if codes_to_delete:
            Document.objects.filter(
                seller=seller,
                sellerVat__vat_country__iso_code=iso,
                documentType__code__in=codes_to_delete
            ).delete()

def remove_deleted_business_proof_from_json(block_data, iso_code, field_to_doc_type):
    valid_field_names = [
        f"business_operating_proof_{i}_{iso_code}" for i in range(1, 6)
    ]

    # Códigos esperados en mapping
    valid_codes = {
        field_to_doc_type.get(field)
        for field in valid_field_names
        if field_to_doc_type.get(field)
    }

    # Aquí usamos directamente las claves completas del block_data
    present_field_keys = {
        f"{k}_{iso_code}" for k in block_data.keys()
        if k.startswith("business_operating_proof_")
    }

    present_codes = {
        field_to_doc_type.get(key)
        for key in present_field_keys
        if key in field_to_doc_type
    }

    codes_to_delete = valid_codes - present_codes

    debug_print(f"🔍 Códigos válidos esperados: {valid_codes}")
    debug_print(f"✅ Códigos presentes en el JSON recibido: {present_codes}")
    debug_print(f"🗑️ Códigos a eliminar del JSON: {codes_to_delete}")

    for deleted_code in codes_to_delete:
        field_names = [
            field for field, code in field_to_doc_type.items()
            if code == deleted_code and field.startswith("business_operating_proof_")
        ]
        for field_name in field_names:
            field_without_iso = field_name.replace(f"_{iso_code}", "")
            removed = block_data.pop(field_without_iso, None)
            if removed:
                debug_print(f"❌ Campo eliminado del JSON: {field_without_iso}")


            
# Utilidad para mergear diccionarios de forma recursiva
def deep_merge_dicts(original, updates):
    for key, value in updates.items():
        if isinstance(value, dict) and isinstance(original.get(key), dict):
            deep_merge_dicts(original[key], value)
        else:
            original[key] = value
    return original