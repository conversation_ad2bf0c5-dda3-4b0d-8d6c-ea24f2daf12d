import csv
from datetime import date
from decimal import Decimal

from django.contrib import admin, messages
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.db.models import Count, Q, Sum
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.urls import path, reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from muaytax.dictionaries.models.currencies import Currency

from ..models.model_5472_1120 import AccountingRecord, PresentedM54721120


class AccountingRecordInline(admin.TabularInline):
    model = AccountingRecord
    extra = 0
    fields = (
        'date', 'transaction_type', 'description', 'amount', 'currency',
        'is_system_generated', 'system_generation_type'
    )
    readonly_fields = ('is_system_generated', 'system_generation_type')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('currency')

class PresentedM54721120Admin(admin.ModelAdmin):
    list_display = (
        'seller', 'year', 'is_processed',
        'total_assets_display', 'automatic_records_count',
        'activos_actions'
    )
    list_filter = [
        'is_processed', 'year',
        'is_first_year'
    ]
    search_fields = ('seller__name', 'year', 'seller__shortname')
    autocomplete_fields = (
        'member_country', 'tax_residence_country', 'activity_country', 'main_activity_countries'
    )
    exclude = ('is_active',)
    list_editable = ('is_processed',)
    readonly_fields = ('activos_info', 'automatic_records_info')
    actions = [
        'regenerate_automatic_records',
        'validate_automatic_records',
        'export_activos_report',
        'clean_duplicate_automatic_records'
    ]
    inlines = [AccountingRecordInline]

    fieldsets = (
        ('Información General', {
            'fields': ('seller', 'year', 'is_processed')
        }),
        ('Información de Activos', {
            'fields': ('total_assets', 'activos_info'),
            'description': 'Los activos totales se usan para generar automáticamente registros contables en formularios del año siguiente.'
        }),
        ('Registros Automáticos', {
            'fields': ('automatic_records_info',),
            'description': 'Información sobre los registros contables generados automáticamente para este formulario.'
        }),
        ('Información del Miembro', {
            'fields': (
                'member_address', 'member_country', 'tax_residence_country',
                'activity_country', 'passport', 'is_itin', 'itin'
            ),
            'classes': ('collapse',)
        }),
        ('Detalles del Formulario', {
            'fields': (
                'casilla_4b3', 'casilla_1F_all_previous_years', 'desc_main_activity',
                'type_of_activity', 'is_foreign_owned', 'is_first_year', 'is_last_year',
                'main_activity_countries', 'is_sl_self_employed', 'incorporation_date'
            ),
            'classes': ('collapse',)
        }),
        ('Traducciones', {
            'fields': ('translations',),
            'classes': ('collapse',)
        }),
    )

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                '<int:object_id>/regenerate-activos/',
                self.admin_site.admin_view(self.regenerate_activos_view),
                name='documents_presentedm54721120_regenerate_activos',
            ),
            path(
                '<int:object_id>/validate-activos/',
                self.admin_site.admin_view(self.validate_activos_view),
                name='documents_presentedm54721120_validate_activos',
            ),
        ]
        return custom_urls + urls

    def total_assets_display(self, obj):
        """Mostrar activos totales con formato"""
        if obj.total_assets:
            return format_html(
                '<span class="admin-success-text" style="font-weight: bold;">${}</span>',
                f'{obj.total_assets:,.2f}'
            )
        return format_html('<span class="admin-muted-text">Sin activos</span>')
    total_assets_display.short_description = 'Activos Totales'

    def automatic_records_count(self, obj):
        """Contar registros automáticos asociados"""

        count = AccountingRecord.objects.filter(
            pm5472_1120=obj,
            is_system_generated=True
        ).count()

        if count > 0:
            return format_html(
                '<span class="admin-badge admin-badge-warning">{} AUTO</span>',
                count
            )
        return format_html('<span class="admin-muted-text">0</span>')
    automatic_records_count.short_description = 'Registros AUTO'

    def activos_actions(self, obj):
        """Botones de acción rápida para gestión de activos"""
        regenerate_url = reverse(
            'admin:documents_presentedm54721120_regenerate_activos',
            args=[obj.pk]
        )
        validate_url = reverse(
            'admin:documents_presentedm54721120_validate_activos',
            args=[obj.pk]
        )

        return format_html(
            '''
            <div class="admin-actions-container">
                <a href="{}" class="admin-btn admin-btn-primary admin-btn-sm"
                   title="Elimina registros automáticos existentes y los recrea basándose en los activos del año anterior">Regenerar</a>
                <a href="{}" class="admin-btn admin-btn-success admin-btn-sm"
                   title="Verifica que los registros automáticos tengan los montos correctos comparando con activos del año anterior">Validar</a>
            </div>
            ''',
            regenerate_url, validate_url
        )
    activos_actions.short_description = 'Acciones Rápidas'

    def activos_info(self, obj):
        """Mostrar información detallada sobre activos"""
        if not obj.total_assets:
            return format_html('<div class="admin-muted-text">No hay activos declarados</div>')

        # Buscar formulario del año siguiente
        next_year_form = type(obj).objects.filter(
            seller=obj.seller,
            year=obj.year + 1
        ).first()

        info_html = [
            f'<div class="admin-info-box">',
            f'<strong>Activos Totales:</strong> ${obj.total_assets:,.2f}',
        ]

        if next_year_form:
            # Verificar si se generó registro automático
            auto_record = AccountingRecord.objects.filter(
                pm5472_1120=next_year_form,
                is_system_generated=True,
                system_generation_type='activos_anteriores'
            ).first()

            if auto_record:
                info_html.append(
                    f'<br/><span class="admin-success-text">✅ Generó registro automático en {next_year_form.year}: ${auto_record.amount:,.2f}</span>'
                )
            else:
                info_html.append(
                    f'<br/><span class="admin-error-text">❌ No generó registro automático en {next_year_form.year}</span>'
                )
        else:
            info_html.append(
                f'<br/><span class="admin-muted-text">No hay formulario del año siguiente ({obj.year + 1})</span>'
            )

        info_html.append('</div>')
        return format_html(''.join(info_html))

    activos_info.short_description = 'Estado de Activos'

    def automatic_records_info(self, obj):
        """Mostrar información sobre registros automáticos"""
        # Buscar formulario del año anterior
        previous_year_form = type(obj).objects.filter(
            seller=obj.seller,
            year=obj.year - 1
        ).first()

        # Buscar registros automáticos para este formulario
        auto_records = AccountingRecord.objects.filter(
            pm5472_1120=obj,
            is_system_generated=True,
            system_generation_type='activos_anteriores'
        )

        info_html = ['<div class="admin-info-box">']

        if not previous_year_form:
            info_html.append('<span class="admin-muted-text">No hay formulario del año anterior</span>')
        elif not previous_year_form.total_assets or previous_year_form.total_assets <= 0:
            info_html.append(f'<span class="admin-muted-text">Año anterior ({obj.year - 1}) sin activos: ${previous_year_form.total_assets or 0:,.2f}</span>')
        else:
            info_html.append(f'<strong>Activos año anterior ({obj.year - 1}):</strong> ${previous_year_form.total_assets:,.2f}')

            if auto_records.exists():
                auto_record = auto_records.first()
                if auto_record.amount == previous_year_form.total_assets:
                    info_html.append(
                        f'<br/><span class="admin-success-text">✅ Registro automático correcto: ${auto_record.amount:,.2f}</span>'
                    )
                else:
                    info_html.append(
                        f'<br/><span class="admin-warning-text">⚠️ Registro automático incorrecto: ${auto_record.amount:,.2f} (debería ser ${previous_year_form.total_assets:,.2f})</span>'
                    )
            else:
                info_html.append(
                    f'<br/><span class="admin-error-text">❌ Falta registro automático (debería ser ${previous_year_form.total_assets:,.2f})</span>'
                )

        info_html.append('</div>')

        # Añadir botones de acción si es necesario
        if previous_year_form and previous_year_form.total_assets and previous_year_form.total_assets > 0:
            regenerate_url = reverse(
                'admin:documents_presentedm54721120_regenerate_activos',
                args=[obj.pk]
            )
            validate_url = reverse(
                'admin:documents_presentedm54721120_validate_activos',
                args=[obj.pk]
            )

            info_html.append(
                f'<div class="admin-actions-container" style="margin-top: 10px;">'
                f'<a href="{regenerate_url}" class="admin-btn admin-btn-primary">Regenerar</a>'
                f'<a href="{validate_url}" class="admin-btn admin-btn-success">Validar</a>'
                f'</div>'
            )

        return format_html(''.join(info_html))

    automatic_records_info.short_description = 'Registros Automáticos'

    # === ACCIONES MASIVAS ===

    def regenerate_automatic_records(self, request, queryset):
        """Regenerar registros automáticos para múltiples formularios"""

        regenerated_count = 0
        errors = []

        for form_obj in queryset:
            try:
                with transaction.atomic():
                    previous_year_form = PresentedM54721120.objects.filter(
                        seller=form_obj.seller,
                        year=form_obj.year - 1
                    ).first()

                    if (not previous_year_form or
                        not previous_year_form.total_assets or
                        previous_year_form.total_assets <= 0 or
                        not previous_year_form.is_processed):
                        continue

                    usd_currency = Currency.objects.get(code='USD')
                    record, created = AccountingRecord.objects.get_or_create(
                        seller=form_obj.seller,
                        pm5472_1120=form_obj,
                        is_system_generated=True,
                        system_generation_type='activos_anteriores',
                        defaults={
                            'date': date(form_obj.year, 1, 1),
                            'transaction_type': '5',
                            'description': f'Contribuciones por activos de años anteriores ({previous_year_form.year})',
                            'amount': previous_year_form.total_assets,
                            'currency': usd_currency,
                            'total_currency': previous_year_form.total_assets,
                        }
                    )

                    if not created and record.amount != previous_year_form.total_assets:
                        record.amount = previous_year_form.total_assets
                        record.total_currency = previous_year_form.total_assets
                        record.save()

                    regenerated_count += 1

            except (Currency.DoesNotExist, IntegrityError):
                continue
            except Exception as e:
                errors.append(f"{form_obj.seller.shortname} ({form_obj.year}): {str(e)}")

        if regenerated_count > 0:
            messages.success(
                request,
                f'✅ {regenerated_count} registros automáticos regenerados correctamente.'
            )

        if errors:
            messages.warning(
                request,
                f'⚠️ Errores en {len(errors)} formularios: {"; ".join(errors[:3])}'
            )

    regenerate_automatic_records.short_description = "Regenerar registros automáticos"

    def validate_automatic_records(self, request, queryset):
        """Validar la consistencia de registros automáticos"""

        validation_results = []

        for form_obj in queryset:
            # Buscar formulario del año anterior
            previous_year_form = PresentedM54721120.objects.filter(
                seller=form_obj.seller,
                year=form_obj.year - 1
            ).first()

            # Buscar registro automático
            auto_record = AccountingRecord.objects.filter(
                pm5472_1120=form_obj,
                is_system_generated=True,
                system_generation_type='activos_anteriores'
            ).first()

            status = "✅ OK"
            details = []

            if not previous_year_form:
                status = "⚠️ SIN AÑO ANTERIOR"
                details.append("No existe formulario del año anterior")
            elif not previous_year_form.total_assets:
                status = "SIN ACTIVOS"
                details.append("Año anterior sin activos declarados")
            elif previous_year_form.total_assets <= 0:
                status = "ACTIVOS CERO"
                details.append("Activos del año anterior = 0")
            elif not auto_record:
                status = "❌ FALTA REGISTRO"
                details.append("Debería existir registro automático pero no existe")
            elif auto_record.amount != previous_year_form.total_assets:
                status = "⚠️ MONTO INCORRECTO"
                details.append(f"Registro: ${auto_record.amount} vs Activos: ${previous_year_form.total_assets}")

            validation_results.append({
                'seller': form_obj.seller.shortname,
                'year': form_obj.year,
                'status': status,
                'details': '; '.join(details) if details else 'Registro automático correcto'
            })

        # Mostrar resultados
        if validation_results:
            report_lines = []
            for result in validation_results[:10]:  # Limitar a 10 para no saturar
                report_lines.append(
                    f"{result['seller']} ({result['year']}): {result['status']} - {result['details']}"
                )

            messages.info(
                request,
                mark_safe(f"Validación completada:<br/>{'<br/>'.join(report_lines)}")
            )

    validate_automatic_records.short_description = "Validar registros automáticos"

    def export_activos_report(self, request, queryset):
        """Exportar reporte de activos y registros automáticos"""

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="activos_report.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Seller', 'Year', 'Total_Assets', 'Previous_Year_Assets',
            'Auto_Records_Count', 'Auto_Record_Amount', 'Status'
        ])

        for form_obj in queryset:
            previous_year_form = PresentedM54721120.objects.filter(
                seller=form_obj.seller,
                year=form_obj.year - 1
            ).first()

            auto_records = AccountingRecord.objects.filter(
                pm5472_1120=form_obj,
                is_system_generated=True,
                system_generation_type='activos_anteriores'
            )

            auto_record_amount = auto_records.first().amount if auto_records.exists() else 0

            status = "OK"
            if previous_year_form and previous_year_form.total_assets and previous_year_form.total_assets > 0:
                if not auto_records.exists():
                    status = "MISSING_AUTO_RECORD"
                elif auto_record_amount != previous_year_form.total_assets:
                    status = "AMOUNT_MISMATCH"

            writer.writerow([
                form_obj.seller.shortname,
                form_obj.year,
                form_obj.total_assets or 0,
                previous_year_form.total_assets if previous_year_form else 0,
                auto_records.count(),
                auto_record_amount,
                status
            ])

        return response

    export_activos_report.short_description = "Exportar reporte de activos"

    def clean_duplicate_automatic_records(self, request, queryset):
        """Limpiar registros automáticos duplicados para los formularios seleccionados"""

        cleaned_count = 0
        total_deleted = 0

        with transaction.atomic():
            for form_obj in queryset:
                # Buscar SOLO registros automáticos de activos anteriores
                automatic_records = AccountingRecord.objects.filter(
                    seller=form_obj.seller,
                    pm5472_1120=form_obj,
                    is_system_generated=True,
                    system_generation_type='activos_anteriores'
                ).order_by('created_at')

                if automatic_records.count() > 1:
                    # Mantener el más reciente, eliminar los anteriores
                    latest_automatic = automatic_records.last()

                    # Eliminar todos los automáticos EXCEPTO el más reciente
                    duplicates_to_delete = automatic_records.exclude(id=latest_automatic.id)
                    deleted_for_form = duplicates_to_delete.count()
                    duplicates_to_delete.delete()

                    total_deleted += deleted_for_form
                    cleaned_count += 1

        if cleaned_count > 0:
            messages.success(
                request,
                f'Limpieza completada: {total_deleted} registros automáticos duplicados eliminados de {cleaned_count} formularios.'
            )
        else:
            messages.info(
                request,
                'No se encontraron registros automáticos duplicados en los formularios seleccionados.'
            )

    clean_duplicate_automatic_records.short_description = "Limpiar registros automáticos duplicados"

    # === VISTAS PERSONALIZADAS ===

    def regenerate_activos_view(self, request, object_id):
        """Regenerar activos para un formulario específico"""

        try:
            form_obj = PresentedM54721120.objects.get(id=object_id)

            with transaction.atomic():
                previous_year_form = PresentedM54721120.objects.filter(
                    seller=form_obj.seller,
                    year=form_obj.year - 1
                ).first()

                if (not previous_year_form or
                    not previous_year_form.total_assets or
                    previous_year_form.total_assets <= 0 or
                    not previous_year_form.is_processed):
                    messages.warning(
                        request,
                        f'⚠️ No se puede regenerar: {form_obj.seller.shortname} ({form_obj.year}) no tiene activos en año anterior'
                    )
                else:
                    usd_currency = Currency.objects.get(code='USD')
                    record, created = AccountingRecord.objects.get_or_create(
                        seller=form_obj.seller,
                        pm5472_1120=form_obj,
                        is_system_generated=True,
                        system_generation_type='activos_anteriores',
                        defaults={
                            'date': date(form_obj.year, 1, 1),
                            'transaction_type': '5',
                            'description': f'Contribuciones por activos de años anteriores ({previous_year_form.year})',
                            'amount': previous_year_form.total_assets,
                            'currency': usd_currency,
                            'total_currency': previous_year_form.total_assets,
                        }
                    )

                    if not created and record.amount != previous_year_form.total_assets:
                        record.amount = previous_year_form.total_assets
                        record.total_currency = previous_year_form.total_assets
                        record.save()

                    status_message = "creado" if created else "actualizado"
                    messages.success(
                        request,
                        f'✅ Registro automático {status_message} para {form_obj.seller.shortname} ({form_obj.year}). '
                        f'Monto: ${previous_year_form.total_assets}'
                    )

        except (Currency.DoesNotExist, IntegrityError):
            messages.error(request, f'❌ Error: No se pudo crear el registro automático')
        except Exception as e:
            messages.error(request, f'❌ Error al regenerar: {str(e)}')

        return redirect('admin:documents_presentedm54721120_changelist')

    def validate_activos_view(self, request, object_id):
        """Validar activos para un formulario específico"""

        try:
            form_obj = PresentedM54721120.objects.get(id=object_id)

            # Buscar formulario del año anterior
            previous_year_form = PresentedM54721120.objects.filter(
                seller=form_obj.seller,
                year=form_obj.year - 1
            ).first()

            # Buscar registro automático
            auto_record = AccountingRecord.objects.filter(
                pm5472_1120=form_obj,
                is_system_generated=True,
                system_generation_type='activos_anteriores'
            ).first()

            validation_msg = []
            validation_msg.append(f"Validación para {form_obj.seller.shortname} ({form_obj.year}):")

            if not previous_year_form:
                validation_msg.append("⚠️ No existe formulario del año anterior")
            elif not previous_year_form.total_assets or previous_year_form.total_assets <= 0:
                validation_msg.append(f"Año anterior sin activos (${previous_year_form.total_assets or 0})")
            else:
                validation_msg.append(f"✅ Activos año anterior: ${previous_year_form.total_assets}")

                if not auto_record:
                    validation_msg.append("❌ FALTA registro automático - debería existir")
                else:
                    validation_msg.append(f"✅ Registro automático existe: ${auto_record.amount}")
                    if auto_record.amount != previous_year_form.total_assets:
                        validation_msg.append(f"⚠️ MONTO INCORRECTO - Debería ser ${previous_year_form.total_assets}")
                    else:
                        validation_msg.append("✅ Monto correcto")

            messages.info(request, mark_safe('<br/>'.join(validation_msg)))

        except Exception as e:
            messages.error(request, f'❌ Error en validación: {str(e)}')

        return redirect('admin:documents_presentedm54721120_changelist')

class AccountingrecordAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'seller', 'pm5472_1120', 'date', 'transaction_type',
        'description', 'amount', 'currency', 'total_currency',
        'is_system_generated', 'system_generation_type'  # NUEVOS CAMPOS
    )
    list_filter = [
        'date', 'transaction_type', 'currency',
        'is_system_generated', 'system_generation_type'  # NUEVOS FILTROS
    ]
    search_fields = ('id', 'seller__name', 'date', 'currency__description')
    exclude = ('is_active',)
    readonly_fields = ('created_at', 'modified_at')

    # Acciones específicas para registros automáticos
    actions = ['mark_as_manual', 'mark_as_system', 'clean_duplicate_records']

    def mark_as_manual(self, request, queryset):
        """Marcar registros como manuales (para correcciones)"""
        updated = queryset.update(
            is_system_generated=False,
            system_generation_type=None
        )
        messages.success(request, f'✅ {updated} registros marcados como manuales')
    mark_as_manual.short_description = "Marcar como manuales"

    def mark_as_system(self, request, queryset):
        """Marcar registros como automáticos del sistema"""
        updated = queryset.update(
            is_system_generated=True,
            system_generation_type='activos_anteriores'
        )
        messages.success(request, f'✅ {updated} registros marcados como automáticos')
    mark_as_system.short_description = "Marcar como automáticos"

    def clean_duplicate_records(self, request, queryset):
        """Limpiar registros automáticos duplicados para los registros seleccionados"""

        cleaned_count = 0
        total_deleted = 0

        with transaction.atomic():
            for record in queryset:
                # Buscar SOLO registros automáticos de activos anteriores
                automatic_records = AccountingRecord.objects.filter(
                    seller=record.seller,
                    pm5472_1120=record.pm5472_1120,
                    is_system_generated=True,
                    system_generation_type='activos_anteriores'
                ).order_by('created_at')

                if automatic_records.count() > 1:
                    # Mantener el más reciente, eliminar los anteriores
                    latest_automatic = automatic_records.last()

                    # Eliminar todos los automáticos EXCEPTO el más reciente
                    duplicates_to_delete = automatic_records.exclude(id=latest_automatic.id)
                    deleted_for_record = duplicates_to_delete.count()
                    duplicates_to_delete.delete()

                    total_deleted += deleted_for_record
                    cleaned_count += 1

        if cleaned_count > 0:
            messages.success(
                request,
                f'Limpieza completada: {total_deleted} registros automáticos duplicados eliminados de {cleaned_count} registros.'
            )
        else:
            messages.info(
                request,
                'No se encontraron registros automáticos duplicados en los registros seleccionados.'
            )

    clean_duplicate_records.short_description = "Limpiar registros automáticos duplicados"



