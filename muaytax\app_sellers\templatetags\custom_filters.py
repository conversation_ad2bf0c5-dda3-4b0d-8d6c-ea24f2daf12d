from django import template
import json

register = template.Library()

@register.filter
def get_item(dictionary, key):
    if isinstance(dictionary, dict):  # Verifica si es un diccionario
        return dictionary.get(key, "")
    return ""  # Evita error devolviendo un string vacío si no es un diccionario

@register.filter(name='getattr_custom')
def getattr_custom(obj, attr_name):
    """Devuelve el atributo de un objeto si existe, sino None."""
    return getattr(obj, attr_name, None)

@register.filter
def get_by_country(partners_list, country_iso):
    """
    Filtra una lista de socios y devuelve solo los que están confirmados,
    sin baja y que pertenecen al país especificado.
    """
    if not isinstance(partners_list, list):
        return []
    return [p for p in partners_list if (
        p.get("vat_country") == country_iso and
        p.get("confirmation_status") is True and
        not p.get("is_deregistered", False)
    )]

@register.filter
def replace_underscore(value):
    return value.replace('_', ' ').title()

@register.filter(name='add_id_suffix')
def add_id_suffix(field, suffix):
    """
    Añade un sufijo al atributo `id` del campo renderizado, para que sea único por país.
    """
    if hasattr(field.field.widget, 'attrs'):
        original_id = field.auto_id
        if original_id:
            field.field.widget.attrs['id'] = f"{original_id}_{suffix}"
    return field

@register.filter
def getitem(value, arg):
    try:
        if hasattr(value, '__getitem__'):
            return value[arg]
    except Exception:
        return None
    return None

@register.filter
def contains(list_obj, item):
    return item in list_obj

@register.filter
def get_json(data, key):
    if isinstance(data, dict):
        raw = data.get(key, "{}")
        return json.loads(raw)
    return {}

@register.filter
def index(sequence, position):
    try:
        return sequence[position]
    except (IndexError, TypeError):
        return ''


@register.filter
def attr(form, field_name):
    """Para acceder a campos dinámicos"""
    try:
        return form[field_name]
    except (KeyError, AttributeError):
        return None

@register.filter
def has_key(dict_obj, key):
    """Devuelve True si el dict tiene la clave indicada"""
    try:
        return key in dict_obj
    except Exception:
        return False
    
@register.filter
def endswith(value, suffix):
    return str(value).endswith(str(suffix))