# Usa la imagen base de Nginx
FROM nginx:latest

# Elimina el archivo de configuración predeterminado de Nginx
RUN rm /etc/nginx/conf.d/default.conf

# Copia tu archivo de configuración personalizado de Nginx al contenedor
COPY ./compose/production-ovh/nginx/nginx.conf /etc/nginx/nginx.conf

# Expone el puerto en el que Nginx escucha las solicitudes HTTP
EXPOSE 80

# Comando para iniciar Nginx en primer plano cuando se inicie el contenedor
CMD ["nginx", "-g", "daemon off;"]