from typing import List

from rest_framework.views import APIView
from rest_framework.generics import CreateAPIView, GenericAPIView, RetrieveUpdateAPIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework import status

from django.shortcuts import get_object_or_404

from muaytax.app_sellers.models import Seller, SellerVat
from muaytax.dictionaries.models import Country

from muaytax.app_sellers.serializers import SellerVatSerializer
from muaytax.dictionaries.serializers import CountrySerializer

from muaytax.app_marketplaces.models.wizard import MarketplaceWizardDraft
from muaytax.app_marketplaces.choices import MarketplaceChoices

from rest_framework.exceptions import NotFound

class SellerVatBaseDRFView(GenericAPIView):
    permission_classes = [AllowAny]
    # permission_classes = [IsAuthenticated]

    def get_seller(self):
        shortname = self.kwargs.get('shortname')
        try:
            return Seller.objects.only('id').get(shortname=shortname)
        except Seller.DoesNotExist:
            raise NotFound({"shortname": "Vendedor no encontrado con este identificador."})
        
    def get_wizard_vat_ids(self, seller: Seller, store: str, exclude_expired: bool = False) -> List[int]:
        """
        Método para obtener los IDs de VAT de los borradores del vendedor.
        :param seller: Vendedor del que se quieren obtener los borradores.
        :param store: Tienda de la que se quieren obtener los borradores.
        :param exclude_expired: Si se quieren excluir los borradores expirados.
        :return: Lista de IDs de VAT de los borradores del vendedor.
        """
        vat_ids = set()
        drafts = MarketplaceWizardDraft.objects.filter(
            seller=seller,
            store=store,
            is_completed=False,
        )

        for draft in drafts:
            if not draft.wizard_data:
                continue
            if exclude_expired and draft.is_expired:
                continue
            vat_ids.update(draft.wizard_data.get("seller_vat_ids", []))

        return list(vat_ids) if vat_ids else []

class ListSellerVatDRFView(SellerVatBaseDRFView):
    serializer_class = SellerVatSerializer

    def get(self, request, *args, **kwargs):
        seller = self.get_seller()
        draft = request.GET.get("draft", False)
        draft_vat_ids = []
        
        if draft:
            draft = draft.lower()
            if draft in MarketplaceChoices.values:
                store = draft
                draft_vat_ids = self.get_wizard_vat_ids(seller, store)

        queryset = SellerVat.objects.filter(
            seller=seller
            ).exclude(
                id__in=draft_vat_ids
            ).select_related("vat_country", "vat_address").order_by("created_at")
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class ListDraftSellerVatDRFView(SellerVatBaseDRFView):
    serializer_class = SellerVatSerializer

    def get(self, request, *args, **kwargs):
        seller = self.get_seller()
        draft = request.GET.get("draft", False)
        draft_vat_ids = []

        if draft:
            draft = draft.lower()
            if draft in MarketplaceChoices.values:
                store = draft
                draft_vat_ids = self.get_wizard_vat_ids(seller, store, exclude_expired=True)

        queryset = SellerVat.objects.filter(
            id__in=draft_vat_ids,
            seller=seller
        ).select_related("vat_country", "vat_address").order_by("created_at")

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class AddSellerVatDRFView(CreateAPIView, SellerVatBaseDRFView):
    queryset = SellerVat.objects.all()
    serializer_class = SellerVatSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['seller'] = self.get_seller()
        context['draft_wizard_id'] = self.request.COOKIES.get("_mtxMkpWizShop")
        return context
    
    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)

        response.data = {
            "id": response.data["id"],
            "message": "IVA creado correctamente."
        }

        response.status_code = status.HTTP_201_CREATED
        return response

class SellerVatDetailDRFView(RetrieveUpdateAPIView, SellerVatBaseDRFView):
    serializer_class = SellerVatSerializer

    def get_queryset(self):
        seller = self.get_seller()
        return SellerVat.objects.filter(seller=seller)

    def get_object(self):
        queryset = self.get_queryset()
        pk = self.kwargs.get('pk')

        try:
            return queryset.get(pk=pk)
        except SellerVat.DoesNotExist:
            raise NotFound(detail="No se encontró el Vat con el identificador proporcionado.")

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response({"message": "IVA actualizado correctamente"}, status=status.HTTP_200_OK)
    
    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        wizard_id = request.COOKIES.get("_mtxMkpWizShop")

        if wizard_id:
            draft_session = MarketplaceWizardDraft.objects.filter(
                seller=instance.seller,
                wizard_id=str(wizard_id)
            ).only("wizard_data").first()

            if draft_session:
                wizard_data = draft_session.wizard_data or {}
                vat_ids = wizard_data.get("seller_vat_ids", [])

                if instance.id in vat_ids:
                    vat_ids.remove(instance.id)
                    wizard_data["seller_vat_ids"] = vat_ids
                    draft_session.wizard_data = wizard_data
                    draft_session.save(update_fields=["wizard_data"])

        instance.delete()
        return Response({"message": "IVA eliminado correctamente"}, status=status.HTTP_204_NO_CONTENT)

class GetCountriesNotRegistered(APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        """
        Endpoint to get countries not registered in the database.
        """
        shortname = self.kwargs.get('shortname')
        if not shortname:
            return Response({"error": "Shortname is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        seller = get_object_or_404(Seller.objects.only("id"), shortname=shortname)

        registered_country_codes = SellerVat.objects.filter(
            seller=seller
        ).values_list('vat_country', flat=True)

        unregistered_countries = Country.objects.exclude(
            iso_code__in=registered_country_codes
        )

        serializer = CountrySerializer(unregistered_countries, many=True) 

        return Response(serializer.data, status=status.HTTP_200_OK)