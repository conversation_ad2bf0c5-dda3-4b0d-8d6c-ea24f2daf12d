from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.http import HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, UpdateView, CreateView, DeleteView
from django.db import IntegrityError, transaction

from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_providers.forms.provider import ProviderChangeForm, ProviderDeleteForm
from muaytax.app_providers.models.provider import Provider
from muaytax.app_sellers.models.seller import Seller
from muaytax.users.permissions import IsSellerShortnamePermission
from muaytax.utils.mixins import JsonableResponseMixin


class ProviderListView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    model = Provider
    template_name_suffix = "_list"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return seller.provider_seller.all()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["seller"] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class ProviderDetailView(JsonableResponseMixin, LoginRequiredMixin, IsSellerShortnamePermission, UpdateView):
    model = Provider
    form_class = ProviderChangeForm
    template_name_suffix = "_detail"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["seller"] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return context
    
    def form_valid(self, form):
        response = None
        try:
            with transaction.atomic():
                form.instance.seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
                response = super().form_valid(form)
        except IntegrityError as e:
            if 'duplicate key value violates unique constraint' in str(e):
                form.errors['__all__'] = form.error_class([f"Ya existe un proveedor con este NIF"])
            response = self.form_invalid(form)

        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.accepts("text/html"):
            return self.render_to_response(self.get_context_data(form=form))
        else:
            return JsonResponse({"errors": form.errors}, status=400)
        
        # return self.render_to_response(self.get_context_data(form=form))

    def dict_data(self, form):
        obj = form.instance
        return {
            'is_created': False,
            'type': 'provider',
            "data": {
                "pk": obj.pk,
                "name": obj.name,
                "nif": obj.nif_cif_iva,
                "zip": obj.zip,
                "vies": obj.vies,
                "country": obj.country.pk if obj.country else None,
                "nif_cif_iva_country": obj.nif_cif_iva_country.pk if obj.nif_cif_iva_country else None,
            }
        }

    def get_success_url(self) -> str:
        return reverse(
            "app_providers:list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class ProviderNewView(JsonableResponseMixin, LoginRequiredMixin, IsSellerShortnamePermission, CreateView):
    model = Provider
    form_class = ProviderChangeForm
    template_name_suffix = "_detail"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["seller"] = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        return context

    def form_valid(self, form):
        response = None
        try:
            with transaction.atomic():
                form.instance.seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
                response = super().form_valid(form)
        except IntegrityError as e:
            if 'duplicate key value violates unique constraint' in str(e):
                form.errors['__all__'] = form.error_class([f"Ya existe un proveedor con este NIF"])
            response = self.form_invalid(form)

        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.accepts("text/html"):
            return self.render_to_response(self.get_context_data(form=form))
        else:
            return JsonResponse({"errors": form.errors}, status=400)
        # return self.render_to_response(self.get_context_data(form=form))

    def get_initial(self):
        initial = super().get_initial()
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        last_provider = Provider.objects.filter(seller_id=seller.id, provider_number__isnull=False).order_by(
            "-provider_number").first()
        next_number = int(last_provider.provider_number) + 1 if last_provider else 1
        initial['provider_number'] = f"{next_number:06}"
        initial['seller'] = seller
        return initial

    def dict_data(self, form):
        obj = form.instance
        return {
            'is_created': True,
            'type': 'provider',
            "data": {
                "pk": obj.pk,
                "name": obj.name,
                "nif": obj.nif_cif_iva,
                "zip": obj.zip,
                "vies": obj.vies,
                "country": obj.country.pk if obj.country else None,
                "nif_cif_iva_country": obj.nif_cif_iva_country.pk if obj.nif_cif_iva_country else None,
            }
        }

    def get_success_url(self) -> str:
        return reverse(
            "app_providers:list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class ProviderDeleteView(LoginRequiredMixin, IsSellerShortnamePermission, SuccessMessageMixin, DeleteView):
    model = Provider
    form_class = ProviderDeleteForm
    template_name_suffix = "_delete"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        provider = get_object_or_404(Provider, seller_id=seller.id, pk=self.kwargs["pk"])
        invoice_count = Provider.objects.filter(seller_id=seller.id, invoice__provider_id=provider.id).count()
        provider_list = Provider.objects.filter(seller_id=seller.id).exclude(id=provider.id).order_by("name")
        print("invoice_count:" + str(invoice_count))

        context = super().get_context_data(**kwargs)
        context["seller"] = seller
        context["provider_list"] = provider_list
        context["invoice_count"] = invoice_count
        return context

    def form_valid(self, form):

        print("form_valid")
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        providerOld = get_object_or_404(Provider, pk=self.kwargs["pk"])
        print("providerOld:" + str(providerOld))
        providerNew = self.request.POST.get('provider')
        print("providerNew:" + str(providerNew))

        if providerOld != providerNew and providerNew != None:
            invoice_list = Invoice.objects.filter(seller_id=seller.id, provider_id=providerOld.id)
            print("invoice_list:" + str(invoice_list))

            for invoice in invoice_list:
                invoice.provider_id = providerNew
                invoice.save()

        return super().form_valid(form)

    def get_success_url(self) -> str:
        return reverse(
            "app_providers:list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class ProviderUpdateView(LoginRequiredMixin, IsSellerShortnamePermission, SuccessMessageMixin, UpdateView):
    model = Provider
    form_class = ProviderChangeForm
    success_message = _("Information successfully updated")
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
