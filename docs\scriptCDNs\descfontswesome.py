import os
import re
import requests
from urllib.parse import urljoin, urlparse

# URL del archivo CSS de FontAwesome
css_url = 'https://use.fontawesome.com/releases/v6.2.1/css/all.css'
# Directorio donde se guardarán los archivos
output_dir = os.path.join('docs', 'scriptCDNs', 'fontawesome', 'v.6.2.1')

# Crear el directorio si no existe
os.makedirs(output_dir, exist_ok=True)

# Descargar el archivo CSS
css_response = requests.get(css_url)
css_content = css_response.text
css_file_path = os.path.join(output_dir, 'all.css')

with open(css_file_path, 'w') as css_file:
    css_file.write(css_content)

print(f"Archivo CSS descargado y guardado en {css_file_path}")

# Extraer todas las URLs dentro del CSS
urls = re.findall(r'url\(([^)]+)\)', css_content)

for url in urls:
    url = url.strip('\'"')

    # Resolver la URL relativa
    full_url = urljoin(css_url, url)
    parsed_url = urlparse(full_url)
    
    # Determinar la ruta relativa del archivo desde la URL
    relative_path = parsed_url.path.lstrip('/')  # Quitar la barra inicial
    
    # Crear el subdirectorio si no existe (preserva la estructura relativa)
    file_dir = os.path.join(output_dir, os.path.dirname(relative_path))
    os.makedirs(file_dir, exist_ok=True)
    
    file_name = os.path.basename(parsed_url.path)
    file_path = os.path.join(file_dir, file_name)

    # Descargar y guardar el archivo
    print(f"Descargando {full_url}...")
    response = requests.get(full_url)
    with open(file_path, 'wb') as file:
        file.write(response.content)

    print(f"Archivo guardado en {file_path}")

print("Descarga completa.")
