-- <PERSON><PERSON>IMIN<PERSON> LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_130' ) THEN
	  DROP FUNCTION func_calc_model_es_130(INTEGER,INTEGER,INTEGER);
      DROP FUNCTION func_calc_model_es_130(INTEGER,INTEGER,INTEGER,NUMERIC,NUMERIC);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_130(sellerid INTEGER, date_year INTEGER, month_max INTEGER, prev_ca07 NUMERIC, prev_ca19 NUMERIC)
RETURNS varchar AS $$
DECLARE
    counter INTEGER := 0;
    GAST07 NUMERIC := 0;
    CA01 NUMERIC := 0;
    CA02 NUMERIC := 0;
    CA03 NUMERIC := 0;
    CA04 NUMERIC := 0;
    CA05 NUMERIC := 0;
    CA06 NUMERIC := 0;
    CA07 NUMERIC := 0;
    CA08 NUMERIC := 0;
    CA09 NUMERIC := 0;
    CA10 NUMERIC := 0;
    CA11 NUMERIC := 0;
    CA12 NUMERIC := 0;
    CA13 NUMERIC := 0;
    CA14 NUMERIC := 0;
    CA15 NUMERIC := 0;
    CA16 NUMERIC := 0;
    CA17 NUMERIC := 0;
    CA18 NUMERIC := 0;
    CA19 NUMERIC := 0;
    CA20 NUMERIC := 0;
    seller_self_employed BOOLEAN := false;
    seller_eqtax BOOLEAN := false;
    seller_direct_estimation BOOLEAN := false;
    net_yields_last_year NUMERIC := 0;
    short_name VARCHAR := '';
    inv_amount_general RECORD;
    inv_amount_surcharge RECORD;
    inv_vat_surcharge RECORD;
    inv_intra_surcharge RECORD;
    inv_eqtax_surcharge RECORD;
    inv_irpf RECORD;

    CA02_BASE NUMERIC := 0;
    CA02_IVA_ES NUMERIC := 0;
    CA02_INTRA600 NUMERIC := 0;
    CA02_INTRA600_CALC NUMERIC := 0;
    CA02_INTRAOTROS NUMERIC := 0;
    CA02_INTRAOTROS_CALC NUMERIC := 0;
    CA02_EQTAX NUMERIC := 0;
    CA02_TOTAL NUMERIC := 0;

    -- NUEVA VARIABLE: TOTAL DE AMORTIZACIONES
    amortization_total NUMERIC := 0;
    CA02_AMORTIZATION NUMERIC := 0;
    
BEGIN
    -- ASIGNAR VALORES A LAS VARIABLES 'seller_self_employed' y 'seller_eqtax'
    SELECT legal_entity = 'self-employed' AS legal_entity, eqtax, shortname INTO seller_self_employed, seller_eqtax, short_name FROM sellers_seller WHERE id = sellerid;

    -- CONSULTA DE AMORTIZACIONES (AJUSTADA CON FECHA FINAL CORRECTA)
    SELECT DISTINCT COALESCE(SUM(
        CASE 
            WHEN (ae.start_date + (INTERVAL '1 month' * (s.num - 1))) 
                BETWEEN (date_year || '-01-01')::date 
                AND (DATE_TRUNC('month', (date_year || '-' || month_max || '-01')::date) + INTERVAL '1 month' - INTERVAL '1 day')::date 
            THEN 
                CASE 
                    WHEN s.num = ae.total_months THEN ae.final_monthly_quota 
                    ELSE ae.monthly_quota 
                END
            ELSE 0
        END
    ), 0) 
    INTO amortization_total
    FROM invoices_amortizationentry ae
    JOIN generate_series(1, ae.total_months) s(num) ON true
    JOIN invoices_invoice inv ON inv.id = ae.invoice_id
    WHERE inv.seller_id = sellerid AND inv.status_id = 'revised';


    --ASIGNAR VALORES A LAS VARIABLES 'seller_direct_estimation'
    SELECT is_direct_estimation INTO seller_direct_estimation FROM sellers_seller WHERE id = sellerid;

    --ASIGNAR VALORES A LAS VARIABLES 'net_yields_last_year' (Rendimientos netos del último año)
    SELECT net_yields INTO net_yields_last_year FROM sellers_selleryieldrecord WHERE seller_id = sellerid AND model_id = 'ES-130' AND year = date_year - 1;
    IF net_yields_last_year IS NULL THEN
        net_yields_last_year := 0;
    END IF;


    -- AMOUNT DE LAS FACTURAS REG.GENERAL
    SELECT 
        common_expenses.common_amount AS common_expenses_amount,
		amz_expenses.amz_amount AS amz_expenses_amount,
        common_sales.common_amount AS common_sales_amount,
        amz_sales.amz_amount AS amz_sales_amount
    INTO inv_amount_general
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        -- INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
        -- INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND transaction_type_id NOT LIKE 'import-dua'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        -- AND sva.regime_id = 'general'
		-- AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND NOT (LEFT(inv.account_expenses_id, 2) = '21' AND LENGTH(inv.account_expenses_id) >= 4) -- EXCLUIR
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        -- INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
        -- INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND transaction_type_id NOT LIKE 'import-dua'
        AND is_txt_amz IS true
        -- AND sva.regime_id = 'general'
		-- AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND NOT (LEFT(inv.account_expenses_id, 2) = '21' AND LENGTH(inv.account_expenses_id) >= 4) -- EXCLUIR
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        -- INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
        -- INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'sales'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        -- AND sva.regime_id = 'general'
		-- AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        -- INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
        -- INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'sales'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND is_txt_amz IS true
        -- AND sva.regime_id = 'general'
		-- AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    --AMOUNT DE LAS FACTURAS REQ.EQUIVALENCIA
    -- SELECT 
    --     common_expenses.common_amount AS common_expenses_amount,
	-- 	amz_expenses.amz_amount AS amz_expenses_amount,
    --     common_sales.common_amount AS common_sales_amount,
    --     amz_sales.amz_amount AS amz_sales_amount
    -- INTO inv_amount_surcharge
    -- FROM
    -- (
    --     SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
    --     FROM invoices_invoice inv
    --     INNER JOIN invoices_concept con ON con.invoice_id = inv.id
    --     INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
    --     INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
    --     WHERE inv.seller_id = sellerid
    --     AND EXTRACT(YEAR FROM accounting_date) = date_year
    --     AND EXTRACT(MONTH FROM accounting_date) <= month_max
    --     AND status_id = 'revised'
    --     AND invoice_category_id = 'expenses'
    --     AND transaction_type_id NOT LIKE '%-transfer'
    --     AND transaction_type_id NOT LIKE 'import-dua'
    --     AND is_generated_amz IS NOT true
    --     AND is_txt_amz IS NOT true
    --     AND sva.regime_id = 'surcharge'
	-- 	AND inv.iae_id = sva.sellervat_activity_iae_id
    --     AND con.is_supplied IS NOT true
    -- ) AS common_expenses,
    -- (
    --     SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
    --     FROM invoices_invoice inv
    --     INNER JOIN invoices_concept con ON con.invoice_id = inv.id
    --     INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
    --     INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
    --     WHERE inv.seller_id = sellerid
    --     AND EXTRACT(YEAR FROM accounting_date) = date_year
    --     AND EXTRACT(MONTH FROM accounting_date) <= month_max
    --     AND status_id = 'revised'
    --     AND invoice_category_id = 'expenses'
    --     AND transaction_type_id NOT LIKE '%-transfer'
    --     AND transaction_type_id NOT LIKE 'import-dua'
    --     AND is_txt_amz IS true
    --     AND sva.regime_id = 'surcharge'
	-- 	AND inv.iae_id = sva.sellervat_activity_iae_id
    --     AND con.is_supplied IS NOT true
    -- ) AS amz_expenses,
    -- (
    --     SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
    --     FROM invoices_invoice inv
    --     INNER JOIN invoices_concept con ON con.invoice_id = inv.id
    --     INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
    --     INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
    --     WHERE inv.seller_id = sellerid
    --     AND EXTRACT(YEAR FROM accounting_date) = date_year
    --     AND EXTRACT(MONTH FROM accounting_date) <= month_max
    --     AND status_id = 'revised'
    --     AND invoice_category_id = 'sales'
    --     AND transaction_type_id NOT LIKE '%-transfer'
    --     AND is_generated_amz IS NOT true
    --     AND is_txt_amz IS NOT true
    --     AND sva.regime_id = 'surcharge'
	-- 	AND inv.iae_id = sva.sellervat_activity_iae_id
    --     AND con.is_supplied IS NOT true
    -- ) AS common_sales,
    -- (
    --     SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
    --     FROM invoices_invoice inv
    --     INNER JOIN invoices_concept con ON con.invoice_id = inv.id
    --     INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid 
    --     INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
    --     WHERE inv.seller_id = sellerid
    --     AND EXTRACT(YEAR FROM accounting_date) = date_year
    --     AND EXTRACT(MONTH FROM accounting_date) <= month_max
    --     AND status_id = 'revised'
    --     AND invoice_category_id = 'sales'
    --     AND transaction_type_id NOT LIKE '%-transfer'
    --     AND is_txt_amz IS true
    --     AND sva.regime_id = 'surcharge'
	-- 	AND inv.iae_id = sva.sellervat_activity_iae_id
    --     AND con.is_supplied IS NOT true
    -- ) AS amz_sales;

    --VAT DE LAS FACTURAS REQ.EQUIVALENCIA de 'ES'
    SELECT 
        common_expenses.common_vat AS common_expenses_vat,
        amz_expenses.amz_vat AS amz_expenses_vat,
        common_sales.common_vat AS common_sales_vat,
        amz_sales.amz_vat AS amz_sales_vat
    INTO inv_vat_surcharge
    FROM
    (
        SELECT DISTINCT 
        COALESCE(SUM(CASE WHEN inv.invoice_type_id = 'import-dua' THEN (con.vat_euros * con.quantity) ELSE (con.vat * con.amount_euros * con.quantity / 100) END), 0 ) AS common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND tax_country_id = 'ES'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT 
        COALESCE(SUM(CASE WHEN inv.invoice_type_id = 'import-dua' THEN (con.vat_euros) ELSE (con.vat * con.amount_euros / 100) END), 0 ) AS amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid 
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND tax_country_id = 'ES'
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.vat * con.amount_euros * con.quantity/100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'sales'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND tax_country_id = 'ES'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT COALESCE(SUM(con.vat * con.amount_euros /100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'sales'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND tax_country_id = 'ES'
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    -- INTRA-COMMUNITY EXPENSE/CREDIT FACTURAS REQ.EQUIVALENCIA
    SELECT 
        common_intra_600.amount AS common_intra_amount_600,
        amz_intra_600.amount AS amz_intra_amount_600,
        common_intra_600.amount + amz_intra_600.amount AS sum_intra_amount_600,

        common_intra_other.amount AS common_intra_amount_other,
        amz_intra_other.amount AS amz_intra_amount_other,
        common_intra_other.amount + amz_intra_other.amount AS sum_intra_amount_other,

        commom_reverse_charge.amount AS common_reverse_charge_amount,
        amz_reverse_charge.amount AS amz_reverse_charge_amount
    INTO inv_intra_surcharge
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND ( transaction_type_id LIKE 'intra-community-expense' OR transaction_type_id LIKE 'intra-community-credit' )
        AND tax_country_id = 'ES'
        AND (inv.account_expenses_id = '600' OR inv.account_expenses_id = '602') 
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_intra_600,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND ( transaction_type_id LIKE 'intra-community-expense' OR transaction_type_id LIKE 'intra-community-credit' )
        AND tax_country_id = 'ES'
        AND (inv.account_expenses_id = '600' OR inv.account_expenses_id = '602') 
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_intra_600,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND ( transaction_type_id LIKE 'intra-community-expense' OR transaction_type_id LIKE 'intra-community-credit' )
        AND tax_country_id = 'ES'
        AND ( inv.account_expenses_id IS NULL OR (inv.account_expenses_id != '600' AND inv.account_expenses_id != '602') )
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_intra_other,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND ( transaction_type_id LIKE 'intra-community-expense' OR transaction_type_id LIKE 'intra-community-credit' )
        AND tax_country_id = 'ES'
        AND ( inv.account_expenses_id IS NULL OR (inv.account_expenses_id != '600' AND inv.account_expenses_id != '602') )
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_intra_other,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND (transaction_type_id IS NULL OR ( transaction_type_id NOT LIKE 'intra-community-expense' OR transaction_type_id LIKE 'intra-community-credit' ))
        AND tax_country_id = 'ES'
        AND (inv.account_expenses_id = '600' OR inv.account_expenses_id = '602') 
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND is_reverse_charge IS true
    ) AS commom_reverse_charge,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND (transaction_type_id IS NULL OR ( transaction_type_id NOT LIKE 'intra-community-expense' OR transaction_type_id LIKE 'intra-community-credit' ))
        AND tax_country_id = 'ES'
        AND (inv.account_expenses_id = '600' OR inv.account_expenses_id = '602') 
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
        AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND is_reverse_charge IS true
    ) AS amz_reverse_charge;

    -- EQTAX DE LAS FACTURAS DE TIPO EXPENSE EN REQ.EQUIVALENCIA
    SELECT 
        common_eqtax.eqtax AS common_eqtax,
        amz_eqtax.eqtax AS amz_eqtax,
        common_eqtax.eqtax + amz_eqtax.eqtax AS sum_eqtax
    INTO inv_eqtax_surcharge
    FROM
    (
        SELECT DISTINCT 
            COALESCE(SUM(
                con.eqtax * con.amount_euros * con.quantity / 100
            ),0) AS eqtax
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND tax_country_id = 'ES'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.vat in (21, 10, 4)
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_eqtax,
    (
        SELECT DISTINCT 
            COALESCE(SUM(
                con.eqtax * con.amount_euros / 100
            ),0) AS eqtax
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'expenses'
        AND transaction_type_id NOT LIKE '%-transfer'
        AND tax_country_id = 'ES'
        AND is_txt_amz IS true
        AND con.vat in (21, 10, 4)
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_eqtax;

    -- IRPF DE LAS FACTURAS 'LOCAL-SALE'
    SELECT 
        common_irpf.irpf AS common_irpf,
        amz_irpf.irpf AS amz_irpf,
        common_irpf.irpf + amz_irpf.irpf AS sum_irpf
    INTO inv_irpf
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.irpf_euros * con.quantity),0) as irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'sales'
        AND transaction_type_id LIKE 'local-sale'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_irpf,
    (
        SELECT DISTINCT COALESCE(SUM(con.irpf_euros),0) as irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND invoice_category_id = 'sales'
        AND transaction_type_id LIKE 'local-sale'
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_irpf;


    --CASILLA 01: VENTAS Y SERVICIOS
    CA01 := inv_amount_general.common_sales_amount + inv_amount_general.amz_sales_amount; -- AMOUNT_SALES (GENERAL)
    -- CA01 := CA01 + inv_amount_surcharge.common_sales_amount + inv_amount_surcharge.amz_sales_amount; -- AMOUNT_SALES (SURCHARGE)
    CA01 := CA01 + inv_vat_surcharge.common_sales_vat + inv_vat_surcharge.amz_sales_vat; -- VAT_SALES (SURCHARGE)
    CA01 := ROUND(CA01, 2);

    --CASILLA 02: COMPRAS Y SERVICIOS
    CA02 := inv_amount_general.common_expenses_amount + inv_amount_general.amz_expenses_amount; -- AMOUNT_EXPENSES (GENERAL)
    -- CA02 := CA02 + inv_amount_surcharge.common_expenses_amount + inv_amount_surcharge.amz_expenses_amount; -- AMOUNT_EXPENSES (SURCHARGE)
    CA02 := CA02 + inv_vat_surcharge.common_expenses_vat + inv_vat_surcharge.amz_expenses_vat; -- VAT_EXPENSES (SURCHARGE)
    CA02 := CA02 + inv_eqtax_surcharge.common_eqtax + inv_eqtax_surcharge.amz_eqtax; -- EQTAX (R.Equivalencia) (SURCHARGE)
    CA02 := CA02 + (0.262 * inv_intra_surcharge.sum_intra_amount_600); -- INTRA-COMMUNITY EXPENSE/CREDIT WITH ACCOUNT 600 => AMOUNT * 0.262 (SURCHARGE)
    CA02 := CA02 + (0.21 * inv_intra_surcharge.sum_intra_amount_other); -- INTRA-COMMUNITY EXPENSE/CREDIT WITHOUT ACCOUNT 600 => AMOUNT * 0.21 (SURCHARGE)
    CA02 := CA02 + inv_intra_surcharge.common_reverse_charge_amount + inv_intra_surcharge.amz_reverse_charge_amount; -- REVERSE CHARGE (SURCHARGE)
    CA02 := CA02 + amortization_total; -- SUMA DE AMORTIZACIONES
    CA02 := ROUND(CA02, 2);
    
    -- COMPROBACIONES DE TOTALES---
    CA02_BASE := inv_amount_general.common_expenses_amount + inv_amount_general.amz_expenses_amount; -- AMOUNT_EXPENSES (GENERAL)
    CA02_IVA_ES := inv_vat_surcharge.common_expenses_vat + inv_vat_surcharge.amz_expenses_vat; -- VAT_EXPENSES (SURCHARGE)
    CA02_EQTAX := inv_eqtax_surcharge.common_eqtax + inv_eqtax_surcharge.amz_eqtax; -- EQTAX (R.Equivalencia) (SURCHARGE)
    CA02_INTRA600 := (inv_intra_surcharge.sum_intra_amount_600); -- INTRA-COMMUNITY EXPENSE/CREDIT WITH ACCOUNT 600 => AMOUNT * 0.262 (SURCHARGE)
    CA02_INTRA600_CALC := (0.262 * inv_intra_surcharge.sum_intra_amount_600); -- INTRA-COMMUNITY EXPENSE/CREDIT WITH ACCOUNT 600 => AMOUNT * 0.262 (SURCHARGE)
    CA02_INTRAOTROS := (inv_intra_surcharge.sum_intra_amount_other); -- INTRA-COMMUNITY EXPENSE/CREDIT WITHOUT ACCOUNT 600 => AMOUNT * 0.21 (SURCHARGE)
    CA02_INTRAOTROS_CALC := (0.21 * inv_intra_surcharge.sum_intra_amount_other); -- INTRA-COMMUNITY EXPENSE/CREDIT WITHOUT ACCOUNT 600 => AMOUNT * 0.21 (SURCHARGE)

    -- NUEVO VALOR: SUMA DE AMORTIZACIONES
    CA02_AMORTIZATION := amortization_total;

    CA02_TOTAL := CA02_BASE + CA02_IVA_ES + CA02_EQTAX + CA02_INTRA600_CALC + CA02_INTRAOTROS_CALC + CA02_AMORTIZATION;
    -- COMPROBACIONES DE TOTALES---


    -- CASILLA 03: DIFERENCIA
    CA03 := CA01 - CA02;
    IF CA03 > 0 THEN
        IF seller_self_employed = true AND seller_direct_estimation = true THEN
            GAST07 := LEAST(CA03 * 0.05, 2000);
            CA02 := CA02 + GAST07;
            CA02 := ROUND( CA02 , 2 );
            CA03 := CA01 - CA02;
        ELSE
            CA03 := CA01 - CA02;
        END IF;
    END IF;
    CA03 := ROUND( CA03 , 2 );

    -- CASILLA 04: Retenciones de las facturas de 'ES'
    CA04 := CA03 * 0.20;
    IF CA04 <= 0 THEN
        CA04 := 0;
    END IF;
    CA04 := ROUND( CA04 , 2 );

    -- Casilla 5: CA07 de Modelos Anteriores
    CA05 := 0;
    IF prev_ca07 IS NOT NULL THEN
        CA05 := prev_ca07;
    END IF;
    CA05 := ROUND( CA05 , 2 );

    -- Casilla 6: IRPF de los Local-Sale
    CA06 := inv_irpf.common_irpf + inv_irpf.amz_irpf;
    CA06 := ROUND( CA06 , 2 );

    -- Casilla 7: CALCULOS
    CA07 := CA04 - CA05 - CA06;
    CA07 := ROUND( CA07 , 2 );

    -- Casilla 12: CALCULOS
    CA12 := CA07 + CA11;
    CA12 := ROUND( CA12 , 2 );
    IF CA12 <= 0 THEN
        CA12 := 0.0;
    END IF;

    -- CASILLA 13: RENDIMIENTOS NETOS DEL EJERCICIO ANTERIOR
    IF net_yields_last_year != 0 AND net_yields_last_year IS NOT NULL THEN
        IF net_yields_last_year <= 9000 THEN
            CA13 := 100;
        ELSIF net_yields_last_year > 9000 AND net_yields_last_year <= 10000 THEN
            CA13 := 75;
        ELSIF net_yields_last_year > 10000 AND net_yields_last_year <= 11000 THEN
            CA13 := 50;
        ELSIF net_yields_last_year > 11000 AND net_yields_last_year <= 12000 THEN
            CA13 := 25;
        ELSE
            CA13 := 0;
        END IF;
    END IF;
    CA13 := ROUND( CA13 , 2 );

    -- Casilla 14: CALCULOS
    CA14 := CA12 - CA13;
    CA14 := ROUND( CA14 , 2 );
	
	-- Casilla 15: CA19 Negativas de Modelos Anteriores (En positivo)
	CA15 := 0;
    IF prev_ca19 IS NOT NULL THEN
        CA15 := prev_ca19;
    END IF;
    CA15 := ROUND( CA15 , 2 );

    -- Casilla 17: CALCULOS
	CA17 := CA14 - CA15 - CA16;
    CA17 := ROUND( CA17 , 2 );

    -- Casilla 19: CALCULOS
    CA19 := CA17 - CA18;
    CA19 := ROUND( CA19 , 2 );

    -- Casilla 20: CALCULOS
    CA20 := CA19;
    IF CA20 <= 0 THEN
        CA20 := 0;
    END IF;
    CA20 := ROUND( CA20 , 2 );
	
	-- ROUND
	--CA01 := ROUND( CA01 , 2 );
	--CA02 := ROUND( CA02 , 2 );
	--CA03 := ROUND( CA03 , 2 );
	--CA04 := ROUND( CA04 , 2 );
	--CA05 := ROUND( CA05 , 2 );
	--CA06 := ROUND( CA06 , 2 );
	--CA07 := ROUND( CA07 , 2 );
	--CA12 := ROUND( CA12 , 2 );
	--CA14 := ROUND( CA14 , 2 );
	--CA15 := ROUND( CA15 , 2 );
	--CA17 := ROUND( CA17 , 2 );
	--CA19 := ROUND( CA19 , 2 );
	CA20 := ROUND( CA20 , 2 );

    -- RETURN 'Updated ' || counter || ' invoices ' || ' | self_employed: ' || seller_self_employed || ' - eqtax: ' || seller_eqtax;
	RETURN json_build_object(
        'ShortName', short_name, 'SelfEmployed', seller_self_employed, 'EQTAX', seller_eqtax,
        'GAST07', GAST07,
        'CA01', CA01, 'CA02', CA02, 'CA03', CA03, 'CA04', CA04, 'CA05', CA05,
        'CA06', CA06, 'CA07', CA07, 'CA08', CA08, 'CA09', CA09, 'CA10', CA10,
        'CA11', CA11, 'CA12', CA12, 'CA13', CA13, 'CA14', CA14, 'CA15', CA15,
        'CA16', CA16, 'CA17', CA17, 'CA18', CA18, 'CA19', CA19, 'CA20', CA20,
        'inv_irpf', inv_irpf, 'inv_amount_general', inv_amount_general, 'inv_amount_surcharge', inv_amount_surcharge,
        'inv_vat_surcharge', inv_vat_surcharge, 'inv_intra_surcharge', inv_intra_surcharge, 'inv_eqtax_surcharge', inv_eqtax_surcharge,'net_yields_last_year', net_yields_last_year,
        'CA02_BASE', CA02_BASE, 'CA02_IVA_ES', CA02_IVA_ES, 'CA02_EQTAX', CA02_EQTAX, 'CA02_INTRA600', CA02_INTRA600, 'CA02_INTRAOTROS', CA02_INTRAOTROS, 'CA02_INTRA600_CALC', CA02_INTRA600_CALC, 
        'CA02_INTRAOTROS_CALC', CA02_INTRAOTROS_CALC, 'CA02_TOTAL', CA02_TOTAL, 'date_year', date_year, 'month_max', month_max, 'sellerid', sellerid, 'seller_direct_estimation', seller_direct_estimation,
        'amortization_total', amortization_total
    )::varchar;
END;
$$ LANGUAGE plpgsql;

-- CREAR / REMPLAZAR FUNCION (SIN CASILLAS PREVIAS)
CREATE OR REPLACE FUNCTION func_calc_model_es_130(sellerid INTEGER, date_year INTEGER, month_max INTEGER)
RETURNS varchar AS $$
BEGIN
	RETURN (SELECT func_calc_model_es_130(sellerid, date_year, month_max, 0, 0)::varchar);
END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION
-- SELECT func_calc_model_es_130(130, 2023, 06);
-- SELECT func_calc_model_es_130(130, 2023, 06, 0, 0);

-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_130(INTEGER,INTEGER,INTEGER);
-- DROP FUNCTION func_calc_model_es_130(INTEGER,INTEGER,INTEGER,NUMERIC,NUMERIC);