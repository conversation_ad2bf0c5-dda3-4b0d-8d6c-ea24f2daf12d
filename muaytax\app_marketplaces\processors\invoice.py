import uuid
from datetime import datetime
from typing import Optional, List
from django.db import transaction
from django.db.models import QuerySet

from muaytax.app_sellers.models import Seller
from muaytax.app_marketplaces.models.order import MarketplaceOrder, MarketplaceOrderItem
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_invoices.models.concept import Concept
from muaytax.app_customers.models.customer import Customer
from muaytax.app_marketplaces.choices import OrderStatusChoices, OrderItemStatusChoices

from muaytax.dictionaries.models import (
    Country,
    TransactionType,
    InvoiceCategory,
    InvoiceType,
    InvoiceStatus,
    CustomerType,
    TaxResponsability,
    AccountSales
)

class InvoiceFromOrdersPocessor:
    def __init__(self, orders: QuerySet[MarketplaceOrder], seller: Seller):
        self.orders = orders
        self.seller = seller

        self.seller_vats = self.seller.vat_seller.all() # type: ignore # Lista de VATs del vendedor
        self.default_customer = self._get_or_create_default_customer()

        self.invoices_to_create = []
        self.concepts_to_create = []
        
        self.order: Optional[MarketplaceOrder] = None
        self.order_status_updates = []  # Aquí guardaremos las órdenes a actualizar
        self.order_items_status_updates = []  # Aquí guardaremos los items de las órdenes a actualizar

        self.error_list = {}  # Lista para almacenar errores durante el procesamiento

    def bulk_generation(self) -> tuple[int, dict]:
        """
        Genera facturas a partir de las órdenes de compra del vendedor.
        Agrupa los items de la orden por fulfillment_id y crea una factura por cada grupo.
        También crea facturas individuales para los items reembolsados.
        :return: Número total de facturas creadas.
        """

        for order in self.orders:
            try:
                self.order = order
                invoice_index = 1
                fulfilled_items = self.group_items_by_fulfillment()
                refunded_items = self.order.order_items.filter(is_refunded=True, total_price_with_tax_euros__gt=0)

                # --- Iteramos sobre los items de la orden agrupados por fulfillment_id ---
                # Esto permite crear una factura por cada fulfillment_id, es decir, por cada envío realizado.
                for ff_id, items in fulfilled_items.items():
                    if not items:
                        continue

                    temp_id = str(uuid.uuid4())
                    invoice = self.build_invoice_from_fulfilled_items(items, invoice_index, ff_id)
                    invoice_index += 1
                    invoice._temp_id = temp_id # type: ignore # Propiedad temporal para identificar la factura

                    concepts = self.build_concepts_from_items(items, temp_id)
                    if not concepts:
                        continue

                    # --- Calcular totales de la factura desde las instancias de conceptos en memoria ---
                    self.set_invoice_totals(invoice, concepts)
                    # --- Añadimos la factura y los conceptos a las listas para crear en bloque ---
                    self.invoices_to_create.append(invoice)
                    self.concepts_to_create.extend(concepts)

                    # --- Creamos las facturas para los items reembolsados ---
                    # --- Cada item reembolsado se convierte en una factura individual. ---
                    related_refunds = refunded_items.filter(fulfillment_id=ff_id)
                    for refunded_item in related_refunds:
                        temp_id = str(uuid.uuid4())
                        refunded_invoice = self.build_refunded_invoice(refunded_item, invoice, invoice_index)
                        invoice_index += 1
                        # --- Asignar Propiedad temporal para identificar la factura original ---
                        refunded_invoice._original_invoice_temp_id = invoice._temp_id # type: ignore
                        refunded_invoice._temp_id = temp_id # type: ignore

                        refunded_concepts = self.build_concepts_from_items([refunded_item], temp_id, rectifyng=True)
                        if not refunded_concepts:
                            continue

                        # --- Calcular totales de la factura reembolsada desde las instancias de conceptos en memoria ---
                        self.set_invoice_totals(refunded_invoice, refunded_concepts)
                        # --- Añadimos la factura reembolsada y los conceptos a las listas para crear en bloque ---
                        self.invoices_to_create.append(refunded_invoice)
                        self.concepts_to_create.extend(refunded_concepts)

                # --- Actualizar el estado de la orden ---
                if not self.invoices_to_create:
                    # order.order_status = OrderStatusChoices.INCOMPLETE
                    # self.order_status_updates.append(order)
                    continue

                order.order_status = OrderStatusChoices.INVOICED
                self.order_status_updates.append(order)

            except Exception as e:
                print(f"Error processing order {order.order_id}: {e}")
                order.order_status = OrderStatusChoices.INVOICED_FAILED
                self.order_status_updates.append(order)
                continue

        # --- Se crean las instancias de facturas ---
        Invoice.objects.bulk_create(self.invoices_to_create)

        # --- Actualizar referencias rectificativas con IDs reales ---
        temp_id_map = {inv._temp_id: inv for inv in self.invoices_to_create}
        for inv in self.invoices_to_create:
            if hasattr(inv, '_original_invoice_temp_id'):
                inv.rectifying_invoices = temp_id_map.get(inv._original_invoice_temp_id)

        # --- Guardar actualizaciones de rectifying_invoices ---
        Invoice.objects.bulk_update(
            [inv for inv in self.invoices_to_create if hasattr(inv, '_original_invoice_temp_id')],
            ['rectifying_invoices']
        )

        # --- Crear mapping ID real a partir de objetos ya en memoria ---
        id_map = {inv._temp_id: inv.id for inv in self.invoices_to_create}
        # --- Actualizar los conceptos con el ID real de la factura ---
        for concept in self.concepts_to_create:
            concept.invoice_id = id_map[concept._temp_invoice_id]
            del concept._temp_invoice_id

        # --- Se crean las instancias de conceptos ---
        Concept.objects.bulk_create(self.concepts_to_create)

        # Finalmente, actualizamos los estados de las órdenes
        MarketplaceOrder.objects.bulk_update(
            self.order_status_updates,
            ['order_status']
        )

        # Actualizamos los estados de los items de las órdenes
        MarketplaceOrderItem.objects.bulk_update(
            self.order_items_status_updates,
            ['order_item_status']
        )

        return len(self.invoices_to_create), self.error_list

    def group_items_by_fulfillment(self) -> dict:
        grouped = {}
        fullfilled_items = self.order.order_items.filter(is_refunded=False) # type: ignore # Filtramos los items que no están reembolsados

        for item in fullfilled_items:
            key = item.fulfillment_id or "no-fulfillment"
            if key not in grouped:
                grouped[key] = [item]
            else:
                grouped[key].append(item)
        return grouped
    
    def build_invoice_from_fulfilled_items(self, items: list, invoice_index: int, fulfillment_id: str) -> Invoice:
        if not items:
            raise ValueError("No items provided to build an invoice.")
        if not self.order:
            raise ValueError("Error. La orden no está definida. No se puede construir la factura.")
        
        departure_country = items[0].departure_country
        delivered_country = items[0].delivered_country
        transaction_type, tax_country = self._get_tax_information(departure_country, delivered_country)
        
        clean_order_id = self.order.order_id.split('/')[-1] if '/' in self.order.order_id else self.order.order_id

        reference_number = f'{self.order.marketplace.marketplace_type.shortcode}-{departure_country.iso_code}-{clean_order_id}-{invoice_index}'
        reference_absolute = f'{self.seller.pk};{reference_number}'

        return Invoice(
            expedition_date=datetime.now(),
            payment_date=self.order.created_at,
            invoice_date=self.order.created_at,
            reference=reference_number,
            transaction_type=TransactionType.objects.get(code=transaction_type),
            reference_absolute=reference_absolute,
            customer=self.default_customer,
            is_api_shopify=(self.order.marketplace.marketplace_type.code == "shopify"),
            is_api_miravia=(self.order.marketplace.marketplace_type.code == "miravia"),
            is_rectifying=False,
            account_sales=AccountSales.objects.get(code="700"),
            is_oss=(transaction_type == "oss"),
            marketplace=self.order.marketplace.marketplace_type,
            marketplace_store=self.order.marketplace,
            marketplace_order=self.order,
            order_fulfillment_group=fulfillment_id,
            tax_country=Country.objects.get(iso_code=tax_country.iso_code) if tax_country else None,
            departure_country=departure_country,
            arrival_country=delivered_country,
            currency=self.order.order_currency,
            customer_type=self.order.order_type,
            tax_responsibility=TaxResponsability.objects.get(code="marketplace"),
            invoice_category=InvoiceCategory.objects.get(code="sales"),
            invoice_type=InvoiceType.objects.get(code="sales"),
            status=InvoiceStatus.objects.get(code="revised"),
            seller=self.seller,
        )
    
    def build_refunded_invoice(self, item: MarketplaceOrderItem, original_invoice: Invoice, invoice_index: int) -> Invoice:
        """
        Construye una factura rectificativa a partir de un item reembolsado.
        :param item: MarketplaceOrderItem que representa el item reembolsado.
        :param original_invoice: Invoice original de la que se deriva la factura rectificativa.
        :param invoice_index: Índice de la factura para generar un número de referencia único.
        :return: Invoice construida a partir del item reembolsado.
        """
        if not self.order:
            raise ValueError("Error. La orden no está definida. No se puede construir la factura rectificativa.")

        clean_order_id = self.order.order_id.split('/')[-1] if '/' in self.order.order_id else self.order.order_id
        reference_number = f'{self.order.marketplace.marketplace_type.shortcode}-{original_invoice.departure_country.iso_code}-{clean_order_id}-{invoice_index}'
        reference_absolute = f'{self.seller.pk};{reference_number}'

        return Invoice(
            expedition_date=item.refunded_date,
            payment_date=item.refunded_date,
            invoice_date=item.created_at,
            reference=reference_number,
            reference_absolute=reference_absolute,
            transaction_type=original_invoice.transaction_type,
            customer=self.default_customer,
            is_api_shopify=original_invoice.is_api_shopify,
            is_api_miravia=original_invoice.is_api_miravia,
            is_rectifying=True,
            account_sales=original_invoice.account_sales,
            is_oss=original_invoice.is_oss,
            marketplace=original_invoice.marketplace,
            marketplace_store=original_invoice.marketplace_store,
            marketplace_order=original_invoice.marketplace_order,
            order_fulfillment_group=original_invoice.order_fulfillment_group,
            tax_country=original_invoice.tax_country,
            departure_country=original_invoice.departure_country,
            arrival_country=original_invoice.arrival_country,
            currency=original_invoice.currency,
            customer_type=original_invoice.customer_type,
            tax_responsibility=original_invoice.tax_responsibility,
            invoice_category=original_invoice.invoice_category,
            invoice_type=original_invoice.invoice_type,
            status=original_invoice.status,
            seller=self.seller,
        )

    def build_concepts_from_items(self, items: list[MarketplaceOrderItem], temp_invoice_id: str, rectifyng: bool = False):
        """
        Construye los conceptos a partir de los items de la orden.
        Cada item se convierte en un concepto de la factura.
        :param items: Lista de MarketplaceOrderItem que representan los items de la orden.
        :param rectifyng: Booleano que indica si se está creando una factura rectificativa
        :param temp_invoice_id: Identificador temporal de la factura para asociar los conceptos.
        :raises ValueError: Si no se proporcionan items.
        :return: Lista de Conceptos construidos a partir de los items.
        """

        if not items:
            return []
        
        sign = -1 if rectifyng else 1
        concepts = []

        for item in items:
            concept = Concept(
                concept=item.product_name or 'Producto sin nombre',
                quantity=item.quantity,
                amount_currency=item.unit_price_no_tax * sign,
                amount_euros=item.unit_price_no_tax_euros * sign,
                amount_original=item.unit_price_no_tax * sign, # Este valor aun no está claro. Validar si es correcto
                vat=item.tax_rate,
                vat_currency=item.tax_amount * sign,
                vat_euros=item.tax_amount_euros * sign,
                total_currency=item.total_price_with_tax * sign,
                total_euros=item.total_price_with_tax_euros * sign,
            )
            concept._temp_invoice_id = temp_invoice_id # type: ignore # Referencia temporal a la factura
            concepts.append(concept)

            item.order_item_status = OrderItemStatusChoices.INVOICED
            self.order_items_status_updates.append(item)

        # Buscamos si este grupo de items tiene un shipping asociado y se agrega como concepto. Se busca solo el primer item porque 
        # en teoría todos los items del fulfillment deben tener el mismo shipping.
        if items[0].shipping_price_with_tax > 0:
            shipping_concept = Concept(
                concept="Gastos de envío",
                quantity=1,
                amount_currency=items[0].shipping_price_no_tax * sign,
                amount_euros=items[0].shipping_price_no_tax_euros * sign,
                amount_original=items[0].shipping_price_no_tax * sign, # Este valor aun no está claro. Validar si es correcto
                vat=items[0].shipping_tax_rate,
                vat_currency=items[0].shipping_tax_amount * sign,
                vat_euros=items[0].shipping_tax_amount_euros * sign,
                total_currency=items[0].shipping_price_with_tax * sign,
                total_euros=items[0].shipping_price_with_tax_euros * sign,
            )
            shipping_concept._temp_invoice_id = temp_invoice_id # type: ignore
            concepts.append(shipping_concept)
        
        return concepts
    
    def set_invoice_totals(self, invoice, concepts):
        if not concepts:
            return

        invoice.total_amount_currency = sum(getattr(c, "amount_currency", 0) * getattr(c, "quantity", 1) for c in concepts)
        invoice.total_amount_euros = sum(getattr(c, "amount_euros", 0) * getattr(c, "quantity", 1) for c in concepts)
        invoice.total_vat_currency = sum(getattr(c, "vat_currency", 0) for c in concepts)
        invoice.total_vat_euros = sum(getattr(c, "vat_euros", 0) for c in concepts)
        invoice.total_currency = invoice.total_amount_currency + invoice.total_vat_currency
        invoice.total_euros = invoice.total_amount_euros + invoice.total_vat_euros

    def _get_or_create_default_customer(self):
        """
        Obtiene o crea un cliente por defecto para el vendedor.
        :return: Instancia del cliente por defecto.
        """
        order = self.orders.first()
        if not order:
            raise ValueError("No orders provided to create a default customer.")
        customer, created = Customer.objects.get_or_create(
            name=f"Clientes Particulares - {order.marketplace.marketplace_type.code}",
            seller = self.seller,
            defaults={
                "customer_type": CustomerType.objects.get(code="B2C"),
            }
        )
        
        return customer

    def _get_tax_information(self, departure_country: Country, delivered_country: Country):
        transaction_type = "local-sale"
        tax_country = None
        if not self.order:
            raise ValueError("Order is not defined. Cannot determine tax information.")

        try:
            if departure_country.is_european_union and delivered_country.is_european_union:
                if departure_country == delivered_country:
                    transaction_type = "local-sale"
                    if self._is_country_in_vat_seller(delivered_country):
                        tax_country = departure_country
                    else:
                        tax_country = None
                else:
                    if self.seller.oss:
                        transaction_type = "oss"
                        tax_country = self.seller.oss_country
                    else:
                        transaction_type = "local-sale"
                        if self._is_country_in_vat_seller(delivered_country):
                            tax_country = delivered_country
                        else:
                            tax_country = departure_country
            else:
                if departure_country == delivered_country:
                    transaction_type = "local-sale"
                    tax_country = departure_country
                else:
                    transaction_type = "export-sale"
                    tax_country = departure_country # Aun no está claro si se debe usar el país de entrega o el de salida
        except Exception as e:
            errors = {
                'error': str(e),
                'message': 'No se pudo obtener la información de tasas',
                'resolution': 'Se crea la factura como venta local y sin país de tasas',
                'method': 'get_tax_information',
            }
            if self.order.order_id not in self.error_list:
                self.error_list[self.order.order_id] = {"errors": []}
            self.error_list[self.order.order_id]["errors"].append(errors)

            transaction_type = "local-sale"
            tax_country = None

        return transaction_type, tax_country

    def _is_country_in_vat_seller(self, country) -> bool:
        """
        Verifica si el país es un país donde el vendedor tiene un VAT registrado.
        :param country: País a verificar.
        :return: True si el país tiene un VAT registrado para el vendedor, False en caso contrario.
        """
        if not country:
            return False
        return any(vat.vat_country == country for vat in self.seller_vats)

class RefundInvoiceGenerator:
    def __init__(self, order: MarketplaceOrder, refunded_items: List[MarketplaceOrderItem], seller: Seller):
        self.order = order
        self.refunded_items = refunded_items
        self.seller = seller

        self.invoices_to_create = []
        self.concepts_to_create = []
        self.items_to_update = []

    def generate_refunded_invoices(self) -> int:
        related_invoices = list(self.order.invoice_marketplace_order.all())  # type: ignore
        if not related_invoices:
            print(f"\033[91m\n[REFUND-INVOICE]: No se encontraron facturas para la orden {self.order.order_id}.\033[0m")
            return 0

        invoice_index = self._get_last_invoice_index(related_invoices)

        for item in self.refunded_items:
            if not item.is_refunded or item.total_price_with_tax_euros <= 0:
                continue

            fulfillment_id = item.fulfillment_id if item.fulfillment_id is not None else "no-fulfillment"
            original_invoice = self._get_original_invoice(fulfillment_id, related_invoices)
            if not original_invoice:
                continue
            
            temp_id = str(uuid.uuid4())
            invoice = self._build_refunded_invoice(item, original_invoice, invoice_index)
            invoice_index += 1
            invoice._temp_id = temp_id  # type: ignore  # Propiedad temporal para identificar la factura

            concepts = self._build_concepts(item, temp_id)
            
            self._set_invoice_totals(invoice, concepts)
            self.invoices_to_create.append(invoice)
            self.concepts_to_create.extend(concepts)

        self._commit()
        return len(self.invoices_to_create)

    def _get_last_invoice_index(self, invoices: List[Invoice]) -> int:
        indices = []
        for inv in invoices:
            try:
                idx = int(inv.reference.split('-')[-1])
                indices.append(idx)
            except Exception:
                continue
        return max(indices, default=0) + 1

    def _get_original_invoice(self, fulfillment_id: str, invoices: List[Invoice]) -> Optional[Invoice]:
        return next((inv for inv in invoices if inv.order_fulfillment_group == fulfillment_id and not inv.is_rectifying), None)

    def _build_refunded_invoice(self, item: MarketplaceOrderItem, original_invoice: Invoice, invoice_index: int) -> Invoice:
        clean_order_id = self.order.order_id.split('/')[-1] if '/' in self.order.order_id else self.order.order_id
        reference_number = f'{self.order.marketplace.marketplace_type.shortcode}-{original_invoice.departure_country.iso_code}-{clean_order_id}-{invoice_index}'
        reference_absolute = f'{self.seller.pk};{reference_number}'

        invoice = Invoice(
            expedition_date=item.created_at,
            payment_date=item.created_at,
            invoice_date=item.created_at,
            reference=reference_number,
            reference_absolute=reference_absolute,
            transaction_type=original_invoice.transaction_type,
            customer=original_invoice.customer,
            is_api_shopify=original_invoice.is_api_shopify,
            is_api_miravia=original_invoice.is_api_miravia,
            is_rectifying=True,
            account_sales=original_invoice.account_sales,
            is_oss=original_invoice.is_oss,
            marketplace=original_invoice.marketplace,
            marketplace_store=original_invoice.marketplace_store,
            marketplace_order=self.order,
            order_fulfillment_group=original_invoice.order_fulfillment_group,
            tax_country=original_invoice.tax_country,
            departure_country=original_invoice.departure_country,
            arrival_country=original_invoice.arrival_country,
            currency=original_invoice.currency,
            customer_type=original_invoice.customer_type,
            tax_responsibility=original_invoice.tax_responsibility,
            invoice_category=original_invoice.invoice_category,
            invoice_type=original_invoice.invoice_type,
            status=InvoiceStatus.objects.get(code="revised"),
            rectifying_invoices=original_invoice,  # Referencia a la factura original
            seller=self.seller
        )
        
        return invoice

    def _build_concepts(self, item: MarketplaceOrderItem, temp_invoice_id: str) -> List[Concept]:
        sign = -1
        concepts = []

        concept = Concept(
            concept=item.product_name or 'Producto sin nombre',
            quantity=item.quantity,
            amount_currency=item.unit_price_no_tax * sign,
            amount_euros=item.unit_price_no_tax_euros * sign,
            amount_original=item.unit_price_no_tax * sign,
            vat=item.tax_rate,
            vat_currency=item.tax_amount * sign,
            vat_euros=item.tax_amount_euros * sign,
            total_currency=item.total_price_with_tax * sign,
            total_euros=item.total_price_with_tax_euros * sign,
        )
        concept._temp_invoice_id = temp_invoice_id # type: ignore # Referencia temporal a la factura
        concepts.append(concept)

        if item.shipping_price_with_tax > 0:
            shipping_concept = Concept(
                concept="Gastos de envío",
                quantity=1,
                amount_currency=item.shipping_price_no_tax * sign,
                amount_euros=item.shipping_price_no_tax_euros * sign,
                amount_original=item.shipping_price_no_tax * sign,
                vat=item.shipping_tax_rate,
                vat_currency=item.shipping_tax_amount * sign,
                vat_euros=item.shipping_tax_amount_euros * sign,
                total_currency=item.shipping_price_with_tax * sign,
                total_euros=item.shipping_price_with_tax_euros * sign,
            )
            shipping_concept._temp_invoice_id = temp_invoice_id  # type: ignore
            concepts.append(shipping_concept)

        item.order_item_status = OrderItemStatusChoices.INVOICED
        self.items_to_update.append(item)

        return concepts

    def _set_invoice_totals(self, invoice, concepts: List[Concept]):
        invoice.total_amount_currency = sum(c.amount_currency * c.quantity for c in concepts)
        invoice.total_amount_euros = sum(c.amount_euros * c.quantity for c in concepts)
        invoice.total_vat_currency = sum(c.vat_currency for c in concepts)
        invoice.total_vat_euros = sum(c.vat_euros for c in concepts)
        invoice.total_currency = invoice.total_amount_currency + invoice.total_vat_currency
        invoice.total_euros = invoice.total_amount_euros + invoice.total_vat_euros

    def _commit(self):
        with transaction.atomic():
            Invoice.objects.bulk_create(self.invoices_to_create)

            id_map = {inv._temp_id: inv.id for inv in self.invoices_to_create}
            for concept in self.concepts_to_create:
                concept.invoice_id = id_map[concept._temp_invoice_id]
                del concept._temp_invoice_id
            Concept.objects.bulk_create(self.concepts_to_create)

            MarketplaceOrderItem.objects.bulk_update(self.items_to_update, ['order_item_status'])
