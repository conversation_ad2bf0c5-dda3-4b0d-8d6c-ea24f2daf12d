from rest_framework.response import Response

class APIResponse(Response):
    def __init__(self, data=None, status: int = 200, message=None):

        response_data = {
            "response": "success",
            "status": status,
            "data": data,
            "message": message
        }

        super().__init__(response_data, status=status)

class ResponseError(Response):
    def __init__(self, errors=None, status=500, message=None, validation_errors=None, code=None):
        if validation_errors:
            error_data = validation_errors
        else:
            error_data = [{"code": error["code"], "msg": error["msg"]} for error in errors] if errors else []
            code = "error"

        response_data = {
            "status": status,
            "errors": error_data,
            "message": message,
            "code": code
        }

        super().__init__(response_data, status=status)
