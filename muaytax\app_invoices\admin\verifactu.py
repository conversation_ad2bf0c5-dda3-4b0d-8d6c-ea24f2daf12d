from django.contrib import admin

class VerifactuAdmin(admin.ModelAdmin):

    list_display = [
        "id",
        "invoice",
        "seller",
        "status_in_verifactu",
        "operation_type",
        "created_at",
        "modified_at",
    ]

    search_fields = [
        "id",
        "seller__name",
        "invoice__reference",
        "invoice__id",
        "status_in_verifactu",
        "created_at",
        "modified_at",
    ]

    readonly_fields = [
        "invoice",
        "seller",
    ]