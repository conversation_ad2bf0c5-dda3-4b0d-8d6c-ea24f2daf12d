DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_seller_vat_ES_yearly') THEN
        DROP FUNCTION func_seller_vat_ES_yearly(date_year INTEGER, date_period VARCHAR);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_seller_vat_ES_yearly(date_year INTEGER, date_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    first_month DATE;
    last_month DATE;
    result_json jsonb := '[]';
BEGIN
    IF date_period = '0A' THEN 
        first_month := date_year || '-01-01';
        last_month := (date_year + 1) || '-01-01';
    END IF;
    SELECT jsonb_agg(sub_data) INTO result_json
    FROM (
        SELECT DISTINCT 
            subselect.*, 
            (
                CASE 
                    WHEN 'required' in (model_347, model_390, model_184) THEN 0
                    WHEN 'warning' in (model_347, model_390, model_184) THEN 1    
                    WHEN 'disagreed' in (model_347, model_390, model_184) THEN 2
                    WHEN 'agreed' in (model_347, model_390, model_184) THEN 3
                    WHEN 'pending' in (model_347, model_390, model_184) THEN 4
                    WHEN 'presented' in (model_347, model_390, model_184) THEN 5
                    WHEN 'not-required' in (model_347, model_390, model_184) THEN 6
                ELSE 7
                END 
            ) AS model_min,
            (
                (
                    CASE
                        WHEN model_347 = 'required' THEN 0
                        WHEN model_347 = 'warning' THEN 1
                        WHEN model_347 = 'disagreed' THEN 2
                        WHEN model_347 = 'agreed' THEN 3
                        WHEN model_347 = 'pending' THEN 4
                        WHEN model_347 = 'presented' THEN 5
                        WHEN model_347 = 'not-required' THEN 6
                        ELSE 7
                    END
                ) + (
                    CASE
                        WHEN model_390 = 'required' THEN 0
                        WHEN model_390 = 'warning' THEN 1
                        WHEN model_390 = 'disagreed' THEN 2
                        WHEN model_390 = 'agreed' THEN 3
                        WHEN model_390 = 'pending' THEN 4
                        WHEN model_390 = 'presented' THEN 5
                        WHEN model_390 = 'not-required' THEN 6
                        ELSE 7
                    END
                ) + (
                    CASE
                        WHEN model_184 = 'required' THEN 0
                        WHEN model_184 = 'warning' THEN 1
                        WHEN model_184 = 'disagreed' THEN 2
                        WHEN model_184 = 'agreed' THEN 3
                        WHEN model_184 = 'pending' THEN 4
                        WHEN model_184 = 'presented' THEN 5
                        WHEN model_184 = 'not-required' THEN 6
                        ELSE 7
                    END
                ) 
            ) * 100 / 10 as model_avg
        FROM (
            SELECT DISTINCT 
                sel.id AS seller_id,
                sel.name AS seller_name,
                sel.shortname AS shortname,
                (SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,
                (SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,
                (to_char((SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1), 'YYYY-MM-DD HH24:MI:SS')) AS last_login,
                COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,
                ROUND(COALESCE(
                    100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
                    0
                ), 2) AS percentage_pending_invoices,
                MAX(
                    CASE WHEN pm_q4.model_303 IS NOT null THEN pm_q4.model_303
                    WHEN pmf_q4.model_303 IS NOT null THEN pmf_q4.model_303
                    ELSE
                        CASE WHEN (
                            sel.contracted_accounting_date <= (date_year||'-12-31')::date AND (
                                sel.contracted_accounting_end_date IS NULL OR 
                                sel.contracted_accounting_end_date >= (date_year||'-10-01')::date
                            )
                        ) 
                        AND (qtax_reg.regime != 'surcharge' OR qtax_reg.regime IS NULL )
                        THEN 'required' ELSE 'not-required' END
                    END
                ) as model_303_q4,
                MAX(
                    CASE WHEN pm.model_347 IS NOT null THEN pm.model_347
                    WHEN pmf.model_347 IS NOT null THEN pmf.model_347
                    ELSE
                        CASE WHEN
                        cust_total IS NOT NULL OR prov_total IS NOT NULL
                        THEN 'required' ELSE 'not-required' END
                    END
                ) as model_347,
                MAX(
                    CASE WHEN pm.model_390 IS NOT null THEN pm.model_390
                    WHEN pmf.model_390 IS NOT null THEN pmf.model_390
                    ELSE
                        CASE WHEN 
                        (qtax_reg.regime != 'surcharge' OR qtax_reg.regime IS NULL )
                        THEN 'required' ELSE 'not-required' END
                    END
                ) as model_390,
                MAX(
                    CASE WHEN pm.model_184 IS NOT null THEN pm.model_184
                    WHEN pmf.model_184 IS NOT null THEN pmf.model_184
                    ELSE
                        CASE 
                            WHEN sel.is_184_contracted THEN 'required'
                            ELSE 'not-required'
                        END
                    END
                ) as model_184
            FROM sellers_seller sel
            LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
            LEFT JOIN (
                SELECT DISTINCT
                    inv_irpf.id as irpf_invoice_id
                FROM
                    invoices_invoice inv_irpf
                INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
                WHERE
                    con_irpf.irpf_euros <> 0
                    AND con_irpf.is_supplied IS NOT true
            ) irpf ON irpf.irpf_invoice_id = inv.id
            LEFT JOIN (
                SELECT
                sel.id as seller_id,
                MAX(CASE WHEN pm.model_id::text = 'ES-347' THEN pm.status_id END) as model_347,
                MAX(CASE WHEN pm.model_id::text = 'ES-390' THEN pm.status_id END) as model_390,
                MAX(CASE WHEN pm.model_id::text = 'ES-184' THEN pm.status_id END) as model_184
                FROM sellers_seller sel
                LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
                WHERE pm.year = date_year AND pm.period_id = date_period AND pm.country_id = 'ES'
                GROUP BY sel.id		
            ) AS pm ON sel.id = pm.seller_id
            LEFT JOIN (
                SELECT
                sel.id as seller_id,
                MAX(CASE WHEN pmf.model_id::text = 'ES-347' THEN pmf.status_id END) as model_347,
                MAX(CASE WHEN pmf.model_id::text = 'ES-390' THEN pmf.status_id END) as model_390,
                MAX(CASE WHEN pmf.model_id::text = 'ES-184' THEN pmf.status_id END) as model_184
                FROM sellers_seller sel
                LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
                WHERE pmf.year = date_year AND pmf.period_id = date_period AND pmf.country_id = 'ES'
                GROUP BY sel.id		
            ) AS pmf ON sel.id = pmf.seller_id
            LEFT JOIN (
                SELECT sel.id as seller_id,
                MAX(CASE WHEN pm.model_id::text = 'ES-303' THEN pm.status_id END) as model_303
                FROM sellers_seller sel
                LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
                WHERE pm.year = date_year AND pm.period_id = 'Q4' AND pm.country_id = 'ES'
                GROUP BY sel.id		
            ) AS pm_q4 ON sel.id = pm_q4.seller_id
            LEFT JOIN (
                SELECT sel.id as seller_id,
                MAX(CASE WHEN pmf.model_id::text = 'ES-303' THEN pmf.status_id END) as model_303
                FROM sellers_seller sel
                LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
                WHERE pmf.year = date_year AND pmf.period_id = 'Q4' AND pmf.country_id = 'ES'
                GROUP BY sel.id		
            ) AS pmf_q4 ON sel.id = pmf_q4.seller_id
            LEFT JOIN (
                SELECT
                    cust.seller_id
                FROM
                    customers_customer cust
                INNER JOIN invoices_invoice inv ON inv.customer_id = cust.id
                INNER JOIN invoices_concept con ON con.invoice_id = inv.id
                LEFT JOIN (
                    SELECT DISTINCT
                        inv_irpf.id AS irpf_invoice_id
                    FROM
                        invoices_invoice inv_irpf
                    INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
                    WHERE
                        inv_irpf.accounting_date >= first_month AND inv_irpf.accounting_date < last_month AND
                        con_irpf.irpf_euros <> 0
                        AND con_irpf.is_supplied IS NOT true
                ) irpf ON irpf.irpf_invoice_id = inv.id
                WHERE
                    inv.tax_country_id = 'ES' AND
                    inv.accounting_date >= first_month AND inv.accounting_date < last_month
                    AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
                    AND NOT (inv.transaction_type_id LIKE 'intra-community-%'
                            OR inv.transaction_type_id LIKE 'import-%'
                            OR inv.transaction_type_id LIKE '%-transfer')
                    AND irpf.irpf_invoice_id IS NULL
                    AND cust.name != 'Clientes Particulares'
                    AND con.is_supplied IS NOT true
                GROUP BY
                    cust.id
                HAVING
                    SUM(con.total_euros) > 3005.06
                ORDER BY
                    cust.seller_id
            ) AS cust_total ON cust_total.seller_id = sel.id
            LEFT JOIN (
                SELECT
                    prov.seller_id
                FROM
                    providers_provider prov
                INNER JOIN invoices_invoice inv ON inv.provider_id = prov.id
                INNER JOIN invoices_concept con ON con.invoice_id = inv.id
                LEFT JOIN (
                    SELECT DISTINCT
                        inv_irpf.id AS irpf_invoice_id
                    FROM
                        invoices_invoice inv_irpf
                    INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
                    WHERE
                        inv_irpf.accounting_date >= first_month AND inv_irpf.accounting_date < last_month
                        AND con_irpf.irpf_euros <> 0
                        AND con_irpf.is_supplied IS NOT true
                ) has_irpf ON has_irpf.irpf_invoice_id = inv.id
                WHERE
                    inv.tax_country_id = 'ES' AND
                    inv.accounting_date >= first_month AND inv.accounting_date < last_month
                    AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
                    AND inv.status_id = 'revised'	
                    AND NOT (inv.transaction_type_id LIKE 'intra-community-%'
                            OR inv.transaction_type_id LIKE 'import-%'
                            OR inv.transaction_type_id LIKE '%-transfer')
                    AND has_irpf IS NULL
                    AND prov.name != 'Seguridad Social'
                    AND con.is_supplied IS NOT true
                    
                GROUP BY
                    prov.id
                HAVING
                    SUM(con.total_euros) > 3005.06
                ORDER BY
                    prov.seller_id
            ) AS prov_total ON prov_total.seller_id = sel.id
            LEFT JOIN (
                SELECT
                    seller.id as seller_id,
                    COUNT(DISTINCT CASE WHEN (i.accounting_date >= first_month AND i.accounting_date < last_month) OR (i.accounting_date IS NULL AND NOT i.status_id = 'discard') THEN i.id END) as num_inv
                FROM sellers_seller seller
                LEFT JOIN invoices_invoice i ON seller.id = i.seller_id
                GROUP BY seller.id
            ) AS i ON sel.id = i.seller_id
            LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
            LEFT JOIN (
                SELECT DISTINCT 
                    sel.id AS seller_id, 
                    sva.regime_id AS regime
                FROM sellers_seller sel
                LEFT JOIN sellers_sellervat sv ON sv.seller_id = sel.id AND sv.vat_country_id ='ES'
                LEFT JOIN sellers_sellervatactivity sva on sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
                GROUP BY sel.id, sva.regime_id
                ORDER BY sel.id
            ) AS qtax_reg ON sel.id = qtax_reg.seller_id
            WHERE
                (   
                    (
                        sel.contracted_accounting_date IS NULL AND
                        sel.contracted_accounting_end_date IS NULL
                    ) OR
                    (
                        sel.contracted_accounting_end_date < first_month AND
                        sel.contracted_accounting_date IS NOT NULL
                    ) OR
                    (
                        sel.contracted_accounting_date > last_month
                    )
                )
                AND
                (
                    sel.legal_entity IS NULL OR 
                    sel.legal_entity NOT IN ('sl', 'self-employed')
                ) 
                AND (
                    (sv.is_contracted = TRUE 
                    AND sv.vat_country_id = 'ES' 
                    AND NOT (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby')
                    AND sv.activation_date < last_month 
                    AND sv.contracting_date < last_month)
                OR (sv.is_contracted = FALSE 
                    AND sv.vat_country_id = 'ES' 
                    AND i.num_inv > 0)
                OR (i.num_inv > 0 
                    AND (NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES' AND sv.seller_id = sel.id )))
                OR (sv.is_contracted = TRUE 
                    AND sv.vat_country_id = 'ES' 
                    AND (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby') 
                    AND i.num_inv > 0)
                )
                AND (sv.vat_country_id = 'ES' AND ( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month))
            GROUP BY sel.id
            ORDER BY sel.id
        ) as subselect
    ) AS sub_data;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;
