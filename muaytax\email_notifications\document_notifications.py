
import datetime

from django.core.mail import EmailMultiAlternatives
from django.db import connection
from django.db.models import Q
from django.http import HttpResponse
from django.template.loader import render_to_string

from muaytax.app_documents.models import Document
from muaytax.email_notifications.utils import get_mail_signature
from muaytax.utils.env_resources import logo_url_head_amzvat, logo_url_head_muaytax


def send_email_expirated_cert_notification(seller):
    mail_signature = get_mail_signature(seller)
    message = render_to_string("emails/notifications/expiration_certificate.html", {
        'seller': seller,
        'mail_signature': mail_signature,
        'logo_head_muaytax': logo_url_head_muaytax(),
    })
    subject = 'MUAYTAX - Firma digital próxima a expirar'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = [mail_signature['email']]
    bcc_email = ['<EMAIL>', '<EMAIL>']
    html_content = message
    text_content = 'Firma digital próximoa a expirar'
    email_send = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_send.attach_alternative(html_content, "text/html")
    email_send.send()

def send_expirated_cert_notification():
    current_date = datetime.datetime.now()
    limit_date = current_date + datetime.timedelta(days=30)
    cert_to_expire = Document.objects.exclude(expiration_date__isnull=True).filter(expiration_date__lte=limit_date,  documentType__code='ES-SIGNATURE', is_notified_expiration=False)
    for cert in cert_to_expire:
        try:
            send_email_expirated_cert_notification(cert.seller)
            cert.is_notified_expiration = True
            cert.save()
        except Exception as e:
            print(f"Error al enviar correo de notificación de certificado expirado de {cert.seller}: {e}")

# Correro de Notificacion modelo VAT_PROOF generado (HMRC)
def send_email_hmrc_model(seller_vat, box_5_value, period_label, model):
    referencia = f"{model.pk} - {model.seller.shortname} - {model.model.code} - {model.period.code} - {model.year}"
    try:
        seller = seller_vat.seller
        contact_name = seller.name or seller.user.username
        payment_method = seller_vat.payment_method_hmrc
        vat_number = seller_vat.vat_number or ""

        is_payment = float(box_5_value) > 0
        is_zero = float(box_5_value) == 0
        amount = f"{abs(float(box_5_value)):.2f}".replace(".", ",")

        is_direct_debit = payment_method and payment_method.code.lower() == "direct-debit"
        is_unknown_method = payment_method is None

        print(f"\nBuscando SellerVat para seller={seller.shortname} (ISO GB)")
        print(f"SellerVat encontrado: vat_number={vat_number}, payment_method={payment_method}")
        print(f"\nEnviando correo con:")
        print(f"➤ box_5_value = {box_5_value}")
        print(f"➤ period_label = {period_label}")
        print(f"➤ referencia = {referencia}")

        message = render_to_string("notifications/hmrc_model_notification.html", {
            'seller': seller,
            'contact_name': contact_name,
            'amount': amount,
            'period_label': period_label,
            'vat_number': vat_number,
            'is_payment': is_payment,
            'is_zero': is_zero,
            'is_direct_debit': is_direct_debit,
            'is_unknown_method': is_unknown_method,
            'payment_method': payment_method,
            'referencia': referencia,
            'logo_head_muaytax': logo_url_head_muaytax(),
        })

        subject = 'MUAYTAX - Resultado declaración HMRC'
        from_email = '<EMAIL>'
        to_email = [seller.user.email]
        reply_to = ['<EMAIL>']
        text_content = 'Resultado declaración HMRC'
        bcc_email = ['<EMAIL>']

        email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
        email.attach_alternative(message, "text/html")
        email.send()

    except Exception as e:
        print(f"Error al enviar email HMRC para modelo {referencia} - {str(e)}")

