DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_seller_list_json') THEN
        DROP FUNCTION func_seller_list_json(INTEGER);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_seller_list_json(date_year INTEGER)
RETURNS jsonb AS $$

DECLARE
    inv_data RECORD;
    result_json jsonb := '[]'; -- Initialize as an empty JSON list

BEGIN
    FOR inv_data IN
        SELECT
            s.id,
            s.name AS seller_name,
            s.shortname AS seller_shortname,
            s.legal_entity AS legal_entity,
            u.email AS seller_email,
            u.name AS user_name,
            u.last_login AS user_last_login,
            COALESCE(COUNT(inv.id), 0) AS inv_count
        FROM
            sellers_seller AS s
        LEFT JOIN
            users_user AS u ON s.user_id = u.id
        LEFT JOIN
            invoices_invoice AS inv ON inv.seller_id = s.id
            AND (inv.invoice_category_id IS NULL 
                 OR inv.invoice_category_id NOT IN ('expenses_copy', 'sales_copy'))
            AND inv.status_id IN ('pending', 'revision-pending')
        GROUP BY
            s.id, u.email, u.name, u.last_login, s.name, s.shortname, s.legal_entity
    LOOP
        result_json := result_json || jsonb_build_object(
            'name', inv_data.seller_name,
            'email', inv_data.seller_email,
            'user_name', inv_data.user_name,
            'shortname', inv_data.seller_shortname,
            'legal_entity', inv_data.legal_entity,
            'last_login', to_char(inv_data.user_last_login, 'YYYY-MM-DD HH24:MI:SS'),
            'inv_count', inv_data.inv_count
        );
    END LOOP;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- SELECT func_seller_list_json(2024);
