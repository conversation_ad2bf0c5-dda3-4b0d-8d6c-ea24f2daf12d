from django.db import models

class StoreProduct(models.Model):
    code = models.CharField(
        max_length=50,
        primary_key=True,
        verbose_name="Código del Producto",
        help_text="Código del producto que ofrece la tienda."
    )
    description = models.TextField(
        verbose_name="Descripción",
        help_text="Descripción del producto que ofrece la tienda."
    )
    shop = models.CharField(
        max_length=50,
        verbose_name="Tienda",
        help_text="Nombre de la tienda que ofrece el producto."
    )

    class Meta:
        verbose_name = "Producto de la Tienda"
        verbose_name_plural = "Productos de la Tienda"

    def __str__(self):
        return self.name