from django.contrib import admin
from django import forms

from ..models.reconciliation import Reconciliation
from ..models.movement import Movement
from ..models.bank import Bank
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.dictionaries.models.accounting_account import AccountingAccount
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType

class ReconciliationAdmin(admin.ModelAdmin):

    list_display = ["id", "get_seller_name", "movement", "amount", "type", "invoice", "accounting_account", "accounting_account_detail", "used_in_entry"]
    search_fields = ["id", "movement__id", "type__description", "invoice__reference", "accounting_account__description", "amount", "movement__bank__bank_seller__name"]
    list_editable = ["used_in_entry"]
    list_filter = ["type", "used_in_entry"]

    fieldsets = (
        ("Conciliacion", {
            "fields": (
                "movement",
                "amount",
                "type",
                "used_in_entry",                     
            )
        }),

        ("Facturas", {
            "fields": (
                "invoice",
            )
        }),
        ("Cuentas Contables", {
            "fields": (
                "accounting_account",
                "accounting_account_detail",
            )
        }),
        ("Transferencias", {
            "fields": (                
                "movement_transfer",
            )
        }),
        ("Otros", {
            "fields": (
                "bank",
            )
        }),

    )

    def formfield_for_foreignkey(self, db_field, request, **kwargs):

        if db_field.name == 'movement':
            reconciliation_id = request.resolver_match.kwargs.get('object_id')
            if reconciliation_id: # Edit / Change
                reconciliation = Reconciliation.objects.get(pk=reconciliation_id)
                movement = reconciliation.movement
                seller = movement.bank.bank_seller
                if (movement):
                    kwargs['queryset'] = Movement.objects.filter(pk=movement.pk, bank__bank_seller=seller)
                else:
                    kwargs['queryset'] = Movement.objects.filter(bank__bank_seller=seller)                
            else: # New / Add   
                kwargs['queryset'] = Movement.objects.all()
                
        if db_field.name == 'invoice':
            kwargs['queryset'] = Invoice.objects.none()
            reconciliation_id = request.resolver_match.kwargs.get('object_id')
            if reconciliation_id: # Edit / Change
                reconciliation = Reconciliation.objects.get(pk=reconciliation_id)
                if (reconciliation.type.pk == "invoice"):
                    movement = reconciliation.movement
                    invoice = reconciliation.invoice
                    seller = movement.bank.bank_seller
                    if (invoice):
                        kwargs['queryset'] = Invoice.objects.filter(pk=invoice.pk, seller=seller)
                    else:
                        kwargs['queryset'] = Invoice.objects.filter(seller=seller)
                else:
                    kwargs['disabled'] = True

        if db_field.name == 'bank':
            kwargs['queryset'] = Bank.objects.none()
            reconciliation_id = request.resolver_match.kwargs.get('object_id')
            if reconciliation_id: # Edit / Change
                reconciliation = Reconciliation.objects.get(pk=reconciliation_id)
                if (reconciliation.type.pk == "bank"):
                    movement = reconciliation.movement
                    bank = reconciliation.bank
                    seller = movement.bank.bank_seller
                    if (bank):
                        kwargs['queryset'] = Bank.objects.filter(pk=bank.pk, bank_seller=seller)
                    else:
                        kwargs['queryset'] = Bank.objects.filter(bank_seller=seller)
                else:
                    kwargs['disabled'] = True

        if db_field.name == 'movement_transfer':
            kwargs['queryset'] = Movement.objects.none()
            reconciliation_id = request.resolver_match.kwargs.get('object_id')
            if reconciliation_id: # Edit / Change
                reconciliation = Reconciliation.objects.get(pk=reconciliation_id)
                if (reconciliation.type.pk == "transfer"):
                    movement = reconciliation.movement
                    movement_transfer = reconciliation.movement_transfer
                    seller = movement.bank.bank_seller
                    bank_id =  movement.bank.pk
                    if (movement_transfer):
                        kwargs['queryset'] = Movement.objects.filter(pk=movement_transfer.pk, bank__bank_seller=seller)
                    else:
                        kwargs['queryset'] = Movement.objects.filter(bank__bank_seller=seller).exclude(bank__pk=bank_id)
                else:
                    kwargs['disabled'] = True

        if db_field.name == 'accounting_account':
            kwargs['queryset'] = AccountingAccount.objects.none()
            reconciliation_id = request.resolver_match.kwargs.get('object_id')
            if reconciliation_id: # Edit / Change
                reconciliation = Reconciliation.objects.get(pk=reconciliation_id)
                if (reconciliation.type.pk == "account"):
                    movement = reconciliation.movement
                    accounting_account = reconciliation.accounting_account
                    accounting_account_detail = reconciliation.accounting_account_detail
                    seller = movement.bank.bank_seller
                    kwargs['queryset'] = AccountingAccount.objects.all()
                else:
                    kwargs['queryset'] = AccountingAccount.objects.none()

        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    # I want to hide textfield accounting_account_detail when type is not account
    def get_form(self, request, obj=None, **kwargs):
        form = super(ReconciliationAdmin, self).get_form(request, obj, **kwargs)

        reconciliation_id = request.resolver_match.kwargs.get('object_id')
        reconciliation = Reconciliation.objects.get(pk=reconciliation_id)
        if (reconciliation.type.pk != "account"):
            form.base_fields['accounting_account_detail'].widget.attrs['disabled'] = True
            form.base_fields['accounting_account_detail'].required = False
        return form
    
    def get_seller_name(self, obj):
        seller_name = ""
        if (obj.movement and obj.movement.bank and obj.movement.bank.bank_seller):
            seller_name = obj.movement.bank.bank_seller.name
        return seller_name
    get_seller_name.short_description = 'Vendedor'

