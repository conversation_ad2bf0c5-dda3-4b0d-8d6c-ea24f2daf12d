from django import forms

from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.app_sellers.models.seller_vat import SellerVat

class SellerVatActivityForm(forms.ModelForm):

    class Meta:
        model = SellerVatActivity
        fields = ["sellervat", "sellervat_activity_iae", "regime", "date", "end_date"]

        labels = {
            "sellervat": "País IVA",
            "sellervat_activity_iae": "Epígrafes IAE",
        }

        widgets = {
            "date": forms.TextInput(attrs={'type': 'date'}),
            "end_date": forms.TextInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        seller = kwargs.pop("seller")
        super(SellerVatActivityForm, self).__init__(*args, **kwargs)
        self.fields["sellervat"].queryset = SellerVat.objects.filter(seller=seller, is_contracted=True)
        self.fields['sellervat'].label_from_instance = self.get_sellervat_label

        self.fields['regime'].initial = None
        self.fields['regime'].choices = [('', '---------')] + list(self.fields['regime'].choices)

    def get_sellervat_label(self, obj):
        return f'{obj.vat_country.iso_code} - {obj.vat_country.name}'
