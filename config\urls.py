from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from django.views import defaults as default_views
from django.shortcuts import render

from rest_framework.authtoken.views import obtain_auth_token
from rest_framework_simplejwt.views import TokenRefreshView
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

from muaytax.users.views import HomeView
from muaytax.users.api.views import MyObtainTokenPairView, RegisterView, WoocommerceSalesFRView, WoocommerceSalesITView, WoocommerceSalesOldView
from muaytax.utils.env_resources import logo_url_head_muaytax
from muaytax.users.api.views import google_login_token
from muaytax.utils.media_auth import serve_media_file

#https://stackoverflow.com/questions/4938491/django-admin-change-header-django-administration-text

admin.site.site_header = 'Muaytax'                    # default: "Django Administration"
admin.site.index_title = 'Muaytax'                 # default: "Site administration"
admin.site.site_title = 'Muaytax' # default: "Django site admin"

urlpatterns = [
    # Django Admin, use {% url 'admin:index' %}
    path(settings.ADMIN_URL, admin.site.urls),

    # Home
    path("", HomeView.as_view(), name="home"),

    # MuayTax "APPS"
    path("accounts/", include("allauth.urls")),
    path("", include("muaytax.users.urls", namespace="users")),
    path("", include("muaytax.app_lists.urls", namespace="app_lists")),
    path("", include("muaytax.app_importers.urls", namespace="app_importers")),
    path("", include("muaytax.app_marketplaces.urls", namespace="app_marketplaces")),
    path("", include("muaytax.app_documents.urls", namespace="app_documents")),
    path("", include("muaytax.app_products.urls", namespace="app_products")),
    path("", include("muaytax.app_customers.urls", namespace="app_customers")),
    path("", include("muaytax.app_providers.urls", namespace="app_providers")),
    path("", include("muaytax.app_workers.urls", namespace="app_workers")),
    path("", include("muaytax.app_partners.urls", namespace="app_partners")),
    path("", include("muaytax.app_representatives.urls", namespace="app_representatives")),
    path("", include("muaytax.app_invoices.urls", namespace="app_invoices")),
    path("", include("muaytax.app_sellers.urls", namespace="app_sellers")),
    path("", include("muaytax.app_banks.urls", namespace="app_banks")),
    path("", include("muaytax.dictionaries.urls", namespace="dictionaries")),
    path("", include("muaytax.app_tasks.urls", namespace="app_tasks")),

    # path("", include("muaytax.app_wizards.urls", namespace="app_wizards")),
    path("", include("muaytax.app_bookings.urls", namespace="app_bookings")),
    path("", include("muaytax.app_esign.urls", namespace="app_esign")),
    path("", include("muaytax.app_fax.urls", namespace="app_fax")),
    path("", include("muaytax.app_notifications.urls", namespace="app_notifications")),
    path("", include("muaytax.app_services.urls", namespace="app_services")),
    path("", include("muaytax.app_annotations.urls", namespace="app_annotations")),
    path("", include("muaytax.app_hmrc.urls", namespace="app_hmrc")),
    path('', include('muaytax.app_landing.urls', namespace="app_landing")),
    path("v1/", include("api_rest.urls", namespace="api_rest")),

    # Incluyendo la vista de previsualización de templates (Email)
    # path("utils/", include("utils.urls")),

    # VUE WIZARDS
    # path("register-wizard/", include("registerWizard.urls", namespace="REGISTER_WIZARD")),

]

# API URLS
urlpatterns += [
    # API base url
    # path("api/", include("config.api_router")),
    path("api/woocommerce/fr/sale/", WoocommerceSalesFRView.as_view(), name='api_woocommerce_fr_sale'),
    path("api/woocommerce/it/sale/", WoocommerceSalesITView.as_view(), name='api_woocommerce_it_sale'),
    path("api/woocommerce/old/sale/", WoocommerceSalesOldView.as_view(), name='api_woocommerce_old_sale'),
    path('register/', RegisterView.as_view(), name='auth_register'),


    # DRF auth token
    path("auth-token/", obtain_auth_token),
    path('login/', MyObtainTokenPairView.as_view(), name='token_obtain_pair'),
    path('login/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path("api/schema/", SpectacularAPIView.as_view(), name="api-schema"),
    path(
        "api/docs/",
        SpectacularSwaggerView.as_view(url_name="api-schema"),
        name="api-docs",
    ),
    path('hijack/', include('hijack.urls')),
    path('accounts/google/login/token/', google_login_token, name='google_login_token'),
    
    # Authenticated media file serving
    path('media/<path:file_path>', serve_media_file, name='serve_media_file'),
]

if settings.DEBUG:
    # This allows the error pages to be debugged during development, just visit
    # these url in browser to see how these error pages look like.
    urlpatterns += [
        path(
            "400/",
            default_views.bad_request,
            kwargs={"exception": Exception("Bad Request!")},
        ),
        path(
            "403/",
            default_views.permission_denied,
            kwargs={"exception": Exception("Permission Denied")},
        ),
        path(
            "404/",
            default_views.page_not_found,
            kwargs={"exception": Exception("Page not Found")},
        ),
        path("500/", default_views.server_error),
    ]
    if "debug_toolbar" in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [path("__debug__/", include(debug_toolbar.urls))] + urlpatterns

if not settings.DEBUG or settings.DEBUG == False:
    def handler401(request, exception=None):
        return render(request, '/app/muaytax/templates/_errors_/error_401.html', status=401)

    def handler403(request, exception=None):
        return render(request, '/app/muaytax/templates/_errors_/error_403.html',  status=403)

    def handler404(request, exception=None):
        return render(request, '/app/muaytax/templates/_errors_/error_404.html',  status=404)

    def handler500(request, exception=None):
        return render(request, '/app/muaytax/templates/_errors_/error_500.html',  status=500)

    def handler502(request, exception=None):
        return render(request, '/app/muaytax/templates/_errors_/error_502.html',  status=502)
