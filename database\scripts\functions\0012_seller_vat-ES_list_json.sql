DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_sellervat_countryES_list_json') THEN
        DROP FUNCTION func_sellervat_countryES_list_json(date_year INTEGER, date_period VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_sellervat_countryES_list_json(date_year INTEGER, date_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
	first_month DATE;
	last_month DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	IF date_period = 'Q1' THEN 
					first_month := date_year || '-01-01';
					last_month := date_year || '-04-01';
	ELSIF date_period = 'Q2' THEN 
					first_month := date_year || '-04-01';
					last_month := date_year || '-07-01';
	ELSIF date_period = 'Q3' THEN 
					first_month := date_year || '-07-01';
					last_month := date_year || '-10-01';
	ELSIF date_period = 'Q4' THEN 
					first_month := date_year || '-10-01';
					last_month := (date_year + 1) || '-01-01';
				END IF;
				

    FOR inv_data IN
	
		SELECT DISTINCT 
					-- SubSelect
					subselect.*, 

					-- Mondel Min
					(
						CASE 
							WHEN 'required' in (model_303) THEN 0
							WHEN 'warning01' in (model_303) THEN 1
							WHEN 'warning02' in (model_303) THEN 2
							WHEN 'warning03' in (model_303) THEN 3
							WHEN 'warning04' in (model_303) THEN 4
							WHEN 'disagreed' in (model_303) THEN 5
							WHEN 'agreed' in (model_303) THEN 6
							WHEN 'pending' in (model_303) THEN 7
							WHEN 'presented' in (model_303) THEN 8
							WHEN 'not-required' in (model_303) THEN 9
						ELSE 10
						END 
					) AS model_min,

					-- Model Average
					(
						(
							CASE
								WHEN model_303 = 'required' THEN 0
								WHEN model_303 = 'warning01' THEN 1
								WHEN model_303 = 'warning02' THEN 2
								WHEN model_303 = 'warning03' THEN 3
								WHEN model_303 = 'warning04' THEN 4
								WHEN model_303 = 'disagreed' THEN 5
								WHEN model_303 = 'agreed' THEN 6
								WHEN model_303 = 'pending' THEN 7
								WHEN model_303 = 'presented' THEN 8
								WHEN model_303 = 'not-required' THEN 9
								ELSE 10
							END) + (
							CASE
								WHEN model_349 = 'required' THEN 0
								WHEN model_349 = 'warning05' THEN 1
								WHEN model_349 = 'warning06' THEN 2
								WHEN model_349 = 'warning07' THEN 3
								WHEN model_349 = 'warning08' THEN 4
								WHEN model_349 = 'disagreed' THEN 5
								WHEN model_349 = 'agreed' THEN 6
								WHEN model_349 = 'pending' THEN 7
								WHEN model_349 = 'presented' THEN 8
								WHEN model_349 = 'not-required' THEN 9
								ELSE 10
							END) + (
							CASE
								WHEN model_369 = 'required' THEN 0
								WHEN model_369 = 'warning05' THEN 1
								WHEN model_369 = 'warning06' THEN 2
								WHEN model_369 = 'warning07' THEN 3
								WHEN model_369 = 'warning08' THEN 4
								WHEN model_369 = 'disagreed' THEN 5
								WHEN model_369 = 'agreed' THEN 6
								WHEN model_369 = 'pending' THEN 7
								WHEN model_369 = 'presented' THEN 8
								WHEN model_369 = 'not-required' THEN 9
								ELSE 10
							END)
					) * 100 / 10 as model_avg

					FROM 
				(

					SELECT DISTINCT 
					-- id
					sel.id,

					-- seller name
					sel.name AS seller_name,

					-- shortname
					sel.shortname AS seller_shortname,

					-- contracted_accounting

					-- email
					(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

					-- user name
					(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

					-- last login
					(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

					-- Num Invoices
					COUNT(DISTINCT inv.id) AS num_invoices,

					-- Num Pending Invoices
					COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

					--Num Invoices this period
					COUNT(DISTINCT CASE WHEN inv.accounting_date >= first_month AND inv.accounting_date < last_month OR (inv.accounting_date IS NULL AND NOT inv.status_id = 'discard') THEN inv.id END) as num_invoices_this_period,
					
					-- Percentage Pending Invoices
					ROUND(COALESCE(
						100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
						0
					), 2) AS percentage_pending_invoices,

					-- Month 1
					BOOL_OR (
						CASE WHEN
						txt.month_1 is True
						THEN true ELSE false END
					) AS month_1,

					-- Month 2
					BOOL_OR (
						CASE WHEN
						txt.month_2 is True
						THEN true ELSE false END
					) AS month_2,

					-- Month 3
					BOOL_OR (
						CASE WHEN
						txt.month_3 is True
						THEN true ELSE false END
					) AS month_3,

					-- Month 4
					BOOL_OR (
						CASE WHEN
						txt.month_4 is True
						THEN true ELSE false END
					) AS month_4,

					-- Month 5
					BOOL_OR (
						CASE WHEN
						txt.month_5 is True
						THEN true ELSE false END
					) AS month_5,

					-- Month 6
					BOOL_OR (
						CASE WHEN
						txt.month_6 is True
						THEN true ELSE false END
					) AS month_6,

					-- Month 7
					BOOL_OR (
						CASE WHEN
						txt.month_7 is True
						THEN true ELSE false END
					) AS month_7,

					-- Month 8
					BOOL_OR (
						CASE WHEN
						txt.month_8 is True
						THEN true ELSE false END
					) AS month_8,

					-- Month 9
					BOOL_OR (
						CASE WHEN
						txt.month_9 is True
						THEN true ELSE false END
					) AS month_9,

					-- Month 10
					BOOL_OR (
						CASE WHEN
						txt.month_10 is True
						THEN true ELSE false END
					) AS month_10,

					-- Month 11
					BOOL_OR (
						CASE WHEN
						txt.month_11 is True
						THEN true ELSE false END
					) AS month_11,

					-- Month 12
					BOOL_OR (
						CASE WHEN
						txt.month_12 is True
						THEN true ELSE false END
					) AS month_12,

					MAX(
						CASE WHEN pm.model_303 IS NOT null THEN pm.model_303
						WHEN pmf.model_303 IS NOT null THEN pmf.model_303
						ELSE
							CASE
						WHEN i.num_inv_null_revised > 0
							THEN 'warning04'
						-- WHEN (
						-- 	(sv.is_contracted = FALSE AND sv.vat_country_id = 'ES' AND i.num_inv >0) OR
						-- 	(i.num_inv > 0 AND NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES')) OR
						-- 	(sv.is_contracted = TRUE AND sv.vat_country_id = 'ES' AND (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby') AND i.num_inv > 0)
						-- ) 	THEN 'warning'
						WHEN sv.is_contracted = FALSE AND sv.vat_country_id = 'ES' AND i.num_inv >0
							THEN 'warning01'
						WHEN i.num_inv > 0 AND NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES' AND sv.seller_id = sel.id)
							THEN 'warning02'
						WHEN sv.is_contracted = TRUE AND sv.vat_country_id = 'ES' AND (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby') AND i.num_inv > 0
							THEN 'warning03'
						WHEN sv.activation_date < last_month  AND (sv.vat_country_id = 'ES' AND sv.is_contracted = TRUE ) AND (qtax_reg.regime != 'surcharge' OR qtax_reg.regime IS NULL )
							THEN 'required'
						ELSE 'not-required' END
						END
					) as model_303,

					MAX(
						CASE WHEN pm.model_349 IS NOT null THEN pm.model_349
						WHEN pmf.model_349 IS NOT null THEN pmf.model_349
						ELSE
							CASE WHEN i.null_revised_intra > 0 THEN 'warning08'
							-- WHEN (sv.is_contracted = FALSE 
								-- 	AND sv.vat_country_id = 'ES' 
								-- 	AND inv.transaction_type_id LIKE 'intra-community-%' 
								-- 	AND inv.accounting_date >= first_month)
								--OR ((NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES') AND (inv.transaction_type_id LIKE 'intra-community-%' AND inv.accounting_date >= first_month)))
								--OR (sv.is_contracted = TRUE AND sv.vat_country_id = 'ES' AND (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby') AND (inv.transaction_type_id LIKE 'intra-community-%' AND inv.accounting_date >= first_month))
							--THEN 'warning05'
							WHEN sv.is_contracted = FALSE AND sv.vat_country_id = 'ES' AND (inv.transaction_type_id LIKE 'intra-community-%' AND inv.accounting_date >= first_month AND inv.accounting_date < last_month)
								THEN 'warning05'
							WHEN (NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES'AND sv.seller_id = sel.id) AND (inv.transaction_type_id LIKE 'intra-community-%' AND inv.accounting_date >= first_month AND inv.accounting_date < last_month))
								THEN 'warning06'
							WHEN (sv.is_contracted = TRUE AND sv.vat_country_id = 'ES' AND (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby') AND (inv.transaction_type_id LIKE 'intra-community-%' AND inv.accounting_date >= first_month AND inv.accounting_date < last_month))
								THEN 'warning07'
							WHEN sv.activation_date < last_month 
								AND inv.transaction_type_id LIKE 'intra-community-%' 
								AND inv.accounting_date >= first_month 
								AND inv.accounting_date < last_month 
								AND (sv.vat_country_id = 'ES' AND sv.is_contracted = TRUE) 
							THEN 'required'						
							ELSE 'not-required' 
							END
						END
					) as model_349,

					MAX(
						CASE WHEN pm.model_369 IS NOT null THEN pm.model_369
						WHEN pmf.model_369 IS NOT null THEN pmf.model_369
						ELSE
							CASE WHEN sel.oss IS NOT TRUE AND oss.invoice_oss = TRUE THEN 'warning09'
							WHEN i.num_inv_null_revised > 0
								THEN 'warning12'
							WHEN sv.is_contracted = FALSE AND sv.vat_country_id = 'ES' AND i.num_inv_oss >0
								THEN 'warning14'
							WHEN i.num_inv > 0 AND NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES' AND sv.seller_id = sel.id)
								THEN 'warning10'
							WHEN sv.is_contracted = TRUE AND sv.vat_country_id = 'ES' AND (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby') AND i.num_inv > 0
								THEN 'warning11'
							WHEN (sv.activation_date < last_month  AND (sv.vat_country_id = 'ES' AND sv.is_contracted = TRUE ) AND sel.oss IS TRUE) OR
									(
										(sel.oss_date < last_month AND (sel.oss_end_date IS NULL OR sel.oss_end_date >= first_month )) 
										OR (sel.oss IS True)
									)
								THEN 'required'
							ELSE 'not-required' END
						END
					) as model_369


					FROM sellers_seller sel
					LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
					LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
					LEFT JOIN (
						SELECT
						sel.id as seller_id,
						BOOL_OR(CASE WHEN month = 1 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_1,
						BOOL_OR(CASE WHEN month = 2 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_2,
						BOOL_OR(CASE WHEN month = 3 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_3,
						BOOL_OR(CASE WHEN month = 4 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_4,
						BOOL_OR(CASE WHEN month = 5 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_5,
						BOOL_OR(CASE WHEN month = 6 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_6,
						BOOL_OR(CASE WHEN month = 7 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_7,
						BOOL_OR(CASE WHEN month = 8 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_8,
						BOOL_OR(CASE WHEN month = 9 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_9,
						BOOL_OR(CASE WHEN month = 10 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_10,
						BOOL_OR(CASE WHEN month = 11 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_11,
						BOOL_OR(CASE WHEN month = 12 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_12

						FROM sellers_seller sel
						LEFT JOIN importers_amazontxteur txt ON sel.id = txt.seller_id
						WHERE year = date_year
						GROUP BY sel.id
					) AS txt ON sel.id = txt.seller_id
					LEFT JOIN (
						SELECT
						sel.id as seller_id,
						MAX(CASE WHEN pm.model_id::text = 'ES-303' THEN pm.status_id END) as model_303,
						MAX(CASE WHEN pm.model_id::text = 'ES-349' THEN pm.status_id END) as model_349,
						MAX(CASE WHEN pm.model_id::text = 'ES-369' THEN pm.status_id END) as model_369
						FROM sellers_seller sel
						LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
						WHERE pm.year = date_year AND pm.period_id = date_period AND pm.country_id = 'ES' 
						GROUP BY sel.id		
					) AS pm ON sel.id = pm.seller_id
					LEFT JOIN (
						SELECT
						sel.id as seller_id,
						MAX(CASE WHEN pmf.model_id::text = 'ES-303' THEN pmf.status_id END) as model_303,
						MAX(CASE WHEN pmf.model_id::text = 'ES-349' THEN pmf.status_id END) as model_349,
						MAX(CASE WHEN pmf.model_id::text = 'ES-369' THEN pmf.status_id END) as model_369
						FROM sellers_seller sel
						LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
						WHERE pmf.year = date_year AND pmf.period_id = date_period AND pmf.country_id = 'ES' 
						GROUP BY sel.id		
					) AS pmf ON sel.id = pmf.seller_id
					LEFT JOIN (
						SELECT DISTINCT sel.id, sel.shortname, sel.oss AS seller_oss, inv.is_oss AS invoice_oss
						FROM sellers_seller sel
						INNER JOIN invoices_invoice inv ON sel.id = inv.seller_id
						WHERE (sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
						--AND sel.contracted_accounting = True
						AND sel.oss IS NOT True
						AND inv.is_oss IS True
						AND inv.accounting_date >= first_month
						AND inv.accounting_date < last_month
						GROUP BY sel.id, inv.is_oss
						ORDER BY sel.id
					) AS oss ON oss.id = sel.id
					LEFT JOIN(
						SELECT
						sel.id as seller_id,
						COUNT(DISTINCT CASE WHEN (i.accounting_date >= first_month AND i.accounting_date < last_month) OR (i.accounting_date IS NULL AND NOT i.status_id = 'discard') THEN i.id END) as num_inv,
						COUNT(DISTINCT CASE WHEN i.is_oss IS TRUE AND ((i.accounting_date >= first_month AND i.accounting_date < last_month) OR (i.accounting_date IS NULL AND NOT i.status_id = 'discard'))  THEN i.id END) as num_inv_oss,
						--COUNT(DISTINCT CASE WHEN i.accounting_date >= first_month AND NOT i.status_id = 'discard' OR (i.accounting_date IS NULL AND (i.status_id = 'pending' OR i.status_id = 'revision-pending')) THEN i.id END) as num_inv,
						COUNT(DISTINCT CASE WHEN  (i.accounting_date IS NULL AND i.status_id = 'revised' AND (i.transaction_type_id IS NULL OR i.transaction_type_id NOT IN ('outgoing-transfer', 'inbound-transfer'))) THEN i.id END) as num_inv_null_revised,
						COUNT(DISTINCT CASE WHEN  (i.accounting_date IS NULL AND i.status_id = 'revised' AND i.transaction_type_id LIKE 'intra-community-%') THEN i.id END) as null_revised_intra
						--COUNT(DISTINCT CASE WHEN  (i.status_id = 'revised' AND i.tax_country_id = 'ES' AND ((i.accounting_date < last_month AND i.accounting_date >= first_month) OR i.accounting_date IS NULL))  THEN i.id END) as num_inv_es
						
						FROM sellers_seller sel
						LEFT JOIN invoices_invoice i ON sel.id = i.seller_id
						WHERE (i.invoice_category_id NOT LIKE '%_copy' OR i.invoice_category_id IS NULL )
						GROUP BY sel.id
					) AS i ON sel.id = i.seller_id
					LEFT JOIN (
						SELECT DISTINCT 
						sel.id AS seller_id, 
						sva.regime_id AS regime
						FROM sellers_seller sel
						LEFT JOIN sellers_sellervat sv ON sv.seller_id = sel.id AND sv.vat_country_id ='ES'
						LEFT JOIN sellers_sellervatactivity sva on sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
						GROUP BY sel.id, sva.regime_id
						ORDER BY sel.id
					) AS qtax_reg ON sel.id = qtax_reg.seller_id
					
					WHERE 
						--sel.contracted_accounting = False 
						(sel.legal_entity IS NULL OR sel.legal_entity NOT IN ('sl', 'self-employed'))
						AND (
								(
									sel.contracted_accounting_date IS NULL AND
									sel.contracted_accounting_end_date IS NULL
								) OR
								(
									sel.contracted_accounting_end_date < first_month AND
									sel.contracted_accounting_date IS NOT NULL
								) OR
								(
									sel.contracted_accounting_date > last_month
								)
							)
						AND( --sel.oss = TRUE
							(
								(
										(sv.is_contracted = TRUE 
										AND sv.vat_country_id = 'ES' 
										AND NOT (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby')
										AND sv.activation_date < last_month 
										AND sv.contracting_date < last_month)
									OR(	sv.is_contracted = FALSE 
										AND sv.vat_country_id = 'ES' 
										AND i.num_inv > 0)
									OR(	i.num_inv > 0 
										AND (NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES' AND sv.seller_id = sel.id )))
										OR(	sv.is_contracted = TRUE 
											AND sv.vat_country_id = 'ES' 
											AND (sv.es_status_activation_id = 'no' or sv.es_status_altaiae_id = 'standby') 
											AND i.num_inv > 0)
								)
								AND (sv.vat_country_id = 'ES' AND ( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month))
							) OR
							(
								(sel.oss_date < last_month AND (sel.oss_end_date IS NULL OR sel.oss_end_date >= first_month ))
								OR
								(sel.oss IS True)
							)
						)
						--OR (i.num_inv_es > 0 AND (NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'ES' AND sv.seller_id = sel.id)))
						--OR (COUNT(DISTINCT CASE WHEN  inv.tax_country_id = 'ES' AND inv.accounting_date >= first_month OR (inv.accounting_date IS NULL AND NOT inv.status_id = 'discard') THEN inv.id END) >0)						
					GROUP BY sel.id
					ORDER BY sel.id
				) AS subselect
		LOOP
			result_json := result_json || jsonb_build_object(
				'seller_id', inv_data.id,
				'seller_name', inv_data.seller_name,
				'shortname', inv_data.seller_shortname,
				'email', inv_data.email,
				'user_name', inv_data.user_name,
				'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
				'num_pending_invoices', inv_data.num_pending_invoices,
				'percentage_pending_invoices', inv_data.percentage_pending_invoices,
				'month1', inv_data.month_1,
				'month2', inv_data.month_2,
				'month3', inv_data.month_3,
				'month4', inv_data.month_4,
				'month5', inv_data.month_5,
				'month6', inv_data.month_6,
				'month7', inv_data.month_7,
				'month8', inv_data.month_8,
				'month9', inv_data.month_9,
				'month10', inv_data.month_10,
				'month11', inv_data.month_11,
				'month12', inv_data.month_12,
				'model_303', inv_data.model_303,
				'model_349', inv_data.model_349,
				'model_369', inv_data.model_369,
				'model_min', inv_data.model_min,
				'model_avg', inv_data.model_avg,
				'num_invoices', inv_data.num_invoices_this_period			
			);
			
		END LOOP;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;