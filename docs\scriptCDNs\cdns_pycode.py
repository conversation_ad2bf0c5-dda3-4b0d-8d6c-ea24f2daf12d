import os
import re
import json

# Definir la ruta donde están los archivos .py y la carpeta donde se guardará el archivo JSON de salida
pycode_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'muaytax')  # Cambia esta ruta si es necesario
output_dir = os.path.dirname(__file__)
output_file = os.path.join(output_dir, 'all_http_pycode.json')

# Expresión regular para detectar URLs de CDN en los archivos .py
cdn_pattern = re.compile(r'https?://[^\s\'"]+', re.IGNORECASE | re.DOTALL)

# Diccionario para almacenar el mapeo de URLs de CDN
cdn_mapping = {}

# Función para extraer la clave de agrupación (dominio principal) de una URL
def extract_group_key(url):
    match = re.search(r'https?://([^/]+)', url)
    if match:
        domain = match.group(1)
        group_key = domain.split('.')[0]
        return group_key
    return "unknown"

# Recorrer los archivos .py para encontrar y mapear URLs de CDNs
for root, dirs, files in os.walk(pycode_dir):
    for file in files:
        if file.endswith(".py"):  # Solo se consideran archivos .py
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                matches = cdn_pattern.findall(content)  # Buscar todas las URLs que coincidan con el patrón de CDN
                for url in matches:
                    # Eliminar la barra invertida del final si existe
                    url = url.rstrip('\\')
                    
                    sub_group = extract_group_key(url)  # Extraer el grupo clave (dominio principal)
                    
                    # Filtrar solo dominios que contienen "cdn" o "cdns"
                    if "cdn" in sub_group or "cdns" in sub_group:
                        # Inicializar el subgrupo si no existe en el mapeo
                        if sub_group not in cdn_mapping:
                            cdn_mapping[sub_group] = {}
                        
                        # Si la URL ya existe, incrementar el contador y añadir el nombre del archivo
                        if url in cdn_mapping[sub_group]:
                            cdn_mapping[sub_group][url]['templates_usados'] += 1
                            if file not in cdn_mapping[sub_group][url]['archivos']:
                                cdn_mapping[sub_group][url]['archivos'].append(file)
                        else:
                            # Si es la primera vez que se encuentra la URL, inicializar el contador y la lista de archivos
                            cdn_mapping[sub_group][url] = {
                                "templates_usados": 1,
                                "archivos": [file]
                            }

# Guardar el mapeo en un archivo JSON
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(cdn_mapping, f, indent=4)

print(f"Todas las URLs de CDN encontradas en los archivos .py se han mapeado en {output_file}")
