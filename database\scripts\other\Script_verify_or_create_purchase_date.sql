DO $$
BEGIN
    -- service_registration_purchase_date
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'sellers_seller' 
        AND column_name = 'service_registration_purchase_date'
    ) THEN
        ALTER TABLE sellers_seller 
        ADD COLUMN service_registration_purchase_date date NULL;

        COMMENT ON COLUMN sellers_seller.service_registration_purchase_date 
        IS 'Date when the company registration service was purchased';
    END IF;

    -- service_cancellation_purchase_date
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'sellers_seller' 
        AND column_name = 'service_cancellation_purchase_date'
    ) THEN
        ALTER TABLE sellers_seller 
        ADD COLUMN service_cancellation_purchase_date date NULL;

        COMMENT ON COLUMN sellers_seller.service_cancellation_purchase_date 
        IS 'Date when the company cancellation service was purchased';
    END IF;

    -- service_llc_registration_purchase_date
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'sellers_seller' 
        AND column_name = 'service_llc_registration_purchase_date'
    ) THEN
        ALTER TABLE sellers_seller 
        ADD COLUMN service_llc_registration_purchase_date date NULL;

        COMMENT ON COLUMN sellers_seller.service_llc_registration_purchase_date 
        IS 'Date when the LLC registration service was purchased';
    END IF;

    -- service_llc_cancellation_purchase_date
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'sellers_seller' 
        AND column_name = 'service_llc_cancellation_purchase_date'
    ) THEN
        ALTER TABLE sellers_seller 
        ADD COLUMN service_llc_cancellation_purchase_date date NULL;

        COMMENT ON COLUMN sellers_seller.service_llc_cancellation_purchase_date 
        IS 'Date when the LLC cancellation service was purchased';
    END IF;
END;
$$ LANGUAGE plpgsql;