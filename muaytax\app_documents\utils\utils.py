import json
import os
import random
import string
import uuid
from datetime import datetime, time, timedelta

import deepl
import pandas as pd
import requests
import xlsxwriter
from django.conf import settings
from django.core.files.base import ContentFile
from django.db import connection
from django.db.models import DateField, DateTimeField
from django.utils import timezone
from django_celery_beat.models import ClockedSchedule, PeriodicTask
from requests_pkcs12 import post

from muaytax.app_documents.models.presented_model import PresentedModel
from muaytax.app_sellers.models import Seller
from muaytax.dictionaries.models import ModelStatus
from muaytax.dictionaries.models.dates_direct_debit import DatesDirectDebit
from muaytax.dictionaries.models.model import Model
from muaytax.email_notifications.model_notifications import (
    send_email_model_presented,
    send_email_presentedModel303,
)
from muaytax.users.models import User
from muaytax.utils.general import get_first_and_last_date


def generate_external_excel_111(seller: Seller, model: PresentedModel) -> str:
    """
    Generate an excel file with the data of the model 111
    It returns the path of the file
    """

    filePath = f"muaytax/media/xls_model_111/{seller.shortname}_modelo_111_{model.year}_{model.period.code}_{uuid.uuid4().hex[:12]}.xlsx"
    today = datetime.now().strftime('%d/%m/%Y')

    with xlsxwriter.Workbook(filePath) as workbook:
        worksheet = workbook.add_worksheet()

        # Define formats
        header_height = 30
        header_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'bg_color': '#d9d9d9',
            'valign': 'vcenter',
            'border': 1,
        })
        centered_title_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'bg_color': '#d9d9d9',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
        })
        total_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'bg_color': '#d9d9d9',
            'align': 'right',
            'border': 1,
        })
        data_row_format = {
            'number_format': workbook.add_format({'border': 1, 'align': 'right'}),
            'text_format': workbook.add_format({'border': 1, 'align': 'left'}),
        }

        # Write title of the document on the first row
        seller_title = f'SOCIEDAD: {seller.name}'
        period_title = f'PERIODO: {model.period} - {model.year}'
        worksheet.merge_range(0, 0, 0, 5, seller_title, header_format)
        worksheet.merge_range(0, 6, 0, 8, period_title, header_format)

        # write titel of table
        title = 'RETENCIONES EFECTUADAS POR PROFESIONALES INDEPENDIENTES'
        worksheet.merge_range(2, 0, 2, 8, title, centered_title_format)

        # Get data from the model WITH SQL
        json_data = get_perceptors_model_111(seller, model.year, model.period)
        json_data = [{
            'N': i+1,
            'APELLIDOS Y NOMBRE': elem['nombre_apellido'],
            'NIF': elem['nif_cif_iva'],
            'DOMICILIO': elem['address'],
            'CP': elem['address_zip'],
            'MUNICIPIO': elem['address_city'],
            'BASE IMP.': elem['total_con_base'],
            '%': elem['irpf'],
            'RETENCION': elem['total_retencion_result']}
            for i, elem in enumerate(json_data)]

        # retencion total
        total_retencion = sum([elem['RETENCION'] for elem in json_data])


        df = pd.DataFrame(json_data)
        columns = ['N', 'APELLIDOS Y NOMBRE', 'NIF', 'DOMICILIO', 'CP', 'MUNICIPIO', 'BASE IMP.', '%', 'RETENCION']
        df = df[columns]

        worksheet.set_row(0, header_height)
        worksheet.set_row(2, header_height)
        worksheet.set_row(3, header_height)
        worksheet.set_column(0, 0, 3)

        # Write headers with specified format
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(3, col_num, value, header_format)

        # Write data
        for row_num, row_data in enumerate(df.values):
            for col_num, value in enumerate(row_data):
                if col_num in [0, 4, 6, 7, 8]:
                    row_format = data_row_format['number_format']
                else:
                    row_format = data_row_format['text_format']
                worksheet.write(row_num + 4, col_num, value, row_format)

        # Set column width
        for i, column in enumerate(df.columns):
            column_len = max(df[column].astype(str).map(len).max() + 3, len(column) + 5)
            worksheet.set_column(i, i, column_len)

        # write a row after the data
        worksheet.write(row_num + 5, 5, 'TOTAL', header_format)
        worksheet.write(row_num + 5, 6, '', header_format)
        worksheet.write(row_num + 5, 7, '', header_format)
        worksheet.write(row_num + 5, 8, total_retencion, total_format)

        # write date of creation
        worksheet.merge_range(row_num+9, 5, row_num+9, 6, f'Valencia, a {today}', data_row_format['text_format'])

    return filePath


def get_translations(presented):
    result = {}
    seller = presented.seller
    accounting_r = presented.accounting_m5472_1120.all()
    translator = deepl.Translator(settings.DEEPL_API_KEY)

    try:
        translated_fields = translator.translate_text([
            seller.seller_address.address_state,
            seller.country_registration.name,
            presented.member_address.address_state,
            presented.member_address.address_country.name,
            presented.member_country.name,
            presented.tax_residence_country.name,
            presented.activity_country.name,
            presented.desc_main_activity,
            *[elem.description for elem in accounting_r],
            *[elem.get_transaction_type_display() for elem in accounting_r],
            *[elem.name for elem in presented.main_activity_countries.all()]
        ], source_lang="ES", target_lang="EN-US")
    except deepl.DeepLException:
        return None

    result_keys = [
        'seller_address_state', 'country_registration', 'member_address_state', 'member_address_country',
        'member_country', 'tax_residence_country', 'activity_country', 'desc_main_activity'
    ]
    result.update(dict(zip(result_keys, [text.text for text in translated_fields[:8]])))

    result['accounting_records'] = [
        {
            'id': elem.id,
            'desc': desc.text,
            'trans': transaction.text,
        } for elem, desc, transaction in zip(
            accounting_r,
            translated_fields[8:8 + len(accounting_r)],
            translated_fields[8 + len(accounting_r):8 + len(accounting_r) * 2]
        )
    ]
    result['main_activity_countries'] = [text.text for text in translated_fields[8 + len(accounting_r) * 2:]]

    return result


def get_perceptors_model_111(seller: Seller, year: str, period: str) -> list:
    first_date, last_date = get_first_and_last_date(year, period.code)
    sql = "SELECT func_get_data_model_111_externo(%s, %s, %s);"
    params = (seller.pk, first_date, last_date)

    try:
        with connection.cursor() as cursor:
            cursor.execute(sql, params)

            result_json = cursor.fetchone()[0]
            result_json = json.loads(result_json)

    except Exception as e:
        print(e)
        result_json = None

    return result_json


def update_model_fromAEAT(existing_pm: PresentedModel, json_response, sent_by):
    """
    Update the model with the data from the AEAT
    """

    today = timezone.now()
    isContrated = existing_pm.seller.contracted_accounting
    legal_entity = existing_pm.seller.legal_entity
    random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=7))
    file_name = "{}_{}_{}_{}_{}_{}.pdf".format(
        existing_pm.seller.shortname,
        existing_pm.country.name,
        existing_pm.model.code,
        existing_pm.period.code,
        existing_pm.year,
        random_string
    )


    file_aeat = json_response.get('respuesta').get('correcta').get('urlPdf')

    # Save pdf file from AEAT
    response = requests.get(file_aeat)
    if response.status_code == 200:
        existing_pm.file.save(file_name, ContentFile(response.content), save=True)
    existing_pm.file.name = f'uploads/presented_models/{file_name}'
    existing_pm.status = ModelStatus.objects.get(code='presented')
    existing_pm.presentation_date = today
    existing_pm.aeat_json_response = json_response
    existing_pm.model_sent_by = sent_by
    existing_pm.save()

    #Send emails to seller
    if existing_pm.country.iso_code == 'ES' and existing_pm.status.code == 'presented':
        if existing_pm.model.code != 'ES-303':
            existing_pm.is_paid = True
            existing_pm.save()
            send_email_model_presented(existing_pm.seller, existing_pm)
        elif existing_pm.model.code == 'ES-303' and isContrated == True:
            existing_pm.is_paid = True
            existing_pm.save()
            send_email_model_presented(existing_pm.seller, existing_pm)
        elif existing_pm.model.code == 'ES-303' and isContrated != True and existing_pm.result.code == 'deposit' and existing_pm.amount != None and existing_pm.amount != 0:
            if (legal_entity is not None and (legal_entity == 'sl' or legal_entity == 'self-employed')):
                existing_pm.is_paid = True
                existing_pm.save()
                send_email_model_presented(existing_pm.seller, existing_pm)
            else:
                existing_pm.save()
                send_email_presentedModel303(existing_pm.seller, existing_pm)

def still_in_limit_date_direct_debit(presented_model, type_date):
    """
    Check if the date is still in the limit date for direct debit
    """

    still_in_limit = False

    #Buscamos si tenemos un registro de fechas para un modelo específico
    model_record = DatesDirectDebit.objects.filter(model__icontains = presented_model.model.code, period = presented_model.period.code, year = presented_model.year ).first()
    if model_record:
        limit_date_direct_debit = model_record
    else:
        limit_date_direct_debit = DatesDirectDebit.objects.filter(model = "all", period = presented_model.period.code, year = presented_model.year).first()

    if limit_date_direct_debit:
        if type_date == 'aeat':
            still_in_limit = limit_date_direct_debit.date_aeat >= timezone.now() >= limit_date_direct_debit.start_date_aeat if limit_date_direct_debit.date_aeat and limit_date_direct_debit.start_date_aeat else False
        elif type_date == 'app':
            still_in_limit = limit_date_direct_debit.date >= timezone.now() >= limit_date_direct_debit.start_date_aeat if limit_date_direct_debit.date and limit_date_direct_debit.start_date_aeat else False
    return still_in_limit


def create_periodic_task_instance (name, task, clocked, one_off, param, kwargs={}):
    clocked_schedule, _ = ClockedSchedule.objects.get_or_create(clocked_time=clocked)
    periodic_task = PeriodicTask.objects.create(
        name=f"Tarea - {name}",
        task=task,
        clocked=clocked_schedule,
        one_off=one_off,
        args=[param],
        kwargs=kwargs,
    )

def present_to_AEAT(presented_model, model_txt, cert_path, cert_password, headers, sent_by):
        json_response = {}
        json_data = {
            "MODELO": presented_model.model.code.replace('ES-', ''),
            "EJERCICIO": f"{presented_model.year}",
            "PERIODO": f"{presented_model.period.code}" if presented_model.period.code == '0A' else f"{presented_model.period.code.replace('Q','')}T",
            "NRC": "" if presented_model.nrc is None else f"{presented_model.nrc}",
            "IDI": "ES",
            "F01": f"{model_txt}",
            "FIR": "FirmaBasica",
            "FIRNIF": "B67659607",
            "FIRNOMBRE": "MUAY TAX ADVISORS SL"
        }
        print("jsonData: ", json_data)
        # Send request to AEAT
        response = post(
                        settings.URL_AEAT,
                        json=json_data,
                        pkcs12_filename=cert_path,
                        pkcs12_password=cert_password,
                        headers=headers,
                        verify=settings.PATH_VERIFY_CA)

        print("response: ", response)
        if response.ok:
            try:
                json_response = response.json()

                if 'correcta' in json_response.get('respuesta'):
                    update_model_fromAEAT(presented_model, json_response, sent_by)
                    json_response = front_json(json_response, presented_model)
                if 'errores' in json_response.get('respuesta'):
                    presented_model.aeat_json_response = json_response
                    presented_model.model_sent_by = sent_by
                    presented_model.save()
            except Exception as e:
                json_response.update({"respuesta": {'errores':['Error en la solicitud a la AEAT, contacta con soporte ']}})
        else:
            json_response.update({"respuesta": {'errores':['Error en la respuesta a la AEAT, contacta con soporte']}})

        return json_response

def draft_valitation(presented_model, model_txt, cert_path, cert_password, headers):
        json_response = {}
        json_data = {
            "MODELO": presented_model.model.code.replace('ES-', ''),
            "EJERCICIO": f"{presented_model.year}",
            "PERIODO": f"{presented_model.period.code}" if presented_model.period.code == '0A' else f"{presented_model.period.code.replace('Q','')}T",
            "IDI": "ES",
            "F01": f"{model_txt}",
        }


        #Send draft validation request to AEAT
        response = post(settings.URL_AEAT_VALIDATION,
                        json=json_data,
                        pkcs12_filename=cert_path,
                        pkcs12_password=cert_password,
                        headers=headers,
                        verify=settings.PATH_VERIFY_CA)

        if response.ok:
            try:
                json_response = response.json()
            except Exception as e:
                json_response.update({"respuesta": {'errores':['Error en la solicitud a la AEAT, contacta con soporte']}})
        else:
            json_response.update({"respuesta": {'errores':['Error en la solicitud a la AEAT, contacta con soporte']}})

        return json_response

def front_json(json_response, presented_model):

        delete_keys = ['FormaPago', 'NIFPresentador', 'ApellidosNombrePresentador', 'TipoRepresentacion', 'NRCPago',
                    'ImporteAIngresar', 'Idioma', 'urlPdf' ]

        rename_keys = {'CodigoSeguroVerificacion': 'Código seguro de verificación',
                        'NIFDeclarante': 'NIF del declarante',
                        'ApellidosNombreDeclarante': 'Apellidos y nombre del declarante',
                        'Periodo': 'Período',
                        'avisos': 'Avisos',
                        'advertencias': 'Advertencias',
                        }

        data = json_response
        correcta = data.get('respuesta', {}).get('correcta', {})

        # Delete keys
        for clave in delete_keys:
            correcta.pop(clave, None)

        # Rename rest of keys
        for old_key, new_key in rename_keys.items():
            if old_key in correcta:
                correcta[new_key] = correcta.pop(old_key)

        # Add new_pdf key
        correcta['new_pdf'] = presented_model.get_file_url()

        return data

def set_execution_time():

    time_now = datetime.now()

    if time_now.weekday() not in [5, 6]:
        if time_now.hour >= 8 and (time_now.time()<=(time(19, 30))):
            execution_time = datetime.now() + timedelta(minutes=30)
        else:
            if time_now.hour <= 12 and time_now.hour >8:
                execution_time = time_now.replace(hour=8, minute=0, second=0, microsecond=0)
            if time_now.weekday() + 1 != 5:
                execution_time = time_now + timedelta(days=1)
                execution_time = execution_time.replace(hour=8, minute=0, second=0, microsecond=0)
            else:
                execution_time = time_now + timedelta(days=3)
                execution_time = execution_time.replace(hour=8, minute=0, second=0, microsecond=0)
    else:
        execution_time = time_now + timedelta(days=2) if time_now.weekday() == 5 else time_now + timedelta(days=1)
        execution_time = execution_time.replace(hour=8, minute=0, second=0, microsecond=0)

    return execution_time

def getPresentedModel(seller, year, periodo, model_id) -> str:
    r = None
    try:
        if 'M' in periodo:
            period = periodo
        elif periodo == '0A':
            period = '0A'
        else:
            period = 'Q' + str(periodo).replace('Q', '').replace('T', '')

        country_model = Model.objects.filter(code__icontains=model_id).first()
        if not country_model:
            print("[Error] Country model not found")
            return r

        # Verificar si model_id ya incluye el código del país
        if model_id.startswith(f"{country_model.country}-") or model_id.startswith("IT-") or model_id.startswith("ES-") or model_id.startswith("US-") or model_id.startswith("FR-") or model_id.startswith("GB-"):
            code_model = model_id
        else:
            code_model = f"{country_model.country}-{model_id}"

        presented_model = PresentedModel.objects.filter(
            seller=seller, year=year,
            period__code=period,
            model__code__icontains=code_model
            ).exclude(status__code='disagreed').first()

        if presented_model:
            file = presented_model.file
            fileName = f"{file.name}"
            filePath = f"muaytax/media/{fileName}"
            filePath2 = f"muaytax/media/uploads/presented_models/{fileName}"
            # CHECK IF FILE EXISTS
            if os.path.exists(filePath):
                r = filePath
            elif os.path.exists(filePath2):
                r = filePath2
            else:
                print(f"[Error] File {fileName} Not Found in getPresentedModel")
        else:
            print("[Info] No presented model found - will generate new model with current data")
    except Exception as e: # It's better to catch specific exceptions if possible
        print(f"[Error] Exception in getPresentedModel: {repr(e)}") # TODO_Hxgg6RF: No debe haber prints en toda esta página. Usamos sistema de logging.
    return r

def getGeneratedModel(seller, year, period, model_id):
    r = None
    try:

        country_model = Model.objects.filter(code__icontains=model_id).first()

        if not country_model:
            print("[Error] Country model not found")
            return r

        # Extraer el código del país del modelo
        country_code = country_model.country

        # Si model_id ya tiene el prefijo del país (como IT-INTRASTAT), no necesitamos limpiarlo
        if model_id.startswith(f"{country_code}-"):
            clean_model_id = model_id
        else:
            clean_model_id = model_id

        fileName = f"{seller.shortname}_{country_code}_{year}_{period}_{clean_model_id}.pdf"
        filePath = f"muaytax/media/generated_models/{fileName}"
        print(f"filePath: {filePath}")

        # CHECK IF FILE EXISTS
        if os.path.exists(filePath):
            r = filePath
        else:
            print(f"[Error] File Not Found in getGeneratedModel")
    except Exception as e:
        print(f"[Error] Exception in getGeneratedModel: {str(e)}")
    return r

def getGeneratedExcelModel(seller, year, periodo, model_id):
    """
    Busca archivos Excel generados previamente en media/generated_models/
    Similar a getGeneratedModel pero para archivos .xlsx
    """
    r = None
    try:
        country_model = Model.objects.filter(code__icontains=model_id).first()

        if not country_model:
            print("[Error] Country model not found in getGeneratedExcelModel")
            return r

        # Extraer el código del país del modelo
        country_code = country_model.country

        # Para modelos Excel, usar el patrón específico que usan las funciones de generación
        if model_id == "PL-excel_form_VAT":
            fileName = f"{seller.shortname}_PL_excel_form_VAT_{year}_{periodo}.xlsx"
        elif model_id == "CZ-excel_form_VAT":
            fileName = f"{seller.shortname}_CZ_excel_form_VAT_{year}_{periodo}.xlsx"
        elif model_id == "FR-CLIENT_DATA_FORM_AGENT":
            fileName = f"{seller.shortname}_FR_CLIENT_DATA_FORM_AGENT_{year}_{periodo}.xlsx"
        else:
            # Patrón genérico para otros modelos Excel futuros
            clean_model_id = model_id.replace(f"{country_code}-", "") if model_id.startswith(f"{country_code}-") else model_id
            fileName = f"{seller.shortname}_{country_code}_{clean_model_id}_{year}_{periodo}.xlsx"

        filePath = f"muaytax/media/generated_models/{fileName}"
        # CHECK IF FILE EXISTS
        if os.path.exists(filePath):
            r = filePath
        else:
            print(f"[Error] Excel File Not Found in getGeneratedExcelModel: {filePath}")
    except Exception as e:
        print(f"[Error] Exception in getGeneratedExcelModel: {str(e)}")
    return r

def setHistorialPresentedModel(presented_model, prev_presented_model, user):

    user = user
    email= None
    name_user = None
    if isinstance(user, User):
        email = user.email
        name_user = user.get_full_name() if user.role == 'manager' else user.seller.name


    historial_json = json.loads(json.dumps(presented_model.historial_change_json)) if presented_model.historial_change_json else {}
    data = {}

    if prev_presented_model is None:
        for field in presented_model._meta.fields:
            field_name = field.name
            field_value = getattr(presented_model, field_name)
            if isinstance(field, (DateField, DateTimeField)) and field_value is not None:
                data[field_name] = field_value.strftime('%d/%m/%Y %H:%M:%S')
            else:
                data[field_name] = f"{field_value}" if field_value is not None else None

        historial_json['create'] = {
            'date': datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
            'user': f"{user}",
            'user_email': email,
            'name_user': name_user,
            'data': data
        }

    elif prev_presented_model and presented_model:
        for field in presented_model._meta.fields:
            if field.name == 'historial_change_json':
                continue
            field_name = field.name
            field_value = getattr(presented_model, field_name)
            prev_field_value = getattr(prev_presented_model, field_name)
            if field_value != prev_field_value:
                if field_value == '' and prev_field_value is None:
                    continue
                if isinstance(field, (DateField, DateTimeField)):
                    prev_field_value = prev_field_value.strftime('%d/%m/%Y %H:%M:%S') if prev_field_value is not None else None
                    field_value = field_value.strftime('%d/%m/%Y %H:%M:%S') if field_value is not None else None

                data[field_name] = {
                    'old_value': f"{prev_field_value}" if prev_field_value is not None else None,
                    'new_value': f"{field_value}" if field_value is not None else None
                }
        if data:
            if historial_json.get('update_model'):
                historial_json['update_model'].append({
                    'date': datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
                    'user': f"{user}",
                    'user_email': email,
                    'name_user': name_user,
                    'data': data
                })
            else:
                historial_json['update_model'] = [{
                    'date': datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
                    'user': f"{user}",
                    'user_email': email,
                    'name_user': name_user,
                    'data': data
                }]
    if data != {}:
        presented_model.historial_change_json = historial_json
    return presented_model
