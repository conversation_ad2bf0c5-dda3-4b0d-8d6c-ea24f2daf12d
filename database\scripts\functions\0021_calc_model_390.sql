-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_390' ) THEN
	  DROP FUNCTION func_calc_model_es_390(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

--CREATE OR REPLACE FUNCTION func_calc_model_es_390(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER, json_tri1 JSONB, json_tri2 JSONB, json_tri3 JSONB, json_tri4 JSONB)
CREATE OR REPLACE FUNCTION func_calc_model_es_390(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE
	CA001 NUMERIC := 0;
	CA002 NUMERIC := 0;
	CA003 NUMERIC := 0;
	CA004 NUMERIC := 0;
	CA005 NUMERIC := 0;
	CA006 NUMERIC := 0;

	CA025 NUMERIC := 0;
	CA026 NUMERIC := 0;

	CA551 NUMERIC := 0;
	CA552 NUMERIC := 0;

	CA027 NUMERIC := 0;
	CA028 NUMERIC := 0;

	CA029 NUMERIC := 0;
	CA030 NUMERIC := 0;

	CA033 NUMERIC := 0;
	CA034 NUMERIC := 0;

	CA035 NUMERIC := 0;
	CA036 NUMERIC := 0;
	CA599 NUMERIC := 0;
	CA600 NUMERIC := 0;
	CA601 NUMERIC := 0;
	CA602 NUMERIC := 0;

	CA043 NUMERIC := 0;
	CA044 NUMERIC := 0;

	CA047 NUMERIC := 0;

	CA190 NUMERIC := 0;
	CA191 NUMERIC := 0;
	CA603 NUMERIC := 0;
	CA604 NUMERIC := 0;
	CA605 NUMERIC := 0;
	CA606 NUMERIC := 0;
	CA606_OLD NUMERIC := 0;

	CA048 NUMERIC := 0;
	CA049 NUMERIC := 0;

	CA202 NUMERIC := 0;
	CA203 NUMERIC := 0;
	CA619 NUMERIC := 0;
	CA620 NUMERIC := 0;
	CA621 NUMERIC := 0;
	CA622 NUMERIC := 0;

	CA052 NUMERIC := 0;
	CA053 NUMERIC := 0;

	CA629 NUMERIC := 0;
	CA630 NUMERIC := 0;

	CA056 NUMERIC := 0;
	CA057 NUMERIC := 0;

	CA637 NUMERIC := 0;
	CA638 NUMERIC := 0;
	CA639 NUMERIC := 0;

	CA597 NUMERIC := 0;
	CA598 NUMERIC := 0;

	CA064 NUMERIC := 0;
	CA065 NUMERIC := 0;

	CA084 NUMERIC := 0;
	CA085 NUMERIC := 0;
	CA086 NUMERIC := 0;
	CA086_OLD NUMERIC := 0;
	ENTRANCE VARCHAR := 'NONE';

	CA095 NUMERIC := 0;
	PREV_CA71_TRI1 NUMERIC := 0;
	PREV_CA71_TRI2 NUMERIC := 0;
	PREV_CA71_TRI3 NUMERIC := 0;
	PREV_CA71_TRI4 NUMERIC := 0;
	CA097 NUMERIC := 0;
	CA098 NUMERIC := 0;
	CA662 NUMERIC := 0;

	CA099 NUMERIC := 0;
	CA103 NUMERIC := 0;
	CA104 NUMERIC := 0;
	CA110 NUMERIC := 0;
	CA125 NUMERIC := 0;
	CA126 NUMERIC := 0;
	CA127 NUMERIC := 0;
	CA108 NUMERIC := 0;

	CA230 NUMERIC := 0;
	CA231 NUMERIC := 0;

	DIF NUMERIC := 0;
	DIF_OLD NUMERIC := 0;
	DIF_PCT VARCHAR := '0%';
	TOTAL_AP9 NUMERIC := 0;
	FIRST_SUM NUMERIC := 0;
	LAST_SUM NUMERIC := 0;
	CA086_CHECK NUMERIC := 0;

	short_name VARCHAR := '';
	total_iae INTEGER := 0;
	total_amount_iae NUMERIC := 0;
	iae_id VARCHAR := '';
	iae_name VARCHAR := '';
	iae_cnae VARCHAR := '';
	iae_code VARCHAR := '';
	choiceseller VARCHAR:= '';
	inv_intra_expenses_600 RECORD;
	inv_intra_expenses_not_600 RECORD;
	inv_tax_4 RECORD;
	inv_tax_10 RECORD;
	inv_tax_21 RECORD;
	inv_dua_tax_4 RECORD;
	inv_dua_tax_10 RECORD;
	inv_dua_tax_21 RECORD;
	inv_tax_0 RECORD;
	iae_row RECORD;
	last_year_303 RECORD;
	json_last_year JSONB;
	json_tri1 JSONB;
	json_tri2 JSONB;
	json_tri3 JSONB;
	json_tri4 JSONB;
	unique_iae RECORD;

	--VARIABLES PARA CONSTRUIR EL 3ER JSON
	tmp_key VARCHAR := '';
    tmp_value VARCHAR := '';
	ACT NUMERIC := 1;
	tmp_json JSONB;
	json_iae JSONB;


	json_result_1 JSONB;
	json_result_2 JSONB;

	--CASILLAS SIN LOGICA

	--CASILLA 64
	CA513 NUMERIC := 0;
	CA051 NUMERIC := 0;
	CA055 NUMERIC := 0;
	CA059 NUMERIC := 0;
	CA061 NUMERIC := 0;
	CA661 NUMERIC := 0;
	CA062 NUMERIC := 0;
	CA652 NUMERIC := 0;
	CA063 NUMERIC := 0;
	CA521 NUMERIC := 0;
	CA522 NUMERIC := 0;

	--CASILLA 47
	CA042 NUMERIC := 0;
	CA046 NUMERIC := 0;

	--CASILLA 84
	CA083 NUMERIC := 0;
	CA658 NUMERIC := 0;

	--CASILLA 86
	CA659 NUMERIC := 0;

	--CASILLA 108
	CA105 NUMERIC := 0;
BEGIN

	-- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

	-- INTRA-COMMUNITY-EXPENSE + INTRA-COMMUNITY-CREDIT + INBOUND TRANSFER con cuenta contable 600
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount
	INTO inv_intra_expenses_600
	FROM
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id in ('intra-community-expense', 'inbound-transfer', 'intra-community-credit')
		AND account_expenses_id = '600'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id in ('intra-community-expense', 'inbound-transfer', 'intra-community-credit')
		AND account_expenses_id = '600'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses;

	-- INTRA-COMMUNITY-EXPENSE + INTRA-COMMUNITY-CREDIT + INBOUND TRANSFER con cuenta contable DIFERENTE a 600
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount
	INTO inv_intra_expenses_not_600
	FROM
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id in ('intra-community-expense', 'inbound-transfer', 'intra-community-credit')
		AND NOT account_expenses_id = '600'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id in ('intra-community-expense', 'inbound-transfer', 'intra-community-credit')
		AND NOT account_expenses_id = '600'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses;

	-- AMOUNT AND VAT 4% (LOCAL EXPENSE)
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount,
		ROUND(common_vat_expenses.common_vat::numeric, 2) AS common_vat_expenses,
		ROUND(amz_vat_expenses.amz_vat::numeric, 2) AS amz_vat_expenses,
		ROUND((common_vat_expenses.common_vat + amz_vat_expenses.amz_vat)::numeric,2) AS sum_vat
	INTO inv_tax_4
	FROM
	(	
		--Todas las BASES IMPONIBLES de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 4%
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '4'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '4'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		--Todas las cuotas IVA de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 4%
		SELECT DISTINCT  COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '4'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_vat_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '4'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_vat_expenses;
	
	-- AMOUNT AND VAT 10% (LOCAL EXPENSE)
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount,
		ROUND(common_vat_expenses.common_vat::numeric, 2) AS common_vat_expenses,
		ROUND(amz_vat_expenses.amz_vat::numeric, 2) AS amz_vat_expenses,
		ROUND((common_vat_expenses.common_vat + amz_vat_expenses.amz_vat)::numeric,2) AS sum_vat
	INTO inv_tax_10
	FROM
	(
		--Todas las BASES IMPONIBLES de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 10%
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '10'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '10'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		--Todas las cuotas IVA de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 10%
		SELECT DISTINCT  COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '10'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_vat_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '10'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_vat_expenses;

	-- AMOUNT AND VAT 21% (LOCAL EXPENSE)
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount,
		ROUND(common_vat_expenses.common_vat::numeric, 2) AS common_vat_expenses,
		ROUND(amz_vat_expenses.amz_vat::numeric, 2) AS amz_vat_expenses,
		ROUND((common_vat_expenses.common_vat + amz_vat_expenses.amz_vat)::numeric,2) AS sum_vat
	INTO inv_tax_21
	FROM
	(
		--Todas las BASES IMPONIBLES de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 21%
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		--Todas las cuotas IVA de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 21%
		SELECT DISTINCT  COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_vat_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_vat_expenses;

	-- AMOUNT AND VAT 4% (IMPORT DUA)
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount,
		ROUND(common_vat_expenses.common_vat::numeric, 2) AS common_vat_expenses,
		ROUND(amz_vat_expenses.amz_vat::numeric, 2) AS amz_vat_expenses,
		ROUND((common_vat_expenses.common_vat + amz_vat_expenses.amz_vat)::numeric,2) AS sum_vat
	INTO inv_dua_tax_4
	FROM
	(
		--Todas las BASES IMPONIBLES de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 4%
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '4'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	)AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '4'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		--Todas las cuotas IVA de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 4%
		SELECT DISTINCT  COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '4'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_vat_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '4'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_vat_expenses;
	
	-- AMOUNT AND VAT 10% (IMPORT DUA)
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount,
		ROUND(common_vat_expenses.common_vat::numeric, 2) AS common_vat_expenses,
		ROUND(amz_vat_expenses.amz_vat::numeric, 2) AS amz_vat_expenses,
		ROUND((common_vat_expenses.common_vat + amz_vat_expenses.amz_vat)::numeric,2) AS sum_vat
	INTO inv_dua_tax_10
	FROM
	(
		--Todas las BASES IMPONIBLES de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 10%
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '10'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '10'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		--Todas las CUOTAS IVA de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 10%
		SELECT DISTINCT  COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '4'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_vat_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '10'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_vat_expenses;

	--AMOUNT AND VAT 21% (IMPORT DUA)
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount,
		ROUND(common_vat_expenses.common_vat::numeric, 2) AS common_vat_expenses,
		ROUND(amz_vat_expenses.amz_vat::numeric, 2) AS amz_vat_expenses,
		ROUND((common_vat_expenses.common_vat + amz_vat_expenses.amz_vat)::numeric,2) AS sum_vat
	INTO inv_dua_tax_21
	FROM
	(
		--Todas las BASES IMPONIBLES de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 21%
		SELECT DISTINCT COALESCE(SUM(con.amount_original * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_original),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		--Todas las CUOTAS IVA de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 21%
		SELECT DISTINCT  COALESCE(SUM(con.amount_original * con.quantity * con.vat / 100),0) as common_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_vat_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_original * con.vat / 100),0) as amz_vat
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND vat = '21'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_vat_expenses;

	--AMOUNT VAT 0% (LOCAL EXPENSES)
	SELECT
		ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses,
		ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses,
		ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric,2) AS sum_amount
	INTO inv_tax_0
	FROM
	(
		--Todas las BASES IMPONIBLES de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 0%
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '0'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) AS amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND vat = '0'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS true
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND con.is_supplied IS NOT true
	) AS amz_expenses;
	
	--EXTRAER TODOS LOS IAE total_amount_iae
		FOR iae_row IN (
			SELECT DISTINCT
			iae.sellervat_activity_iae_id,
			ROUND(COALESCE(SUM(con_amz.amount_euros), 0)::numeric, 2) as total_euros_amz,
			ROUND(COALESCE(SUM(con_common.amount_euros * con_common.quantity), 0)::numeric, 2) as total_euros_common,
			CASE WHEN (ROUND(COALESCE(SUM(con_amz.amount_euros), 0)::numeric, 2) + ROUND(COALESCE(SUM(con_common.amount_euros * con_common.quantity), 0)::numeric, 2)) < 0 THEN 0 
			ELSE ROUND(COALESCE(SUM(con_amz.amount_euros), 0)::numeric, 2) + ROUND(COALESCE(SUM(con_common.amount_euros * con_common.quantity), 0)::numeric, 2) END as total_euros,
			COALESCE(COUNT(DISTINCT con_amz.id), 0) AS total_amz_inv,
			COALESCE(COUNT(DISTINCT con_common.id), 0) AS total_commom_inv
			FROM
			(
				SELECT DISTINCT 
				sva.sellervat_activity_iae_id
				FROM sellers_seller sel
				INNER JOIN sellers_sellervat sv ON sv.seller_id = sel.id
				INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id 
				WHERE sel.id = sellerid
				AND sv.vat_country_id = 'ES'
				AND sva.sellervat_activity_iae_id LIKE 'ES%'
				AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
				GROUP BY sva.sellervat_activity_iae_id
			) as iae
			LEFT JOIN (
				SELECT DISTINCT 
				con.id,
				con.amount_euros,
				con.quantity,
				inv.iae_id
				FROM sellers_seller sel
				INNER JOIN invoices_invoice inv ON inv.seller_id = sel.id
				INNER JOIN invoices_concept con ON con.invoice_id = inv.id
				INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        		INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
				WHERE sel.id = sellerid
				AND EXTRACT(YEAR FROM accounting_date) = date_year
				AND EXTRACT(MONTH FROM accounting_date) >= month_min
				AND EXTRACT(MONTH FROM accounting_date) <= month_max
				AND status_id = 'revised'
				AND tax_country_id = 'ES'
				AND invoice_category_id = 'sales'
				AND is_generated_amz IS NOT true
				AND is_txt_amz IS NOT true
				AND transaction_type_id not like '%-transfer'
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
				AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
				AND inv.iae_id = sva.sellervat_activity_iae_id
				AND con.is_supplied IS NOT true
				GROUP BY con.id, con.amount_euros, inv.iae_id
			) as con_common ON iae.sellervat_activity_iae_id = con_common.iae_id
			LEFT JOIN (
				SELECT DISTINCT 
				con.id,
				con.amount_euros,
				inv.iae_id
				FROM sellers_seller sel
				INNER JOIN invoices_invoice inv ON inv.seller_id = sel.id
				INNER JOIN invoices_concept con ON con.invoice_id = inv.id
				INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        		INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
				WHERE sel.id = sellerid
				AND EXTRACT(YEAR FROM accounting_date) = date_year
				AND EXTRACT(MONTH FROM accounting_date) >= month_min
				AND EXTRACT(MONTH FROM accounting_date) <= month_max
				AND status_id = 'revised'
				AND tax_country_id = 'ES'
				AND invoice_category_id = 'sales'
				AND is_generated_amz IS NOT true
				AND is_txt_amz IS true
				AND transaction_type_id not like '%-transfer'
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
				AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
				AND inv.iae_id = sva.sellervat_activity_iae_id
				AND con.is_supplied IS NOT true
				GROUP BY con.id, con.amount_euros, inv.iae_id
			) as con_amz ON iae.sellervat_activity_iae_id = con_amz.iae_id
			GROUP BY iae.sellervat_activity_iae_id
		)	
		LOOP
			total_amount_iae := total_amount_iae + iae_row.total_euros;
			IF iae_row.total_euros > 0 THEN
				total_iae := total_iae + 1;
			END IF;
		END LOOP;
	
	json_iae := jsonb_build_object();
	IF total_iae > 1 THEN		

		FOR iae_row IN (
			SELECT DISTINCT
			iae.sellervat_activity_iae_id,
			ROUND(COALESCE(SUM(con_amz.amount_euros), 0)::numeric, 2) as total_euros_amz,
			ROUND(COALESCE(SUM(con_common.amount_euros * con_common.quantity), 0)::numeric, 2) as total_euros_common,
			CASE WHEN (ROUND(COALESCE(SUM(con_amz.amount_euros), 0)::numeric, 2) + ROUND(COALESCE(SUM(con_common.amount_euros * con_common.quantity), 0)::numeric, 2)) < 0 THEN 0 
			ELSE ROUND(COALESCE(SUM(con_amz.amount_euros), 0)::numeric, 2) + ROUND(COALESCE(SUM(con_common.amount_euros * con_common.quantity), 0)::numeric, 2) END as total_euros,
			COALESCE(COUNT(DISTINCT con_amz.id), 0) AS total_amz_inv,
			COALESCE(COUNT(DISTINCT con_common.id), 0) AS total_commom_inv
			FROM
			(
				SELECT DISTINCT 
				sva.sellervat_activity_iae_id
				FROM sellers_seller sel
				INNER JOIN sellers_sellervat sv ON sv.seller_id = sel.id
				INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id
				WHERE sel.id = sellerid
				AND sv.vat_country_id = 'ES'
				AND sva.sellervat_activity_iae_id LIKE 'ES%'
				AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
				GROUP BY sva.sellervat_activity_iae_id
			) as iae
			LEFT JOIN (
				SELECT DISTINCT 
				con.id,
				con.amount_euros,
				con.quantity,
				inv.iae_id
				FROM sellers_seller sel
				INNER JOIN invoices_invoice inv ON inv.seller_id = sel.id
				INNER JOIN invoices_concept con ON con.invoice_id = inv.id
				INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        		INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
				WHERE sel.id = sellerid
				AND EXTRACT(YEAR FROM accounting_date) = date_year
				AND EXTRACT(MONTH FROM accounting_date) >= month_min
				AND EXTRACT(MONTH FROM accounting_date) <= month_max
				AND status_id = 'revised'
				AND tax_country_id = 'ES'
				AND invoice_category_id = 'sales'
				AND is_generated_amz IS NOT true
				AND is_txt_amz IS NOT true
				AND transaction_type_id not like '%-transfer'
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
				AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
				AND inv.iae_id = sva.sellervat_activity_iae_id
				AND con.is_supplied IS NOT true
				GROUP BY con.id, con.amount_euros, inv.iae_id
			) as con_common ON iae.sellervat_activity_iae_id = con_common.iae_id
			LEFT JOIN (
				SELECT DISTINCT 
				con.id,
				con.amount_euros,
				inv.iae_id
				FROM sellers_seller sel
				INNER JOIN invoices_invoice inv ON inv.seller_id = sel.id
				INNER JOIN invoices_concept con ON con.invoice_id = inv.id
				INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        		INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
				WHERE sel.id = sellerid
				AND EXTRACT(YEAR FROM accounting_date) = date_year
				AND EXTRACT(MONTH FROM accounting_date) >= month_min
				AND EXTRACT(MONTH FROM accounting_date) <= month_max
				AND status_id = 'revised'
				AND tax_country_id = 'ES'
				AND invoice_category_id = 'sales'
				AND is_generated_amz IS NOT true
				AND is_txt_amz IS true
				AND transaction_type_id not like '%-transfer'
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
				AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
				AND inv.iae_id = sva.sellervat_activity_iae_id
				AND con.is_supplied IS NOT true
				GROUP BY con.id, con.amount_euros, inv.iae_id
			) as con_amz ON iae.sellervat_activity_iae_id = con_amz.iae_id
			GROUP BY iae.sellervat_activity_iae_id
		)	
		LOOP

			IF iae_row.total_euros != 0 THEN			
				--TOTAL de las facturas
				tmp_value := total_amount_iae;
				tmp_key := 'TOTAL' || ACT::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_iae := jsonb_concat(json_iae, tmp_json);
				
				--IAE de la factura
				tmp_value := iae_row.sellervat_activity_iae_id;
				tmp_key := 'IAE_' || ACT::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_iae := jsonb_concat(json_iae, tmp_json);

				--CNAE
				SELECT cnae INTO tmp_value FROM dictionaries_economicactivity WHERE code = iae_row.sellervat_activity_iae_id LIMIT 1;
				tmp_key := 'CA114_' || ACT::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_iae := jsonb_concat(json_iae, tmp_json);

				--Todas las SALE anuales TAX COUNTRY ES que esten asociadas a ese IAE
				tmp_value := iae_row.total_euros;
				tmp_key := 'CA115_' || ACT::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_iae := jsonb_concat(json_iae, tmp_json);

				--Tipo
				tmp_value := 'G';
				tmp_key := 'CA117_' || ACT::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_iae := jsonb_concat(json_iae, tmp_json);

				--Porcentaje del total de SALE de ese IAE en relación al total de ventas de todos los IAE
				tmp_value := ROUND((iae_row.total_euros / total_amount_iae) * 100, 2);
				tmp_key := 'CA118_' || ACT::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_iae := jsonb_concat(json_iae, tmp_json);

				--Incrementar ACT
				ACT := ACT + 1;
			END IF;
		END LOOP;
	END IF;

	IF total_iae = 1 OR total_iae = 0 THEN
		SELECT 
		sva.sellervat_activity_iae_id,
		dic_iae.description,
		dic_iae.cnae,
		dic_iae.activity_code
		INTO unique_iae
		FROM sellers_seller sel
		INNER JOIN sellers_sellervat sv ON sv.seller_id = sel.id
		INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id
		INNER JOIN dictionaries_economicactivity dic_iae ON dic_iae.code = sva.sellervat_activity_iae_id
		WHERE sel.id = sellerid AND sv.vat_country_id = 'ES'
		AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		LIMIT 1;

		--ASIGNAR VALORES A LAS VARIABLES
		iae_id := unique_iae.sellervat_activity_iae_id;
		iae_name := unique_iae.description;
		iae_cnae := unique_iae.cnae;
		iae_code := unique_iae.activity_code;
	END IF;

	--RAISE NOTICE 'Este es un mensaje de tipo NOTICE: %', unique_iae.iae_name;

	--EXTRAER JSON DEL 303 DEL 4 TRIMESTRE DEL AÑO PASADO
	SELECT
		json_pdf,
		result_id
	INTO last_year_303
	FROM(
		SELECT json_pdf
		FROM documents_presentedmodel
		WHERE seller_id = sellerid
		AND model_id = 'ES-303'
		AND year = date_year - 1
		AND period_id = 'Q4'
	) AS json_pdf,
	(
		SELECT result_id
		FROM documents_presentedmodel
		WHERE seller_id = sellerid
		AND model_id = 'ES-303'
		AND year = date_year - 1
		AND period_id = 'Q4'
	) AS result_id;
	

	--EXTRAER JSON DEL 303 DEL 1 TRIMESTRE DE ESTE AÑO
	SELECT
		json_pdf
	INTO json_tri1
	FROM documents_presentedmodel
	WHERE seller_id = sellerid
	AND model_id = 'ES-303'
	AND year = date_year
	AND period_id = 'Q1';

	--EXTRAER JSON DEL 303 DEL 2 TRIMESTRE DE ESTE AÑO
	SELECT
		json_pdf
	INTO json_tri2
	FROM documents_presentedmodel
	WHERE seller_id = sellerid
	AND model_id = 'ES-303'
	AND year = date_year
	AND period_id = 'Q2';
	
	--EXTRAER JSON DEL 303 DEL 3 TRIMESTRE DE ESTE AÑO
	SELECT
		json_pdf
	INTO json_tri3
	FROM documents_presentedmodel
	WHERE seller_id = sellerid
	AND model_id = 'ES-303'
	AND year = date_year
	AND period_id = 'Q3';

	--EXTRAER JSON DEL 303 DEL 4 TRIMESTRE DE ESTE AÑO
	SELECT
		json_pdf
	INTO json_tri4
	FROM documents_presentedmodel
	WHERE seller_id = sellerid
	AND model_id = 'ES-303'
	AND year = date_year
	AND period_id = 'Q4';

	--ELECCIÓN DEL SELLER
	SELECT
		choice_seller
	INTO choiceseller
	FROM documents_presentedmodel
	WHERE seller_id = sellerid
	AND model_id = 'ES-303'
	AND year = date_year
	AND period_id = 'Q4';


	--ASIGNAR VALORES A LAS VARIABLES

	--CA001: RECUPERA Suma de la casilla 01 de todos los 303 del año
	CA001 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_1', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_1', ''), ',', '.')::NUMERIC), 0) +
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_1', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_1', ''), ',', '.')::NUMERIC), 0);
	CA001 := ROUND( CA001 , 2 );

	--CA002: RECUPERA Suma de la casilla 03 de todos los 303 del año
	-- CA002 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_3', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri2->>'campo_3', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri3->>'campo_3', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri4->>'campo_3', ''), ',', '.')::NUMERIC), 0);
	CA002 := (CA001 * 0.04);
	CA002 := ROUND( CA002 , 2 );

	--CA003: RECUPERA Suma de la casilla 04 de todos los 303 del año
	CA003 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_4', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_4', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_4', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_4', ''), ',', '.')::NUMERIC), 0);
	CA003 := ROUND( CA003 , 2 );

	--CA004: RECUPERA Suma de la casilla 06 de todos los 303 del año
	-- CA004 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_6', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri2->>'campo_6', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri3->>'campo_6', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri4->>'campo_6', ''), ',', '.')::NUMERIC), 0);
	CA004 := (CA003 * 0.1);
	CA004 := ROUND( CA004 , 2 );

	--CA005: RECUPERA Suma de la casilla 07 de todos los 303 del año
	CA005 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_7', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_7', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_7', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_7', ''), ',', '.')::NUMERIC), 0);
	CA005 := ROUND( CA005 , 2 );

	--CA006: RECUPERA Suma de la casilla 09 de todos los 303 del año
	-- CA006 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_9', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri2->>'campo_9', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri3->>'campo_9', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri4->>'campo_9', ''), ',', '.')::NUMERIC), 0);
	CA006 := (CA005 * 0.21);
	CA006 := ROUND( CA006 , 2 );

	--CA025: Todas las intracommunity expenses (restandole las intracomunnuty credit) del año que tengan la cuenta contable 600
	CA025 := inv_intra_expenses_600.sum_amount;
	CA025 := ROUND( CA025 , 2 );

	--CA026: el 21% del resultado de la casilla 25
	CA026 := (CA025 * 0.21);
	CA026 := ROUND( CA026 , 2 );

	--CA551: Todas las intracommunity expenses (restandole las intracomunnuty credit) del año que tengan la cuenta contable DIFERENTE a 600
	CA551 := inv_intra_expenses_not_600.sum_amount;
	CA551 := ROUND( CA551 , 2 );

	--CA552: el 21% del resultado de la casilla 551
	CA552 := (CA551 * 0.21);
	CA552 := ROUND( CA552 , 2 );

	--CA027: RECUPERA Suma de la casilla 12 de todos los 303 del año
	CA027 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_12', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_12', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_12', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_12', ''), ',', '.')::NUMERIC), 0);
	CA027 := ROUND( CA027 , 2 );

	--CA028: RECUPERA Suma de la casilla 13 de todos los 303 del año
	CA028 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_13', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_13', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_13', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_13', ''), ',', '.')::NUMERIC), 0);
	CA028 := ROUND( CA028 , 2 );

	--CA029: RECUPERA Suma de la casilla 25 de todos los 303 del año
	--CA029 := ((json_tri1->>'campo_25')::NUMERIC + (json_tri2->>'campo_25')::NUMERIC + (json_tri3->>'campo_25')::NUMERIC + (json_tri4->>'campo_25')::NUMERIC);
	CA029 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_14', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_14', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_14', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_14', ''), ',', '.')::NUMERIC), 0);
	CA029 := ROUND( CA029 , 2 );

	--CA030: RECUPERA Suma de la casilla 26 de todos los 303 del año
	CA030 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_15', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_15', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_15', ''), ',', '.')::NUMERIC), 0) +
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_15', ''), ',', '.')::NUMERIC), 0);
	CA030 := ROUND( CA030 , 2 );

	--CA033: Suma de las casillas 1, 3, 5, 25, 551, 27, 29
	CA033:= CA001 + CA003 + CA005 + CA025 + CA551 + CA027 + CA029;
	CA033 := ROUND( CA033 , 2 );

	--CA034: Suma de las casillas 2, 4, 6, 26, 552, 28, 30 
	CA034:= CA002 + CA004 + CA006 + CA026 + CA552 + CA028 + CA030;
	CA034 := ROUND( CA034 , 2 );

	--CA035: RECUPERA Suma de la casilla 16 de todos los 303 del año
	CA035 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_16', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_16', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_16', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_16', ''), ',', '.')::NUMERIC), 0);
	CA035 := ROUND( CA035 , 2 );

	--CA036: RECUPERA Suma de la casilla 18 de todos los 303 del año
	-- CA036 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_18', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri2->>'campo_18', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri3->>'campo_18', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri4->>'campo_18', ''), ',', '.')::NUMERIC), 0);
	CA036 := (CA035 * 0.0005);
	CA036 := ROUND( CA036 , 2 );

	--CA599: RECUPERA Suma de la casilla 19 de todos los 303 del año
	CA599 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_19', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_19', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_19', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_19', ''), ',', '.')::NUMERIC), 0);
	CA599 := ROUND( CA599 , 2 );

	--CA600: RECUPERA Suma de la casilla 21 de todos los 303 del año
	-- CA600 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_21', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri2->>'campo_21', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri3->>'campo_21', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri4->>'campo_21', ''), ',', '.')::NUMERIC), 0);
	CA600 := (CA599 *  0.014);
	CA600 := ROUND( CA600 , 2 );

	--CA601: RECUPERA Suma de la casilla 22 de todos los 303 del año
	CA601 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_22', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_22', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_22', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_22', ''), ',', '.')::NUMERIC), 0);
	CA601 := ROUND( CA601 , 2 );

	--CA602: RECUPERA Suma de la casilla 24 de todos los 303 del año
	-- CA602 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_24', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri2->>'campo_24', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri3->>'campo_24', ''), ',', '.')::NUMERIC), 0) + 
	-- 		COALESCE((REPLACE(NULLIF(json_tri4->>'campo_24', ''), ',', '.')::NUMERIC), 0);
	CA602 := (CA601 *  0.052);
	CA602 := ROUND( CA602 , 2 );

	--CA043: RECUPERA Suma de la casilla 25 de todos los 303 del año
	CA043 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_25', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_25', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_25', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_25', ''), ',', '.')::NUMERIC), 0);
	CA043 := ROUND( CA043 , 2 );

	--CA044: RECUPERA Suma de la casilla 26 de todos los 303 del año
	CA044 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_26', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_26', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_26', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_26', ''), ',', '.')::NUMERIC), 0);
	CA044 := ROUND( CA044 , 2 );

	--CA047: Suma de las casillas 34, 36, 600, 42, 44 y 46
	CA047 := CA034 + CA036 + CA600 + CA042 + CA044 + CA046;
	CA047 := ROUND( CA047 , 2 );

	--CA190: Todas las BASES IMPONIBLES de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 4%
	CA190 := inv_tax_4.sum_amount;
	CA190 := ROUND( CA190 , 2 );

	--CA191: Todas las cuotas IVA de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 4%
	CA191 := inv_tax_4.sum_vat;
	CA191 := ROUND( CA191 , 2 );

	--CA603: Todas las BASES IMPONIBLES de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 10%
	CA603 := inv_tax_10.sum_amount;
	CA603 := ROUND( CA603 , 2 );

	--CA604: Todas las cuotas IVA de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 10%
	CA604 := inv_tax_10.sum_vat;
	CA604 := ROUND( CA604 , 2 );

	--CA605: Todas las BASES IMPONIBLES de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 21% + CASILLA 27
	CA605 := inv_tax_21.sum_amount + CA027;
	CA605 := ROUND( CA605 , 2 );

	--CA606: Todas las cuotas IVA de todo el año de las LOCAL EXPENSE, TAX COUNTRY ES, VAT 21% + CASILLA 28
	--CA606 := inv_tax_21.sum_vat + CA028;
	CA606 := (CA605 *  0.21);
	CA606 := ROUND( CA606 , 2 );
	CA606_OLD := CA606;

	--CA048: Suma de las casillas 190, 603 y 605
	CA048 := CA190 + CA603 + CA605;
	CA048 := ROUND( CA048 , 2 );

	--CA049: Suma de las casillas 191, 604 y 606
	CA049 := CA191 + CA604 + CA606;
	CA049 := ROUND( CA049 , 2 );

	--CA202: Todas las BASES IMPONIBLES de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 4%
	CA202 := inv_dua_tax_4.sum_amount;
	CA202 := ROUND( CA202 , 2 );

	--CA203: Todas las cuotas IVA de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 4%
	CA203 := inv_dua_tax_4.sum_vat;
	CA203 := ROUND( CA203 , 2 );

	--CA619: Todas las BASES IMPONIBLES de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 10%
	CA619 := inv_dua_tax_10.sum_amount;
	CA619 := ROUND( CA619 , 2 );

	--CA620: Todas las cuotas IVA de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 10%
	CA620 := inv_dua_tax_10.sum_vat;
	CA620 := ROUND( CA620 , 2 );

	--CA621: Todas las BASES IMPONIBLES de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 21%
	CA621 := inv_dua_tax_21.sum_amount;
	CA621 := ROUND( CA621 , 2 );

	--CA622: Todas las cuotas IVA de todo el año de las IMPORT DUA, TAX COUNTRY ES, VAT 21%
	--CA622 := inv_dua_tax_21.sum_vat;
	CA622 := (CA621 * 0.21);
	CA622 := ROUND( CA622 , 2 );

	--CA052: Suma de las casillas 202, 619 y 621
	CA052 := CA202 + CA619 + CA621;
	CA052 := ROUND( CA052 , 2 );

	--CA053: Suma de las casillas 203, 620 y 622
	CA053 := CA203 + CA620 + CA622;
	CA053 := ROUND( CA053 , 2 );

	--CA629: Igual Casilla 25
	CA629 := CA025;
	CA629 := ROUND( CA629 , 2 );

	--CA630: Igual Casilla 26
	CA630 := CA026;
	CA630 := ROUND( CA630 , 2 );

	--CA056: Igual casilla 629
	CA056 := CA629;
	CA056 := ROUND( CA056 , 2 );

	--CA057: Igual casilla 630
	CA057 := CA630;
	CA057 := ROUND( CA057 , 2 );

	--CA637: Casilla 551
	CA637 := CA551;
	CA637 := ROUND( CA637 , 2 );

	--CA638: Casilla 552
	CA638 := CA552;
	CA638 := ROUND( CA638 , 2 );

	--CA639: RECUPERA Suma de la casilla 40 de todos los 303 del año
	CA639 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_40', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_40', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_40', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_40', ''), ',', '.')::NUMERIC), 0);
	CA639 := ROUND( CA639 , 2 );

	--CA062: RECUPERA Suma de la casilla 41 de todos los 303 del año
	CA062 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_41', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_41', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_41', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_41', ''), ',', '.')::NUMERIC), 0);
	CA062 := ROUND( CA062 , 2 );

	--CA597: Igual casilla 637
	CA597 := CA637;
	CA597 := ROUND( CA597 , 2 );

	--CA598: Igual casilla 638
	CA598 := CA638;
	CA598 := ROUND( CA598 , 2 );

	--CA064: Suma de las casillas 49, 513, 51, 521, 53, 55, 57, 59, 598, 61, 661, 62, 652, 63 y 522
	CA064 := CA049 + CA513 + CA051 + CA521 + CA053 + CA055 + CA057 + CA059 + CA598 + CA061 + CA661 + CA062 + CA652 + CA063 + CA522;
	CA064 := ROUND( CA064 , 2 );

	--CA065: Casilla 47 menos 64
	CA065 := CA047 - CA064;
	CA065 := ROUND( CA065 , 2 );

	--CA084: Suma de las casillas 65, 83 y 658
	CA084 := CA065 + CA083 + CA658;
	CA084 := ROUND( CA084 , 2 );

	--CA085: casilla 110 del trimestre 1
	CA085 := COALESCE((REPLACE(NULLIF(json_tri1->>'campo_110', ''), ',', '.')::NUMERIC), 0);	
	CA085 := ROUND(CA085, 2);


	--CA086: Suma de las casillas 84 y 659 menos la casilla 85
	CA086 := CA084 + CA659 - CA085;
	CA086 := ROUND( CA086 , 2 );
	CA086_OLD := CA086;

	--CA095: RECUPERA De todos los MOD 303 del año, la suma de todas las casillas 71 que sean positivas
	IF COALESCE((REPLACE(NULLIF(json_tri1->>'campo_71', ''), ',', '.')::NUMERIC), 0) > 0 THEN
		PREV_CA71_TRI1 := COALESCE((REPLACE(NULLIF(json_tri1->>'campo_71', ''), ',', '.')::NUMERIC), 0);
	ELSE
		PREV_CA71_TRI1 := 0;
	END IF;
	
	IF COALESCE((REPLACE(NULLIF(json_tri2->>'campo_71', ''), ',', '.')::NUMERIC), 0) > 0 THEN
		PREV_CA71_TRI2 := COALESCE((REPLACE(NULLIF(json_tri2->>'campo_71', ''), ',', '.')::NUMERIC), 0);
	ELSE
		PREV_CA71_TRI2 := 0;
	END IF;

	IF COALESCE((REPLACE(NULLIF(json_tri3->>'campo_71', ''), ',', '.')::NUMERIC), 0) > 0 THEN
		PREV_CA71_TRI3 := COALESCE((REPLACE(NULLIF(json_tri3->>'campo_71', ''), ',', '.')::NUMERIC), 0);
	ELSE
		PREV_CA71_TRI3 := 0;
	END IF;

	IF COALESCE((REPLACE(NULLIF(json_tri4->>'campo_71', ''), ',', '.')::NUMERIC), 0) > 0 THEN
		PREV_CA71_TRI4 := COALESCE((REPLACE(NULLIF(json_tri4->>'campo_71', ''), ',', '.')::NUMERIC), 0);
	ELSE
		PREV_CA71_TRI4 := 0;
	END IF;

	CA095 := PREV_CA71_TRI1 + PREV_CA71_TRI2 + PREV_CA71_TRI3 + PREV_CA71_TRI4;
	CA095 := ROUND( CA095 , 2 );

	--CA097: RECUPERA Si la casilla 71 del MOD 303 del 4T es negativa, y se ha solicitado compensación, se pone el importe aquí
	IF COALESCE((REPLACE(NULLIF(json_tri4->>'campo_71', ''), ',', '.')::NUMERIC), 0) < 0 AND choiceseller = 'compensate' THEN
		CA097 := ABS(COALESCE((REPLACE(NULLIF(json_tri4->>'campo_71', ''), ',', '.')::NUMERIC), 0));
	END IF;
	
	--CA098: RECUPERA Si la casilla 71 del MOD 303 del 4T es negativa, y se ha solicitado devolución, se pone el importe aquí
	IF COALESCE((REPLACE(NULLIF(json_tri4->>'campo_71', ''), ',', '.')::NUMERIC), 0) < 0 AND choiceseller = 'devolution' THEN
		CA098 := ABS(COALESCE((REPLACE(NULLIF(json_tri4->>'campo_71', ''), ',', '.')::NUMERIC), 0));
	END IF;

	--CA662: RECUPERA De todos los MOD 303 del año (menos del 4T), la suma de todas las casillas 71 que sean negativas
	IF  COALESCE((REPLACE(NULLIF(json_tri1->>'campo_71', ''), ',', '.')::NUMERIC), 0) < 0 THEN
		PREV_CA71_TRI1 := COALESCE((REPLACE(NULLIF(json_tri1->>'campo_71', ''), ',', '.')::NUMERIC), 0);
	ELSE
		PREV_CA71_TRI1 := 0;
	END IF;

	IF COALESCE((REPLACE(NULLIF(json_tri2->>'campo_71', ''), ',', '.')::NUMERIC), 0) < 0 THEN
		PREV_CA71_TRI2 := COALESCE((REPLACE(NULLIF(json_tri2->>'campo_71', ''), ',', '.')::NUMERIC), 0);
	ELSE
		PREV_CA71_TRI2 := 0;
	END IF;

	IF COALESCE((REPLACE(NULLIF(json_tri3->>'campo_71', ''), ',', '.')::NUMERIC), 0) < 0 THEN
		PREV_CA71_TRI3 := COALESCE((REPLACE(NULLIF(json_tri3->>'campo_71', ''), ',', '.')::NUMERIC), 0);
	ELSE
		PREV_CA71_TRI3 := 0;
	END IF;

	CA662 := COALESCE((REPLACE(NULLIF(json_tri4->>'campo_87', ''), ',', '.')::NUMERIC), 0);
	CA662 := ABS(ROUND( CA662 , 2 ));

	--CA099: Suma de las casillas 1, 3 y 5
	CA099 := CA001 + CA003 + CA005;
	CA099 := ROUND( CA099 , 2 );

	--CA103: RECUPERA De todos los 303 del año, suma de las casillas 59
	CA103 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_59', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_59', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_59', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_59', ''), ',', '.')::NUMERIC), 0);
	CA103 := ROUND( CA104 , 2 );

	--CA104: RECUPERA De todos los 303 del año, suma de las casillas 60
	CA104 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_60', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_60', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_60', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_60', ''), ',', '.')::NUMERIC), 0);
	CA104 := ROUND( CA104 , 2 );

	--CA110: RECUPERA De todos los 303 del año, suma de las casillas 120
	CA110 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_120', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_120', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_120', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_120', ''), ',', '.')::NUMERIC), 0);
	CA110 := ROUND( CA110 , 2 );

	--CA125: RECUPERA De todos los 303 del año, suma de las casillas 12
	CA125 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_12', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_12', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_12', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_12', ''), ',', '.')::NUMERIC), 0);
	CA125 := ROUND( CA125 , 2 );

	--CA126: RECUPERA De todos los 303 del año, suma de las casillas 123
	CA126 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_123', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_123', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_123', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_123', ''), ',', '.')::NUMERIC), 0);
	CA126 := ROUND( CA126 , 2 );

	--CA127: RECUPERA De todos los 303 del año, suma de las casillas 124
	CA127 :=COALESCE((REPLACE(NULLIF(json_tri1->>'campo_124', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri2->>'campo_124', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri3->>'campo_124', ''), ',', '.')::NUMERIC), 0) + 
			COALESCE((REPLACE(NULLIF(json_tri4->>'campo_124', ''), ',', '.')::NUMERIC), 0);
	CA127 := ROUND( CA127 , 2 );

	----------------------------------------------------------------------------------------------------------------

	--AJUSTES DE LA CASILLA 605 Y 606 PARA HACER COINCIDIR LOS VALORES DE 95 - 97 - 98 - 662 = 86

	--VALOR APT9 (95 - 97 - 98 - 662)
	TOTAL_AP9 := CA095 - CA097 - CA098 - CA662;
	FIRST_SUM := TOTAL_AP9;	
	LAST_SUM := TOTAL_AP9;	

	--DIFERENCIA ENTRE APT9 Y CA086 (EN ABSOLUTO)
	DIF := ABS(CA086 - TOTAL_AP9); 
	DIF_OLD := DIF;

	IF DIF != 0 THEN
		IF CA086 < TOTAL_AP9 THEN
			CA606 := (CA606 - DIF);
		ELSE 
			CA606 := (CA606 + DIF);
		END IF;
		CA605 := ROUND((CA606/0.21), 2);
		CA606 := ROUND(CA606, 2);
	END IF;

	------------------------------------------------------------------------------------------------------------------
	
	--RECALCULO DEL RESULTADO DE LA LIQUIDACIÓN TRAS LOS AJUSTES

	--CA049: Suma de las casillas 191, 604 y 606
	CA049 := CA191 + CA604 + CA606;
	CA049 := ROUND( CA049 , 2 );

	--CA048: Suma de las casillas 190, 603 y 605
	CA048 := CA190 + CA603 + CA605;
	CA048 := ROUND( CA048 , 2 );

	--CA064: Suma de las casillas 49, 513, 51, 521, 53, 55, 57, 59, 598, 61, 661, 62, 652, 63 y 522
	CA064 := CA049 + CA513 + CA051 + CA521 + CA053 + CA055 + CA057 + CA059 + CA598 + CA061 + CA661 + CA062 + CA652 + CA063 + CA522;
	CA064 := ROUND( CA064 , 2 );

	--CA065: Casilla 47 menos 64
	CA065 := CA047 - CA064;
	CA065 := ROUND( CA065 , 2 );

	--CA084: Suma de las casillas 65, 83 y 658
	CA084 := CA065 + CA083 + CA658;
	CA084 := ROUND( CA084 , 2 );

	--CA085: casilla 110 del trimestre 1
	CA085 := COALESCE((REPLACE(NULLIF(json_tri1->>'campo_110', ''), ',', '.')::NUMERIC), 0);	
	CA085 := ROUND(CA085, 2);

	--CA086: Suma de las casillas 84 y 659 menos la casilla 85
	CA086 := CA084 + CA659 - CA085;
	CA086 := ROUND( CA086 , 2 );

	--CA108: Suma de las casillas 99, 103, 104, 105, 110, 125, 126 y 127
	CA108 := CA099 + CA103 + CA104 + CA105 + CA110 + CA125 + CA126 + CA127;
	CA108 := ROUND( CA108 , 2 );
	-----------------------------------------------------------------------------------------------------------------------------------

	-- CALCULAR PORCENTAJE DE ERROR SOBRE LA CASILLA 606

	IF CA606_OLD = 0 AND CA606 = 0 THEN
		DIF := 0;
		DIF_PCT := '0%';
	ELSE 
		IF ABS(CA086_OLD) > ABS(CA086) THEN
			DIF := ABS(CA086_OLD - CA086);
			DIF_PCT :=  ROUND(((DIF/ABS(CA086_OLD)) * 100 ), 2)::VARCHAR || '%';
		ELSE
			DIF := ABS(CA086 - CA086_OLD);
			DIF_PCT :=  ROUND(((DIF/ABS(CA086)) * 100 ), 2)::VARCHAR || '%';
		END IF;
	END IF;
	CA086_CHECK := ABS(CA086);

	------------------------------------------------------------------------------------------------------------------------

	-- MAKE JSON (BY PARTS BECAUSE OF LIMIT OF 100 ARGUMENTES IN FUNCTION CALL) AND RETURN

	json_result_1 := json_build_object(
        'Shortname', short_name,
		'CA001', CA001, 'CA002', CA002, 'CA003', CA003, 'CA004', CA004, 'CA005', CA005, 'CA006', CA006,
		'CA025', CA025, 'CA026', CA026, 
		'CA551', CA551, 'CA552', CA552, 
		'CA027', CA027, 'CA028', CA028,
		'CA029', CA029, 'CA030', CA030,
		'CA033', CA033, 'CA034', CA034,
		'CA035', CA035, 'CA036', CA036, 'CA599', CA599, 'CA600', CA600, 'CA601', CA601, 'CA602', CA602,
		'CA043', CA043, 'CA044', CA044,
		'CA047', CA047,
		'CA190', CA190, 'CA191', CA191, 'CA603', CA603, 'CA604', CA604, 'CA605', CA605, 'CA606', CA606,
		'CA048', CA048, 'CA049', CA049,
		'CA639', CA639,
		'CA062', CA062
    );

	json_result_2 := json_build_object(
		'CA202', CA202, 'CA203', CA203, 'CA619', CA619, 'CA620', CA620, 'CA621', CA621, 'CA622', CA622,
		'CA052', CA052, 'CA053', CA053,
		'CA629', CA629, 'CA630', CA630,
		'CA056', CA056, 'CA057', CA057,
		'CA637', CA637, 'CA638', CA638,
		'CA597', CA597, 'CA598', CA598,
		'CA064', CA064, 'CA065', CA065,
		'CA084', CA084, 'CA085', CA085, 'CA086', CA086,
		'CA095', CA095, 'CA097', CA097, 'CA098', CA098, 'CA662', CA662,
		'CA099', CA099, 'CA103', CA103, 'CA104', CA104, 'CA110', CA110, 'CA125', CA125, 'CA126', CA126, 'CA127', CA127, 'CA108', CA108,
		'CA230', CA230,
		'CA231', CA231,
		'TOTAL_IAE', total_iae,
		'iae_id', iae_id,
		'iae_name', iae_name,
		'iae_cnae', iae_cnae,
		'iae_code', iae_code,
		'DIF', DIF,
		'DIF_PCT', DIF_PCT,
		'CA606_OLD', CA606_OLD,
		'CA086_OLD', CA086_OLD,
		'TOTAL_AP9', TOTAL_AP9,
		'ENTRANCE', ENTRANCE,
		'DIF_OLD', DIF_OLD,
		'LAST_SUM', LAST_SUM,
		'CA086_CHECK', CA086_CHECK,
		'FIRST_SUM', FIRST_SUM
	);

	RETURN json_result_1 || json_result_2 || json_iae;

END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION

--SELECT func_calc_model_es_390(37, 2023, 01, 12);