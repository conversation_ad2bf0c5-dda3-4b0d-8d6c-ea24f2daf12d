import json
from datetime import date

import deepl
from django.conf import settings
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.serializers.json import DjangoJSONEncoder
from django.db import transaction
from django.db.models import QuerySet
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import render
from django.urls import reverse
from django.views.generic import UpdateView, View
from django_datatables_view.base_datatable_view import BaseDatatableView
from phonenumber_field.phonenumber import PhoneNumber

from muaytax.app_documents.forms.M54721120 import (
    AccountingRecordForm,
    M54721120Form,
    SellerM54721120Form,
    UserM54721120Form,
)
from muaytax.app_documents.models import (
    PresentedM54721120,
    PresentedModel,
    ProcessedForm,
)
from muaytax.app_documents.models.model_5472_1120 import AccountingRecord
from muaytax.app_documents.utils.utils import get_translations
from muaytax.app_sellers.forms.seller import ProcessExcelForm
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_services.models import Service
from muaytax.dictionaries.models import Country, Currency, TypeForm, UsZipCodes
from muaytax.users.permissions import (
    IsSellerRequestM54721120Permission,
    IsSellerShortnamePermission,
)
from muaytax.utils.api_currency import CurrencyConverter


class SellerRequestM54721120View(LoginRequiredMixin,
                                 (IsSellerShortnamePermission and IsSellerRequestM54721120Permission), UpdateView):
    model = Seller
    form_class = SellerM54721120Form
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name_suffix = '_request5472_1120'
    m_form = None

    def create_automatic_records_if_needed(self, seller, m54721120_instance):
        """
        Crear registros automáticos cuando sea necesario
        """
        if not m54721120_instance:
            return

        try:
            with transaction.atomic():
                current_year = m54721120_instance.year
                instance_last_year = seller.seller_m5472_1120.filter(year=current_year - 1).first()

                # Si no debe existir registro automático, eliminar existentes
                if (not instance_last_year or
                    not instance_last_year.total_assets or
                    instance_last_year.total_assets <= 0 or
                    not instance_last_year.is_processed):

                    AccountingRecord.objects.filter(
                        seller=seller,
                        pm5472_1120=m54721120_instance,
                        is_system_generated=True,
                        system_generation_type='activos_anteriores'
                    ).delete()
                    return

                # Crear o actualizar registro automático
                usd_currency = Currency.objects.get(code='USD')
                assets_amount = instance_last_year.total_assets

                record, created = AccountingRecord.objects.get_or_create(
                    seller=seller,
                    pm5472_1120=m54721120_instance,
                    is_system_generated=True,
                    system_generation_type='activos_anteriores',
                    defaults={
                        'date': date(current_year, 1, 1),
                        'transaction_type': '5',
                        'description': f'Contribuciones por activos de años anteriores ({instance_last_year.year})',
                        'amount': assets_amount,
                        'currency': usd_currency,
                        'total_currency': assets_amount,
                    }
                )

                if not created and record.amount != assets_amount:
                    record.amount = assets_amount
                    record.total_currency = assets_amount
                    record.save()

        except Exception:
            pass

    def serialize_form_data(self, forms):
        serialized_data = {}
        for form_name, form in forms.items():
            if form and hasattr(form, 'cleaned_data') and form.cleaned_data:
                cleaned_data = {}
                for field_name, value in form.cleaned_data.items():
                    if isinstance(value, PhoneNumber):
                        value = str(value)
                    elif isinstance(value, QuerySet):
                        if all(isinstance(val, Country) for val in value):
                            value = [val.iso_code for val in value]
                    elif isinstance(value, Country):
                        value = value.iso_code
                    elif field_name == 'country_registration':
                        value = 'US'
                    cleaned_data[field_name] = value
                serialized_data[form_name] = cleaned_data
        return json.dumps(serialized_data, cls=DjangoJSONEncoder)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['m_form'] = self.m_form
        kwargs['required'] = self.is_required_fields()
        kwargs['processed_form'] = self.processed_form.json_form if self.processed_form else None
        kwargs['prev_processed_form'] = self.prev_processed_form.json_form if self.prev_processed_form else None
        return kwargs

    def get_success_url(self):
        return reverse("app_sellers:summary", args=[self.object.shortname])

    def get_object(self, **kwargs):
        obj = super().get_object()
        self.m_form = obj.seller_m5472_1120.filter(year=int(self.kwargs.get('year'))).first()
        self.instance_last_year = obj.seller_m5472_1120.filter(year=int(self.kwargs.get('year')) - 1).first()
        self.previous_pm5472 = PresentedM54721120.objects.filter( seller=obj, is_processed=True).exists()

        # Detectar si existe declaración del año anterior
        self.has_previous_year_form = PresentedM54721120.objects.filter(
            seller=obj,
            year=int(self.kwargs.get('year')) - 1
        ).exists()
        self.previous_year_instance = obj.seller_m5472_1120.filter(year=int(self.kwargs.get('year')) - 1).first()

        self.service_5472 = Service.objects.filter(
            seller=obj,
            service_name__code__in=['model_54721120', 'model_54721120_limited'],
            year=int(self.kwargs.get('year'))
        ).first()
        self.processed_form = ProcessedForm.objects.filter(seller=obj, category_form__code = 'model_US5472_1120', year=int(self.kwargs.get('year'))).first()
        self.prev_processed_form = ProcessedForm.objects.filter(seller=obj, category_form__code = 'model_US5472_1120', year=int(self.kwargs.get('year')) - 1).first()
        self.prev_pres_model = PresentedModel.objects.filter(seller=obj, model__code='US-5472', year=int(self.kwargs.get('year')) - 1).first()
        return obj

    def prepare_5472_forms(self, method=None):
        required = self.is_required_fields()

        # Verificar si existe registro automático de activos anteriores
        has_automatic_assets_record = False
        automatic_assets_amount = 0
        if self.m_form and self.m_form.pk:
            automatic_record = AccountingRecord.objects.filter(
                pm5472_1120=self.m_form,
                is_system_generated=True,
                system_generation_type='activos_anteriores'
            ).first()

            if automatic_record:
                has_automatic_assets_record = True
                automatic_assets_amount = automatic_record.amount

        kwargs = {
            "form": self.get_form(),
            'userM54721120Form': UserM54721120Form(
                method,
                instance=self.object.user,
                m_form=self.m_form,
                required=required,
                processed_form = self.processed_form.json_form if self.processed_form else None,
                prev_processed_form = self.prev_processed_form.json_form if self.prev_processed_form else None
            ),
            'm54721120Form': M54721120Form(
                method,
                instance=self.m_form,
                instance_last_year=self.instance_last_year,
                required=required,
                seller=self.object,
                prev_model=self.prev_pres_model,
                has_previous_year_form=self.has_previous_year_form,
                has_automatic_assets_record=has_automatic_assets_record,
                automatic_assets_amount=automatic_assets_amount
            ),
            # Pasar contexto sobre registro automático
            'has_automatic_assets_record': has_automatic_assets_record,
            'automatic_assets_amount': automatic_assets_amount
        }

        return kwargs

    def is_required_fields(self):
        # si se dio click en el boton de procesar o cuando entra por get
        return 'proccess-submit' in self.request.POST or self.request.method == 'GET'

    def get(self, request, *args, **kwargs):
        """
        Sobrescribir get() para asegurar que existe el formulario
        """
        # Llamar al get() del padre para obtener el objeto y configurar las variables
        self.object = self.get_object()

        # ASEGURAR QUE EXISTE EL FORMULARIO
        if not self.m_form:
            # Crear el formulario si no existe
            self.m_form = PresentedM54721120.objects.create(
                seller=self.object,
                year=int(self.kwargs.get('year'))
            )

        # Continuar con el flujo normal
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        save_edit = self.request.session.get('save_edit', False)
        submit5472 = self.request.session.get('submit5472', False)

        kwargs['save_edit'] = save_edit
        kwargs['submit5472'] = submit5472

        if save_edit:
            del self.request.session['save_edit']
        if submit5472:
            del self.request.session['submit5472']

        # La lógica de creación automática ya se ejecutó en get(), solo preparar formularios
        kwargs = self.prepare_5472_forms() | kwargs  # uno los dictionaries
        kwargs['is_processed'] = self.m_form.is_processed if self.m_form else False
        kwargs['limit_records'] = bool(self.service_5472.service_name.code == 'model_54721120_limited') if self.service_5472 else False
        kwargs['pm5472_1120_instance'] = self.m_form.pk if self.m_form is not None else None
        kwargs['accountingRecordForm1'] = AccountingRecordForm(required=self.is_required_fields(), report_type=1)
        kwargs['accountingRecordForm2'] = AccountingRecordForm(required=self.is_required_fields(), report_type=2, limit_records=kwargs['limit_records'])
        kwargs['model5472PresentationYear'] = self.kwargs.get('year')
        kwargs['previous_pm5472'] = self.previous_pm5472
        kwargs['is_debug'] = settings.DEBUG  # Añadir variable DEBUG para el template

        # Pasar información sobre año anterior al contexto
        kwargs['has_previous_year_form'] = self.has_previous_year_form
        kwargs['previous_year_instance'] = self.previous_year_instance

        kwargs['excel_form'] = ProcessExcelForm()

        # Ejecutar creación de registros automáticos al cargar la vista
        if self.m_form and self.m_form.pk:
            self.create_automatic_records_if_needed(self.object, self.m_form)

        # Añadir al contexto si existe un registro automático de activos
        automatic_record_final = None
        if self.m_form and self.m_form.pk:
            automatic_record_final = AccountingRecord.objects.filter(
                pm5472_1120=self.m_form,
                is_system_generated=True,
                system_generation_type='activos_anteriores'
            ).first()

        kwargs['has_automatic_assets_record'] = automatic_record_final is not None
        kwargs['automatic_assets_amount'] = automatic_record_final.amount if automatic_record_final else 0

        # Pasar información de activos del año anterior al contexto
        kwargs['previous_year_assets_amount'] = 0
        if self.instance_last_year and self.instance_last_year.total_assets:
            kwargs['previous_year_assets_amount'] = self.instance_last_year.total_assets

        return super().get_context_data(**kwargs)



    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        kwargs = self.prepare_5472_forms(request.POST)

        # Filtrar solo los formularios para validación
        forms_to_validate = {k: v for k, v in kwargs.items() if hasattr(v, 'is_valid')}

        if all(form.is_valid() for form in forms_to_validate.values()):
            return self.form_valid_edit(**kwargs)
        else:
            print("Algun formulario no es valido")

            # Iterar sobre los formularios y mostrar los errores
            for form_name, form in forms_to_validate.items():
                if not form.is_valid():
                    print(f"Formulario '{form_name}' no es válido:")
                    for field, errors in form.errors.items():
                        print(f"  Campo '{field}': {', '.join(errors)}")

            # Prepare los formularios con los errores
            return self.render_to_response(self.get_context_data(**kwargs))

    def form_valid_edit(self, **kwargs):
        # busca modelos procesados para este seller
        previous_pm5472_1120 = PresentedM54721120.objects.filter(seller=self.object, is_processed=True).first()

        seller = kwargs["form"].save(commit=False)  # save seller form
        seller.country_registration = Country.objects.get(iso_code="US")
        seller.save()
        kwargs["userM54721120Form"].save()  # save user form

        m54721120Form = kwargs["m54721120Form"].save(commit=False)  # save m54721120 form
        m54721120Form.seller = seller
        m54721120Form.year = int(self.kwargs.get('year'))

        # Aplicar automatización del campo is_first_year
        if self.has_previous_year_form:
            # Si existe formulario del año anterior, forzar a False
            m54721120Form.is_first_year = False
        elif previous_pm5472_1120:
            # Lógica existente: si el modelo anterior fue procesado, entonces no es el primer año
            m54721120Form.is_first_year = False

        m54721120Form.is_processed = self.is_required_fields()
        m54721120Form.save()
        kwargs["m54721120Form"].save_m2m()

        accounting_r = None

        #######  Guardar datos serializados JSON del formulario #######
        forms_data = {
            "sellerForm": kwargs["form"],
            "userM54721120Form": kwargs["userM54721120Form"],
            "m54721120Form": kwargs["m54721120Form"],
        }

        serialized_data = json.loads(self.serialize_form_data(forms_data))

        # si ya existe un formulario procesado, se actualiza el JSON
        if self.processed_form:
            existing_json = json.loads(self.processed_form.json_form)
            existing_json.update(serialized_data)

        self.processed_form = ProcessedForm.objects.update_or_create(
            id=self.processed_form.id if self.processed_form else None,
            defaults={
                'seller':seller,
                'json_form': json.dumps(existing_json) if self.processed_form else json.dumps(serialized_data, ensure_ascii=False),
                'category_form': TypeForm.objects.filter(code="model_US5472_1120").first(),
                'year' : self.kwargs.get('year'),
                'is_form_processed': False if 'save_edit' in self.request.POST else True
            }
        )
        ################################################################


        if 'record_id' in self.request.POST:
            record_ids = self.request.POST.getlist('record_id')
            accounting_r = AccountingRecord.objects.filter(id__in=record_ids).order_by('date')
            # accounting_r.update(pm5472_1120=m54721120Form)
            print("Empezando a convertir monedas...")

            taxDict = {}
            converter = CurrencyConverter()

            for record in accounting_r:
                if record.total_currency is None or record.total_currency == 0:

                    if record.amount == 0:
                        record.total_currency = 0
                    else:
                        key = f"{record.date}-{record.currency.code}"
                        if key in taxDict:
                            exchange_rate = taxDict[key]
                        else:
                            # Obtener solo la tasa de cambio
                            exchange_rate = converter.convertCurrencyToDollar(
                                record.currency.code,
                                1,  # Solo necesitamos la tasa de cambio, no el monto
                                record.date
                            )
                            taxDict[key] = exchange_rate

                        # Aplicar la tasa de cambio al monto
                        record.total_currency = float(record.amount) * exchange_rate

                    record.save()
            print(f"Monedas convertidas")

        if 'save_edit' in self.request.POST:
            self.request.session['save_edit'] = True
            return HttpResponseRedirect(reverse("app_documents:request5472_1120", args=[seller.shortname, self.kwargs.get('year')]))

        m54721120Form.translations = get_translations(m54721120Form)
        m54721120Form.save()

        self.request.session['submit5472'] = True
        return HttpResponseRedirect(reverse("app_documents:request5472_1120", args=[seller.shortname, self.kwargs.get('year')]))

    def translate_form(self, *elements):
        result = {}
        seller_form, m54721120_form, accounting_r = elements
        translator = deepl.Translator(settings.DEEPL_API_KEY)

        try:
            translated_fields = translator.translate_text([
                seller_form.cleaned_data['seller_address_state'],
                seller_form.instance.country_registration.name,
                m54721120_form.cleaned_data['member_address_state'],
                m54721120_form.cleaned_data['member_address_country'].name,
                m54721120_form.instance.member_country.name,
                m54721120_form.instance.tax_residence_country.name,
                m54721120_form.instance.activity_country.name,
                m54721120_form.instance.desc_main_activity,
                *[elem.description for elem in accounting_r],
                *[elem.get_transaction_type_display() for elem in accounting_r],
                *[elem.name for elem in m54721120_form.instance.main_activity_countries.all()]
            ], target_lang="EN-US")
        except deepl.DeepLException as e:
            return None

        result_keys = [
            'seller_address_state', 'country_registration', 'member_address_state', 'member_address_country',
            'member_country', 'tax_residence_country', 'activity_country', 'desc_main_activity'
        ]
        result.update(dict(zip(result_keys, [text.text for text in translated_fields[:8]])))

        result['accounting_records'] = [
            {
                'id': elem.id,
                'desc': desc.text,
                'trans': transaction.text,
            } for elem, desc, transaction in zip(
                accounting_r,
                translated_fields[8:8 + len(accounting_r)],
                translated_fields[8 + len(accounting_r):8 + len(accounting_r) * 2]
            )
        ]
        result['main_activity_countries'] = [text.text for text in translated_fields[8 + len(accounting_r) * 2:]]

        return result

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class AddReportDataView(LoginRequiredMixin, IsSellerShortnamePermission, View):

    def post(self, request, *args, **kwargs):
        seller = Seller.objects.get(shortname=kwargs.get('shortname'))
        form = AccountingRecordForm(request.POST, required=True)

        if form.is_valid():
            instance_form5472, created = PresentedM54721120.objects.get_or_create(
                seller=seller,
                year=int(request.POST.get('year'))
            )

            report_data = form.save(commit=False)
            report_data.seller = seller
            report_data.pm5472_1120 = instance_form5472

            # Establecer valores por defecto para registros manuales (no automáticos)
            report_data.is_system_generated = False
            report_data.system_generation_type = None

            if report_data.currency.code == 'USD':
                report_data.total_currency = report_data.amount
            else:
                converter = CurrencyConverter()
                # Obtener solo la tasa de cambio
                exchange_rate = converter.convertCurrencyToDollar(
                    report_data.currency.code,
                    1,  # Solo necesitamos la tasa de cambio, no el monto
                    report_data.date
                )
                # Aplicar la tasa de cambio al monto
                report_data.total_currency = float(report_data.amount) * exchange_rate

            report_data.save()


            return JsonResponse({'pm5472_1120_instance': instance_form5472.pk}, status=200)
        else:
            return JsonResponse(form.errors, status=400)


class DetailReportDataView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def get(self, request, shortname, report_id):
        seller = Seller.objects.get(shortname=shortname)
        report = AccountingRecord.objects.get(pk=report_id)
        context = {
            'accountRecord': report,
            "object": seller,
        }
        return render(request, 'sellers/include/m54721120/partials/accounting_data_row.html', context)


class DeleteReportDataView(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def post(self, request, *args, **kwargs):
        data = json.loads(request.body).get('data')

        # Filtrar registros del sistema
        # NOTA: Esta protección NO aplica al admin de Django
        records_to_delete = AccountingRecord.objects.filter(
            id__in=data,
            is_system_generated=False  # Solo permitir borrar registros manuales en UI normal
        )

        # Obtener IDs que se intentaron borrar pero están protegidos
        protected_ids = AccountingRecord.objects.filter(
            id__in=data,
            is_system_generated=True
        ).values_list('id', flat=True)

        if protected_ids:
            return JsonResponse({
                'error': 'No se pueden eliminar registros generados automáticamente por el sistema. Usa el Admin de Django si necesitas eliminarlos.',
                'protected_ids': list(protected_ids)
            }, status=400)

        records_to_delete.delete()
        return HttpResponse('')


class AccountingRecordListDT(LoginRequiredMixin, IsSellerShortnamePermission, BaseDatatableView):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    model = AccountingRecord
    columns =[
        'pk',  # Para el checkbox (columna 0)
        'date',  # Columna 1
        'transaction_type',  # Columna 2
        'description',  # Columna 3
        'amount',  # Columna 4
        'currency',  # Columna 5
        'actions'  # Para los botones de acción (columna 6)
    ]
    order_columns = [
        'pk',
        'date',
        'transaction_type',
        'description',
        'amount',
        'currency',
        ''  # No ordenable para acciones
    ]
    max_display_length = 9999999

    def _ensure_automatic_records(self, seller, m54721120_instance):
        """
        Asegurar que existan los registros automáticos
        """
        if not m54721120_instance:
            return

        try:
            with transaction.atomic():
                current_year = m54721120_instance.year
                instance_last_year = seller.seller_m5472_1120.filter(year=current_year - 1).first()

                # Si no debe existir registro automático, eliminar existentes
                if (not instance_last_year or
                    not instance_last_year.total_assets or
                    instance_last_year.total_assets <= 0 or
                    not instance_last_year.is_processed):

                    AccountingRecord.objects.filter(
                        seller=seller,
                        pm5472_1120=m54721120_instance,
                        is_system_generated=True,
                        system_generation_type='activos_anteriores'
                    ).delete()
                    return

                # Crear o actualizar registro automático
                usd_currency = Currency.objects.get(code='USD')
                assets_amount = instance_last_year.total_assets

                record, created = AccountingRecord.objects.get_or_create(
                    seller=seller,
                    pm5472_1120=m54721120_instance,
                    is_system_generated=True,
                    system_generation_type='activos_anteriores',
                    defaults={
                        'date': date(current_year, 1, 1),
                        'transaction_type': '5',
                        'description': f'Contribuciones por activos de años anteriores ({instance_last_year.year})',
                        'amount': assets_amount,
                        'currency': usd_currency,
                        'total_currency': assets_amount,
                    }
                )

                if not created and record.amount != assets_amount:
                    record.amount = assets_amount
                    record.total_currency = assets_amount
                    record.save()

        except Exception:
            pass

    def create_automatic_records_if_needed(self, seller, m54721120_instance):
        """
        Método público para crear registros automáticos cuando sea necesario
        """
        self._ensure_automatic_records(seller, m54721120_instance)

    def filter_queryset(self, qs):
        search = self.request.GET.get('search[value]', '')
        transaction_type = self.request.GET.getlist('transaction_type[]')
        m54721120_instance = self.request.GET.get('m54721120_instance', '')
        seller = Seller.objects.get(shortname=self.kwargs["shortname"])

        if m54721120_instance != 'None' and m54721120_instance is not None:
            try:
                m54721120_obj = PresentedM54721120.objects.get(pk=m54721120_instance)
            except PresentedM54721120.DoesNotExist:
                pass

            qs = seller.accounting_record_m5472_1120_seller.filter(pm5472_1120=m54721120_instance)

            if search:
                qs = qs.filter(description__icontains=search)
            if transaction_type:
                qs = qs.filter(transaction_type__in=transaction_type)
        else:
            qs = qs.none()
        return qs

    #NOTE Si queremos usar el ServerSide a true y paginación comentar este método (este get se trae todos los registros de golpe)
    def get(self, request, *args, **kwargs):
        qs = self.filter_queryset(self.get_initial_queryset())

        TRANSACTION_TYPE_CHOICES_DICT = {
            '1': "Venta de productos",
            '2': "Compra de productos",
            '3': "Venta de servicios",
            '4': "Compra de servicios",
            '5': "Contribución",
            '6': "Constitución",
            '7': "Disolución",
            '8': "Adquisición",
            '9': "Distribución",
        }

        records = list(qs.values(
            'pk', 'date', 'transaction_type', 'description',
            'amount', 'currency', 'total_currency',
            'is_system_generated', 'system_generation_type'
        ))

        # Transformar los datos al formato esperado por el frontend
        formatted_records = []
        for record in records:
            formatted_record = {
                'pk': record['pk'],  # Columna 0 (checkbox)
                'date': record['date'],  # Columna 1
                'transaction_type': TRANSACTION_TYPE_CHOICES_DICT.get(record['transaction_type'], ''),  # Columna 2
                'description': record['description'],  # Columna 3
                'amount': record['amount'],  # Columna 4
                'currency': record['currency'],  # Columna 5
                'actions': '',  # Columna 6 - Campo vacío para que DataTables renderice los botones
                'total_currency': record['total_currency'],  # Para cálculos
                'is_system_generated': record['is_system_generated'],  # Para lógica de renderizado
                'system_generation_type': record['system_generation_type']  # Para lógica de renderizado
            }
            formatted_records.append(formatted_record)

        return JsonResponse({'data': formatted_records})


class SellerRequestZipCodeUS(LoginRequiredMixin, IsSellerShortnamePermission, View):
    def post(self, request, *args, **kwargs):

        zip_code = json.loads(request.body).get('zip_code')

        us_location = UsZipCodes.objects.filter(zip_code=zip_code).first()

        if not us_location:
            return JsonResponse({'error': 'No se encontró el código postal'}, status=400)
        else:
            us_location = {
                'city': us_location.city,
                'state': us_location.state
            }
        return JsonResponse(us_location, status=200, safe=False)
