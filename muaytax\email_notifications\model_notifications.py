import urllib.parse
from django.template.loader import render_to_string
from django.core.mail import EmailMultiAlternatives

from muaytax.app_sellers.models import SellerVat
from muaytax.email_notifications.utils import get_mail_signature, get_mail_signature_muaytax
from muaytax.utils.env_resources import (
    logo_url_head_muaytax,
    logo_url_head_amzvat
)
from muaytax.dictionaries.models import GeneralSettings

def send_notification_email_model_347(seller, carts_attached, declarados):

    mail_signature = get_mail_signature(seller)
    message = render_to_string("emails/modelos/notification_mod_347.html", {
        'seller': seller,
        'mail_signature': mail_signature,
        'declarados': declarados,
        'cartas': carts_attached,
        'logo_head_muaytax': logo_url_head_muaytax(),
        'logo_head_amzvat': logo_url_head_amzvat()
    })
    subject = 'MUAYTAX - Notificación de modelo 347'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = [mail_signature['email']]
    html_content = message
    text_content = 'Notificación de modelo 347'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email_mod_347 = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_mod_347.attach_alternative(html_content, "text/html")

    if carts_attached:
        for i, cart_path in enumerate(carts_attached):
            pdf_filename = cart_path.split('/')[-1]
            with open(cart_path, 'rb') as cart_file:
                email_mod_347.attach(pdf_filename, cart_file.read(), "application/pdf")

    email_mod_347.send()

def send_notification_email_model_boir(seller):

    mail_signature = get_mail_signature_muaytax()
    message = render_to_string("emails/modelos/notification-mod-boir.html", {'seller': seller})
    subject = '¡IMPORTANTE! - Presentación del form BOIR'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = [mail_signature['email']]
    html_content = message
    text_content = '¡IMPORTANTE! - Presentación del form BOIR'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email_mod = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_mod.attach_alternative(html_content, "text/html")
    email_mod.send()

def send_notification_email_model_lipe(seller):

    mail_signature = get_mail_signature_muaytax()
    message = render_to_string("emails/modelos/notification-mod-lipe.html", {
        'seller': seller,
        'logo_head_muaytax': logo_url_head_muaytax(),
        })
    subject = '¡ÚLTIMO AVISO! - Modelo obligatorio para Italia LIPE'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = [mail_signature['email']]
    html_content = message
    text_content = '¡ÚLTIMO AVISO! - Modelo obligatorio para Italia LIPE'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email_mod = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_mod.attach_alternative(html_content, "text/html")
    email_mod.send()

def send_notification_email_model_vatannuale(seller):
    # mail_signature = get_mail_signature_muaytax()
    message = render_to_string("emails/modelos/notification-mod-vatannuale.html", {
        'seller': seller,
        'logo_head_muaytax': logo_url_head_muaytax(),
        })
    subject = '¡ÚLTIMO AVISO! - Modelo obligatorio para Italia VAT ANNUALE'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = ['<EMAIL>']
    html_content = message
    text_content = '¡ÚLTIMO AVISO! - Modelo obligatorio para Italia VAT ANNUALE'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email_mod = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_mod.attach_alternative(html_content, "text/html")
    email_mod.send()

def send_notification_email_model_303(seller):

    mail_signature = get_mail_signature(seller)
    message = render_to_string("emails/modelos/notification_mod_303.html", {
        'seller': seller, 
        'mail_signature': mail_signature,
        'logo_head_muaytax': logo_url_head_muaytax()
        })
    subject = '¡ÚLTIMO AVISO! - Modelo obligatorio para España M303'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = [mail_signature['email']]
    html_content = message
    text_content = '¡ÚLTIMO AVISO! - Modelo obligatorio para España M303'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email_mod_184 = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_mod_184.attach_alternative(html_content, "text/html")
    email_mod_184.send()

def send_notification_email_model_184(seller):

    mail_signature = get_mail_signature_muaytax()
    message = render_to_string("emails/modelos/notification_mod_184.html", {
        'seller': seller,
        'logo_head_muaytax': logo_url_head_muaytax()
        })
    subject = 'Presentación MOD 184 - Abierto periodo recopilación de información'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = [mail_signature['email']]
    html_content = message
    text_content = 'Notificación de modelo 184'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email_mod_184 = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_mod_184.attach_alternative(html_content, "text/html")
    email_mod_184.send()

def send_notification_we_are_closed(seller):

    mail_signature = get_mail_signature_muaytax()
    message = render_to_string("emails/modelos/notification_we_are_closed.html", {
        'seller': seller,
        'logo_head_muaytax': logo_url_head_muaytax()
        })
    subject = 'Aviso de vacaciones MUAYTAX - AMZVAT'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    # reply_to = [mail_signature['email']]
    reply_to = ['<EMAIL>']
    html_content = message
    text_content = 'Aviso de vacaciones MUAYTAX - AMZVAT'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email_closed = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_closed.attach_alternative(html_content, "text/html")
    email_closed.send()

def send_notification_generic(seller):

    # mail_signature = get_mail_signature_muaytax()
    subject = 'IMPORTANTE - IMPUESTOS 2T 2024 - MUAYTAX'
    from_email = '<EMAIL>'
    to_email = [seller.user.email]
    reply_to = ['<EMAIL>'] 
    # reply_to = ['<EMAIL>']
    message = render_to_string("emails/modelos/notification_generic.html", {
        'seller': seller,
        'email': reply_to[0],
        'logo_head_muaytax': logo_url_head_muaytax()
        })
    html_content = message
    text_content = 'IMPORTANTE - IMPUESTOS 2T 2024 - MUAYTAX'
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email.attach_alternative(html_content, "text/html")
    email.send()

def send_email_model_111_to_external_manager(seller, model, external_excel_path):
    print('enviando.....')

    mail_signature = get_mail_signature_muaytax()
    external_manager_email = GeneralSettings.objects.get(code='email-external-manager').value
    message = render_to_string("emails/modelos/email_to_external_manager_m111.html", {
        'seller': seller,
        'mail_signature': mail_signature,
        'logo_head_muaytax': logo_url_head_muaytax()
        })
    subject = 'Gestoría España - MUAYTAX'
    from_email = '<EMAIL>'
    to_email = [external_manager_email] # el mail será definido para todos aquellos que contratado ese servicio

    reply_to = [mail_signature['email']]
    html_content = message
    text_content = 'Gestoría España - MUAYTAX'
    bcc_email = ['<EMAIL>', '<EMAIL>']

    email_ext = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
    email_ext.attach_alternative(html_content, "text/html")

    if model.file:
        pdf_path = f'/app/muaytax/{model.get_file_url()}'
        pdf_path = urllib.parse.unquote(pdf_path)
        pdf_filename = model.file.name.split('/')[-1]
        with open(pdf_path, 'rb') as model_file:
            email_ext.attach(pdf_filename, model_file.read(), "application/pdf")

    if external_excel_path:
        excel_name = external_excel_path.split('/')[-1]
        with open(external_excel_path, 'rb') as excel_file:
            email_ext.attach(excel_name, excel_file.read(), "application/vnd.ms-excel")

    email_ext.send()

def send_email_model_presented(seller, model):
    print('send_email_model_presented')
    user = seller.user
    message = render_to_string("emails/model_presented.html", {
        'user': user,
        'model': model,
        'seller': seller,
        'logo_head_muaytax': logo_url_head_muaytax()
    })
    subject = 'MUAYTAX - Modelo presentado y cargado en la APP'
    from_email = '<EMAIL>'
    to_email = [user.email]
    reply_to = [user.email]

    html_content = message

    text_content = 'Tiene un modelo presentado y cargado en la APP'
    bcc_email = ['<EMAIL>', '<EMAIL>']

    email_discard = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)

    email_discard.attach_alternative(html_content, "text/html")

    email_discard.send()

def send_email_presentedModel303(seller, model):
    print('send_email_presentedModel303')
    user = seller.user
    vat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()

    if vat is not None:
        seller_vat = str(vat.vat_number).replace('ES', '')
    else:
        seller_vat = str(seller.nif_registration).replace('ES', '')

    message = render_to_string("emails/payment_model303.html", {
        'user': user,
        'model': model,
        'seller': seller,
        'seller_vat': seller_vat,
        'logo_head_muaytax': logo_url_head_muaytax()
    })
    subject = 'MUAYTAX - ACCIÓN REQUERIDA - Pago Modelo 303'
    from_email = '<EMAIL>'
    to_email = [user.email]
    reply_to = [user.email]

    html_content = message

    text_content = 'ACCIÓN REQUERIDA - Pago Modelo 303'
    bcc_email = ['<EMAIL>', '<EMAIL>']

    email_discard = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)

    email_discard.attach_alternative(html_content, "text/html")

    email_discard.send()