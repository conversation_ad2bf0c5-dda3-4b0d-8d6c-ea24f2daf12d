CREATE OR REPLACE FUNCTION func_entrylist_csv_json(
    seller VARCHAR,
	first_date VARCHAR, 
    last_date VARCHAR,
	search VARCHAR
)
RETURNS jsonb AS $$
DECLARE
    result_json jsonb := '[]';
BEGIN
    result_json := (
        SELECT jsonb_agg(
            jsonb_build_object(
				'Fecha asiento', to_char(e.entry_date, 'DD/MM/YYYY'),
                'Número asiento', e.entry_num,
                'Documento', e.entry_document,
                'Concepto', e.entry_concept,
                'Cuenta', COALESCE(NULLIF(e.entry_accounting_account, ''), '0')::numeric,
                'Importe debe', e.entry_debit,
                'Importe haber', e.entry_credit,
                'Descripción cuenta', e.entry_accounting_account_description,
                'ID', e.id
            )
        )
        FROM (
			SELECT DISTINCT entry.*
			FROM banks_entry entry		
			-- LEFT JOIN
			-- (
			-- 	SELECT DISTINCT
			-- 	ROW_NUMBER() OVER (ORDER BY subq.entry_document) AS num,
			-- 	entry_document
			-- 	FROM (
			-- 		SELECT DISTINCT
			-- 		ben.entry_document
			-- 		FROM public.banks_entry ben
			-- 		WHERE entry_seller_id = seller::bigint 
			-- 		AND used_in_export = false 
			-- 		AND ( 
			-- 			ben.entry_reconciliation_id IS NOT Null 
			-- 			OR ben.entry_invoice_id IS NOT Null
			-- 			OR ben.entry_accounting_account IS NOT Null
			-- 		)
			-- 		GROUP BY ben.entry_document
			-- 	) as subq	
			-- ) as q ON (q.entry_document = entry.entry_document)
			WHERE entry_seller_id = seller::bigint 
			AND used_in_export = false
			AND ( 
				(first_date = '' AND last_date = '')
				OR
				(entry.entry_date >= TO_DATE(first_date, 'YYYY-MM-DD') AND entry.entry_date < TO_DATE(last_date, 'YYYY-MM-DD')) 
			)
			AND (
				search = '' OR
				entry.entry_document ILIKE '%' || search || '%' OR
				entry.entry_concept ILIKE '%' || search || '%' OR
				entry.entry_accounting_account ILIKE '%' || search || '%' OR
				entry.entry_accounting_account_description ILIKE '%' || search || '%' OR
				entry.entry_debit::text ILIKE '%' || search || '%' OR 
				entry.entry_credit::text ILIKE '%' || search || '%'
			)
			ORDER BY entry.entry_num, entry.id
        ) AS e
    );

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- -- SELECT * FROM func_entrylist_csv_json('251')
