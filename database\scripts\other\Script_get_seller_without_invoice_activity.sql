-- sl o autonomo
-- que no tengan facturas contabilizadas en el primer trimestre 2024 ni facturas pendientes

SELECT DISTINCT sel.name, sel.shortname, usr.email
FROM sellers_seller sel
INNER JOIN users_user usr ON usr.id = sel.user_id
LEFT JOIN invoices_invoice inv ON inv.seller_id = sel.id
WHERE sel.legal_entity in ('sl', 'self-employed') AND
sel.id not in (
	SELECT DISTINCT sel_act.id
	FROM sellers_seller sel_act
	LEFT JOIN invoices_invoice inv_act ON inv_act.seller_id = sel_act.id
	WHERE sel_act.legal_entity in ('sl', 'self-employed')
	AND (
		inv_act.status_id = 'pending' OR
		inv_act.status_id = 'pending-revision' OR 
		(
			inv_act.status_id = 'revised' AND 
			inv_act.accounting_date >= '2024-01-01' AND 
			inv_act.accounting_date < '2024-04-01'
		)
	)
	GROUP BY sel_act.id
)
GROUP BY sel.id, usr.id
