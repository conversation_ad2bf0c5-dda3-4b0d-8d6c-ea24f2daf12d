from django.urls import path
from muaytax.app_representatives import views

app_name = "app_representatives"
urlpatterns = [
    path(
        "sellers/<shortname>/representatives/",
        view=views.RepresentativeListView.as_view(),
        name="representative_list",
    ),

    path(
        "sellers/<shortname>/representatives/new/",
        view=views.RepresentativeCreateView.as_view(),
        name="representative_create",
    ),

    path(
        "sellers/<shortname>/representatives/update/<pk>",
        view=views.RepresentativeUpdateView.as_view(),
        name="representative_update",
    ),

    path(
        "sellers/<shortname>/representatives/delete/<pk>",
        view=views.RepresentativeDeleteView.as_view(),
        name="representative_delete",
    ),
]