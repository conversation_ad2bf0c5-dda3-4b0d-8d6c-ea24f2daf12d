CREATE OR REPLACE FUNCTION func_invoicelist_csv_json(
	seller VARCHAR,  
	inv_status VARCHAR[], 
	tax_country VARCHAR[], 
	vat_rates FLOAT[],
	dep_country VARCHAR[], 
	first_dates DATE[], 
	last_dates DATE[],
	trans_type VARCHAR[],
	invoice_category VARCHAR,
	iae VARCHAR[],
	accounting_accounts VARCHAR[],
	origin VARCHAR[],
	reverse_charge VARCHAR[],
	eqtax_rates VARCHAR[],
	search VARCHAR
)
RETURNS jsonb AS $$
DECLARE
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
    -- Utilizamos jsonb_agg para agrupar los resultados en un solo JSON array
    result_json := (
        
		WITH date_ranges AS (
			SELECT * FROM UNNEST(
				first_dates, -- Fechas inicio
				last_dates   -- Fechas fin
			) AS t(start_date, end_date)
		)

		SELECT jsonb_agg(DISTINCT
            jsonb_build_object(
                'ID', inv.id,
                'Estado', inv.status_id,
                'Número', inv.reference
			) 
			|| 
			jsonb_build_object(

                'Cliente', (SELECT name FROM customers_customer WHERE id = inv.customer_id),
								'CIF Cliente', (SELECT nif_cif_iva FROM customers_customer WHERE id = inv.customer_id),
                'Cuenta ingresos', (SELECT description FROM dictionaries_accountsales WHERE code = inv.account_sales_id),
                'Proveedor', (SELECT name FROM providers_provider WHERE id = inv.provider_id),
								'CIF Proveedor', (SELECT nif_cif_iva FROM providers_provider WHERE id = inv.provider_id),
                'Cuenta de gastos', (SELECT description FROM dictionaries_accountexpenses WHERE code = inv.account_expenses_id),
                'Fecha Contabilización', inv.accounting_date,
                'Fecha Expedición', inv.expedition_date,
                'País IVA', (SELECT name FROM dictionaries_country WHERE iso_code = inv.tax_country_id),
                'País Salida', (SELECT iso_code FROM dictionaries_country WHERE iso_code = inv.departure_country_id),
								'País Llegada', (SELECT iso_code FROM dictionaries_country WHERE iso_code = inv.arrival_country_id),
                'Categoría', (SELECT description FROM dictionaries_invoicecategory WHERE code = inv.invoice_category_id),
                'Tipo', (SELECT description FROM dictionaries_invoicetype WHERE code = inv.invoice_type_id),
                'Tipo de Transacción', (SELECT description FROM dictionaries_transactiontype WHERE code = inv.transaction_type_id),
                'IAE', (SELECT description FROM dictionaries_economicactivity WHERE code = inv.iae_id),
				
				-- EUROS
				'IRPF(€)', 
					COALESCE((SELECT ROUND(SUM(
						CASE 
							WHEN inv.is_txt_amz = TRUE THEN (amount_euros * irpf / 100) 
							ELSE (amount_euros * irpf / 100) * quantity 
						END)::NUMERIC, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true), 0),

				'Base(€)',
					COALESCE((
						CASE WHEN inv.transaction_type_id = 'import-dua' THEN 0
						ELSE
							(SELECT ROUND(SUM(
								CASE 
									WHEN inv.is_txt_amz = True THEN amount_euros 
									ELSE amount_euros * quantity 
								END)::numeric, 2)
							FROM invoices_concept
							WHERE invoice_id = inv.id AND is_supplied IS NOT true)
						END
					), 0),

				'Re.EQ(€)',
					COALESCE((SELECT ROUND(SUM(
						CASE 
							WHEN inv.is_txt_amz = True THEN (amount_euros * eqtax / 100) 
							ELSE (amount_euros * eqtax / 100) * quantity 
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true), 0),

				'IVA(€)', 
					COALESCE((SELECT ROUND(
						SUM(
							CASE 
								WHEN inv.is_txt_amz = TRUE THEN 
									CASE 
										WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
										ELSE (amount_euros * vat / 100)
									END
								ELSE 
									CASE 
										WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros * quantity)
										ELSE (amount_euros * vat / 100) * quantity
									END
							END
						)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true), 0),

				'Total(€)',
					COALESCE((
						CASE 
							WHEN inv.transaction_type_id = 'import-dua' THEN 
								(SELECT ROUND(SUM(
									CASE 
										WHEN inv.is_txt_amz = True THEN vat_euros 
										ELSE vat_euros * quantity 
									END)::numeric, 2)
								FROM invoices_concept
								WHERE invoice_id = inv.id AND is_supplied IS NOT true)
							ELSE 
								(SELECT ROUND(SUM(
									CASE 
										WHEN inv.is_txt_amz = True THEN 
											amount_euros + (amount_euros * vat / 100) - (amount_euros * irpf / 100) + (amount_euros * eqtax / 100)
										ELSE 
											(amount_euros + (amount_euros * vat / 100) - (amount_euros * irpf / 100) + (amount_euros * eqtax / 100)) * quantity
									END
								)::numeric, 2)
								FROM invoices_concept
								WHERE invoice_id = inv.id AND is_supplied IS NOT true)
						END
					), 0),

				-- VAT BY TYPE
				'IVA 2,1%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 2.1 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 2.1 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 3%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 3 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 3 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 4%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 4 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 4 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 4,8%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 4.8 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 4.8 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 5%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 5,5%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 5.5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 5.5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 6%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 6 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 6 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 7%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 7 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 7 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 8%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 8 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 8 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 9%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 9 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 9 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 9,5%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 9.5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 9.5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 10%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 10 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 10 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 12%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 12 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 12 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 13%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 13 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 13 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 13,5%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 13.5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 13.5 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 14%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 14 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 14 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 15%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 15 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 15 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 17%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 17 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 17 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 18%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 18 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 18 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 19%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 19 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 19 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 20%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 20 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 20 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 21%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 21 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 21 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 22%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 22 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 22 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 23%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 23 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 23 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 24%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 24 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 24 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 25%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 25 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 25 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 27%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 27 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 27 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
				'IVA 28%(€)',
					(SELECT ROUND(SUM(
						CASE WHEN inv.is_txt_amz = True THEN (
							CASE WHEN ROUND(vat::numeric, 1) = 28 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros)
								ELSE (amount_euros * vat / 100)
								END 
							) ELSE 0 END) 
						ELSE (
							CASE WHEN ROUND(vat::numeric, 1) = 28 THEN (
								CASE WHEN inv.transaction_type_id = 'import-dua' THEN (vat_euros) * quantity
								ELSE (amount_euros * vat / 100) * quantity
								END 	
							) ELSE 0 END)
						END)::numeric, 2)
					FROM invoices_concept
					WHERE invoice_id = inv.id AND is_supplied IS NOT true),
                'Generada', inv.is_generated
            )
        )
        FROM (
            SELECT DISTINCT inv.*
            FROM invoices_invoice inv
            INNER JOIN sellers_seller sel ON inv.seller_id = sel.id
			LEFT JOIN invoices_concept con ON con.invoice_id = inv.id
            WHERE sel.shortname = seller
				-- AND con.is_supplied IS NOT true
				AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id is NULL)
                AND (inv_status = '{}' OR inv.status_id = ANY(inv_status) OR inv.status_id IS NULL)
				AND (iae = '{}' OR inv.iae_id IS NULL OR EXISTS (SELECT 1 FROM unnest(iae) AS iae_value WHERE inv.iae_id LIKE '%' || iae_value) )
				AND (tax_country = '{}' OR inv.tax_country_id = ANY(tax_country))

				AND (dep_country = '{}' OR inv.departure_country_id = ANY(dep_country))
				AND (origin = '{}' OR inv.is_txt_amz = ANY(SELECT val::boolean FROM unnest(origin) AS val))
				AND (reverse_charge = '{}' OR inv.is_reverse_charge = ANY(SELECT val::boolean FROM unnest(reverse_charge) AS val))
				-- AND (
				-- 	(first_date = '' AND last_date = '')
				-- 	OR 
				-- 	(inv.accounting_date >= TO_DATE(first_date, 'YYYY-MM-DD') AND inv.accounting_date < TO_DATE(last_date, 'YYYY-MM-DD'))
				-- 	OR (CASE
				-- 			WHEN inv.accounting_date IS NULL THEN
				-- 				inv.expedition_date >= TO_DATE(first_date, 'YYYY-MM-DD') AND inv.expedition_date < TO_DATE(last_date, 'YYYY-MM-DD')
				-- 			ELSE
				-- 				inv.expedition_date IS NULL
				-- 		END)
				-- 	)
				AND (
					NOT EXISTS (SELECT 1 FROM date_ranges) -- Si no hay rangos, no aplica el filtro
					OR 
					EXISTS (
						SELECT 1
						FROM date_ranges dr
						WHERE 
							(
								inv.accounting_date >= dr.start_date AND inv.accounting_date < dr.end_date
							)
							OR (
								inv.accounting_date IS NULL AND 
								inv.expedition_date >= dr.start_date AND inv.expedition_date < dr.end_date
							)
					)
				)
				AND (CASE 
						WHEN invoice_category = 'transfers' THEN
							inv.invoice_category_id IN ('sales', 'expenses')
						ELSE
							invoice_category = '' OR inv.invoice_category_id = invoice_category
					END)
				AND ( CASE 
						WHEN invoice_category = 'transfers' THEN
							(inv.transaction_type_id LIKE '%transfer%')
						ELSE
							(inv.transaction_type_id NOT LIKE '%transfer%' OR inv.transaction_type_id IS NULL)
					END)
				AND (inv.is_generated_amz IS NOT true)
				AND (trans_type = '{}' OR inv.transaction_type_id = ANY(trans_type))
				AND (
					search = '' OR 
					inv.amz_txt_eur_id IN (SELECT id FROM importers_amazontxteur WHERE file ILIKE '%' || search || '%') OR
					inv.reference ILIKE '%' || search || '%' OR 
					inv.customer_id IN (SELECT id FROM customers_customer WHERE name ILIKE '%' || search || '%') OR
					inv.provider_id IN (SELECT id FROM providers_provider WHERE name ILIKE '%' || search || '%') OR
					inv.status_id IN (SELECT code FROM dictionaries_invoicestatus WHERE description ILIKE '%' || search || '%') OR
					inv.tax_country_id IN (SELECT iso_code FROM dictionaries_country WHERE name ILIKE '%' || search || '%') OR
					inv.invoice_category_id IN (SELECT code FROM dictionaries_invoicecategory WHERE description ILIKE '%' || search || '%') OR
					inv.invoice_type_id IN (SELECT code FROM dictionaries_invoicetype WHERE description ILIKE '%' || search || '%') OR
					inv.transaction_type_id IN (SELECT code FROM dictionaries_transactiontype WHERE description ILIKE '%' || search || '%') OR
					inv.total_amount_euros::text ILIKE '%' || search || '%' OR
					inv.total_euros::text ILIKE '%' || search || '%' OR
					inv.iae_id IN (SELECT code FROM dictionaries_economicactivity WHERE description ILIKE '%' || search || '%')
					)
				AND (vat_rates = '{}' OR EXISTS (
                    SELECT 1
                    FROM invoices_concept ic
                    WHERE
						ic.invoice_id = inv.id
						AND ic.is_supplied IS NOT true
						AND ic.vat = ANY(vat_rates)
                ))
				AND (
					eqtax_rates = '{}' OR EXISTS (
						SELECT 1
						FROM invoices_concept ic
						WHERE
							ic.invoice_id = inv.id
							AND ic.is_supplied IS NOT true
							AND ic.eqtax = ANY(SELECT val::float FROM unnest(eqtax_rates) AS val)
					)
				)
				AND (
					accounting_accounts = '{}' -- Si no hay filtro, incluir todas
					OR inv.account_sales_id = ANY(accounting_accounts)
					OR inv.account_expenses_id = ANY(accounting_accounts)
				)
        	) AS inv
    	);

	result_json := (
        SELECT jsonb_agg(elem ORDER BY (elem->>'accounting_date')::date ASC)
        FROM jsonb_array_elements(result_json) AS elem
    );
	
    RETURN result_json;
END;
$$ LANGUAGE plpgsql;