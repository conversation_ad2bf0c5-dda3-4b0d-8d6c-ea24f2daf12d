-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_309' ) THEN
	  DROP FUNCTION func_calc_model_es_309(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_309(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE
    CA01 NUMERIC := 0;
    CA02 NUMERIC := 0;
    CA03 NUMERIC := 0;
    CA10 NUMERIC := 0;
    CA11 NUMERIC := 0;
    CA12 NUMERIC := 0;
    CA22 NUMERIC := 0;
    CA24 NUMERIC := 0;
    CA_SEC06_01 VARCHAR := '';
    CA_SEC06_02 VARCHAR := '';
    CA_SEC06_03 VARCHAR := '';
    CA_SEC06_04 VARCHAR := '';
    CA_SEC06_05 VARCHAR := '';
    CA_SEC06_06 VARCHAR := '';
    mercaderias_id VARCHAR := '600';
    short_name VARCHAR := '';
    inv_amount RECORD;
    inv_eqtax RECORD;
    has_reverse_charge BOOLEAN := FALSE;
BEGIN
    -- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

    -- AMOUNT
    SELECT
        ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses_amount,
        ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses_amount,
        ROUND(common_credit.common_amount::numeric, 2) AS common_credit_amount,
        ROUND(amz_credit.amz_amount::numeric, 2) AS amz_credit_amount,
        ROUND(common_reverse_charge.common_amount::numeric, 2) AS common_reverse_charge_amount,
        ROUND(amz_reverse_charge.amz_amount::numeric, 2) AS amz_reverse_charge_amount,
        ROUND((common_expenses.common_amount 
                + amz_expenses.amz_amount 
                + common_credit.common_amount 
                + amz_credit.amz_amount
                + common_reverse_charge.common_amount
                + amz_reverse_charge.amz_amount
                )::numeric, 2) AS sum_amount
    INTO inv_amount
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-expense' OR (is_reverse_charge IS true AND (is_rectifying IS NOT true OR is_rectifying IS NULL)))
		AND transaction_type_id LIKE 'intra-community-expense'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-expense' OR (is_reverse_charge IS true AND (is_rectifying IS NOT true OR is_rectifying IS NULL)))
		AND transaction_type_id LIKE 'intra-community-expense'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        --AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-credit' OR (is_reverse_charge IS true AND is_rectifying IS true ))
        AND transaction_type_id LIKE 'intra-community-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        --AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-credit' OR (is_reverse_charge IS true AND is_rectifying IS true ))
        AND transaction_type_id LIKE 'intra-community-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
		AND invoice_category_id = 'expenses'
        AND (transaction_type_id IS NULL OR (transaction_type_id NOT IN ('intra-community-credit', 'intra-community-expense')))
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND is_reverse_charge IS true
    ) AS common_reverse_charge,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
		AND invoice_category_id = 'expenses'
        AND (transaction_type_id IS NULL OR (transaction_type_id NOT IN ('intra-community-credit', 'intra-community-expense')))
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND is_reverse_charge IS true
    ) AS amz_reverse_charge;

    -- EQTAX
    SELECT
        ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses_amount,
        ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses_amount,
        ROUND(common_credit.common_amount::numeric, 2) AS common_credit_amount,
        ROUND(amz_credit.amz_amount::numeric, 2) AS amz_credit_amount,
        ROUND(common_reverse_charge.common_amount::numeric, 2) AS common_reverse_charge_amount,
        ROUND(amz_reverse_charge.amz_amount::numeric, 2) AS amz_reverse_charge_amount,
        ROUND((common_expenses.common_amount 
                + amz_expenses.amz_amount 
                + common_credit.common_amount 
                + amz_credit.amz_amount
                + common_reverse_charge.common_amount
                + amz_reverse_charge.amz_amount
                )::numeric, 2) AS sum_amount
    INTO inv_eqtax
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-expense' OR (is_reverse_charge IS true AND (is_rectifying IS NOT true OR is_rectifying IS NULL)))
        AND transaction_type_id LIKE 'intra-community-expense'
        AND account_expenses_id IN ('600', '601', '602')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-expense' OR (is_reverse_charge IS true AND (is_rectifying IS NOT true OR is_rectifying IS NULL)))
		AND transaction_type_id LIKE 'intra-community-expense' 
        AND account_expenses_id IN ('600', '601', '602')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        --AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-credit' OR (is_reverse_charge IS true AND is_rectifying IS true ))
		AND transaction_type_id LIKE 'intra-community-credit'
        AND account_expenses_id IN ('600', '601', '602')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        --AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        --AND (transaction_type_id LIKE 'intra-community-credit' OR (is_reverse_charge IS true AND is_rectifying IS true ))
		AND transaction_type_id LIKE 'intra-community-credit'
        AND account_expenses_id IN ('600', '601', '602')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
		AND invoice_category_id = 'expenses'
        AND (transaction_type_id IS NULL OR (transaction_type_id NOT IN ('intra-community-credit', 'intra-community-expense')))
        AND account_expenses_id IN ('600', '601', '602')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND is_reverse_charge IS true
    ) AS common_reverse_charge,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
		AND invoice_category_id = 'expenses'
        AND (transaction_type_id IS NULL OR (transaction_type_id NOT IN ('intra-community-credit', 'intra-community-expense')))
        AND account_expenses_id IN ('600', '601', '602')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND sva.regime_id = 'surcharge'
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND is_reverse_charge IS true
    ) AS amz_reverse_charge;
    
    -- REVERSE CHARGE
    SELECT INTO has_reverse_charge
    CASE
        WHEN EXISTS (
            SELECT 1
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
            INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND invoice_category_id = 'expenses'
            AND is_reverse_charge IS true
            AND sva.regime_id = 'surcharge'
            AND inv.iae_id = sva.sellervat_activity_iae_id
            AND con.is_supplied IS NOT true
        ) THEN TRUE
        ELSE FALSE
    END;

    -- SECCION 6: HECHO IMPONIBLE
    IF has_reverse_charge THEN
        CA_SEC06_03 := 'X';
    ELSE
        CA_SEC06_01 := 'X';
    END IF;

    -- CASILLA 01: total_amount (Base imponible)
    CA01 := inv_amount.sum_amount;
    CA01 := ROUND( CA01 , 2 );

    -- CASILLA 02: 21% (IVA)
    CA02 := 21;

    -- CASILLA 03: CA01 * 21%
    CA03 := CA01 * 0.21;
    CA03 := ROUND( CA03 , 2 );

    -- CASILLA 10 total_amount (Base imponible) EQTAX
    CA10 := inv_eqtax.sum_amount;
    CA10 := ROUND( CA10 , 2 );

    -- CASILLA 11: 5,2% EQTAX
    CA11 := 5.2;

    -- CASILLA 12: CA10 * 5,2% 
    CA12 := CA10 * 0.052;
    CA12 := ROUND( CA12 , 2 );

    -- CASILLA 22: CA03 + CA12
    CA22 := CA03 + CA12;
    CA22 := ROUND( CA22 , 2 );

    -- CASILLA 24: CA22
    CA24 := CA22;
    CA24 := ROUND( CA24 , 2 );
	
	-- ROUND
	--CA01 := ROUND( CA01 , 2 );
    --CA03 := ROUND( CA03 , 2 );
	--CA10 := ROUND( CA10 , 2 );
    --CA12 := ROUND( CA12 , 2 );
    --CA22 := ROUND( CA22 , 2 );
    --CA24 := ROUND( CA24 , 2 );

    -- RETURN JSON
	RETURN json_build_object(
        'Shortname', short_name, 'CA01', CA01, 'CA02', CA02, 'CA03', CA03, 'CA10', CA10, 'CA11', CA11, 'CA12', CA12, 'CA22', CA22, 'CA24', CA24,
        'CA_SEC06_01', CA_SEC06_01, 'CA_SEC06_02', CA_SEC06_02, 'CA_SEC06_03', CA_SEC06_03, 'CA_SEC06_04', CA_SEC06_04, 'CA_SEC06_05', CA_SEC06_05, 'CA_SEC06_06', CA_SEC06_06,
        'inv_amount', inv_amount, 'inv_eqtax', inv_eqtax, 'has_reverse_charge', has_reverse_charge
    )::varchar;
END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION
-- SELECT func_calc_model_es_309(5, 2023, 07, 09);
-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_309(INTEGER,INTEGER,INTEGER,INTEGER);