# Definir eventos para el servidor Nginx
events {
    worker_connections 1024;
}

# Configuración HTTP
http {
    # Configuraciones generales
    include mime.types;
    default_type application/octet-stream;
    sendfile on;
    keepalive_timeout 36055;

    # Ajustes para manejar URLs largas
    large_client_header_buffers 4 256k;

    # Tamaño máximo de carga de archivos
    client_max_body_size 100M;

    # Configuración de los servidores virtuales
    server {
        listen 80;  # Puerto en el que escucha Nginx

        location /static/ {
            alias /app/staticfiles/;  # Ruta a los archivos estáticos de Django
            expires 15m; # Expira la cache después de 15 minutos
            add_header Cache-Control "public, max-age=900, must-revalidate"; # Cabeceras de cache con expiracion de 15 minutos
            add_header Pragma "public";  # Mejora compatibilidad con navegadores antiguos
            add_header Vary "Accept-Encoding";  # Mejora la gestión de caché cuando se usa compresión gzip
        }

        # Media files are now served through Django with authentication
        # Remove direct access to /media/ directory for security

        error_page 401 /error_401.html;
        location = /error_401.html {
                root /app/muaytax/templates/_errors_;
                internal;
        }

        error_page 403 /error_403.html;
        location = /error_403.html {
                root /app/muaytax/templates/_errors_;
                internal;
        }

        error_page 404 /error_404.html;
        location = /error_404.html {
                root /app/muaytax/templates/_errors_;
                internal;
        }

        error_page 500 /error_500.html;
        location = /error_500.html {
                root /app/muaytax/templates/_errors_;
                internal;
        }

        error_page 502 /error_502.html;
        location = /error_502.html {
                root /app/muaytax/templates/_errors_;
                internal;
        }

        error_page 504 /error_504.html;
        location = /error_504.html {
                root /app/muaytax/templates/_errors_;
                internal;
        }

        # Ruta para pasar las solicitudes dinámicas a Django
        location / {
            proxy_pass http://muaytax_dev_django:8000;  # Se pasa la solicitud a Django en el contenedor llamado "django" en el puerto 8000
            proxy_read_timeout 36055;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}