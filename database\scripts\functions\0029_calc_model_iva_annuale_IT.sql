-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_iva_annuale_it' ) THEN
	  DROP FUNCTION func_calc_model_iva_annuale_it(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;


-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_iva_annuale_it(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE
    short_name VARCHAR := '';

    VE20_1 NUMERIC := 0;
    VE20_2 NUMERIC := 0;
    VE21_1 NUMERIC := 0;
    VE22_1 NUMERIC := 0;
    VE22_2 NUMERIC := 0;
    VE21_2 NUMERIC := 0;
    VE23_1 NUMERIC := 0;
    VE23_2 NUMERIC := 0;
    VE24_1 NUMERIC := 0;
    VE24_2 NUMERIC := 0;
    VE26 NUMERIC := 0;
    VE33 NUMERIC := 0;
    VE50 NUMERIC := 0;

    VF13_1 NUMERIC := 0;
    VF13_2 NUMERIC := 0;
    VF25_1 NUMERIC := 0;
    VF25_2 NUMERIC := 0;
    VF27 NUMERIC := 0;
    VF29_3 NUMERIC := 0;
    VF71 NUMERIC := 0;

    VL1 NUMERIC := 0;
    VL2 NUMERIC := 0;
    VL3 NUMERIC := 0;
    VL4 NUMERIC := 0;
    VL3_4_operation NUMERIC := 0;
    VL8_1 NUMERIC := 0;
    VL23 NUMERIC := 0;
    VL25 NUMERIC := 0;
    VL30_1 NUMERIC := 0;
    VL30_2 NUMERIC := 0;
    VL30_3 NUMERIC := 0;
    VL32 NUMERIC := 0;
    VL32_CALC NUMERIC := 0;
    VL33 NUMERIC := 0;
    VL33_CALC NUMERIC := 0;
    VL32_33_operation NUMERIC := 0;
    VL36 numeric := 0;
    VL38 NUMERIC := 0; 
    VL39 NUMERIC := 0;

    VT1_1 NUMERIC := 0;
    VT1_2 NUMERIC := 0;
    VT1_3 NUMERIC := 0;
    VT1_4 NUMERIC := 0;
    VT2_1 NUMERIC := 0;
    VT2_2 NUMERIC := 0;
    VT11_1 NUMERIC := 0;
    VT11_2 NUMERIC := 0;

    VX1 NUMERIC := 0;
    VX2_1 NUMERIC := 0;
    VX5 NUMERIC := 0;
    total_acconto NUMERIC := 0;

    type_rep VARCHAR := '';
    VE_CHAR VARCHAR := 'X';
    VF_CHAR VARCHAR := 'X';
    VL_CHAR VARCHAR :='X';
    VT_CHAR VARCHAR := 'X';
    VX_CHAR VARCHAR := 'X';

    json_last_year JSONB;
    result_value VARCHAR;
    amount_value NUMERIC;
    lipe_1t JSONB;
    lipe_2t JSONB;
    lipe_3t JSONB;
    lipe_4t JSONB;
    acconto JSONB;

    inv_local_sale_with_vat_4 RECORD;
    inv_local_sale_with_vat_5 RECORD;
    inv_local_sale_with_vat_10 RECORD;
    inv_local_sale_with_vat_22 RECORD;
    inv_local_sale RECORD;
    inv_local_refund_with_vat_4 RECORD;
    inv_local_refund_with_vat_5 RECORD;
    inv_local_refund_with_vat_10 RECORD;
    inv_local_refund_with_vat_22 RECORD;
    inv_local_refund RECORD;
    inv_local_expense_with_vat_22 RECORD;
    inv_local_credit_with_vat_22 RECORD;

    json_result1 JSONB;
    json_result2 JSONB;

BEGIN

    -- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

    --TIPO DE REPRESENTACIÓN DEL DEL SELLER
    SELECT 
        it_representation_type
    INTO type_rep
    FROM sellers_sellervat
    WHERE seller_id = sellerid
    AND vat_country_id = 'IT'
    LIMIT 1;

    --EXTRAER JSON DEL IVA ANNUALE DEL AÑO PASADO
    SELECT
        json_pdf,
        result_id,
        amount
    INTO json_last_year, result_value, amount_value
    FROM documents_presentedmodel
	WHERE seller_id = sellerid
    AND model_id = 'IT-VATANNUALE'
	AND year = date_year - 1
	AND period_id = '0A';

    --EXTRAER JSON DEL LIPE 1T DE ESTE AÑO
    SELECT
        json_pdf
    INTO lipe_1t
    FROM documents_presentedmodel
	WHERE seller_id = sellerid
    AND model_id = 'IT-LIPE'
	AND year = date_year
	AND period_id = 'Q1';

    --EXTRAER JSON DEL LIPE 2T DE ESTE AÑO
    SELECT
        json_pdf
    INTO lipe_2t
    FROM documents_presentedmodel
	WHERE seller_id = sellerid
    AND model_id = 'IT-LIPE'
	AND year = date_year
	AND period_id = 'Q2';

    --EXTRAER JSON DEL LIPE 3T DE ESTE AÑO
    SELECT
        json_pdf
    INTO lipe_3t
    FROM documents_presentedmodel
	WHERE seller_id = sellerid
    AND model_id = 'IT-LIPE'
	AND year = date_year
	AND period_id = 'Q3';

    --EXTRAER JSON DEL LIPE 4T DE ESTE AÑO
    SELECT
        json_pdf
    INTO lipe_4t
    FROM documents_presentedmodel
	WHERE seller_id = sellerid
    AND model_id = 'IT-LIPE'
	AND year = date_year
	AND period_id = 'Q4';

    --EXTRAER EL ACCONTO DE ESTE AÑO
    SELECT
        json_pdf
    INTO acconto
    FROM documents_presentedmodel
	WHERE seller_id = sellerid
    AND model_id = 'IT-ACCONTO'
	AND year = date_year
	AND period_id = '0A';

    IF (SELECT is_paid FROM documents_presentedmodel WHERE seller_id = sellerid AND model_id = 'IT-ACCONTO' AND year = date_year AND period_id = '0A' LIMIT 1) THEN
        total_acconto := COALESCE(CAST(REPLACE(REPLACE(NULLIF(acconto->>'total', ''), '.', ''), ',', '.') AS NUMERIC), 0);
    END IF;


    --LOCAL SALE AMOUNT/VAT CON IVA al 4%
    SELECT
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_sale_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_sale_vat
    INTO inv_local_sale_with_vat_4
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 4
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 4
        AND con.is_supplied IS NOT true
    ) AS amz_sales;


    --LOCAL SALE AMOUNT/VAT CON IVA al 5%
    SELECT
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_sale_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_sale_vat
    INTO inv_local_sale_with_vat_5
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 5
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 5
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    --LOCAL SALE AMOUNT/VAT CON IVA al 10%
    SELECT
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_sale_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_sale_vat
    INTO inv_local_sale_with_vat_10
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 10
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 10
        AND con.is_supplied IS NOT true
    ) AS amz_sales;



    --LOCAL SALE AMOUNT/VAT CON IVA al 22%
    SELECT
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_sale_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_sale_vat
    INTO inv_local_sale_with_vat_22
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS amz_sales;


    --LOCAL SALE AMOUNT SIN IVA
    SELECT
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_sale_amount
    INTO inv_local_sale
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 0
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-sale'
        AND con.vat = 0
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    --LOCAL REFUND AMOUNT/VAT CON IVA AL 4%
    SELECT 
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_refund_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_refund_vat
    INTO  inv_local_refund_with_vat_4
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 4
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 4
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    --LOCAL REFUND AMOUNT/VAT CON IVA AL 5%
    SELECT 
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_refund_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_refund_vat
    INTO  inv_local_refund_with_vat_5
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 5
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 5
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    --LOCAL REFUND AMOUNT/VAT CON IVA AL 10%
    SELECT 
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_refund_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_refund_vat
    INTO  inv_local_refund_with_vat_10
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 10
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 10
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    --LOCAL REFUND AMOUNT/VAT CON IVA AL 22%
    SELECT 
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_refund_amount,
        ROUND((common_sales.commom_sale_vat + amz_sales.amz_sale_vat)::NUMERIC, 2) AS local_refund_vat
    INTO  inv_local_refund_with_vat_22
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS commom_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_sale_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    --LOCAL REFUND AMOUNT SIN IVA
    SELECT
        ROUND((common_sales.common_sale_amount + amz_sales.amz_sale_amount)::NUMERIC, 2) AS local_refund_amount
    INTO inv_local_refund
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_sale_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 0
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_sale_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-refund'
        AND con.vat = 0
        AND con.is_supplied IS NOT true
    ) AS amz_sales;


    --LOCAL EXPENSE AMOUNT/VAT CON IVA
    SELECT
        ROUND((common_expenses.common_expense_amount + amz_expenses.amz_expense_amount)::NUMERIC, 2) AS local_expense_amount,
        ROUND((common_expenses.common_expense_vat * amz_expenses.amz_expense_vat)::NUMERIC, 2) AS local_expense_vat
    INTO inv_local_expense_with_vat_22
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) AS common_expense_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) AS common_expense_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-expense'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) AS amz_expense_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) AS amz_expense_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-expense'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS amz_expenses;


    --LOCAL CREDIT AMOUNT/VAT CON IVA
    SELECT
        ROUND((common_expenses.common_expense_amount + amz_expenses.amz_expense_amount)::NUMERIC, 2) AS local_credit_amount,
        ROUND((common_expenses.common_expense_vat + amz_expenses.amz_expense_vat)::NUMERIC, 2) AS local_credit_vat
    INTO inv_local_credit_with_vat_22
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity), 0) AS common_expense_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100), 0) AS common_expense_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND transaction_type_id = 'local-credit'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros), 0) AS amz_expense_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100), 0) AS amz_expense_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND transaction_type_id = 'local-credit'
        AND con.vat = 22
        AND con.is_supplied IS NOT true
    ) AS amz_expenses;

    --VE20_1: Suma de todas las bases imponibles de las local sale (quitandoles las local refund), tax country IT, que tengan IVA al 4%
    VE20_1 := inv_local_sale_with_vat_4.local_sale_amount + inv_local_refund_with_vat_4.local_refund_amount;
    VE20_1 := round(VE20_1);

    --VE20_2 el 4% de VE20_1
    VE20_2 := VE20_1 * 0.04;
    VE20_2 := ROUND(VE20_2);

    --VE21_1: Suma de todas las bases imponibles de las local sale (quitandoles las local refund), tax country IT, que tengan IVA al 5%
    VE21_1 := inv_local_sale_with_vat_5.local_sale_amount + inv_local_refund_with_vat_5.local_refund_amount;
    VE21_1 := ROUND(VE21_1);

    --VE21_2 el 5% de VE21_1
    VE21_2 := VE21_1 * 0.05;
    VE21_2 := ROUND(VE21_2);

    --VE22_1: Suma de todas las bases imponibles de las local sale (quitandoles las local refund), tax country IT, que tengan IVA al 10%
    VE22_1 := inv_local_sale_with_vat_10.local_sale_amount + inv_local_refund_with_vat_10.local_refund_amount;
    VE22_1 := ROUND(VE22_1);

    --VE22_2 el 10% de VE22_1
    VE22_2 := VE22_1 * 0.10;
    VE22_2 := ROUND(VE22_2);

    --VE23_1 (campo 25): Suma de todas las bases imponibles de las local sale (quitandoles las local refund), tax country IT, que tengan IVA al 22%
    VE23_1 := inv_local_sale_with_vat_22.local_sale_amount + inv_local_refund_with_vat_22.local_refund_amount;
    VE23_1 := ROUND(VE23_1);

    --VE23_2 (campo 26): Suma de todos los importes IVA de las local sale (quitandoles las local refund), tax country IT, que tengan IVA al 22%
    --VE23_2 := inv_local_sale_with_vat_22.local_sale_vat + inv_local_refund_with_vat_22.local_refund_vat;
    VE23_2 := VE23_1 * 0.22;
    VE23_2 := ROUND(VE23_2);

    --VE24_1 (campo 27): Lo mismo que la casilla VE23_1 (campo 25)
    VE24_1 := VE20_1 + VE21_1 + VE22_1 + VE23_1;
    VE24_1 := ROUND(VE24_1);

    --VE24_2 (campo 28): Lo mismo que la casilla VE23_2 (campo 26)
    VE24_2 := VE20_2 + VE21_2 + VE22_2 + VE23_2;
    VE24_2 := ROUND(VE24_2);

    -- VE26 (campo 29): Lo mismo que la casilla VE23_2 (campo 26)
    VE26 := VE24_2;
    VE26 := ROUND(VE26);

    --VE33 (campo 29b): Suma de todas las bases imponibles de las local sale (quitandoles las local refund), tax country IT, que NO tengan IVA
    VE33 := inv_local_sale.local_sale_amount + inv_local_refund.local_refund_amount;
    VE33 := ROUND(VE33);

    --VE50 (campo 30): Suma de la casilla VE23_1 (campo 25) más el importe de la casilla VE33 (campo 29b)
    VE50 := VE24_1 + VE33;
    VE50 := ROUND(VE50);

    --VF13_1 (campo D2): Suma de todas las bases imponibles de las local expense (quitandoles las local credit), tax country IT, que tengan IVA
    VF13_1 := inv_local_expense_with_vat_22.local_expense_amount + inv_local_credit_with_vat_22.local_credit_amount;
    VF13_1 := ROUND(VF13_1);

    --VF13_2 (campo D3): Suma de todos los importes IVA de las local expense (quitandoles las local credit), tax country IT, que tengan IVA
    --VF13_2 := inv_local_expense_with_vat_22.local_expense_vat + inv_local_credit_with_vat_22.local_credit_vat;
    VF13_2 := VF13_1 * 0.22;
    VF13_2 := ROUND(VF13_2);

    --VF25_1 (campo D4): Lo mismo que la casilla VF13_1 (campo D2)
    VF25_1 := VF13_1;
    VF25_1 := ROUND(VF25_1);

    --VF25_2 (campo D5): Lo mismo que la casilla VF13_2 (campo D3)
    VF25_2 := VF13_2;
    VF25_2 := ROUND(VF25_2);

    --VF27 (campo D6): Lo mismo que la casilla VF13_2 (campo D3)
    VF27 := VF13_2;
    VF27 := ROUND(VF27);

    --VF29_3 (campo D7): Lo mismo que la casilla VF25_1 (campo D4)
    VF29_3 := VF25_1;
    VF29_3 := ROUND(VF29_3);

    --VF71 (campo R2): Igual que la casilla VF27 (campo D6) 
    VF71 := VF27;
    VF71 := ROUND(VF71);

    --VL1 (campo L2): Lo mismo que en el campo VE26 (campo 29)
    VL1 := VE26;
    VL1 := ROUND(VL1);

    --VL2 (campo L3): Lo mismo que en el VF71 (campo R2)
    VL2 := VF71;
    VL2 := ROUND(VL2);

    --VL3_4_operation: resta entre VL1 (campo L2) menos VL2 (campo L3)
    VL3_4_operation := VL1 - VL2;
    VL3_4_operation := ROUND(VL3_4_operation);

    --VL3 (campo L4A): Si el Resultado de la resta entre VL1 (campo L2) menos VL2 (campo L3) es positivo se pone aquí 
    IF VL3_4_operation > 0 THEN
        VL3 := VL3_4_operation;
        VL3 := ROUND(VL3);
    END IF;

    --VL4 (campo L4): Si el Resultado de la resta entre VL1 (campo L2) menos VL2 (campo L3) es negativo se pone aquí con valor absoluto
    IF VL3_4_operation < 0 THEN
        VL4 := ABS(VL3_4_operation);
        VL4 := ROUND(VL4);
    END IF;

    --VL8_1 (campo L5): Importe de la casilla VL38 (campo L8A) del año pasado.
    VL8_1 := COALESCE(
        CAST(
            REPLACE(
                REPLACE(
                    NULLIF(json_last_year->>'page9_VL39', ''),
                    '.', ''
                ),
                ',', '.'
            ) AS NUMERIC
        ),
        CASE
            WHEN result_value = 'compensate' THEN amount_value
            ELSE 0
        END
    );
    VL8_1 := ROUND(VL8_1);

    --VL23 Suma de las casillas VP12 de las declaraciones LIPE de los trimestres 1, 2 y 3.
    VL23 := COALESCE(CAST(REPLACE(REPLACE(NULLIF(lipe_1t->>'VP12', ''), '.', ''), ',', '.') AS NUMERIC), 0) + 
			COALESCE(CAST(REPLACE(REPLACE(NULLIF(lipe_2t->>'VP12', ''), '.', ''), ',', '.') AS NUMERIC), 0) + 
			COALESCE(CAST(REPLACE(REPLACE(NULLIF(lipe_3t->>'VP12', ''), '.', ''), ',', '.') AS NUMERIC), 0);
    VL23 := ROUND(VL23);

    --VL25 (campo L6): Lo mismo que en la VL8_1 (campo L5)
    VL25 := VL8_1;
    VL25 := ROUND(VL25);

    --VL30_1 Suma de las casillas VP14(1) de las declaraciones LIPE de los trimestres 1, 2 y 3. Hay que sumarle también la VP13(2) del 4 trimestre y la casilla VL23
    VL30_1 := COALESCE(CAST(REPLACE(REPLACE(NULLIF(lipe_1t->>'VP14-1', ''), '.', ''), ',', '.') AS NUMERIC), 0) + 
			  COALESCE(CAST(REPLACE(REPLACE(NULLIF(lipe_2t->>'VP14-1', ''), '.', ''), ',', '.') AS NUMERIC), 0) + 
			  COALESCE(CAST(REPLACE(REPLACE(NULLIF(lipe_3t->>'VP14-1', ''), '.', ''), ',', '.') AS NUMERIC), 0) +
              --COALESCE(CAST(REPLACE(REPLACE(NULLIF(lipe_4t->>'VP13-2', ''), '.', ''), ',', '.') AS NUMERIC), 0) +
              total_acconto;
    VL30_1 := ROUND(VL30_1);

    --VL30_2 Lo mismo que en la VL30_1
    VL30_2 := VL30_1;
    VL30_2 := ROUND(VL30_2);

    --VL30_3 Lo mismo que en la VL30_2
    VL30_3 := VL30_2;
    VL30_3 := ROUND(VL30_3);

    --VL32: VL3 + VL23 - VL30_3 (Sólo se calcula si hay valor en VL3)
    IF VL3 != 0 THEN
        VL32_CALC := VL3 + VL23 - VL25 - VL30_3;
        VL32_CALC := ROUND(VL32_CALC);
    END IF;

    --VL33: (VL4*(-1)) + VL23 - VL30_3 (Sólo se calcula si hay datos en VL4)
    IF VL4 != 0 THEN
        VL33_CALC := (VL4*(-1)) + VL23 - VL25 - VL30_3;
        VL33_CALC := ROUND(VL33_CALC);
    END IF;

    IF VL33_CALC > 0 THEN
        VL32 := VL33_CALC;
    ELSE
        VL33 := VL33_CALC;
    END IF;

    IF VL32_CALC < 0 THEN
        VL33 := VL32_CALC;
    ELSE
        VL32 := VL32_CALC;
    END IF;

    IF VL4 = 0 AND VL3 = 0 THEN
        VL33 := VL30_1 + VL25;
    END IF;

    --VL36 El 1% de la casilla VL32
    VL36 := VL32 * 0.01;
    VL36 := ROUND(VL36);

    --VL38 (campo L8A): Suma de las casillas VL32 más VL36
    VL38 := VL32 + VL36;
    VL38 := ROUND(VL38);

    --VL39 (campo L8): Igual que VL33
    VL39 := VL33;
    VL39 := ROUND(VL39);

    --VT1_1 (campo T2): Lo mismo que en la casilla VE24_1 (campo 27)
    VT1_1 := VE24_1;
    VT1_1 := ROUND(VT1_1);

    --VT1_2 (campo T3): Lo mismo que en la VE24_2 (campo 28)
    VT1_2 := VE24_2;
    VT1_2 := ROUND(VT1_2);

    --VT1_3 (campo T4): Lo mismo que en la casilla VT1_1 (campo T2)
    VT1_3 := VT1_1;
    VT1_3 := ROUND(VT1_3);

    --VT1_4 (campo T5): Lo mismo que en la casilla VT1_2 (campo T3)
    VT1_4 := VT1_2;
    VT1_4 := ROUND(VT1_4);

    --VT2_1 (Abruzzo (1)): Lo mismo que en la casilla VT1_1 (campo T2) si es representación indirecta
    IF type_rep = '1' THEN
        VT2_1 := VT1_1;
        VT2_1 := ROUND(VT2_1);
    END IF;

    -- VT2_2 (Abruzzo (1)): Lo mismo que en la casilla VT1_2 (campo T3) si es representación indirecta
    IF type_rep = '1' THEN
        VT2_2 := VT1_2;
        VT2_2 := ROUND(VT2_2);
    END IF;

    --VT11_1 (Lombardia (2)): Lo mismo que en la casilla VT1_1 (campo T2) si es representación directa
    IF type_rep = '2' THEN
        VT11_1 := VT1_1;
        VT11_1 := ROUND(VT11_1);
    END IF;

    --VT11_2 (Lombardia (2)): Lo mismo que en la casilla VT1_2 (campo T3) si es representación directa
    IF type_rep = '2' THEN
        VT11_2 := VT1_2;
        VT11_2 := ROUND(VT11_2);
    END IF;

    --VX1 (campo X2): Lo mismo que en la casilla VL38 (campo L8A)
    VX1 := VL38;
    VX1 := ROUND(VX1);

    --VX2_1 (campo X3): Lo mismo que en la casilla VL39 (campo L8)
    VX2_1 := VL39;
    VX2_1 := ROUND(VX2_1);

    --VX5 (campo X3(2)): Lo mismo que en la casilla VX2_1 (campo X3)
    VX5 := VX2_1;
    VX5 := ROUND(VX5);

    --VALORES 'X' QUADRI COMPILATI
    --APARTADO VE
    IF VE21_1 = 0 AND VE21_2 = 0 AND VE23_1 = 0 AND VE23_2 = 0 AND VE24_1 = 0
        AND VE24_2 = 0 AND VE26 = 0 AND VE33 = 0 AND VE50 = 0 THEN
            VE_CHAR := '';
    END IF;

    --APARTADO VF (comentado, porque se pone siempre una X en una casilla de la página)
    -- IF VF13_1 = 0 AND VF13_2 = 0 AND VF25_1 = 0 AND VF25_2 = 0 AND VF27 = 0
    --     AND VF29_3 = 0 AND VF71 = 0 THEN
    --         VF_CHAR := '';
    -- END IF;

    --APARTADO VL
    IF VL1 = 0 AND VL2 = 0 AND VL3 = 0 AND VL4 = 0 AND VL8_1 = 0 AND VL23 = 0 AND VL25 = 0
        AND VL30_1 = 0 AND VL30_2 = 0 AND VL30_3 = 0 AND VL32 = 0 AND VL33 = 0 AND VL36 = 0
        AND VL38 = 0 AND VL39 = 0 THEN
            VL_CHAR := '';
    END IF;

    --APARTADO VT
    IF VT1_1 = 0 AND VT1_2 = 0 AND VT1_3 = 0 AND VT1_4 = 0 AND VT2_1 = 0 AND 
        VT2_2 = 0 AND VT11_1 = 0 AND VT11_2 = 0 THEN 
            VT_CHAR := '';
    END IF;

    --APARTADO VX
    IF VX1 = 0 AND VX2_1 = 0 AND VX5 = 0 THEN
        VX_CHAR := '';
    END IF;


    --MAKE JSON 1
    json_result1 := json_build_object(
        'shortname', short_name,
        'VE20_1', VE20_1,
        'VE20_2', VE20_2,
        'VE21_1', VE21_1,
        'VE21_2', VE21_2,
        'VE22_1', VE22_1,
        'VE22_2', VE22_2,
        'VE23_1', VE23_1,
        'VE23_2', VE23_2,
        'VE24_1', VE24_1,
        'VE24_2', VE24_2,
        'VE26', VE26,
        'VE33', VE33,
        'VE50', VE50,
        'VF13_1', VF13_1,
        'VF13_2', VF13_2,
        'VF25_1', VF25_1,
        'VF25_2', VF25_2,
        'VF27', VF27,
        'VF29_3', VF29_3,
        'VF71', VF71,
        'VL1', VL1,
        'VL2', VL2,
        'VL3', VL3,
        'VL4', VL4,
        'VL8_1', VL8_1,
        'VL23', VL23,
        'VL25', VL25,
        'VL30_1', VL30_1,
        'VL30_2', VL30_2,
        'VL30_3', VL30_3,
        'VL32', VL32,
        'VL33', VL33,
        'VL36', VL36,
        'VL38', VL38,
        'VL39', VL39,
        'VT1_1', VT1_1,
        'VT1_2', VT1_2,
        'VT1_3', VT1_3,
        'VT1_4', VT1_4,
        'VT2_1', VT2_1,
        'VT2_2', VT2_2,
        'VT11_1', VT11_1,
        'VT11_2', VT11_2
    );

    json_result2 := json_build_object(
        'VX1', VX1,
        'VX2_1', VX2_1,
        'VX5', VX5,
        'VL32_CALC', VL32_CALC,
        'VL33_CALC', VL33_CALC,
        'VE_CHAR', VE_CHAR,
        --'VF_CHAR', VF_CHAR,
        'VL_CHAR', VL_CHAR,
        'VT_CHAR', VT_CHAR,
        'VX_CHAR', VX_CHAR
    );

    -- RETURN JSON
	RETURN json_result1 || json_result2;

END;
$$ LANGUAGE plpgsql;

--PROBAR FUNCIÓN
--SELECT func_calc_model_iva_annuale_it(38, 2023, 1, 12);
