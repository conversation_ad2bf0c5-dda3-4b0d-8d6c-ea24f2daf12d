DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_sellervat_countryDE_list_json') THEN
        DROP FUNCTION func_sellervat_countryDE_list_json(date_year INTEGER, date_period VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_sellervat_countryDE_list_json(date_year INTEGER, date_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
		first_month DATE;
		last_month DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	IF date_period = 'Q1' THEN 
					first_month := date_year || '-01-01';
					last_month := date_year || '-04-01';
	ELSIF date_period = 'Q2' THEN 
					first_month := date_year || '-04-01';
					last_month := date_year || '-07-01';
	ELSIF date_period = 'Q3' THEN 
					first_month := date_year || '-07-01';
					last_month := date_year || '-10-01';
	ELSIF date_period = 'Q4' THEN 
					first_month := date_year || '-10-01';
					last_month := (date_year + 1) || '-01-01';
				END IF;
				
		SELECT jsonb_agg(sub_data) INTO result_json
		FROM (
    --FOR inv_data IN	
			SELECT DISTINCT
					-- id
					sel.id,

					-- seller name
					sel.name AS seller_name,

					-- shortname
					sel.shortname AS shortname,

					-- email
					(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

					-- user name
					(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

					-- gestor asignado
					MAX(sv.manager_assigned_id) AS manager_assigned,

					-- last login
					(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

					-- Num Invoices
					COUNT(DISTINCT inv.id) AS num_invoices,

					-- Num Pending Invoices
					COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

					-- Percentage Pending Invoices
					ROUND(COALESCE(
						100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
						0
					), 2) AS percentage_pending_invoices,

					-- Month 1
					BOOL_OR (
						CASE WHEN
						txt.month_1 is True
						THEN true ELSE false END
					) AS month1,

					-- Month 2
					BOOL_OR (
						CASE WHEN
						txt.month_2 is True
						THEN true ELSE false END
					) AS month2,

					-- Month 3
					BOOL_OR (
						CASE WHEN
						txt.month_3 is True
						THEN true ELSE false END
					) AS month3,

					-- Month 4
					BOOL_OR (
						CASE WHEN
						txt.month_4 is True
						THEN true ELSE false END
					) AS month4,

					-- Month 5
					BOOL_OR (
						CASE WHEN
						txt.month_5 is True
						THEN true ELSE false END
					) AS month5,

					-- Month 6
					BOOL_OR (
						CASE WHEN
						txt.month_6 is True
						THEN true ELSE false END
					) AS month6,

					-- Month 7
					BOOL_OR (
						CASE WHEN
						txt.month_7 is True
						THEN true ELSE false END
					) AS month7,

					-- Month 8
					BOOL_OR (
						CASE WHEN
						txt.month_8 is True
						THEN true ELSE false END
					) AS month8,

					-- Month 9
					BOOL_OR (
						CASE WHEN
						txt.month_9 is True
						THEN true ELSE false END
					) AS month9,

					-- Month 10
					BOOL_OR (
						CASE WHEN
						txt.month_10 is True
						THEN true ELSE false END
					) AS month10,

					-- Month 11
					BOOL_OR (
						CASE WHEN
						txt.month_11 is True
						THEN true ELSE false END
					) AS month11,

					-- Month 12
					BOOL_OR (
						CASE WHEN
						txt.month_12 is True
						THEN true ELSE false END
					) AS month12
					
					FROM sellers_seller sel
					LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
					LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
					LEFT JOIN (
						SELECT
						sel.id as seller_id,
						BOOL_OR(CASE WHEN month = 1 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_1,
						BOOL_OR(CASE WHEN month = 2 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_2,
						BOOL_OR(CASE WHEN month = 3 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_3,
						BOOL_OR(CASE WHEN month = 4 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_4,
						BOOL_OR(CASE WHEN month = 5 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_5,
						BOOL_OR(CASE WHEN month = 6 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_6,
						BOOL_OR(CASE WHEN month = 7 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_7,
						BOOL_OR(CASE WHEN month = 8 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_8,
						BOOL_OR(CASE WHEN month = 9 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_9,
						BOOL_OR(CASE WHEN month = 10 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_10,
						BOOL_OR(CASE WHEN month = 11 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_11,
						BOOL_OR(CASE WHEN month = 12 AND status_id = 'processed' THEN true ELSE false END) 							
						AS month_12

						FROM sellers_seller sel
						LEFT JOIN importers_amazontxteur txt ON sel.id = txt.seller_id
						WHERE year = date_year
						GROUP BY sel.id
					) AS txt ON sel.id = txt.seller_id
					WHERE sv.is_contracted = True AND sv.vat_country_id = 'DE'
					GROUP BY sel.id
					ORDER BY sel.id
		) AS sub_data;
			
		-- 	LOOP
		-- 	result_json := result_json || jsonb_build_object(
		-- 		'seller_id', inv_data.id,
		-- 		'seller_name', inv_data.seller_name,
		-- 		'shortname', inv_data.seller_shortname,
		-- 		'email', inv_data.email,
		-- 		'user_name', inv_data.user_name,
		-- 		'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
		-- 		'num_pending_invoices', inv_data.num_pending_invoices,
		-- 		'percentage_pending_invoices', inv_data.percentage_pending_invoices,
		-- 		'month1', inv_data.month_1,
		-- 		'month2', inv_data.month_2,
		-- 		'month3', inv_data.month_3,
		-- 		'month4', inv_data.month_4,
		-- 		'month5', inv_data.month_5,
		-- 		'month6', inv_data.month_6,
		-- 		'month7', inv_data.month_7,
		-- 		'month8', inv_data.month_8,
		-- 		'month9', inv_data.month_9,
		-- 		'month10', inv_data.month_10,
		-- 		'month11', inv_data.month_11,
		-- 		'month12', inv_data.month_12		
		-- 	);
			
		-- END LOOP;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;