from django.db import models

from django.contrib.auth import get_user_model

User = get_user_model()

class DailyReports(models.Model):
    date_created = models.DateField(
        null=True,
        blank=True,
        verbose_name="<PERSON>cha de creación",
        help_text="Fecha a la que corresponde el reporte"
        
    )
    department = models.ForeignKey(
        "dictionaries.MuaytaxDepartment",
        null=True,
        blank=True,
        related_name="daily_report_department",
        on_delete=models.SET_NULL,
        verbose_name="Departamento"
    )
    manager = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="daily_report_manager",
        verbose_name="Gestor",
        limit_choices_to={'role': 'manager'}
    )
    is_sent = models.BooleanField(
        default=False,
        verbose_name="¿Reporte enviado?"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    # @admin.display(description="Reporte")
    def get_title(self):
        formatted_date = self.date_created.strftime("%d-%m-%Y")
        department_name = f"departamento de {self.department}" if self.department else ""
        manager_name = f"gestor: {self.manager}" if self.manager else ""
        return f"Reporte día {formatted_date} - {department_name} - {manager_name}"

    class Meta:
        verbose_name = "Reporte diario de citas telefónicas"
        verbose_name_plural = "Reporte diario de citas telefónicas"

    def __str__(self):
        return self.get_title()


# class BookingsInline(admin.TabularInline):
#     model = Bookings
#     extra = 0
#     fields = ["id", "seller", "manager", "subject", "get_time"]
#     readonly_fields = ["seller", "manager", "subject", "get_time"]
#     can_delete = False
#     show_change_link = True
#     verbose_name = "Cita"
#     verbose_name_plural = "Citas"
#     ordering = ["date"]

#     def has_add_permission(self, request, obj=None):
#         return False
    



# class DailyReportsAdmin(admin.ModelAdmin):
#     inlines = [BookingsInline]
#     list_display = ["id", "get_title", "department", "manager", "is_sent", "date_created"]
#     search_fields = ["id","get_title", "department", "manager", "date_created", ]
# admin.site.register(DailyReports, DailyReportsAdmin)