-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_349' ) THEN
	  DROP FUNCTION func_calc_model_es_349(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_349(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE
    CA01 NUMERIC := 0;
    CA02 NUMERIC := 0;
	TOTAL NUMERIC :=0;
    PAG NUMERIC := 1;
    OP NUMERIC := 1;
    tmp_key VARCHAR := '';
    tmp_value VARCHAR := '';
    tmp_value2 VARCHAR := '';
	tmp_json JSONB;
    short_name VARCHAR := '';
    inv_amount_349 RECORD;
	count_operators_349 NUMERIC := 0;
    customers_349 RECORD;
    providers_349 RECORD;
    operator RECORD;
	json_result JSONB;
    json_operators JSONB;
BEGIN
    -- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

	json_operators :=  json_build_object();

    -- FOR CUSTOMERS
    FOR operator IN (
        SELECT DISTINCT
            cust.*,
            ROUND((COALESCE(common_700.amount_euros,0) + COALESCE(amz_700.amount_euros,0))::numeric ,2) AS sum_amount_700,
            ROUND((COALESCE(common_not_700.amount_euros,0) + COALESCE(amz_not_700.amount_euros,0))::numeric ,2) AS sum_amount_not_700,
            ROUND(COALESCE(transfers.amount_euros,0)::numeric ,2) AS transfers_amount
        FROM(
            SELECT DISTINCT
            inv.id AS inv_id,
            inv.customer_id
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND customer_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit', 'outgoing-transfer', 'inbound-transfer')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND con.is_supplied IS NOT true
        ) AS inv_acc
        INNER JOIN invoices_concept con ON con.invoice_id = inv_acc.inv_id
        LEFT JOIN customers_customer cust ON cust.id = inv_acc.customer_id
        LEFT JOIN dictionaries_country dc_country ON dc_country.iso_code = cust.country_id
        LEFT JOIN (
            SELECT DISTINCT cust.id, SUM(con.amount_euros * con.quantity) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN customers_customer cust ON cust.id = inv.customer_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND cust.name NOT ILIKE 'Clientes Particulares%'
            AND customer_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS NOT true
            AND con.is_supplied IS NOT true
            AND inv.account_sales_id = '700'
            GROUP BY cust.id
        ) AS common_700 ON (common_700.id = inv_acc.customer_id)
        LEFT JOIN (
            SELECT DISTINCT cust.id, SUM(con.amount_euros * con.quantity) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN customers_customer cust ON cust.id = inv.customer_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND cust.name NOT ILIKE 'Clientes Particulares%'
            AND customer_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS NOT true
            AND con.is_supplied IS NOT true
            AND (inv.account_sales_id != '700' OR inv.account_sales_id IS NULL)
            GROUP BY cust.id
        ) AS common_not_700 ON (common_not_700.id = inv_acc.customer_id)
        LEFT JOIN (
            SELECT DISTINCT cust.id, SUM(con.amount_euros) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN customers_customer cust ON cust.id = inv.customer_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND cust.name NOT ILIKE 'Clientes Particulares%'
            AND customer_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS true
            AND con.is_supplied IS NOT true
            AND inv.account_sales_id = '700'
            GROUP BY cust.id
        ) AS amz_700 ON (amz_700.id = inv_acc.customer_id)
        LEFT JOIN (
            SELECT DISTINCT cust.id, SUM(con.amount_euros) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN customers_customer cust ON cust.id = inv.customer_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND cust.name NOT ILIKE 'Clientes Particulares%'
            AND customer_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS true
            AND con.is_supplied IS NOT true
            AND (inv.account_sales_id != '700' OR inv.account_sales_id IS NULL)
            GROUP BY cust.id
        ) AS amz_not_700 ON (amz_not_700.id = inv_acc.customer_id)
        LEFT JOIN (
            SELECT DISTINCT cust.id, 
            SUM(con.amount_euros) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN customers_customer cust ON cust.id = inv.customer_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND cust.name NOT ILIKE 'Clientes Particulares%'
            AND customer_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('outgoing-transfer', 'inbound-transfer')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS true
            AND con.is_supplied IS NOT true
            GROUP BY cust.id
        ) AS transfers ON transfers.id = inv_acc.customer_id
        WHERE dc_country.is_european_union IS TRUE
        GROUP BY cust.id, amz_700.amount_euros, amz_not_700.amount_euros, common_700.amount_euros, common_not_700.amount_euros, transfers.amount_euros
    ) 
    LOOP
        IF operator.id is not null THEN

            -- COMMON CALCS ACCOUNT 700
            IF operator.sum_amount_700 > 0 OR ( operator.transfers_amount > 0 AND operator.sum_amount_700 > 0 ) THEN
                -- Country
                tmp_value := operator.country_id;
                tmp_key := 'doc_' || PAG::text || '_cod_pais_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- NIF_CIF_IVA
                tmp_value := operator.nif_cif_iva;
                tmp_key := 'doc_' || PAG::text || '_nif_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Name
                tmp_value2 := REPLACE(operator.name, '''', ' ');
                tmp_value := REPLACE(tmp_value2, '"', ' ');
                tmp_key := 'doc_' || PAG::text || '_nombre_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Clave
                tmp_value := 'E';   
                tmp_key := 'doc_' || PAG::text || '_clave_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Amount
                tmp_value := operator.sum_amount_700 + operator.transfers_amount;
                tmp_key := 'doc_' || PAG::text || '_base_imponible_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);
                
                -- TOTAL
                TOTAL := TOTAL + tmp_value::numeric;
                count_operators_349 := count_operators_349 + 1; 

                -- Update OP and PAG
                OP := OP + 1;
                IF OP >= 9 THEN
                    OP := 1;
                    PAG := PAG + 1;
                END IF;
            END IF;


            -- COMMON CALCS ACCOUNT NOT 700
            IF operator.sum_amount_not_700 > 0 THEN
                -- Country
                tmp_value := operator.country_id;
                tmp_key := 'doc_' || PAG::text || '_cod_pais_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- NIF_CIF_IVA
                tmp_value := operator.nif_cif_iva;
                tmp_key := 'doc_' || PAG::text || '_nif_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Name
                tmp_value2 := REPLACE(operator.name, '''', ' ');
                tmp_value := REPLACE(tmp_value2, '"', ' ');
                tmp_key := 'doc_' || PAG::text || '_nombre_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Clave
                tmp_value := 'S';   
                tmp_key := 'doc_' || PAG::text || '_clave_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Amount
                tmp_value := operator.sum_amount_not_700;
                tmp_key := 'doc_' || PAG::text || '_base_imponible_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);
                
                -- TOTAL
                TOTAL := TOTAL + tmp_value::numeric;
                count_operators_349 := count_operators_349 + 1; 

                -- Update OP and PAG
                OP := OP + 1;
                IF OP >= 9 THEN
                    OP := 1;
                    PAG := PAG + 1;
                END IF;
            END IF;

            -- ALTERNATIVE CALCS
            IF operator.transfers_amount > 0 AND operator.sum_amount_700 = 0 THEN
                -- Solo incluir si el operador está identificado (tiene nombre y NIF/CIF/IVA)
                IF operator.name IS NOT NULL AND operator.name != '' 
                   AND operator.nif_cif_iva IS NOT NULL AND operator.nif_cif_iva != '' THEN

                    -- Country
                    tmp_value := operator.country_id;
                    tmp_key := 'doc_' || PAG::text || '_cod_pais_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- NIF_CIF_IVA
                    tmp_value := operator.nif_cif_iva;
                    tmp_key := 'doc_' || PAG::text || '_nif_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- Name
                    tmp_value2 := REPLACE(operator.name, '''', ' ');
                    tmp_value := REPLACE(tmp_value2, '"', ' ');
                    tmp_key := 'doc_' || PAG::text || '_nombre_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- Clave
                    tmp_value := 'E';
                    tmp_key := 'doc_' || PAG::text || '_clave_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- Amount
                    tmp_value := operator.transfers_amount;
                    tmp_key := 'doc_' || PAG::text || '_base_imponible_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- TOTAL
                    TOTAL := TOTAL + tmp_value::numeric;
                    count_operators_349 := count_operators_349 + 1; 

                    -- Update OP and PAG
                    OP := OP + 1;
                    IF OP >= 9 THEN
                        OP := 1;
                        PAG := PAG + 1;
                    END IF;

                END IF; -- Fin de validación de operador identificado
            END IF;

        END IF;
    END LOOP;

    -- FOR PROVIDERS
    FOR operator IN (
        SELECT DISTINCT
            prov.*,
            ROUND((COALESCE(common_600.amount_euros,0) + COALESCE(amz_600.amount_euros,0))::numeric ,2) AS sum_amount_600,
            ROUND((COALESCE(common_not_600.amount_euros,0) + COALESCE(amz_not_600.amount_euros,0))::numeric ,2) AS sum_amount_not_600,
            ROUND(COALESCE(transfers.amount_euros,0)::numeric ,2) AS transfers_amount
        FROM 
        (
            SELECT DISTINCT
			inv.id AS inv_id,
			inv.provider_id
			FROM invoices_invoice inv
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			WHERE inv.seller_id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND provider_id IS NOT null
			AND status_id = 'revised'
			AND tax_country_id = 'ES'
			AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit', 'outgoing-transfer', 'inbound-transfer')
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
			AND is_generated_amz IS NOT true
			AND con.is_supplied IS NOT true
        ) AS inv_acc
        INNER JOIN invoices_concept con ON con.invoice_id = inv_acc.inv_id
        LEFT JOIN providers_provider prov ON prov.id = inv_acc.provider_id
        LEFT JOIN dictionaries_country dp_country ON dp_country.iso_code = prov.country_id
        LEFT JOIN (
            SELECT DISTINCT prov.id, SUM(con.amount_euros * con.quantity) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS NOT true
            AND con.is_supplied IS NOT true
            AND inv.account_expenses_id = '600'
            GROUP BY prov.id
        ) AS common_600 ON (common_600.id = inv_acc.provider_id)
        LEFT JOIN (
            SELECT DISTINCT prov.id, SUM(con.amount_euros * con.quantity) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS NOT true
            AND con.is_supplied IS NOT true
            AND (inv.account_expenses_id != '600' OR inv.account_expenses_id IS NULL)
            GROUP BY prov.id
        ) AS common_not_600 ON (common_not_600.id = inv_acc.provider_id)
        LEFT JOIN (
            SELECT DISTINCT prov.id, SUM(con.amount_euros) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS true
            AND con.is_supplied IS NOT true
            AND inv.account_expenses_id = '600'
            GROUP BY prov.id
        ) AS amz_600 ON (amz_600.id = inv_acc.provider_id)
        LEFT JOIN (
            SELECT DISTINCT prov.id, SUM(con.amount_euros) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('intra-community-expense', 'intra-community-sale', 'intra-community-refund', 'intra-community-credit')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS true
            AND con.is_supplied IS NOT true
            AND inv.account_expenses_id != '600'
            GROUP BY prov.id
        ) AS amz_not_600 ON (amz_not_600.id = inv_acc.provider_id)
        LEFT JOIN (
            SELECT DISTINCT prov.id,
			SUM(con.amount_euros) as amount_euros
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND transaction_type_id in ('outgoing-transfer', 'inbound-transfer')
            AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS true
            AND con.is_supplied IS NOT true
            GROUP BY prov.id
        ) AS transfers ON transfers.id = inv_acc.provider_id
        WHERE dp_country.is_european_union IS TRUE
        GROUP BY prov.id, amz_600.amount_euros,amz_not_600.amount_euros, common_600.amount_euros, common_not_600.amount_euros, transfers.amount_euros
    )
    LOOP
        IF operator.id is not null THEN

            -- COMMON CALCS ACCOUNT 600
            IF operator.sum_amount_600 > 0 OR (operator.transfers_amount > 0 AND operator.sum_amount_600 > 0)THEN
                -- Country
                tmp_value := operator.country_id;
                tmp_key := 'doc_' || PAG::text || '_cod_pais_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- NIF_CIF_IVA
                tmp_value := operator.nif_cif_iva;
                tmp_key := 'doc_' || PAG::text || '_nif_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Name
                tmp_value2 := REPLACE(operator.name, '''', ' '); 
                tmp_value := REPLACE(tmp_value2, '"', ' '); 
                tmp_key := 'doc_' || PAG::text || '_nombre_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Clave
                tmp_value := 'A';   
                tmp_key := 'doc_' || PAG::text || '_clave_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Amount
                tmp_value := operator.sum_amount_600 + operator.transfers_amount;
                tmp_key := 'doc_' || PAG::text || '_base_imponible_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- TOTAL
                TOTAL := TOTAL + tmp_value::numeric;
                count_operators_349 := count_operators_349 + 1; 

                -- Update OP and PAG
                OP := OP + 1;
                IF OP >= 9 THEN
                    OP := 1;
                    PAG := PAG + 1;
                END IF;
            END IF;


            -- COMMOM CALCS ACCOUNT NOT 600
            IF operator.sum_amount_not_600 > 0  THEN
                -- Country
                tmp_value := operator.country_id;
                tmp_key := 'doc_' || PAG::text || '_cod_pais_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- NIF_CIF_IVA
                tmp_value := operator.nif_cif_iva;
                tmp_key := 'doc_' || PAG::text || '_nif_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Name
                tmp_value2 := REPLACE(operator.name, '''', ' '); 
                tmp_value := REPLACE(tmp_value2, '"', ' '); 
                tmp_key := 'doc_' || PAG::text || '_nombre_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Clave
                tmp_value := 'I';   
                tmp_key := 'doc_' || PAG::text || '_clave_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- Amount
                tmp_value := operator.sum_amount_not_600;
                tmp_key := 'doc_' || PAG::text || '_base_imponible_op' || OP::text;
                tmp_json := json_build_object(tmp_key, tmp_value);
                json_operators := jsonb_concat(json_operators, tmp_json);

                -- TOTAL
                TOTAL := TOTAL + tmp_value::numeric;
                count_operators_349 := count_operators_349 + 1; 

                -- Update OP and PAG
                OP := OP + 1;
                IF OP >= 9 THEN
                    OP := 1;
                    PAG := PAG + 1;
                END IF;
            END IF;

            -- ALTERNATIVE CALCS (TRANSFERS)
            IF operator.transfers_amount > 0 AND operator.sum_amount_600 = 0 THEN
                -- Solo incluir si el operador está identificado (tiene nombre y NIF/CIF/IVA)
                IF operator.name IS NOT NULL AND operator.name != '' 
                   AND operator.nif_cif_iva IS NOT NULL AND operator.nif_cif_iva != '' THEN

                    -- Country
                    tmp_value := operator.country_id;
                    tmp_key := 'doc_' || PAG::text || '_cod_pais_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- NIF_CIF_IVA
                    tmp_value := operator.nif_cif_iva;
                    tmp_key := 'doc_' || PAG::text || '_nif_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- Name
                    tmp_value2 := REPLACE(operator.name, '''', ' ');
                    tmp_value := REPLACE(tmp_value2, '"', ' ');
                    tmp_key := 'doc_' || PAG::text || '_nombre_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- Clave
                    tmp_value := 'A';
                    tmp_key := 'doc_' || PAG::text || '_clave_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- Amount
                    tmp_value := operator.transfers_amount;
                    tmp_key := 'doc_' || PAG::text || '_base_imponible_op' || OP::text;
                    tmp_json := json_build_object(tmp_key, tmp_value);
                    json_operators := jsonb_concat(json_operators, tmp_json);

                    -- TOTAL
                    TOTAL := TOTAL + tmp_value::numeric;
                    count_operators_349 := count_operators_349 + 1; 

                    -- Update OP and PAG
                    OP := OP + 1;
                    IF OP >= 9 THEN
                        OP := 1;
                        PAG := PAG + 1;
                    END IF;

                END IF; -- Fin de validación de operador identificado
            END IF;

        END IF;
    END LOOP;

    IF OP = 1 THEN
        PAG := PAG -1;
    END IF;

    -- MAKE JSON
	json_result := json_build_object(
        'Shortname', short_name,
        'CA01', count_operators_349, 
        'CA02', ROUND( TOTAL::numeric, 2),
        'LAST_PAGE', PAG
    );
	
    json_result := jsonb_concat( json_result, json_operators );
	
	-- RETURN JSON
	RETURN json_result::varchar;
END;
$$ LANGUAGE plpgsql;


-- USAR LA FUNCION
-- SELECT func_calc_model_es_349(5, 2023, 01, 09);
-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_349(INTEGER,INTEGER,INTEGER,INTEGER);