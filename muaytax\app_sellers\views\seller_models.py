import os
import tempfile
import zipfile
import json
import requests
import time

from django.views import View
from django.shortcuts import get_object_or_404, render
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Count
from django.conf import settings
from collections import defaultdict

from muaytax.app_documents.models.presented_model_forced import PresentedModelForced
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.model import Model
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.dictionaries.models.period import Period
from datetime import datetime
# import datetime
from itertools import chain

from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_documents.models.model_5472_1120 import AccountingRecord
from muaytax.app_sellers.utils import get_model347_carts, get_providers_model347, get_customers_model347
from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission
from muaytax.email_notifications.model_notifications import send_notification_email_model_347, send_notification_email_model_boir, send_notification_email_model_184, send_notification_email_model_303, send_notification_we_are_closed, send_notification_generic, send_notification_email_model_lipe,  send_notification_email_model_vatannuale
from muaytax.email_notifications.txt_notifications import send_txt_notification_email
from muaytax.utils.api_currency import CurrencyConverter


class SellerModelCartaGen(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    template_name = 'documents/cartas/carta_model347.html'

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        year = self.kwargs["year"]

        customers_invoices_347 = get_customers_model347(seller, year)
        providers_invoices_347  = get_providers_model347(seller, year)

        # add both querysets
        declarados_invoices_347 = customers_invoices_347 + providers_invoices_347

        if not declarados_invoices_347:
            message = "Vendedor no tiene clientes/proveedores que cumplan la condición del modelo 347"
            response =  JsonResponse({"empty": True, "message": message})
            return response

        pdf_files = get_model347_carts(seller, declarados_invoices_347, year)
        if not pdf_files:
            message = "No hay cartas de empresas Españolas"
            response =  JsonResponse({"empty": True, "message": message})
            return response
        
        temp_dir = tempfile.mkdtemp()
        try:
            zip_file = os.path.join(temp_dir, "carta_model347.zip")
            with zipfile.ZipFile(zip_file, "w") as zipf:
                for pdf_path in pdf_files:
                    pdf_filename = os.path.basename(pdf_path)
                    zipf.write(pdf_path, arcname=pdf_filename)
            
            # Enviar el ZIP
            with open(zip_file, "rb") as f:
                response = HttpResponse(f.read(), content_type="application/zip")
                response["Content-Disposition"] = f"attachment; filename=cartas_modelo347_{seller.shortname}.zip"
                return response
        finally:
            # Eliminar el directorio temporal
            for file in os.listdir(temp_dir):
                os.remove(os.path.join(temp_dir, file))
            os.rmdir(temp_dir)

class SendNotificationModel347(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def post(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        year = self.kwargs["year"]

        customers_invoices_347 = get_customers_model347(seller, year)
        providers_invoices_347  = get_providers_model347(seller, year)
        # add both querysets
        declarados = customers_invoices_347 + providers_invoices_347

        if not declarados:
            return JsonResponse({
            "status": 204,
            "message": "No se ha enviado email. Vendedor no tiene clientes/proveedores que cumplan la condición del modelo 347"
            })
        carts_attached = get_model347_carts(seller, declarados, year)
        send_notification_email_model_347(seller, carts_attached, declarados)

        try:
            pmf = PresentedModelForced.objects.create(
                seller=seller,
                country=Country.objects.get(iso_code='ES'),
                model=Model.objects.get(code='ES-347'),
                status=ModelStatus.objects.get(code='email-sent-347'),
                period=Period.objects.get(code='0A'),
                year=year,
            )
        except Exception as e:
            print(f"[Error] Error al crear el PMF 'email-sent-347': {e}")

        return JsonResponse({
            "status": 200,
            "message": "Email enviado correctamente"
            })

class SendNotificationModel5472_1120(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        sellers = Seller.objects.filter(legal_entity = 'llc')
        sellers = sellers.filter(
            Q(is_5472_1120_contracted = True) | 
            Q(is_5472_1120_inactive_contracted = True) |
            Q(contracted_maintenance_llc_date__lte='2023-12-31', contracted_maintenance_llc_end_date__isnull=True) |
            Q(contracted_maintenance_llc_date__lte='2023-12-31', contracted_maintenance_llc_end_date__gte='2023-01-01')
        ).distinct()

        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_email_model_5472_1120(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                
                # contracted_maintenance_llc
                contracted_maintenance_llc = False
                if seller.contracted_maintenance_llc_date:
                    if datetime.date.fromisoformat(str(seller.contracted_maintenance_llc_date)) <= datetime.date(2023, 12, 31):
                        if seller.contracted_maintenance_llc_end_date:
                            if datetime.date.fromisoformat(str(seller.contracted_maintenance_llc_end_date)) >= datetime.date(2023, 1, 1):
                                contracted_maintenance_llc = True
                        else:
                            contracted_maintenance_llc = True
                        
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email,
                    "is_5472_1120_contracted": seller.is_5472_1120_contracted,
                    "is_5472_1120_inactive_contracted": seller.is_5472_1120_inactive_contracted,
                    "contracted_maintenance_llc": contracted_maintenance_llc,
                    "contracted_maintenance_llc_date": seller.contracted_maintenance_llc_date,
                    "contracted_maintenance_llc_end_date": seller.contracted_maintenance_llc_end_date
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })

class SendNotificationModel184(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        # sellers = Seller.objects.filter(Q(legal_entity='other') | Q(legal_entity='llc') | Q(vat_seller__vat_country__iso_code='ES', vat_seller__is_local=False) )
        # sellers = sellers.exclude(is_184_contracted = True).distinct()
        sellers = Seller.objects.filter(is_184_contracted = True).distinct()

        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_email_model_184(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                sv = SellerVat.objects.filter(Q(seller=seller) & Q(vat_country__iso_code='ES', is_local=False)).first()
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email,
                    "legal_entity": seller.legal_entity,
                    "is_184_contracted": seller.is_184_contracted,
                    "sellervat_country": sv.vat_country.iso_code if sv else None,
                    "sellervat_islocal": sv.is_local if sv else None,
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })
    
class SendNotificationModel303(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        sellers = Seller.objects.filter(seller_presented_model__model__code='ES-303', seller_presented_model__year='2023', seller_presented_model__period='Q4', seller_presented_model__status__code='pending').distinct()

        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_email_model_303(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                sv = SellerVat.objects.filter(Q(seller=seller) & Q(vat_country__iso_code='ES', is_local=False)).first()
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })

class SendNotificationModelBoir(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        sellers = Seller.objects.filter(legal_entity='llc').exclude(contracted_maintenance_llc_end_date__isnull = False, contracted_maintenance_llc_end_date__lt='2024-01-01').distinct()

        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_email_model_boir(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email,
                    "legal_entity": seller.legal_entity,
                    "contracted_maintenance_llc_date": seller.contracted_maintenance_llc_date,
                    "contracted_maintenance_llc_end_date": seller.contracted_maintenance_llc_end_date
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })
    
class SendNotificationModelLipe(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        vat_list = ['*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','*************','IT03880090547','IT03883240545','*************','*************','*************','*************','*************','*************']
        codice_fiscale_list = ['ITBLSTSS99C26Z131W','ITMLGCST89T20Z131U','ITZNRJCR64H02Z131O','ITMZNFNN85C23Z131L','ITRMSRNA94C05Z131K','ITSLNRRD96R01Z131E']
        sellers = Seller.objects.filter(seller_presented_model__model__code='IT-LIPE', seller_presented_model__year='2023', seller_presented_model__period='Q4', seller_presented_model__status__code='pending').distinct()
        sellers = sellers.exclude(vat_seller__vat_country__iso_code='IT', vat_seller__vat_number__in=vat_list ).distinct()
        sellers = sellers.exclude(vat_seller__vat_country__iso_code='IT', vat_seller__codice_fiscale__in=codice_fiscale_list ).distinct()

        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_email_model_lipe(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })

class SendNotificationModelVatAnuale(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        sellers = sellers = Seller.objects.filter(seller_presented_model__model__code='IT-VATANNUALE', seller_presented_model__year='2023', seller_presented_model__period='0A', seller_presented_model__status__code='pending').distinct()

        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_email_model_vatannuale(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })

class SendNotificationWeAreClosed(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        # sellers = Seller.objects.filter(shortname='cristianmateugomez')
        # sellers = Seller.objects.filter(Q(contracted_accounting = True)).filter(Q(legal_entity='self-employed') | Q(legal_entity='sl') ).distinct()
        
        # Filtrar sellers con algún servicio contratado en la fecha actual
        # sellers_with_services = Seller.objects.filter(
        #     Q(contracted_accounting_date__lte=datetime.date.today(), contracted_accounting_end_date__isnull=True) |
        #     Q(contracted_accounting_date__lte=datetime.date.today(), contracted_accounting_end_date__gte=datetime.date.today()) |
        #     Q(contracted_accounting_txt_date__lte=datetime.date.today(), contracted_accounting_txt_end_date__isnull=True) |
        #     Q(contracted_accounting_txt_date__lte=datetime.date.today(), contracted_accounting_txt_end_date__gte=datetime.date.today()) |
        #     Q(contracted_model_presentation_date__lte=datetime.date.today(), contracted_model_presentation_end_date__isnull=True) |
        #     Q(contracted_model_presentation_date__lte=datetime.date.today(), contracted_model_presentation_end_date__gte=datetime.date.today()) |
        #     Q(contracted_maintenance_llc_date__lte=datetime.date.today(), contracted_maintenance_llc_end_date__isnull=True) |
        #     Q(contracted_maintenance_llc_date__lte=datetime.date.today(), contracted_maintenance_llc_end_date__gte=datetime.date.today()) |
        #     Q(contracted_corporate_payroll_date__lte=datetime.date.today(), contracted_corporate_payroll_end_date__isnull=True) |
        #     Q(contracted_corporate_payroll_date__lte=datetime.date.today(), contracted_corporate_payroll_end_date__gte=datetime.date.today()) |
        #     Q(contracted_labor_payroll_date__lte=datetime.date.today(), contracted_labor_payroll_end_date__isnull=True) |
        #     Q(contracted_labor_payroll_date__lte=datetime.date.today(), contracted_labor_payroll_end_date__gte=datetime.date.today()) |
        #     Q(oss_date__lte=datetime.date.today(), oss_end_date__isnull=True) |
        #     Q(oss_date__lte=datetime.date.today(), oss_end_date__gte=datetime.date.today()) |
        #     Q(withholdings_payments_account_date__lte=datetime.date.today(), withholdings_payments_account_end_date__isnull=True) |
        #     Q(withholdings_payments_account_date__lte=datetime.date.today(), withholdings_payments_account_end_date__gte=datetime.date.today())
        # ).distinct()

   
        # sellersvats = Seller.objects.all()
        # for seller in Seller.objects.all():
        #     seller_vats = SellerVat.objects.filter(seller=seller, is_contracted=True)
        #     for sv in seller_vats:
        #         if sv.activation_date and sv.activation_date <= datetime.date.today():
        #             if not sv.deactivation_date or sv.deactivation_date >= datetime.date.today():
        #                 sellers_with_services |= Seller.objects.filter(shortname=seller.shortname)

        # sellers = sellers_with_services.distinct()

 
        # Filtrar sellers con algún servicio contratado en la fecha actual
        sellers_with_services = Seller.objects.filter(
            Q(contracted_accounting_date__lte=datetime.date.today(), contracted_accounting_end_date__isnull=True) |
            Q(contracted_accounting_date__lte=datetime.date.today(), contracted_accounting_end_date__gte=datetime.date.today()) |
            Q(contracted_accounting_txt_date__lte=datetime.date.today(), contracted_accounting_txt_end_date__isnull=True) |
            Q(contracted_accounting_txt_date__lte=datetime.date.today(), contracted_accounting_txt_end_date__gte=datetime.date.today()) |
            Q(contracted_model_presentation_date__lte=datetime.date.today(), contracted_model_presentation_end_date__isnull=True) |
            Q(contracted_model_presentation_date__lte=datetime.date.today(), contracted_model_presentation_end_date__gte=datetime.date.today()) |
            Q(contracted_maintenance_llc_date__lte=datetime.date.today(), contracted_maintenance_llc_end_date__isnull=True) |
            Q(contracted_maintenance_llc_date__lte=datetime.date.today(), contracted_maintenance_llc_end_date__gte=datetime.date.today()) |
            Q(contracted_corporate_payroll_date__lte=datetime.date.today(), contracted_corporate_payroll_end_date__isnull=True) |
            Q(contracted_corporate_payroll_date__lte=datetime.date.today(), contracted_corporate_payroll_end_date__gte=datetime.date.today()) |
            Q(contracted_labor_payroll_date__lte=datetime.date.today(), contracted_labor_payroll_end_date__isnull=True) |
            Q(contracted_labor_payroll_date__lte=datetime.date.today(), contracted_labor_payroll_end_date__gte=datetime.date.today()) |
            Q(oss_date__lte=datetime.date.today(), oss_end_date__isnull=True) |
            Q(oss_date__lte=datetime.date.today(), oss_end_date__gte=datetime.date.today()) |
            Q(withholdings_payments_account_date__lte=datetime.date.today(), withholdings_payments_account_end_date__isnull=True) |
            Q(withholdings_payments_account_date__lte=datetime.date.today(), withholdings_payments_account_end_date__gte=datetime.date.today())
        ).distinct()

        # Filtrar sellers con al menos un sellerVat contratado que tenga la fecha actual entre las fechas de activación y desactivación
        seller_shortnames = set()
        for seller in Seller.objects.all():
            seller_vats = SellerVat.objects.filter(seller=seller, is_contracted=True)
            for sv in seller_vats:
                if sv.activation_date and sv.activation_date <= datetime.date.today():
                    if not sv.deactivation_date or sv.deactivation_date >= datetime.date.today():
                        seller_shortnames.add(seller.shortname)

        # Filtrar los sellers usando los shortnames obtenidos
        sellers_with_vats = Seller.objects.filter(shortname__in=seller_shortnames)

        # Unir ambos conjuntos de resultados
        # sellers = list(chain(sellers_with_services, sellers_with_vats))
        # Unir ambos conjuntos de resultados y eliminar duplicados
        sellers = list(set(chain(sellers_with_services, sellers_with_vats)))




        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_we_are_closed(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                sv = SellerVat.objects.filter(Q(seller=seller) & Q(vat_country__iso_code='ES', is_local=False)).first()
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email,
                    "legal_entity": seller.legal_entity,
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })
    
class SendNotificationGeneric(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get(self, request, *args, **kwargs):
        # sellers = Seller.objects.filter(shortname='usuariopruebas')
        sellers = Seller.objects.filter( legal_entity__in=['self-employed', 'sl']).distinct() # FOR GESTORIA ES
        # sellers = Seller.objects.filter(legal_entity='llc').distinct() # FOR IVA ES
        sellers = sellers.filter(seller_presented_model__year='2024', seller_presented_model__period='Q2', seller_presented_model__status__code='pending', seller_presented_model__country__iso_code='ES')

        qty = 0
        mails = []
        errors = []
        for seller in sellers:
            try:
                # time.sleep(1)
                # send_notification_generic(seller)
                print(f"[Success] {seller.shortname}: Email enviado correctamente")
                qty += 1
                mails.append({
                    "name": seller.name,
                    "shortname": seller.shortname,
                    "email": seller.user.email,
                    "legal_entity": seller.legal_entity,
                })
            except Exception as e:
                print(f"[Error] {seller.shortname}: {e}")
                errors.append(f"{seller.shortname}: {e}")

        return JsonResponse({
            "status": 200,
            "message": f"{qty} Emails enviados correctamente",
            "mails": mails,
            "errors": errors
        })

# class SendNotificationAmzTxt(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), View):
#     slug_url_kwarg = 'shortname'
#     slug_field = 'shortname'

#     def get(self, request, *args, **kwargs):
#         send_txt_notification_email()

#         return JsonResponse({
#             "status": 200,
#             "message": f"Emails enviados",
#         })
    
class FixCurrencyRecords5472(LoginRequiredMixin, IsManagerRolePermission, View):

    def get(self, request, *args, **kwargs):
        converter = CurrencyConverter()
        usd_exchange_rates = defaultdict(dict)

        records = AccountingRecord.objects.filter(
            Q(total_currency__isnull=True) |
            Q(total_currency=0.0)
        ).exclude(amount=0.0).distinct()


        records_to_update = []

        currency_api_use = 0

        for record in records:
            if record.currency.code == 'USD':
                record.total_currency = record.amount
            else:
                exchange_rate = usd_exchange_rates[record.currency.code].get(record.date)
                if not exchange_rate:
                    exchange_rate = converter.get_exchange_rate(
                        base_currency=record.currency.code,
                        target_currency='USD',
                        date=record.date
                    )
                    currency_api_use += 1
                    usd_exchange_rates[record.currency.code][record.date] = exchange_rate

                record.total_currency = float(record.amount) * exchange_rate

            records_to_update.append(record)

        AccountingRecord.objects.bulk_update(records_to_update, ['total_currency'])

        return JsonResponse({
            "status": 200,
            "message": f"Se actualizaron {len(records_to_update)} records.",
            "currency_api_use": currency_api_use
        })