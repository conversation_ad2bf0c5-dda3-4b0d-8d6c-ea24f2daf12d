import os
import json
import django

# Configurar Django si es necesario
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "muaytax.settings")
django.setup()

from muaytax.dictionaries.models.document_type import DocumentType

# Ruta del archivo JSON
JSON_FILE = "muaytax/dictionaries/data/document_type.json"

def load_document_type():
    """Carga los datos de document_type.json en la base de datos"""
    if not os.path.exists(JSON_FILE):
        print(f"❌ No se encontró el archivo: {JSON_FILE}")
        return

    with open(JSON_FILE, "r", encoding="utf-8") as file:
        data = json.load(file)

    for item in data:
        obj, created = DocumentType.objects.update_or_create(
            code=item["code"],  # La clave primaria
            defaults={"description": item["description"]}
        )

        if created:
            print(f"✅ Creado: {obj.code} - {obj.description}")
        else:
            print(f"🔄 Actualizado: {obj.code} - {obj.description}")

if __name__ == "__main__":
    load_document_type()


# Lista
# import os
# import json
# import django

# # Configurar Django si es necesario
# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "muaytax.settings")
# django.setup()

# from django.apps import apps

# # 🔹 Lista manual de diccionarios que quieres cargar
# SELECTED_DICTIONARIES = ["document_type"]  # Agrega más modelos aquí en el futuro

# # Directorios base
# DATA_DIR = "muaytax/dictionaries/data"
# MODELS_APP = "dictionaries"

# def get_primary_key_field(model):
#     """Obtiene el nombre del campo de clave primaria de un modelo Django."""
#     for field in model._meta.fields:
#         if field.primary_key:
#             return field.name
#     raise ValueError(f"No se encontró clave primaria en el modelo {model.__name__}")

# def load_data_for_model(model_name):
#     """Carga los datos desde JSON e inserta/actualiza en la base de datos."""
#     try:
#         model = apps.get_model(MODELS_APP, model_name)
#     except LookupError:
#         print(f"❌ No se encontró el modelo: {model_name}")
#         return

#     json_path = os.path.join(DATA_DIR, f"{model_name}.json")

#     if not os.path.exists(json_path):
#         print(f"❌ No se encontró el archivo: {json_path}")
#         return

#     with open(json_path, "r", encoding="utf-8") as file:
#         data = json.load(file)

#     primary_key_field = get_primary_key_field(model)

#     for item in data:
#         primary_key_value = item.get(primary_key_field)
#         if primary_key_value is None:
#             print(f"⚠️ Falta la clave primaria '{primary_key_field}' en: {item}")
#             continue

#         obj, created = model.objects.update_or_create(
#             **{primary_key_field: primary_key_value},
#             defaults=item
#         )

#         if created:
#             print(f"✅ Creado: {model_name} ({primary_key_value})")
#         else:
#             print(f"🔄 Actualizado: {model_name} ({primary_key_value})")

# def main():
#     """Carga los datos de los modelos especificados en SELECTED_DICTIONARIES"""
#     for model_name in SELECTED_DICTIONARIES:
#         print(f"\n📌 Cargando datos para: {model_name}...")
#         load_data_for_model(model_name)

# if __name__ == "__main__":
#     main()

# EJECUCION EN LA SHELL
# exec(open("docs/load_data_dictionaries_shell/load_data_dictionaries.py").read())

