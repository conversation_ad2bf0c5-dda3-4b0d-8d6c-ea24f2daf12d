# Crear un modelo 5472

## Opción 1: <PERSON><PERSON> (shell de Django)

```python
from datetime import date
from muaytax.app_sellers.models import Seller
from muaytax.users.api.serializers import RegisterSerializer
from muaytax.dictionaries.models import ServiceType

seller = Seller.objects.get(shortname='test_5472_copy_d') #ask user the shortname
serializer = RegisterSerializer()
service = serializer.create_service(
    seller=seller,
    service_code='model_54721120',
    fecha_compra=date.today(),
    quantity=1,
    year=2024
)
```

## Opción 2: Manualmente desde el Panel de Admin

Para contratar un servicio 5472 a un seller (LLC) manualmente:

1. **Acceder al Admin de Django**
2. **Ir a la tabla "Servicios" (Services)**
3. **Crear nuevo servicio o editar existente** con los siguientes datos:
   - **Seller**: Seleccionar el seller (debe ser LLC)
   - **Servicio**: `model_54721120` (5472 completo) o `model_54721120_limited` (5472 limitado)
   - **Año**: Año específico para el que se contrata el servicio
   - **Otros campos**: Completar según sea necesario

**Notas importantes:**

- El seller debe tener `legal_entity='llc'` (no funciona con 'self-employed')
- Puede tener o no otros servicios contratados con anterioridad
- Se puede contratar tanto el servicio completo como la versión limitada
