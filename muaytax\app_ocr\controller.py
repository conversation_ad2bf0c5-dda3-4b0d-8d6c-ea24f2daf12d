import json

from muaytax.app_ocr.processors import AmazonTextractProcessor, OpenAIProcessor

# @dataclass
class MuaytaxOCRProcessor:
    """
    Clase que procesa un documento usando Amazon Textract y OpenAI.
    :param seller: objeto de la clase Seller.
    :param processing_document: tipo de documento a procesar.
    :param extractor: objeto de la clase AmazonTextractProcessor.
    :param processor: objeto de la clase OpenAIProcessor.
    """
    def __init__(self, processing_document, seller=None):
        self.seller = seller
        self.processing_document = processing_document
        self.extractor = AmazonTextractProcessor(self.processing_document)
        self.processor = OpenAIProcessor(self.seller, self.processing_document)

    def extract_text(self, file_path: str) -> str:
        """
        Extrae el texto de un documento usando Amazon Textract.
        """
        return self.extractor.extract_text_async(file_path)
    
    def process_extracted_text(self, extracted_text: str) -> dict:
        """
        Procesa el texto extraído con OpenAI.
        """
        json_output = self.processor.process_extracted_text_with_openai(extracted_text)
        return json.loads(json_output)