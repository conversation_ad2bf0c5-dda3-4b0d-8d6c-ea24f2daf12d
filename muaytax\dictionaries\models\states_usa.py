from django.db import models

class StatesUSA(models.Model):
    code = models.CharField(
        max_length=2,
        primary_key=True,
        verbose_name="Código del Estado",
        help_text="Código de 2 letras asignado a ese estado."
    )
    name = models.CharField(
        max_length=50,
        verbose_name="Nombre del estado",
    )

    class Meta:
        verbose_name = "Estado USA"
        verbose_name_plural = "Estados USA"
        ordering = ["name"]

    def __str__(self):
        return self.name
    
# @admin.register(StatesUSA)
# class StatesUSAAdmin(admin.ModelAdmin):
#     list_display = ["code", "name"]
#     search_fields = ["code", "name"]