UPDATE invoices_concept con
SET vat_euros = ROUND((con.amount_euros * con.vat / 100)::numeric,2),
vat_currency = ROUND((con.amount_currency * con.vat / 100)::numeric,2),
irpf_euros = ROUND((con.amount_euros * con.irpf / 100)::numeric,2),
irpf_currency = ROUND((con.amount_currency * con.irpf / 100)::numeric,2),
eqtax_euros = ROUND((con.amount_euros * con.eqtax / 100)::numeric,2),
eqtax_currency = ROUND((con.amount_currency * con.eqtax / 100)::numeric,2)
FROM invoices_invoice inv
WHERE inv.id = con.invoice_id
AND inv.status_id = 'revised';