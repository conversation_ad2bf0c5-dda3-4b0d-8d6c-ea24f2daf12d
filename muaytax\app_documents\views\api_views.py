import os
from datetime import datetime, timedelta

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny

from django.http import FileResponse, Http404
from django.shortcuts import get_object_or_404
from django.utils.http import http_date
from django.core.signing import Signer

from muaytax.app_sellers.models import Seller
from muaytax.app_documents.utils.utils import getPresentedModel
from muaytax.users.permissions import IsManagerDRFPermission

class GetGeneratedPresentedModel(APIView):
    # permission_classes = [AllowAny]
    permission_classes = [IsManagerDRFPermission]

    def get(self, request, *args, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        year = request.GET.get('year')
        period = request.GET.get('period')
        model_id = request.GET.get('model_id')

        if not year or not period or not model_id:
            return Response({"error": "Faltan uno o varios parámetros obligatorios"}, status=400)

        generated_model_path  = getPresentedModel(seller, year, period, model_id)
        if not generated_model_path:
            return Response({"error": "No existe el modelo generado con los parámetros proporcionados"}, status=404)
        
        file_name = os.path.basename(generated_model_path)

        signer = Signer()
        signed_url = f"/media/uploads/presented_models/{file_name}?signature={signer.sign(file_name)}&expires={int((datetime.now() + timedelta(minutes=10)).timestamp())}"

        response_data = {
            "signed_url": signed_url
        }

        return Response(response_data, status=200)