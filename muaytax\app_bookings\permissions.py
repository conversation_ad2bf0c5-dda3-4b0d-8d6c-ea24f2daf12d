from rest_framework.exceptions import AuthenticationFailed
from rest_framework.permissions import Base<PERSON><PERSON><PERSON>
from rest_framework.authentication import get_authorization_header
from django.conf import settings
import jwt

from api_rest.api_auth.models import PublicAccessCodeToken


class PublicBookingPermission(BasePermission):
    def has_permission(self, request, view):
        # En desarrollo, permitimos todo el acceso
        if settings.DEBUG:
            return True

        auth = get_authorization_header(request).split()
        if not auth or auth[0].lower() != b'bearer' or len(auth) != 2:
            raise AuthenticationFailed('No token provided')  # No token provided
        
        token = auth[1].decode('utf-8') if len(auth) == 2 else ''

        try:
            payload = jwt.decode(token, settings.SIMPLE_JWT['SIGNING_KEY'], algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            raise AuthenticationFailed('Token is expired')
        except jwt.InvalidTokenError:
            raise AuthenticationFailed('Invalid token')
        
        if 'email' not in payload:
            raise AuthenticationFailed('Invalid token. Missing email')
        
        if not self.validate_token(token):
            raise AuthenticationFailed('Invalid token or token is expired')
        
        return True
    
    def validate_token(self, token: str) -> bool:
        # En desarrollo, permitimos todo el acceso
        if settings.DEBUG:
            return True

        try:
            # find the token in the database
            public_access_token = PublicAccessCodeToken.objects.get(private_token=token)

            if not public_access_token.is_active:
                return False
            return True
        
        except PublicAccessCodeToken.DoesNotExist:
            return False