from django.urls import path
from django.views.generic.base import RedirectView
from muaytax.app_hmrc.views import *

app_name = "app_hmrc"

urlpatterns = [
    path(
        "sellers/<shortname>/hmrc/",
        RedirectView.as_view(url="dashboard/", permanent=True),
        name="hmrc_dashboard_redirect",
    ),
    path(
        "sellers/<shortname>/hmrc/dashboard/",
        view=HmrcDashboardView.as_view(),
        name="hmrc_dashboard",
    ),
    path(
        "hmrc/grant-access/",
        view=AuthorizeAccessHmrcView.as_view(),
        name="hmrc_grant_access",
    ),
    path(
        "hmrc/grant-access/callback/",
        view=AuthorizeHmrcCallBackView.as_view(),
        name="hmrc_grant_authority_callback",
    ),
    path(
        "sellers/<shortname>/hmrc/vat-return/<model_id>/send/",
        view=SendVatReturnView.as_view(),
        name="hmrc_vat_return_send",
    )
]

# Urls for the API
urlpatterns += [
    path(
        "hmrc/<shortname>/obligations/",
        view=RetrieveObligationsApiView.as_view(),
        name="view_vat_obligations",
    ),
    path(
        "hmrc/<shortname>/vat-return/<period_key>/",
        view=RetrieveVatReturnApiView.as_view(),
        name="view_vat_return",
    ),
    path(
        "hmrc/<shortname>/payments/",
        view=RetrievePaymentsApiView.as_view(),
        name="view_vat_payments",
    ),
    path(
        "hmrc/<shortname>/liabilities/",
        view=RetrieveLiabilitiesApiView.as_view(),
        name="view_vat_liabilities",
    ),
    path(
        "hmrc/<shortname>/penalties/",
        view=RetrievePenaltiesApiView.as_view(),
        name="view_vat_penalties",
    ),
    path(
        "hmrc/<shortname>/financial-details/<charge_reference>/",
        view=RetrieveFinancialDetails.as_view(),
        name="view_financial_details",
    ),
]