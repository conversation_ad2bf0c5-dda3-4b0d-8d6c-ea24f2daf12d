--Count para facturas de tipo gasto que tengan cliente
SELECT COUNT(*) FROM INVOICES_INVOICE where invoice_category_id = 'expenses' AND customer_id IS NOT NULL
--Eliminar el cliente de las facturas de tipo gasto
UPDATE invoices_invoice SET customer_id = NULL WHERE invoice_category_id = 'expenses' AND customer_id IS NOT NULL

---------------------------------------------------------------------------------------------------------------

--Count para facturas de tipo venta que tengan proveedor
SELECT COUNT(*) FROM INVOICES_INVOICE where invoice_category_id = 'sales' AND provider_id IS NOT NULL

--Eliminar el proveedor de las facturas de tipo venta
UPDATE invoices_invoice SET provider_id = NULL WHERE invoice_category_id = 'sales' AND provider_id IS NOT NULL


----------------------------------------------------------------------------------------------------

--Comprobar que no hay facturas que contengan a la vez tanto cliente como proveedor
SELECT invoice_category_id FROM invoices_invoice WHERE customer_id IS NOT NULL AND provider_id IS NOT NULL AND (invoice_category_id = 'expenses' OR invoice_category_id = 'sales' )