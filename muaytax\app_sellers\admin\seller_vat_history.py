from django.contrib import admin

class SellerVatHistoryAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user",
        "sellervat",
        "vat_country",
        "vat_number",
        "siret",
        "steuernummer",
        "vat_vies",
        "is_contracted",
        "contracting_date",
        "start_contracting_date",
        "end_contracting_date",
        "contracting_discontinue",
        "es_status_activation",
        "es_status_altaiae",
        "es_status_cdigital",
        "es_status_vies",
        "es_status_eori",
        "status_process",
        "is_max_priority",
        "type",
        "vat_status",
        "activation_date",
        "deactivation_date",
        "period",
        "quarter",
        "comment",
        "address_name",
        "address",
        "address_number",
        "address_continue",
        "address_zip",
        "address_city",
        "address_country",
        "address_catastral",
        "created_at",
    ]

    search_fields = [
        "id",
        "user",
        "sellervat__vat_number",
        "vat_country__name",
        "vat_number",
        "siret",
        "steuernummer",
        "comment",
        "address_name",
        "address",
        "address_number",
        "address_continue",
        "address_zip",
        "address_city",
        "address_country__name",
        "address_catastral",
    ]