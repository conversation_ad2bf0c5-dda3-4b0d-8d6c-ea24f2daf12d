DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_managementES_list_json') THEN
        DROP FUNCTION func_managementES_cached_list_json(date_year INTEGER, date_period VARCHAR, entity VARCHAR);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_managementES_cached_list_json(date_year INTEGER, date_period VARCHAR, entity VARCHAR)
RETURNS jsonb AS $$
DECLARE
    first_month DATE;
    last_month DATE;
    lentity VARCHAR;
    allow_all_entities BOOLEAN; -- Bandera para permitir todas las entidades en caso de no especificar
    result_json jsonb;
BEGIN
    -- Determinar el primer y último mes según el período o mes especifico
    IF date_period = 'Q1' THEN
        first_month := (date_year || '-01-01')::DATE;
        last_month := (date_year || '-04-01')::DATE;
    ELSIF date_period = 'Q2' THEN
        first_month := (date_year || '-04-01')::DATE;
        last_month := (date_year || '-07-01')::DATE;
    ELSIF date_period = 'Q3' THEN
        first_month := (date_year || '-07-01')::DATE;
        last_month := (date_year || '-10-01')::DATE;
    ELSIF date_period = 'Q4' THEN
        first_month := (date_year || '-10-01')::DATE;
        last_month := ((date_year + 1) || '-01-01')::DATE;
    ELSIF date_period = 'M1' THEN
        first_month := (date_year || '-01-01')::DATE;
        last_month := (date_year || '-02-01')::DATE;
    ELSIF date_period = 'M2' THEN
        first_month := (date_year || '-02-01')::DATE;
        last_month := (date_year || '-03-01')::DATE;
    ELSIF date_period = 'M3' THEN
        first_month := (date_year || '-03-01')::DATE;
        last_month := (date_year || '-04-01')::DATE;
    ELSIF date_period = 'M4' THEN
        first_month := (date_year || '-04-01')::DATE;
        last_month := (date_year || '-05-01')::DATE;
    ELSIF date_period = 'M5' THEN
        first_month := (date_year || '-05-01')::DATE;
        last_month := (date_year || '-06-01')::DATE;
    ELSIF date_period = 'M6' THEN
        first_month := (date_year || '-06-01')::DATE;
        last_month := (date_year || '-07-01')::DATE;
    ELSIF date_period = 'M7' THEN
        first_month := (date_year || '-07-01')::DATE;
        last_month := (date_year || '-08-01')::DATE;
    ELSIF date_period = 'M8' THEN
        first_month := (date_year || '-08-01')::DATE;
        last_month := (date_year || '-09-01')::DATE;
    ELSIF date_period = 'M9' THEN
        first_month := (date_year || '-09-01')::DATE;
        last_month := (date_year || '-10-01')::DATE;
    ELSIF date_period = 'M10' THEN
        first_month := (date_year || '-10-01')::DATE;
        last_month := (date_year || '-11-01')::DATE;
    ELSIF date_period = 'M11' THEN
        first_month := (date_year || '-11-01')::DATE;
        last_month := (date_year || '-12-01')::DATE;
    ELSIF date_period = 'M12' THEN
        first_month := (date_year || '-12-01')::DATE;
        last_month := ((date_year + 1) || '-01-01')::DATE;
    END IF;

    -- Filtrar la entidad
    IF entity = 'sl' THEN
        lentity := 'sl';
        allow_all_entities := FALSE;
    ELSIF entity = 'self-employed' THEN
        lentity := 'self-employed';
        allow_all_entities := FALSE;
    ELSE
        -- Si no se especifica, usar todas las entidades válidas explícitamente
        allow_all_entities := TRUE;
    END IF;

    -- Consulta principal optimizada
    SELECT jsonb_build_object(
        'pending_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_111 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_115 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_123 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_130 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_131 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_200 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_200 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_202 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_309 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_180 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_184 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_190 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'pending' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_intrastat = 'pending' THEN 1 ELSE 0 END)
            ), 0
        ),
        'required_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_111 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_115 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_123 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_130 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_131 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_200 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_202 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_216 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_309 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_180 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_184 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_190 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 IN ('required', 'processed') THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_intrastat IN ('required', 'processed') THEN 1 ELSE 0 END)
            ), 0
        ),
        'disagreed_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_111 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_115 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_123 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_130 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_131 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_200 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_202 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_216 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_309 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_180 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_184 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_190 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'disagreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_intrastat = 'disagreed' THEN 1 ELSE 0 END)
            ), 0
        ),
        'agreed_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_111 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_115 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_123 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_130 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_131 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_200 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_202 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_216 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_309 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_180 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_184 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_190 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'agreed' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_intrastat = 'agreed' THEN 1 ELSE 0 END)
            ), 0
        ),
        'presented_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_111 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_115 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_123 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_130 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_131 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_200 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_202 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_216 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_303 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_309 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_349 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_369 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_180 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_184 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_190 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_347 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_390 = 'presented' THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_intrastat = 'presented' THEN 1 ELSE 0 END)
            ), 0
        ),
        'total_pending_invoices', COALESCE(SUM(subq.num_pending_invoices), 0),
        'data', COALESCE(jsonb_agg(
            jsonb_build_object(
                'seller_id', subq.subq_seller_id,
                'seller_name', subq.seller_name,
                'shortname', subq.seller_shortname,
                'status_amazon_txt', COALESCE(
                    CASE WHEN subq.seller_amazon_sell IS TRUE THEN 'require' ELSE 'no_require' END, 'no_require'
                ),
                'email', subq.email,
                'user_name', subq.user_name,
                'last_login', to_char(subq.last_login, 'YYYY-MM-DD HH24:MI:SS'),
                'total_invoices_from_period', subq.total_invoices_from_period,
                'num_pending_invoices', subq.num_pending_invoices,
                'percentage_pending_invoices', subq.percentage_pending_invoices,
                'model_111', subq.model_111,
                'model_115', subq.model_115,
                'model_123', subq.model_123,
                'model_130', subq.model_130,
                'model_131', subq.model_131,
                'model_200', subq.model_200,
                'model_216', subq.model_216,
                'model_303', subq.model_303,
                'model_309', subq.model_309,
                'model_349', subq.model_349,
                'model_intrastat', subq.model_intrastat,
                'model_369', subq.model_369,
                'model_180', subq.model_180,
                'model_184', subq.model_184,
                'model_190', subq.model_190,
                'model_347', subq.model_347,
                'model_390', subq.model_390,
                'model_296', subq.model_296,
                'model_193', subq.model_193,
                'model_202', subq.model_202,
                'month1', subq.txt_01,
                'month2', subq.txt_02,
                'month3', subq.txt_03,
                'month4', subq.txt_04,
                'month5', subq.txt_05,
                'month6', subq.txt_06,
                'month7', subq.txt_07,
                'month8', subq.txt_08,
                'month9', subq.txt_09,
                'month10', subq.txt_10,
                'month11', subq.txt_11,
                'month12', subq.txt_12,
                'model_json_result', subq.model_json_result,
                'monthly_muaytax_json_invoices', subq.monthly_muaytax_json_invoices
            )
        ), '[]')  -- Si no hay datos, devolver un array vacío
    )
    INTO result_json
    FROM (
        WITH invs AS (
            SELECT DISTINCT
                inv.seller_id AS invs_seller_id,
                COALESCE(COUNT(inv.id), 0) AS total_invoices_from_period,
                COALESCE(SUM(CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE 0 END), 0) AS num_pending_invoices,
                CASE 
                    WHEN COUNT(inv.id) = 0 THEN 0 
                    ELSE ROUND(COALESCE(SUM(CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE 0 END), 0)::numeric / COUNT(inv.id)::numeric * 100.00, 2)
                END AS percentage_pending_invoices
            FROM invoices_invoice inv
            WHERE 
                (inv.invoice_category_id IS NULL OR inv.invoice_category_id NOT LIKE '%_copy')
                AND (inv.status_id IN ('pending', 'revision-pending') OR (inv.accounting_date >= first_month AND inv.accounting_date < last_month))
            GROUP BY inv.seller_id
        )
        SELECT DISTINCT
            sel.id as subq_seller_id,
            sel.name as seller_name,
            sel.shortname as seller_shortname,
            sel.amazon_sell as seller_amazon_sell,
            usr.email,
            usr.name as user_name,
            usr.last_login,
            list.*,
            inv.total_invoices_from_period,
            inv.num_pending_invoices,
            inv.percentage_pending_invoices
        FROM lists_sellerlistmanagementes list
        INNER JOIN sellers_seller sel ON sel.id = list.seller_id
        LEFT JOIN users_user usr ON usr.id = sel.user_id
        LEFT JOIN invs inv ON inv.invs_seller_id = sel.id
        WHERE
            list.show IS TRUE
            AND list.year = date_year
            AND list.period_id = date_period
            AND (
                (allow_all_entities IS TRUE AND sel.legal_entity IN ('self-employed', 'sl')) OR
                (allow_all_entities IS FALSE AND sel.legal_entity = lentity)
            )
    ) AS subq;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- Example call to the function:
-- SELECT func_managementES_cached_list_json(2024, 'Q3', 'all');
