DO $$ 
DECLARE 
    function_record record;
BEGIN
    -- Obtener la lista de funciones y sus firmas de parámetros
    FOR function_record IN (SELECT proname, proargnames, pg_get_function_identity_arguments(oid) AS arguments  FROM pg_proc WHERE proname LIKE 'func_%') 
    LOOP
        -- Construir el comando DROP FUNCTION
        EXECUTE 'DROP FUNCTION IF EXISTS ' || function_record.proname || '(' || function_record.arguments || ');';
    END LOOP;
END $$;