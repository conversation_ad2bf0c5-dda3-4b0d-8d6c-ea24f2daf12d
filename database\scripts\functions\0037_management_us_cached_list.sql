-- DO $$
-- BEGIN
--     IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_seller_list_json') THEN
--         DROP FUNCTION management_us_cached_list_json(INTEGER, VARCHAR);
--     END IF;
-- END $$;

CREATE OR REPLACE FUNCTION management_us_cached_list_json(
    date_year INTEGER,
    date_period VARCHAR,
	excludes VARCHAR[] DEFAULT '{}'::VARCHAR[]  -- Default to an empty array
) RETURNS jsonb AS $$
DECLARE
    first_month DATE;
    last_month DATE;
    result_json jsonb;
BEGIN
    -- Determine the first and last month based on the period
    IF date_period = 'Q1' THEN
        first_month := (date_year || '-01-01')::DATE;
        last_month := (date_year || '-04-01')::DATE;
    ELSIF date_period = 'Q2' THEN
        first_month := (date_year || '-04-01')::DATE;
        last_month := (date_year || '-07-01')::DATE;
    ELSIF date_period = 'Q3' THEN
        first_month := (date_year || '-07-01')::DATE;
        last_month := (date_year || '-10-01')::DATE;
    ELSIF date_period = 'Q4' THEN
        first_month := (date_year || '-10-01')::DATE;
        last_month := ((date_year + 1) || '-01-01')::DATE;
    ELSIF date_period = '0A' THEN
        first_month := (date_year || '-01-01')::DATE;
        last_month := ((date_year + 1) || '-01-01')::DATE;
    END IF;

    -- Main query with aggregation and JSON construction
    SELECT jsonb_build_object(
        'required_notstarted_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 = 'not-started'  AND NOT 'model_5472'  = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 = 'not-started' AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),
        'required_notcompleted_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 = 'not-processed'  AND NOT 'model_5472'  = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 = 'not-processed' AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),
        'required_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 IN ('required', 'processed') AND NOT 'model_5472' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_7004 = 'required' AND NOT 'model_7004' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 IN ('required', 'processed') AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),
        'pending_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 = 'pending'  AND NOT 'model_5472' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_7004 = 'pending'  AND NOT 'model_7004' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 = 'pending' AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),        
        'disagreed_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 = 'disagreed'  AND NOT 'model_5472' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_7004 = 'disagreed'  AND NOT 'model_7004' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 = 'disagreed' AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),
        'agreed_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 = 'agreed'  AND NOT 'model_5472' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_7004 = 'agreed'  AND NOT 'model_7004' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 = 'agreed' AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),
        'fax_send_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 = 'fax_send'  AND NOT 'model_5472' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_7004 = 'fax_send'  AND NOT 'model_7004' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 = 'fax_send' AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),
        'presented_model_count', COALESCE(SUM(
            (CASE WHEN subq.model_5472 = 'presented'  AND NOT 'model_5472' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_7004 = 'presented'  AND NOT 'model_7004' = ANY(excludes) THEN 1 ELSE 0 END) +
            (CASE WHEN subq.model_be_15 = 'presented' AND NOT 'model_be_15' = ANY(excludes) THEN 1 ELSE 0 END)
            ), 0
        ),
        'total_pending_invoices', COALESCE(SUM(subq.num_pending_invoices), 0),
        'data', COALESCE(jsonb_agg(
            jsonb_build_object(
                'seller_id', subq.subq_seller_id,
                'seller_name', subq.seller_name,
                'shortname', subq.seller_shortname,
                'email', subq.email,
                'user_name', subq.user_name,
                'last_login', to_char(subq.last_login, 'YYYY-MM-DD HH24:MI:SS'),
                'total_invoices_from_period', subq.total_invoices_from_period,
                'num_pending_invoices', subq.num_pending_invoices,
                'percentage_pending_invoices', subq.percentage_pending_invoices,
                'contracted_maintenance_llc_date', subq.contracted_maintenance_llc_date,
                'contracted_maintenance_llc_end_date', subq.contracted_maintenance_llc_end_date,
                'is_llc_premium_direction', subq.is_llc_premium_direction,
                'contracted_accounting_usa_date', subq.contracted_accounting_usa_date,
                'contracted_accounting_usa_basic_date', subq.contracted_accounting_usa_basic_date,
                'contracted_accounting_usa_all_date', subq.contracted_accounting_usa_all_date,
                'model_5472', CASE WHEN 'model_5472' = ANY(excludes) THEN '' ELSE subq.model_5472 END,
                'model_7004', CASE WHEN 'model_7004' = ANY(excludes) THEN '' ELSE subq.model_7004 END,
                'model_be_15', CASE WHEN 'model_be_15' = ANY(excludes) THEN '' ELSE subq.model_be_15 END,
                'model_es_184', CASE WHEN 'model_es_184' = ANY(excludes) THEN '' ELSE subq.model_es_184 END
            )
        ), '[]')
    ) INTO result_json
    FROM (
        WITH invs AS (
            SELECT DISTINCT
                inv.seller_id AS invs_seller_id,
                COALESCE(COUNT(inv.id), 0) AS total_invoices_from_period,
                COALESCE(SUM(CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE 0 END), 0) AS num_pending_invoices,
                CASE 
                    WHEN COUNT(inv.id) = 0 THEN 0 
                    ELSE ROUND(COALESCE(SUM(CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE 0 END), 0)::numeric / COUNT(inv.id)::numeric * 100.00, 2)
                END AS percentage_pending_invoices
            FROM invoices_invoice inv
            WHERE 
                (inv.invoice_category_id IS NULL OR inv.invoice_category_id NOT LIKE '%_copy')
                AND (inv.status_id IN ('pending', 'revision-pending') OR (inv.accounting_date >= first_month AND inv.accounting_date < last_month))
            GROUP BY inv.seller_id
        )
        SELECT DISTINCT
            sel.id AS subq_seller_id,
            sel.name AS seller_name, 
            sel.shortname AS seller_shortname, 
            sel.contracted_maintenance_llc_date AS contracted_maintenance_llc_date,
            sel.contracted_maintenance_llc_end_date AS contracted_maintenance_llc_end_date,
            sel.is_llc_premium_direction AS is_llc_premium_direction,
            sel.contracted_accounting_usa_date AS contracted_accounting_usa_date,
            sel.contracted_accounting_usa_basic_date AS contracted_accounting_usa_basic_date,
            -- nueva columna
            COALESCE(sel.contracted_accounting_usa_date, sel.contracted_accounting_usa_basic_date) AS contracted_accounting_usa_all_date,
            usr.name AS user_name,
            usr.email AS email,
            usr.last_login,
            list.*,
            COALESCE(invs.total_invoices_from_period, 0) AS total_invoices_from_period, 
            COALESCE(invs.num_pending_invoices, 0) AS num_pending_invoices, 
            COALESCE(invs.percentage_pending_invoices, 0) AS percentage_pending_invoices
        FROM lists_sellerlistmanagementus list
        INNER JOIN sellers_seller sel ON sel.id = list.seller_id
        LEFT JOIN users_user usr ON usr.id = sel.user_id
        LEFT JOIN invs ON invs.invs_seller_id = sel.id
        WHERE 
            list.show IS TRUE
            AND sel.legal_entity NOT IN ('sl', 'self-employed')
            AND list.year = date_year
            AND list.period_id = date_period
    ) AS subq;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- Example call to the function:
-- SELECT * FROM management_us_cached_list_json(2023, '0A', '{"model_7004","model_be_15","model_es_184"}');