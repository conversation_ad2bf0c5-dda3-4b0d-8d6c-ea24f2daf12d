import json
import pandas as pd
from pathlib import Path

# Rutas de los archivos
BASE_PATH = Path('docs/script_store_products')
EXCEL_PATH = BASE_PATH / 'gestoria_store_product.xlsx'
JSON_PATH = Path('muaytax/dictionaries/data/store_products.json')
UPDATED_JSON_PATH = Path('muaytax/dictionaries/data/store_products_actualizado.json')

# Leer archivo JSON existente
def read_json(json_path):
    with open(json_path, 'r', encoding='utf-8') as file:
        return json.load(file)

# Guardar el JSON actualizado
def save_json(data, json_path):
    with open(json_path, 'w', encoding='utf-8') as file:
        json.dump(data, file, indent=2, ensure_ascii=False)

# Leer archivo Excel
def read_excel(excel_path):
    return pd.read_excel(excel_path)

# Actualizar el JSON con los nuevos datos, sin duplicados
def update_json(existing_data, new_data):
    data_dict = {item['code']: item for item in existing_data}  # Diccionario para búsqueda rápida

    for _, row in new_data.iterrows():
        code = str(row['code'])
        description = row['description']
        shop = row['shop']

        # Si el código ya existe, actualizar la descripción y tienda
        if code in data_dict:
            data_dict[code]['description'] = description
            data_dict[code]['shop'] = shop
        else:
            # Si es un nuevo código, agregarlo
            data_dict[code] = {
                'code': code,
                'description': description,
                'shop': shop
            }

    return list(data_dict.values())

# Ordenar los productos por tienda
def sort_products(data):
    shop_priority = {'gestoria': 1, 'amzvat': 2, 'muaytax': 3}
    return sorted(data, key=lambda x: (shop_priority.get(x['shop'], 99), x['description']))

# Función principal
def process_store_products():
    # Leer datos existentes y nuevos
    existing_data = read_json(JSON_PATH)
    new_data = read_excel(EXCEL_PATH)

    # Completar valores en columna `shop` si están vacíos
    new_data['shop'] = new_data['shop'].fillna(method='ffill')

    # Actualizar y ordenar los productos
    updated_data = update_json(existing_data, new_data)
    sorted_data = sort_products(updated_data)

    # Guardar el JSON actualizado
    save_json(sorted_data, UPDATED_JSON_PATH)

    print(f"JSON actualizado guardado en: {UPDATED_JSON_PATH}")

# Ejecución
if __name__ == "__main__":
    process_store_products()


# codigo para ejecutar el script en consola
# docker compose -f local.yml run --rm django python docs/script_store_products/update_store_products.py 