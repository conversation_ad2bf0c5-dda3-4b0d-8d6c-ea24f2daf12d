# Guía para Añadir Nuevos Modelos de Documentos

Esta guía describe el proceso para añadir nuevos modelos de documentos PDF o Excel al sistema.

## Resumen del Proceso

1. **Configuración**: Añadir la información del modelo y el botón en la interfaz.
2. **Lógica**: Implementar la función de cálculo del modelo.
3. **Integración**: Conectar la nueva función con las vistas de la aplicación.
4. **Actualización**: Registrar el nuevo modelo en la base de datos.
5. **Pruebas**: Verificar que el modelo se genera correctamente.

---

## Tipos de Modelos

El sistema soporta 4 tipos de modelos:

1. **PDF Simple (Tipo 1)**: Genera un PDF estándar sin firma digital.

   - **Ejemplos**: Modelos básicos de impuestos españoles, modelos de EE. UU. (`5472`, `BE15`).

2. **PDF con Firma Digital (Tipo 2)**: Genera un PDF que requiere una firma digital. El PDF final se aplana para evitar modificaciones.

   - **Ejemplos**: `PL-VAT_POWER_OF_ATTORNEY`, `FR-MANDAT-REPRESENTATION-VAT-289A`.

3. **Tipo 3: Modelos con Cálculo Fiscal desde BBDD**
   Modelos que, además de obtener datos básicos, **realizan cálculos fiscales complejos agregando datos transaccionales** (facturas, gastos) directamente desde la base de datos, a menudo mediante funciones SQL específicas.

   - **Ejemplos**: Modelos `303DB`, `347DB` de España.
   - **Procesamiento**: Consulta y agregación de datos fiscales → Mapeo a PDF.

4. **Plantillas de Excel (Tipo 4)**: Genera un archivo Excel a partir de una plantilla.
   - **Ejemplos**: `PL-excel_form_VAT`.

---

## Pasos para la Implementación

### Paso 1: Configuración de Metadatos y UI

#### 1.1. Añadir al Registro de Modelos

Añade una nueva entrada en `muaytax/dictionaries/data/model.json`. Mantén el orden alfabético por país y código.

```json
{
  "code": "[PAIS]-[NOMBRE_MODELO]",
  "description": "Modelo [NOMBRE] ([País])",
  "country": "[CODIGO_ISO_PAIS]"
}
```

#### 1.2. Añadir la Tarjeta del Modelo en la UI

Añade la tarjeta (card) para el nuevo modelo en `muaytax/templates/sellers/seller_summary.html` bajo la sección del país correspondiente.

```html
<div class="col-2 card shadow m-2" style="min-width: 18rem;">
  <div class="card-body">
    <h5 class="card-title">Generar [NOMBRE_MODELO] ([PAÍS])</h5>
    <p class="card-text">
      <h1><i class="fas fa-lg fa-file-invoice"></i></h1>
    </p>
    <a href="./model/[CODIGO_MODELO]" class="btn btn-primary">Ver modelo</a>
  </div>
</div>
```

### Paso 2: Implementar la Lógica de Negocio

#### 2.1. Crear la Función de Cálculo

En `muaytax/utils/calc_models.py`, crea una nueva función para generar el documento. Sigue la convención de nombres `calcModel[Pais][NombreModelo]`.

Copia la estructura de una función existente del mismo tipo (ej. `calcModelIRTR2` para un PDF firmado) para reutilizar el patrón de validación, preparación de datos y generación de PDF.

**Estructura básica de la función:**

```python
def calcModel[Pais][NombreModelo](seller, writer, year, periodo):
    """
    Genera el PDF del modelo [NombreModelo] para [País].
    """
    try:
        # 1. Función interna de validación de datos
        def _validate_data(seller):
            missing_fields = []
            # ... (validar campos de seller, SellerVat, dirección, etc.)
            return missing_fields, sv_country, address

        # 2. Función interna de preparación de datos
        def _prepare_form_data(seller, sv_country, address, year, periodo):
            # ... (crear el diccionario `json_values` con los datos para el PDF)
            # ⚠️ Usa los nombres de campo de la BBDD correctos (ver referencia)
            return {
                'pdf_field_name_1': data_value_1,
                'pdf_field_name_2': data_value_2,
                # ...
            }

        # 3. Función interna de generación de documento
        # (Esta función suele ser genérica y se puede copiar de otro modelo)
        def _generate_document(seller, writer, json_values, year, periodo):
            # ... (lógica para leer plantilla, rellenar campos, firmar y aplanar)
            # Asegúrate de que `template_path` apunta al PDF correcto.
            template_path = os.path.join(
                "muaytax", "static", "assets", "pdf", "[CODIGO_MODELO].pdf"
            )
            # ...

        # Flujo principal
        missing_fields, sv_country, address = _validate_data(seller)
        if missing_fields:
            return JsonResponse(...)  # Devuelve error si faltan datos

        json_values = _prepare_form_data(seller, sv_country, address, year, periodo)
        return _generate_document(seller, writer, json_values, year, periodo)

    except Exception as e:
        # ... (manejo de errores)
```

#### 2.2. Colocar la Plantilla del Documento

Guarda el archivo PDF o Excel de la plantilla en `muaytax/static/assets/pdf/[CODIGO_MODELO].pdf`.

**Importante**: Los nombres de los campos en el formulario PDF deben coincidir **exactamente** con las claves del diccionario `json_values` que creas en `_prepare_form_data`.

### Paso 3: Integración en las Vistas

Modifica el archivo `muaytax/app_sellers/views/seller.py`.

#### 3.1. Importar la Nueva Función

Añade tu nueva función de cálculo a la lista de imports desde `muaytax.utils.calc_models`.

```python
from muaytax.utils.calc_models import (
    # ...
    calcModel[Pais][NombreModelo],
)
```

#### 3.2. Añadir a `signature_required_models` (si aplica)

Si el modelo es de Tipo 2 (con firma), añade su código a la lista `signature_required_models`.

```python
signature_required_models = [
    # ...,
    '[CODIGO_MODELO]'
]
```

#### 3.3. Añadir el Manejador del Modelo

En la función `SellerModelVatViewPDF`, añade un bloque `elif` para tu nuevo modelo. Esto conectará la URL con tu función de cálculo.

```python
elif model_id == "[CODIGO_MODELO]":
    if generated_model is not None:
        response = FileResponse(open(generated_model, 'rb'), content_type='application/pdf')
    else:
        response = calcModel[Pais][NombreModelo](seller, writer, year, periodo)
```

### Paso 4: Actualizar la Base de Datos

Para que el sistema reconozca el nuevo modelo añadido en `model.json`, ejecuta el siguiente comando en la terminal:

```bash
docker compose -f light-local.yml exec django python manage.py shell -c "import database as db; db.initialize()"
```

---

## Referencia de Campos de la Base de Datos

Usa estos campos para obtener los datos del vendedor. **No uses nombres de campo que no estén en esta lista.**

### Modelo `Seller`

- `seller.first_name`
- `seller.last_name`
- `seller.shortname`
- `seller.signature_image`
- `seller.seller_address` (ForeignKey a `Address`)

### Modelo `Address`

- `address.address` (Dirección principal)
- `address.address_city`
- `address.address_state`
- `address.address_zip`
- `address.address_country` (Objeto `Country`)
- `address.address_country.name` (Nombre del país)

### Modelo `SellerVat`

- `sv_country.vat_number`
- `sv_country.activation_date` (Fecha de alta de IVA, **no uses `vat_start_date`**)
- `sv_country.deactivation_date`
- ... (otros campos de `SellerVat`)

---

## Verificación de Campos del PDF

[CLICK AQUÍ](./pdf_field_check.py)
