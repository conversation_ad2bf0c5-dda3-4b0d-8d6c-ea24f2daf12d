DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_management_ES_yearly_list_json') THEN
        DROP FUNCTION func_management_ES_yearly_list_json(date_year INTEGER, date_period VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_management_ES_yearly_list_json(date_year INTEGER, date_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
	legal_entity VARCHAR;
	first_month DATE;
	last_month DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	IF date_period = '0A' THEN 
        first_month := date_year || '-01-01';
        last_month := (date_year + 1) || '-01-01';
    END IF;
				

    FOR inv_data IN

				SELECT DISTINCT 
				-- SubSelect
				subselect.*, 

				-- <PERSON><PERSON>
				(
					CASE 
						WHEN 'required' in (model_180, model_190, model_347, model_390) THEN 0
						-- WHEN 'warning09' in (model_369) THEN 1
						WHEN 'warning' in (model_180, model_190, model_347, model_390) THEN 1	
						WHEN 'disagreed' in (model_180, model_190, model_347, model_390) THEN 2
						WHEN 'agreed' in (model_180, model_190, model_347, model_390) THEN 3
						WHEN 'pending' in (model_180, model_190, model_347, model_390) THEN 4
						WHEN 'presented' in (model_180, model_190, model_347, model_390) THEN 5
						WHEN 'not-required' in (model_180, model_190, model_347, model_390) THEN 6
					ELSE 7
					END 
				) AS model_min,

				-- Model Average
				(
					(
						CASE
							WHEN model_180 = 'required' THEN 0
							WHEN model_180 = 'warning' THEN 1
							WHEN model_180 = 'disagreed' THEN 2
							WHEN model_180 = 'agreed' THEN 3
							WHEN model_180 = 'pending' THEN 4
							WHEN model_180 = 'presented' THEN 5
							WHEN model_180 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_190 = 'required' THEN 0
							WHEN model_190 = 'warning' THEN 1
							WHEN model_190 = 'disagreed' THEN 2
							WHEN model_190 = 'agreed' THEN 3
							WHEN model_190 = 'pending' THEN 4
							WHEN model_190 = 'presented' THEN 5
							WHEN model_190 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_347 = 'required' THEN 0
							WHEN model_347 = 'warning' THEN 1
							WHEN model_347 = 'disagreed' THEN 2
							WHEN model_347 = 'agreed' THEN 3
							WHEN model_347 = 'pending' THEN 4
							WHEN model_347 = 'presented' THEN 5
							WHEN model_347 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_390 = 'required' THEN 0
							WHEN model_390 = 'warning' THEN 1
							WHEN model_390 = 'disagreed' THEN 2
							WHEN model_390 = 'agreed' THEN 3
							WHEN model_390 = 'pending' THEN 4
							WHEN model_390 = 'presented' THEN 5
							WHEN model_390 = 'not-required' THEN 6
							ELSE 7
						END
					) 
				) * 100 / 7 as model_avg

			FROM 
			(
				
				SELECT DISTINCT 
				-- id
				sel.id,

				-- seller name
				sel.name AS seller_name,

				-- shortname
				sel.shortname AS seller_shortname,

				-- email
				(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

				-- user name
				(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

				-- last login
				(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

				-- Num Invoices
				COUNT(DISTINCT inv.id) AS num_invoices,

				-- Num Pending Invoices
				COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

				-- Percentage Pending Invoices
				ROUND(COALESCE(
					100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
					0
				), 2) AS percentage_pending_invoices,

				-- Model 180
				MAX(
					CASE WHEN pm.model_180 IS NOT null 
					THEN pm.model_180 
					ELSE
						CASE WHEN
						inv.accounting_date >= first_month AND
						inv.accounting_date < last_month AND
						inv.tax_country_id = 'ES' AND
						irpf IS NOT NULL AND
						-- inv.total_irpf_euros > 0 AND
						inv.account_expenses_id = '621' AND
						inv.transaction_type_id = 'local-expense'
						THEN 'required' ELSE 'not-required' END
					END
				) as model_180,

				-- Model 190
				MAX(
					CASE WHEN pm.model_190 IS NOT null 
					THEN pm.model_190 
					ELSE
						CASE WHEN
						inv.accounting_date >= first_month AND
						inv.accounting_date < last_month AND
						inv.tax_country_id = 'ES' AND
						irpf IS NOT NULL AND
						-- inv.total_irpf_euros > 0 AND
						inv.account_expenses_id != '621' AND
						inv.transaction_type_id = 'local-expense'
						THEN 'required' ELSE 'not-required' END
					END
				) as model_190,

				-- Model 347
				MAX(
					CASE WHEN pm.model_347 IS NOT null 
					THEN pm.model_347
					ELSE
						CASE WHEN
						cust_total IS NOT NULL OR prov_total IS NOT NULL
						THEN 'required' ELSE 'not-required' END
					END
				) as model_347,

				-- Model 390
				MAX(
					CASE WHEN pm.model_390 IS NOT null 
					THEN pm.model_390
					ELSE
						'required' 
					END
				) as model_390

				FROM sellers_seller sel
				LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
				-- LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
				LEFT JOIN (
					SELECT DISTINCT
						inv_irpf.id AS irpf_invoice_id
					FROM
						invoices_invoice inv_irpf
					INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
					WHERE
						con_irpf.irpf_euros <> 0
				) irpf ON irpf.irpf_invoice_id = inv.id
				LEFT JOIN (
					SELECT
					sel.id as seller_id,
					MAX(CASE WHEN pm.model_id::text = 'ES-180' THEN pm.status_id END) as model_180,
					MAX(CASE WHEN pm.model_id::text = 'ES-190' THEN pm.status_id END) as model_190,
					MAX(CASE WHEN pm.model_id::text = 'ES-347' THEN pm.status_id END) as model_347,
					MAX(CASE WHEN pm.model_id::text = 'ES-390' THEN pm.status_id END) as model_390
					FROM sellers_seller sel
					LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
					WHERE pm.year = date_year AND pm.period_id = date_period AND pm.country_id = 'ES'
					GROUP BY sel.id		
				) AS pm ON sel.id = pm.seller_id
				LEFT JOIN (
					SELECT DISTINCT sel.id, sel.shortname, sel.oss AS seller_oss, inv.is_oss AS invoice_oss
					FROM sellers_seller sel
					INNER JOIN invoices_invoice inv ON sel.id = inv.seller_id
					WHERE sel.contracted_accounting = True 
					AND sel.oss IS NOT True
					AND inv.is_oss IS True
					AND inv.accounting_date >= first_month
					AND inv.accounting_date < last_month
					GROUP BY sel.id, inv.is_oss
					ORDER BY sel.id
				) AS oss ON oss.id = sel.id

				-- devuelve los clientes que tienen facturas con concepto mayor que 3005.06
				LEFT JOIN(
					SELECT
						cust.id,
						cust.seller_id
					FROM
						customers_customer cust
					INNER JOIN invoices_invoice inv ON inv.customer_id = cust.id
					INNER JOIN invoices_concept con ON con.invoice_id = inv.id
					LEFT JOIN (
						SELECT DISTINCT
							inv_irpf.id AS irpf_invoice_id
						FROM
							invoices_invoice inv_irpf
						INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
						WHERE
							con_irpf.irpf_euros <> 0
					) irpf ON irpf.irpf_invoice_id = inv.id
					WHERE
						inv.accounting_date >= first_month AND inv.accounting_date < last_month
						AND NOT (inv.transaction_type_id LIKE 'intra-community-%'
								OR inv.transaction_type_id LIKE 'import-%'
								OR inv.transaction_type_id LIKE '%-transfer')
						AND irpf.irpf_invoice_id IS NULL
						AND cust.name != 'Clientes Particulares'
					GROUP BY
						cust.id
					HAVING
						SUM(con.total_euros) > 3005.06
					ORDER BY
						cust.seller_id
				) AS cust_total ON cust_total.seller_id = sel.id

				-- devuelve los proveedores que tienen facturas con concepto mayor que 3005.06
				LEFT JOIN(
					SELECT
						prov.id,
						prov.seller_id
					FROM
						providers_provider prov
					INNER JOIN invoices_invoice inv ON inv.customer_id = prov.id
					INNER JOIN invoices_concept con ON con.invoice_id = inv.id
					LEFT JOIN (
						SELECT DISTINCT
							inv_irpf.id AS irpf_invoice_id
						FROM
							invoices_invoice inv_irpf
						INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
						WHERE
							con_irpf.irpf_euros <> 0
					) irpf ON irpf.irpf_invoice_id = inv.id
					WHERE
						inv.accounting_date >= first_month AND inv.accounting_date < last_month
						AND NOT (inv.transaction_type_id LIKE 'intra-community-%'
								OR inv.transaction_type_id LIKE 'import-%'
								OR inv.transaction_type_id LIKE '%-transfer')
						AND irpf.irpf_invoice_id IS NULL
					GROUP BY
						prov.id
					HAVING
						SUM(con.total_euros) > 3005.06
					ORDER BY
						prov.seller_id
				) AS prov_total ON prov_total.seller_id = sel.id
				-- WHERE sel.contracted_accounting = True OR sel.oss IS True
				WHERE
					(sel.legal_entity != 'llc' AND sel.contracted_accounting = True)
						OR
					(sel.legal_entity != 'llc' AND sel.oss IS True)

				GROUP BY sel.id
				ORDER BY sel.id
			) AS subselect
			
			
			
    LOOP
        result_json := result_json || jsonb_build_object(
            'seller_id', inv_data.id,
            'seller_name', inv_data.seller_name,
            'shortname', inv_data.seller_shortname,
            'email', inv_data.email,
            'user_name', inv_data.user_name,
            'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
            'num_pending_invoices', inv_data.num_pending_invoices,
            'percentage_pending_invoices', inv_data.percentage_pending_invoices,
            'model_180', inv_data.model_180,
            'model_190', inv_data.model_190,
            'model_347', inv_data.model_347,
            'model_390', inv_data.model_390,
            'model_min', inv_data.model_min,
            'model_avg', inv_data.model_avg			
        );
        
    END LOOP;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;
-- SELECT * FROM func_management_ES_yearly_list_json(2023, '0A');