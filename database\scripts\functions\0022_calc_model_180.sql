-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_180' ) THEN
	  DROP FUNCTION func_calc_model_es_180(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_180(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE

CA01 INTEGER:= 0;
CA02 NUMERIC:= 0;
CA03 NUMERIC:= 0;
total NUMERIC :=0;

short_name VARCHAR := '';
total_providers INTEGER := 0;
inv_amount RECORD;
inv_irpf RECORD;
irpf_row RECORD;
json_result JSONB;


--VARIABLES PARA CONSTRUIR EL 2o JSON
tmp_key VARCHAR := '';
tmp_value VARCHAR := '';
OP NUMERIC := 1;
PAG NUMERIC := 2;
tmp_json JSONB;
json_perceptores JSONB;


BEGIN

	-- ASIGNAR VALORES A LAS VARIABLES 'shortname'
	SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

	-- TOTAL PROVIDERS
	SELECT COUNT(DISTINCT inv.provider_id) as total_providers
	INTO total_providers
	FROM invoices_invoice inv
	INNER JOIN invoices_concept con ON con.invoice_id = inv.id
	WHERE inv.seller_id = sellerid
	AND EXTRACT(YEAR FROM accounting_date) = date_year
	AND EXTRACT(MONTH FROM accounting_date) >= month_min
	AND EXTRACT(MONTH FROM accounting_date) <= month_max
	AND status_id = 'revised'
	AND tax_country_id = 'ES'
	AND (transaction_type_id LIKE 'local-expense' OR transaction_type_id LIKE 'local-credit')
	AND con.irpf > 0
	AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
	AND con.is_supplied IS NOT true
	AND inv.account_expenses_id = '621';

	-- AMOUNT
	SELECT
			common_expenses.common_amount AS common_expenses_amount,
			amz_expenses.amz_amount AS amz_expenses_amount,
			common_credit.common_amount AS common_credit_amount,
			amz_credit.amz_amount AS amz_credit_amount,
			common_expenses.common_amount + amz_expenses.amz_amount + common_credit.common_amount + amz_credit.amz_amount AS sum_amount
	INTO inv_amount
	FROM
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-expense'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-expense'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_txt_amz IS true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-credit'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS common_credit,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-credit'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_txt_amz IS true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS amz_credit;

	-- IRPF
	SELECT
		common_expenses.common_irpf AS common_expenses_irpf,
		amz_expenses.amz_irpf AS amz_expenses_irpf,
		common_credit.common_irpf AS common_credit_irpf,
		amz_credit.amz_irpf AS amz_credit_irpf,
		common_expenses.common_irpf + amz_expenses.amz_irpf + common_credit.common_irpf + amz_credit.amz_irpf AS sum_irpf
	INTO inv_irpf
	FROM
	(
		SELECT DISTINCT COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) as common_irpf
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-expense'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.irpf / 100),0) as amz_irpf
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-expense'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_txt_amz IS true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS amz_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) as common_irpf
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-credit'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS common_credit,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.irpf / 100),0) as amz_irpf
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND transaction_type_id LIKE 'local-credit'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND is_txt_amz IS true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND invoice_type_id = 'rent'
		AND con.is_supplied IS NOT true
	) AS amz_credit;


-- CALCULOS DE DATOS POR PROVEEDOR
json_perceptores := jsonb_build_object();
FOR irpf_row IN (
	SELECT DISTINCT
		sr.*,
		pv.name,
		pv.nif_cif_iva,
		rent_irpf.irpf_perc,
		rent_irpf.rent_id,
		--rent_irpf.inv_id,
		rent_irpf.inv_prov,
		SUM(ROUND( COALESCE(con_commom.common_amount::numeric,0) + COALESCE(con_amz.amz_amount::numeric,0), 2)) as sum_amount,
		SUM(ROUND( COALESCE(con_commom.common_irpf::numeric,0) + COALESCE(con_amz.amz_irpf::numeric,0), 2)) as sum_irpf
	FROM
	(
		SELECT DISTINCT
		con.irpf AS irpf_perc,
		con.id AS con_id,
		inv.id AS inv_id, 
		rent.id AS rent_id,
		inv.provider_id AS inv_prov
		FROM sellers_sellerrental rent
		INNER JOIN invoices_invoice inv ON inv.seller_rental_id = rent.id
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE rent.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND inv.status_id = 'revised'
		AND inv.tax_country_id = 'ES'
		AND con.irpf > 0
		AND inv.account_expenses_id = '621'
		AND (inv.transaction_type_id LIKE 'local-expense' OR inv.transaction_type_id LIKE 'local-credit')
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND con.is_supplied IS NOT true
		GROUP BY  inv.id, con.irpf,  rent.id, con.id , inv.provider_id
		ORDER BY inv.id
	) AS rent_irpf
	LEFT JOIN(
		SELECT DISTINCT 
			ROUND(COALESCE(SUM(con.amount_euros * con.quantity),0)::numeric, 2) as common_amount,
			ROUND(COALESCE(SUM((con.amount_euros * con.irpf/100)* con.quantity),0)::numeric, 2) as common_irpf,
			con.id as con_id,
			con.irpf AS con_irf,
			inv.seller_rental_id
			FROM invoices_invoice inv
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			WHERE inv.seller_id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND status_id = 'revised'
			AND tax_country_id = 'ES'
			AND (inv.transaction_type_id LIKE 'local-expense' OR inv.transaction_type_id LIKE 'local-credit')
			AND con.irpf > 0
			AND inv.account_expenses_id = '621'
			AND is_generated_amz IS NOT true
			AND is_txt_amz IS NOT true
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
			AND con.is_supplied IS NOT true
		GROUP BY inv.seller_rental_id, con.id, con.irpf
	) AS con_commom ON (rent_irpf.con_id = con_commom.con_id AND rent_irpf.irpf_perc = con_commom.con_irf)
	LEFT JOIN(
		SELECT DISTINCT 
			ROUND(COALESCE(SUM(con.amount_euros),0)::numeric, 2) as amz_amount,
			ROUND(COALESCE(SUM((con.amount_euros * con.irpf/100)),0)::numeric, 2) as amz_irpf,
			con.id as con_id,
			con.irpf AS con_irf,
			inv.seller_rental_id
			FROM invoices_invoice inv
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			WHERE inv.seller_id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND status_id = 'revised'
			AND tax_country_id = 'ES'
			AND (inv.transaction_type_id LIKE 'local-expense' OR inv.transaction_type_id LIKE 'local-credit')
			AND con.irpf > 0
			AND inv.account_expenses_id = '621'
			AND is_generated_amz IS NOT true
			AND is_txt_amz IS  true
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
			AND con.is_supplied IS NOT true
		GROUP BY inv.seller_rental_id, con.id, con.irpf
	) AS con_amz ON (rent_irpf.con_id = con_amz.con_id AND rent_irpf.irpf_perc = con_amz.con_irf)
	LEFT JOIN sellers_sellerrental sr ON sr.id = rent_id
	LEFT JOIN providers_provider pv ON pv.id = inv_prov
	GROUP BY rent_id, irpf_perc, sr.id, rent_irpf.inv_prov, pv.name, pv.nif_cif_iva
) LOOP

	IF OP < 5 THEN
		-- NIF PROVEEDOR
		IF irpf_row.nif_cif_iva IS NULL THEN
			irpf_row.nif_cif_iva := '';
		END IF;
		tmp_value := irpf_row.nif_cif_iva;
		tmp_key := 'NIF_PROV_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		-- NOMBRE PROVEEDOR
		IF irpf_row.name IS NULL THEN
			irpf_row.name := '';
		END IF;
		tmp_value := irpf_row.name;
		tmp_key := 'NOMBRE_PROV_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		-- PROVINCIA (CÓDIGO)
		IF irpf_row.first_char_province_id IS NULL THEN
			irpf_row.first_char_province_id := '';
		END IF;
		tmp_value := irpf_row.first_char_province_id;
		tmp_key := 'COD_PROVINCIA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--MODALIDAD
		IF irpf_row.modality IS NULL THEN
			irpf_row.modality := '';
		END IF;
		tmp_value := irpf_row.modality;
		tmp_key := 'MODALIDAD_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--BASE DE RETENCIONES E INGRESOS A CUENTA
		tmp_value := irpf_row.sum_amount;
		tmp_key := 'BASE_RETENCIONES_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--% RETENCIONES
		tmp_value := irpf_row.irpf_perc;
		tmp_key := 'PORCENTAJE_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--SUMA RETENCIONES E INGRESOS A CUENTA
		tmp_value := irpf_row.sum_irpf;
		tmp_key := 'SUMA_RETENCIONES_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--EJERCICIO DEVENGO
		tmp_value := date_year;
		tmp_key := 'EJERCICIO_DEVENGO_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--SITUACIÓN
		IF irpf_row.situation_id IS NULL THEN
			irpf_row.situation_id := '';
		END IF;
		tmp_value := irpf_row.situation_id;
		tmp_key := 'SITUACION_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--REFERECIA CATASTRAL
		IF irpf_row.catastral_reference IS NULL THEN
			irpf_row.catastral_reference := '';
		END IF;
		tmp_value := irpf_row.catastral_reference;
		tmp_key := 'REF_CATASTRAL_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--TIPO DE VIA
		IF irpf_row.type_road_id IS NULL THEN
			irpf_row.type_road_id := '';
		END IF;
		tmp_value := irpf_row.type_road_id;
		tmp_key := 'TIPO_VIA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--NOMBRE DE LA VIA
		IF irpf_row.name_road IS NULL THEN
			irpf_row.name_road := '';
		END IF;
		tmp_value := irpf_row.name_road;
		tmp_key := 'NOMBRE_VIA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--TIPO NUM
		IF irpf_row.type_number IS NULL THEN
			irpf_row.type_number := '';
		END IF;
		tmp_value := irpf_row.type_number;
		tmp_key := 'TIPO_NUM_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--NUM CASA
		IF irpf_row.num_house IS NULL THEN
			irpf_row.num_house := '';
		END IF;
		tmp_value := irpf_row.num_house;
		tmp_key := 'NUM_CASA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--CALIF.NU
		IF irpf_row.calif_nu IS NULL THEN
			irpf_row.calif_nu := '';
		END IF;
		tmp_value := irpf_row.calif_nu;
		tmp_key := 'CALIF_NU_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--BLOQUE
		IF irpf_row.bloq IS NULL THEN
			irpf_row.bloq := '';
		END IF;
		tmp_value := irpf_row.bloq;
		tmp_key := 'BLOQUE_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--PORTAL
		IF irpf_row.portal IS NULL THEN
			irpf_row.portal := '';
		END IF;
		tmp_value := irpf_row.portal;
		tmp_key := 'PORTAL_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--ESCALERA
		IF irpf_row.stair IS NULL THEN
			irpf_row.stair := '';
		END IF;
		tmp_value := irpf_row.stair;
		tmp_key := 'ESCALERA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--PLANTA
		IF irpf_row.floor IS NULL THEN
			irpf_row.floor := '';
		END IF;
		tmp_value := irpf_row.floor;
		tmp_key := 'PLANTA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--PUERTA
		IF irpf_row.door IS NULL THEN
			irpf_row.door := '';
		END IF;
		tmp_value := irpf_row.door;
		tmp_key := 'PUERTA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--COMPLEMENTO DOMICILIARIO
		IF irpf_row.domiciliary_complement IS NULL THEN
			irpf_row.domiciliary_complement := '';
		END IF;
		tmp_value := irpf_row.domiciliary_complement;
		tmp_key := 'COMP_DOM_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		-- --LOCALIDAD/POBLACIÓN
		IF irpf_row.location IS NULL THEN
			irpf_row.location := '';
		END IF;
		tmp_value := irpf_row.location;
		tmp_key := 'LOCALIDAD_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--NOMBEW MUNICIPIO
		IF irpf_row.name_municipality_id IS NULL THEN
			irpf_row.name_municipality_id := '';
		END IF;
		SELECT description INTO tmp_value FROM dictionaries_municipalitycode WHERE code = irpf_row.name_municipality_id LIMIT 1;
		tmp_key := 'NOMBRE_MUNICIPIO_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--CODIGO MUNICIPIO
		IF irpf_row.name_municipality_id IS NULL THEN
			irpf_row.name_municipality_id := '';
		END IF;
		SELECT cod_mun INTO tmp_value FROM dictionaries_municipalitycode WHERE code = irpf_row.name_municipality_id LIMIT 1;
		tmp_key := 'CODIGO_MUNICIPIO_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--PROVINCIA
		IF irpf_row.province_id IS NULL THEN
			irpf_row.province_id := '';
		END IF;
		SELECT description INTO tmp_value FROM dictionaries_provincecode WHERE code = irpf_row.province_id LIMIT 1;
		tmp_key := 'PROVINCIA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--CODIGO PROVINCIA
		IF irpf_row.province_id IS NULL THEN
			irpf_row.province_id := '';
		END IF;
		tmp_value := irpf_row.province_id;
		tmp_key := 'COD_PROVINCIA_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		--CODIGO POSTAL
		IF irpf_row.postal_code IS NULL THEN
			irpf_row.postal_code := '';
		END IF;
		tmp_value := irpf_row.postal_code;
		tmp_key := 'COD_POSTAL_' ||OP || '_PAG_' || PAG;
		tmp_json := jsonb_build_object(tmp_key, tmp_value);
		json_perceptores := jsonb_concat(json_perceptores, tmp_json);

		-- INCREMENTAR OPERADOR
		OP := OP + 1;
	END IF;
	IF OP = 5 THEN
		-- INCREMENTAR PAGINA
		PAG := PAG + 1;
		-- RESETEAR OPERADOR
		OP := 1;
	END IF;
	total := total + 1;
END LOOP;

--CASILLA 01: Número total de perceptores incluidos. 
CA01 := total;
	
--CASILLA 02: Suma de todas las bases que han tenido retención. Todas las facturas TAX COUNTRY ES, con retención de IRPF y que tengan cuenta contable 621.
CA02 := inv_amount.sum_amount;
CA02 := ROUND( CA02 , 2 );

--CASILLA 03: Suma de todas las retenciones de IRPF del año que sean alquileres (cuenta contable 621)
CA03 := inv_irpf.sum_irpf;
CA03 := ROUND( CA03 , 2 );


-- MAKE JSON
json_result := jsonb_build_object(
	'CA01', CA01,
	'CA02', CA02,
	'CA03', CA03,
	'total', total
);


-- RETURN JSON
	RETURN json_result || json_perceptores;
END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION

--SELECT func_calc_model_es_180(5, 2024, 01, 12);