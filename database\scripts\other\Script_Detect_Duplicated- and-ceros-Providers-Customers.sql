

WITH Duplicados AS (
    SELECT pp.nif_cif_iva
    FROM providers_provider pp
    INNER JOIN sellers_seller ss ON pp.seller_id = ss.id
    WHERE pp.nif_cif_iva IS NOT NULL
    GROUP BY pp.nif_cif_iva, ss.shortname
    HAVING COUNT(*) > 1
)
UPDATE providers_provider
SET nif_cif_iva = NULL
WHERE nif_cif_iva IN (SELECT nif_cif_iva FROM Duplicados);



/*el nombre corto del vendedor, el nombre del proveedor, la referencia de la factura asociada y el nombre de usuario del gestor de esa factura, para cada proveedor cuyo nif_cif_iva esté compuesto exclusivamente por ceros.*/

SELECT
  ss.shortname AS "Shortname",
  pp.name AS "Proveedor",
  ii.reference AS "FacturaReference",
  uu.username AS "Username"
FROM providers_provider pp
INNER JOIN sellers_seller ss ON pp.seller_id = ss.id
LEFT JOIN invoices_invoice ii ON pp.id = ii.provider_id AND ss.id = ii.seller_id
LEFT JOIN invoices_invoicehistory ih ON ii.id = ih.original_invoice_id_id
LEFT JOIN users_user uu ON ih.user_manager_id = uu.id
WHERE LENGTH(REPLACE(pp.nif_cif_iva, '0', '')) = 0;





/*script simplificado, proveedores con x numero de caracteres dstintos a 0*/

SELECT
  sellers_seller.shortname AS "Shortname",
  providers_provider.name AS "Proveedor",
  providers_provider.nif_cif_iva AS "NIF/CIF/IVA"
FROM providers_provider
INNER JOIN sellers_seller ON providers_provider.seller_id = sellers_seller.id
WHERE
  LENGTH(REPLACE(providers_provider.nif_cif_iva, '0', '')) = 0;




/*script simplificado, clientes con x numero de caracteres dstintos a 0*/

SELECT
  sellers_seller.shortname AS "Shortname",
  customers_customer.name AS "Cliente",
  customers_customer.nif_cif_iva AS "NIF/CIF/IVA"
FROM customers_customer
INNER JOIN sellers_seller ON customers_customer.seller_id = sellers_seller.id
WHERE
  LENGTH(REPLACE(customers_customer.nif_cif_iva, '0', '')) = 0;


-- UPDATE providers_provider
-- SET nif_cif_iva = NULL
-- WHERE LENGTH(REPLACE(nif_cif_iva, '0', '')) = 0;

-- UPDATE customers_customer
-- SET nif_cif_iva = NULL
-- WHERE LENGTH(REPLACE(nif_cif_iva, '0', '')) = 0;




/*script para obtener los cif de proveedores duplicados en cada seller*/

SELECT
  ss.shortname AS "Shortname",
  pp.nif_cif_iva AS "NIF/CIF/IVA",
  COUNT(*) AS "Cantidad"
FROM providers_provider pp
INNER JOIN sellers_seller ss ON pp.seller_id = ss.id
WHERE pp.nif_cif_iva IS NOT NULL
GROUP BY ss.shortname, pp.nif_cif_iva
HAVING COUNT(*) > 1;


/*script para obtener los cif de clientes duplicados en cada seller*/

SELECT
  ss.shortname AS "Shortname",
  pp.nif_cif_iva AS "NIF/CIF/IVA",
  COUNT(*) AS "Cantidad"
FROM customers_customer pp
INNER JOIN sellers_seller ss ON pp.seller_id = ss.id
WHERE pp.nif_cif_iva IS NOT NULL
GROUP BY ss.shortname, pp.nif_cif_iva
HAVING COUNT(*) > 1;






SELECT
  sellers_seller.shortname AS "Seller",
 /* seller_id AS "ID del seller",*/
  nif_cif_iva AS "NIF/CIF/IVA",
  /*providers_provider.name AS "Proveedor",*/
  COUNT(*) AS "Cantidad"
  
FROM providers_provider
INNER JOIN sellers_seller ON sellers_seller.id = providers_provider.seller_id
GROUP BY seller_id,sellers_seller.shortname, providers_provider.nif_cif_iva, providers_provider.name
HAVING COUNT(*) > 1;


SELECT
  sellers_seller.shortname AS "Vendedor",
  /*seller_id AS "ID del vendedor",*/
  nif_cif_iva AS "NIF/CIF/IVA",
  /*customers_customer.name AS "Cliente",*/
  COUNT(*) AS "Cantidad"
  
FROM customers_customer
INNER JOIN sellers_seller ON sellers_seller.id = customers_customer.seller_id
GROUP BY seller_id,sellers_seller.shortname, customers_customer.nif_cif_iva, customers_customer.name
HAVING COUNT(*) > 1;


/*SELECT all ceros proveedor*/

SELECT
  sellers_seller.shortname AS "Shortname",
  providers_provider.name AS "Proveedor",
  providers_provider.nif_cif_iva AS "NIF/CIF/IVA"
FROM providers_provider
INNER JOIN sellers_seller ON providers_provider.seller_id = sellers_seller.id
WHERE
  SUBSTRING(providers_provider.nif_cif_iva, 1, 1) = '0'
  AND CASE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    WHEN SUBSTRING(providers_provider.nif_cif_iva, 2, 1) = '0' THEN TRUE
    ELSE FALSE
  END = TRUE


  /*SELECT todos los proveedores que tienen ceros en el cif y facturas de tipo no ticket DE ESPAÑA*/

SELECT
  s.shortname AS "Shortname",
  p.name AS "Proveedor",
  p.nif_cif_iva AS "NIF/CIF/IVA",
  i.invoice_type_id AS "Invoice Type ID"
FROM providers_provider AS p
INNER JOIN sellers_seller AS s ON p.seller_id = s.id
INNER JOIN invoices_invoice AS i ON p.id = i.provider_id
WHERE
  SUBSTRING(p.nif_cif_iva, 1, 1) = '0'
  AND SUBSTRING(p.nif_cif_iva, 2, 1) IN ('0')
  AND i.invoice_type_id != 'ticket'
  AND p.country_id = 'ES'
GROUP BY s.shortname, p.name, p.nif_cif_iva, i.invoice_type_id;


  /*SELECT todos los proveedores que tienen ceros en el cif y facturas de tipo no ticket*/

  SELECT
  sellers_seller.shortname AS "Shortname",
  providers_provider.name AS "Proveedor",
  providers_provider.nif_cif_iva AS "NIF/CIF/IVA",
  invoices_invoice.invoice_type_id AS "Invoice Type ID"
FROM providers_provider
INNER JOIN sellers_seller ON providers_provider.seller_id = sellers_seller.id
INNER JOIN invoices_invoice ON providers_provider.id = invoices_invoice.provider_id
WHERE
  SUBSTRING(providers_provider.nif_cif_iva, 1, 1) = '0'
  AND SUBSTRING(providers_provider.nif_cif_iva, 2, 1) IN ('0')
  AND invoices_invoice.invoice_type_id != 'ticket'
GROUP BY sellers_seller.shortname, providers_provider.name, providers_provider.nif_cif_iva, invoices_invoice.invoice_type_id


  /*SELECT todos los customers que tienen ceros en el cif y facturas de tipo no ticket*/
SELECT
  sellers_seller.shortname AS "Shortname",
  customers_customer.name AS "Cliente",
  customers_customer.nif_cif_iva AS "NIF/CIF/IVA",
  invoices_invoice.invoice_type_id AS "Invoice Type ID"
FROM customers_customer
INNER JOIN sellers_seller ON customers_customer.seller_id = sellers_seller.id
INNER JOIN invoices_invoice ON customers_customer.id = invoices_invoice.customer_id
WHERE
  SUBSTRING(customers_customer.nif_cif_iva, 1, 1) = '0'
  AND SUBSTRING(customers_customer.nif_cif_iva, 2, 1) IN ('0')
  AND invoices_invoice.invoice_type_id != 'ticket'
GROUP BY sellers_seller.shortname, customers_customer.name, customers_customer.nif_cif_iva, invoices_invoice.invoice_type_id


 /*SELECT para cada seller, busque proveedores que tengan el mismo NIF en los últimos 5 dígitos*/

SELECT
  s.shortname AS "Vendedor",
  p1.name AS "Proveedor1",
  p1.nif_cif_iva AS "NIF/CIF/IVA1",
  p2.name AS "Proveedor2",
  p2.nif_cif_iva AS "NIF/CIF/IVA2"
FROM sellers_seller s
INNER JOIN providers_provider p1 ON s.id = p1.seller_id
INNER JOIN providers_provider p2 ON s.id = p2.seller_id
WHERE
  p1.id <> p2.id
  AND RIGHT(p1.nif_cif_iva, 5) = RIGHT(p2.nif_cif_iva, 5);
