# 📄 Documentación Técnica – Formulario IVA [-- Template Principal --] (`formIVA`)

## 1. Estructura General

El formulario está dividido modularmente en bloques reutilizables. Se basa en el uso de `base.html` y define los siguientes `blocks` principales:

- `title`: <PERSON><PERSON><PERSON><PERSON> de la página.
- `stylesheets`: Importación de CSS (Select2, DataTables, FontAwesome, SweetAlert, etc.).
- `breadcrumb`: Barra de navegación (include separado).
- `content`: Contenido HTML del formulario dividido en pestañas.
- `javascripts`: Definición de scripts JS, variables globales, y objetos funcionales.

---

## 2. Tabs y navegación

### Tabs principales
Se usan `nav-tabs` de Bootstrap para dividir el formulario en 4 pestañas:

- `general`: Información de empresa y países contratados
- `members`: Información de socios
- `documents`: Documentación por país
- `summary`: Resumen y confirmación final

### Tabs internos
En `migration_info_form.html` y `country_documents.html` se usan tabs internos para gestionar múltiples países con formularios separados.

---

## 3. Formularios incluidos

### `company_info_form.html`
Formulario principal con los datos generales de la empresa.

### `migration_info_form.html`
Un formulario por país con mantenimiento contratado.

### `country_documents.html`
Un formulario por país con preguntas condicionales.

### `seller_vat_partners.html`
Modal emergente para agregar y validar socios.

---

## 4. Variables globales y contexto JS

Se definen objetos en `window` para mantener estados compartidos y facilitar modularidad como:

```js
window.formIds = {
    company: "company_info_form",
    migration: (id) => `${id}`,
    document: (id) => `${id}`,
    partners: "addPartnerModal",
};
```

Contextos por módulo: `contextCompany`, `contextMigration`, `contextDocuments`.

---

## 5. Objeto principal: `window.generalFormModule`

Agrupa toda la funcionalidad de `formIVA`:

```js
window.generalFormModule = {
    generalMain,
    generalHandlers,
    generalDynamic,
    generalStyles,
    generalDebugger,
    generalSubmitIVA,
    generalLock
};
```

### Principales responsabilidades
- `generalMain`: Carga de datos, ajustes.
- `generalHandlers`: Observadores y eventos.
- `generalDynamic`: Tooltips y etiquetas.
- `generalStyles`: UI y visibilidad.
- `generalSubmitIVA`: Guardado y envío.
- `generalLock`: Bloqueo según validación.
- `generalDebugger`: Logs.

---

## 6. Inicialización `DOMContentLoaded`

Se inicializan:
- Tooltips
- Etiquetas dinámicas
- Observadores
- Validaciones por país
- Restauración de pestañas secundarias

---

## 7. Guardado y Envío

### Guardado parcial
```js
generalFormModule.generalSubmitIVA.saveForm()
```

### Envío final
```js
generalFormModule.generalSubmitIVA.processedFormularioIVA()
```

---

## 8. Validación

- Requeridos
- Documentos
- Porcentaje de socios
- Archivos ya cargados vs nuevos

Herramientas de debugging visual: `logFormFieldsStatus`, `printMigrationDataByCountry`, etc.

---

## 9. Fortalezas técnicas

| Aspecto | Descripción |
|--------|-------------|
| Modularidad | Componentes reutilizables y mantenibles |
| Debugging | Herramientas internas de depuración |
| Compatibilidad | Preparado para múltiples países y variantes |
| Accesibilidad | Tooltips, validaciones visuales |
| Seguridad | CSRF, validaciones backend y frontend |

---