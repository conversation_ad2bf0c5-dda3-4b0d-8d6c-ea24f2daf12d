from django.urls import path
from muaytax.app_providers import views

app_name = "app_providers"
urlpatterns = [
    path(
        "sellers/<shortname>/providers/",
        view=views.ProviderListView.as_view(),
        name="list",
    ),
    path(
        "sellers/<shortname>/providers/new/",
        view=views.ProviderNewView.as_view(),
        name="new",
    ),
    path(
        "sellers/<shortname>/providers/<pk>/",
        view=views.ProviderDetailView.as_view(),
        name="detail",
    ),
    path(
        "sellers/<shortname>/providers/<pk>/delete/",
        view=views.ProviderDeleteView.as_view(),
        name="delete",
    ),
    path(
        "sellers/<shortname>/providers/~update/",
        view=views.ProviderUpdateView.as_view(),
        name="update",
    ),
]
