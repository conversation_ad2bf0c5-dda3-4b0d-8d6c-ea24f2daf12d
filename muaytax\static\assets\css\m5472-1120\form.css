.bg-tab-light {
  background: #f2f2f2!important;
}
.btn-icon-custom {
  width: 30px!important;
  height: 30px!important;
  padding: 5px!important;
}

.hidden {
  display: none;
}

.row-element {
  margin: 5px 0px;
  padding-top: 10px;
}

.row-activity {
  padding-top: 5px;
  padding-bottom: 10px;
}

.accordion-section {
  margin: 10px;
  padding: 10px 0px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.accordion-section h5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  cursor: pointer;
}

.toggle-icon {
  font-size: 20px;
  margin-left: 10px;
}

.col-custom {
  width: 20%; /* 100% / 5 */
  float: left;
}

.tooltip-icon {
  position: relative;
  cursor: pointer;
}

fieldset.form-borders {
  border: 1px groove #838383 !important;
  padding: 0 1.4em 1.4em 1.4em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000;
}

legend.form-borders {
  text-align: left !important;
  width: inherit; /* Or auto */
  padding: 0 10px; /* To give a bit of padding on the left and right */
  border-bottom: none;
  float: unset !important;
}

.col-form-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* unset arrow */
#general input::-webkit-outer-spin-button,
#general input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

#percent_input input::-webkit-outer-spin-button,
#percent_input input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.spinner-grow.animation-delay-1 {
  animation: spinner-grow .7s .1s linear infinite;
  color: #04ac64 !important;
}

.spinner-grow.animation-delay-2 {
  animation: spinner-grow .7s .3s linear infinite;
  color: #36e093 !important;
}

.spinner-grow.animation-delay-3 {
  animation: spinner-grow .7s .5s linear infinite;
  color: #04ac64 !important;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  display: none;
}

input[type="date"]::-webkit-inner-spin-button {
  display: none;
}

input[type="date"]::-webkit-clear-button {
  display: none;
}

.select2-container--default .select2-selection--single {
  height: 100% !important; /* Ajusta esto al alto de tus otros campos de formulario */
  border: 1px solid #ced4da;
}

.select2-container .select2-selection.is-invalid {
  border-color: #dc3545;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 2.93; /* Ajusta esto al alto de tus otros campos de formulario */
  padding-left: 16px;
}

.select2-container--default .select2-selection--multiple {
  border: 1px solid #ced4da;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  line-height: 2.05; /* Ajusta esto al alto de tus otros campos de formulario */
  padding-left: 16px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: #fff;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #03ad65;
}

.select2-selection__choice {
  display: flex;
  flex-direction: row-reverse !important;
}

.select2-selection__choice__remove {
  margin-left: 5px;
  margin-right: 0;
}

.select2-container .select2-selection__placeholder {
  color: #6c757d !important;
}

.select2-search__field:placeholder-shown {
  width: 100% !important; /*para mostrar el placeholder en el select multiple cuando no tiene seleccionado elementos*/
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 40px;
}

textarea {
  height: 6em !important;
  resize: none;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: .875em;
  color: #dc3545;
}

.report-hidden {
  display: none;
}

#secondTab.nav-tabs .nav-item.show .nav-link,
#secondTab.nav-tabs .nav-link.active {
  position: relative;
  background: #fff;
  color: #888;
  border-top: 1px solid #e0dcdc !important;
  border-right: 1px solid #e0dcdc !important;
  border-left: 1px solid #e0dcdc !important;
}

#secondTab.nav-tabs .nav-link {
  background: #efefef;
  border: none;
  color: #888;
}

#secondTab.nav-tabs .nav-item.show .nav-link::after,
#secondTab.nav-tabs .nav-link.active::after{
  content: "";
  background: #fff;
  display: block;
  height: 2px;
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
}


/* unset arrow */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* modificación del choices Select para tener igual height */
.choices[data-type*=select-one] .choices__inner {
  padding-bottom: 4.5px!important;
}
.is-focused .choices__inner,
.is-open .choices__inner {
  border-color: #86b7fe !important; }

.choices__inner {
  color: #495057 !important;
  font-size: 14px!important;
  display: inline-block;
  vertical-align: top;
  width: 100%;
  background-color: #fff;
  padding: 5.5px 7.5px 3.75px;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  min-height: 44px;
  overflow: hidden;
}
.choices__list--dropdown {
  color: #212529 !important;
}

.choices.is-disabled .choices__inner, .choices.is-disabled .choices__input {
  background-color: #e9ecef;
  cursor: not-allowed;
  -webkit-user-select: none;
  user-select: none;
}

/* modify disabled bootstrap inputs */
.form-control:disabled, .form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;

}

.form-check-input:disabled {
  cursor: not-allowed!important;
}

.select2-container--default.select2-container--disabled .select2-selection--multiple {
  cursor: not-allowed!important;
  pointer-events: none;
  background-color: #e9ecef!important;
}

.select2-container--default.select2-container--disabled .select2-selection--single {
  background-color: #e9ecef!important;
  cursor: not-allowed!important;
}


.tipoTransaccion{
  position: absolute;
  top: 0;
  left: 0;
  transform: translate(30%, -100%);
  z-index: 1000;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

}
.tipoTransaccion > p{
  font-size: 12px;
}