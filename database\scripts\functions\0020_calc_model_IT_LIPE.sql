-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_it_lipe' ) THEN
	  DROP FUNCTION func_calc_model_it_lipe(INTEGER,INTEGER,INTEGER,INTEGER,NUMERIC,NUMERIC, NUMERIC);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_it_lipe(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER, prev_vp14_1 NUMERIC, prev_vp14_2 NUMERIC, acconto_iva NUMERIC)
RETURNS varchar AS $$
DECLARE
    VP2 NUMERIC := 0;
    VP3 NUMERIC := 0;
    VP4 NUMERIC := 0;
    VP5 NUMERIC := 0;
    VP6 NUMERIC := 0;
    VP6_1_NUM NUMERIC := 0;
    VP6_2_NUM NUMERIC := 0;
    VP6_1 VARCHAR := '';
    VP6_2 VARCHAR := '';
    VP7 NUMERIC := 0;
    VP8 NUMERIC := 0;
    VP9 NUMERIC := 0;
    VP10 NUMERIC := 0;
    VP11 NUMERIC := 0;
    VP12 NUMERIC := 0;
    VP13 NUMERIC := 0;
    VP13_1 VARCHAR := '';
    VP13_2_NUM NUMERIC := 0;
    VP13_2 VARCHAR := '';
    VP14 NUMERIC := 0;
    VP14_1_NUM NUMERIC := 0;
    VP14_2_NUM NUMERIC := 0;
    VP14_1 VARCHAR := '';
    VP14_2 VARCHAR := '';

    short_name VARCHAR := '';
    quarter_id VARCHAR := '';
    vatanual_last_year_result RECORD;
    lipe_last_quarter RECORD;
    inv_amount RECORD;
    inv_vat RECORD;

BEGIN

    IF month_min = 1 THEN
        quarter_id := 'Q4';
    ELSIF month_min = 4 THEN
        quarter_id := 'Q1';
    ELSIF month_min = 7 THEN
        quarter_id := 'Q2';
    ELSIF month_min = 10 THEN
        quarter_id := 'Q3';
    END IF;

    -- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

    --RESULTADO DEL VAT-ANNUALE DEL AÑO ANTERIOR
    SELECT 
        result_id,
        json_pdf
    INTO vatanual_last_year_result 
    FROM documents_presentedmodel 
    WHERE seller_id = sellerid 
    AND year = date_year - 1 
    AND model_id = 'IT-VATANNUALE';

    --RESULTADO DEL LIPE DEL TRIMESTRE ANTERIOR
    SELECT
        is_paid,
        file
    INTO lipe_last_quarter
    FROM documents_presentedmodel
    WHERE seller_id = sellerid
    AND year = date_year
    AND model_id = 'IT-LIPE'
    AND period_id = quarter_id;
    
    --AMOUNT
    SELECT
        ROUND((common_sales.common_amount + amz_sales.amz_amount)::numeric, 2) AS sales_amount,
        ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric, 2) AS expenses_amount
    INTO inv_amount
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS amz_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount,
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros),0) as amz_amount,
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS amz_expenses;

    --VAT
    SELECT
        ROUND((common_sales.common_vat)::numeric, 2) + ROUND((amz_sales.amz_vat)::numeric, 2) AS sales_vat,
        ROUND((common_expenses.common_vat)::numeric, 2) + ROUND((amz_expenses.amz_vat)::numeric, 2) AS expenses_vat
    INTO inv_vat
    FROM
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND con.vat != 0
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'sales'
        AND is_generated_amz IS NOT true
        AND con.vat != 0
        AND is_txt_amz IS true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS amz_sales,
    (
        SELECT DISTINCT
            COALESCE(SUM(
                CASE
                    WHEN transaction_type_id = 'import-dua'
                    THEN con.amount_original * con.quantity * con.vat / 100
                    ELSE con.amount_euros * con.quantity * con.vat / 100
                END
            ),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND con.vat != 0
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT
            COALESCE(SUM(
                CASE
                    WHEN transaction_type_id = 'import-dua'
                    THEN con.amount_original * con.vat / 100
                    ELSE con.amount_euros * con.vat / 100
                END
            ),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'IT'
        AND invoice_category_id = 'expenses'
        AND con.vat != 0
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND ((transaction_type_id NOT LIKE 'oss' AND transaction_type_id NOT LIKE 'oss%' AND transaction_type_id NOT LIKE '%-transfer') OR transaction_type_id IS NULL)
        AND con.is_supplied IS NOT true
    ) AS amz_expenses;

    -- CASILLA VP2: total_amount de SALES (Base imponible) TAX COUNTRY IT
    VP2 := inv_amount.sales_amount;
    VP2 := ROUND( VP2 , 2);

    --CASILLA VP3: total_amount de EXPENSES (Base imponible) TAX COUNTRY IT
    VP3 := inv_amount.expenses_amount;
    VP3 := ROUND( VP3 , 2);

    --CASILLA VP4: total_vat de SALES (IVA) TAX COUNTRY IT
    VP4 := inv_vat.sales_vat;
    VP4 := ROUND( VP4 , 2);

    --CASILLA VP5: total_vat de EXPENSES (IVA) TAX COUNTRY IT
    VP5 := inv_vat.expenses_vat;
    VP5 := ROUND( VP5 , 2);

    --CASILLA VP6: VP4 - VP5
    VP6 := VP4 - VP5;
    VP6 := ROUND( VP6 , 2);
    IF VP6 > 0 THEN
        VP6_1_NUM := VP6;
        VP6_2_NUM := 0;
    ELSE
        VP6_1_NUM := 0;
        VP6_2_NUM := VP6;
    END IF;
    VP6_1_NUM := ROUND( VP6_1_NUM , 2);
    VP6_2_NUM := ROUND( VP6_2_NUM , 2);

    --CASILLA VP7: Siempre será 0, sin importar trimestre
    VP7 := 0;

    -------------------------------------CÁLCULOS ANTIGUOS---------------------------------------------------
    -- IF month_min = 1 THEN
    --     --CASILLA VP9: Del último trimestre anterior, el importe de la casilla 2 VP14 (VP14_2)
    --     VP9 := prev_vp14_2;
    --     VP9 := ROUND( VP9 , 2);
    -- ELSE
    -- --esto para q2,3,4 (que se haga)
    --     --CASILLA VP8: Se recupera del trimestre anterior el importe de la casilla 2 VP14 (VP14_2)
    --     VP8 := prev_vp14_2;
    --     VP8 := ROUND( VP8 , 2);
    -- END IF;
    -------------------------------------CÁLCULOS ANTIGUOS---------------------------------------------------


    --CASILLA VP9: (Sólo para el trimestre 1) Si el IVA anual del año anterior es a compensar, se toman en cuanta los crédito (casilla VX2_1 del VATANNUALE)
    IF month_min = 1 THEN
        IF vatanual_last_year_result.result_id = 'compensate' THEN
            VP9 := COALESCE(CAST(REPLACE(REPLACE(NULLIF(vatanual_last_year_result.json_pdf::jsonb->>'page12_VX5', ''), '.', ''), ',', '.') AS NUMERIC), 0);
            VP9 := ROUND( VP9 , 2);
        ELSE
            VP9 := 0;
        END IF;
    ELSE
        --CASILLA VP8: Se recupera del trimestre anterior el importe de la casilla 2 VP14 (VP14_2)
        VP8 := prev_vp14_2;
        VP8 := ROUND( VP8 , 2);
    END IF;

    --CASILLA VP12: Si hay importe en la casilla 1 VP6 se pone el 1% de ese importe. Si hay importe en la casilla 2 VP6, se deja en blanco.
    IF VP6_1_NUM > 0 THEN
        VP12 := VP6_1_NUM * 0.01;
    ELSE
        VP12 := 0;
    END IF;
    VP12 := ROUND( VP12 , 2);

    --CASILLA VP13: (Solo para el 4 trimestre) Casilla Metodo: 1. (Solo para el 4 trimestre) Casilla 2: el importe del "acconto IVA" del año que se ha pagado. Este es una "declaración" especial que se hará en diciembre
    IF month_min = 10 THEN
        VP13_1 := '1';
        VP13_2_NUM := acconto_iva; 
    END IF;


    -------------------------------------CÁLCULOS ANTIGUOS---------------------------------------------------
    -- --CASILLA VP14:
    -- IF VP6_1_NUM > 0 THEN
    --     VP14:= VP6_1_NUM + VP7 + VP12 - VP13_2_NUM;
    --     IF VP14 > 0 THEN
    --         VP14_1_NUM := VP14;
    --         VP14_1_NUM := ROUND( VP14_1_NUM , 2);
    --         VP14_2_NUM := 0;
    --     ELSE
    --         VP14_1_NUM := 0;
    --         VP14_2_NUM := VP14;
    --         VP14_2_NUM := ROUND( VP14_2_NUM , 2);
    --     END IF;
    -- ELSIF VP6_2_NUM > 0 THEN
    --     VP14:= VP6_2_NUM - VP7 - VP12 - VP13_2_NUM;
    --     VP14_1_NUM :=VP14;
    --     VP14_1_NUM := ROUND( VP14_1_NUM , 2);
    -- END IF;
    -------------------------------------CÁLCULOS ANTIGUOS---------------------------------------------------
    
    --CASILLA VP14:
    IF VP6_1_NUM > 0 THEN
        VP14:= VP6_1_NUM + VP7 + VP12 -VP8 - VP9 - VP13_2_NUM;
        IF VP14 > 0 THEN
            VP14_1_NUM := VP14;
            VP14_1_NUM := ROUND(VP14_1_NUM, 2);
            VP14_2_NUM := 0;
        ELSE
            VP14_1_NUM := 0;
            VP14_2_NUM := VP14;
            VP14_2_NUM := ROUND(VP14_2_NUM, 2);
        END IF;
    ELSIF VP6_2_NUM < 0 THEN
        VP14 := ABS(VP6_2_NUM) - VP7 + VP8 + VP9 - VP12 - VP13_2_NUM;
        IF VP14 > 0 THEN -- En este caso va al revés cuando la cifra es positiva va en VP14_2, si es negativa va a VP14_1
            VP14_1_NUM := 0;
            VP14_2_NUM := VP14;
            VP14_2_NUM := ROUND(VP14_2_NUM, 2);
        ELSE
            VP14_1_NUM := VP14;
            VP14_1_NUM := ROUND(VP14_1_NUM, 2);
            VP14_2_NUM := 0;
        END IF;
    ELSE
        VP14 := VP8 + VP9 - VP7 - VP12 - VP13_2_NUM;
        IF VP14 > 0 THEN
            VP14_1_NUM := 0;
            VP14_2_NUM := VP14;
            VP14_2_NUM := ROUND(VP14_2_NUM, 2);
        ELSE
            VP14_1_NUM := VP14;
            VP14_1_NUM := ROUND(VP14_1_NUM, 2);
            VP14_2_NUM := 0;
        END IF;
    END IF;

    --REEMPLAZAR VALORES 0 EN CASILLAS DONDE NO SE DEBE MOSTRAR NADA CUANDO EL VALOR SE INSERTA EN UNO DE LOS CAMPOS
    
    --CASILLA VP6
    IF VP6_1_NUM = 0 THEN
        VP6_1 := '';
    ELSE
        VP6_1 := VP6_1_NUM;
    END IF;

    IF VP6_2_NUM = 0 THEN
        VP6_2 := '';
    ELSE
        VP6_2 := VP6_2_NUM;
    END IF;

    --CASILLA VP13
    IF VP13_2_NUM = 0 THEN
        VP13_2 := '';
        VP13_1 := '';
    ELSE
        VP13_2 := VP13_2_NUM;
    END IF;
    --VP13_2 := VP13_2_NUM;


    --CASILLA VP14
    IF VP14_1_NUM = 0 THEN
        VP14_1 := '';
    ELSE
        VP14_1 := VP14_1_NUM;
    END IF;

    IF VP14_2_NUM = 0 THEN
        VP14_2 := '';
    ELSE
        VP14_2 := VP14_2_NUM;
    END IF;

    IF VP14_1_NUM = 0 AND VP14_2_NUM = 0 THEN
        VP14_1 := '0,0';
    END IF;

    -- RETURN JSON
    RETURN json_build_object(
        'Shortname', short_name, 
        'VP2', VP2, 
        'VP3', VP3, 
        'VP4', VP4, 
        'VP5', VP5, 
        -- 'VP6', VP6, 
        'VP6_1', VP6_1, 
        'VP6_2', VP6_2, 
        'VP7', VP7, 
        'VP8', VP8, 
        'VP9', VP9, 
        'VP10', VP10, 
        'VP11', VP11, 
        'VP12', VP12, 
        'VP13_1', VP13_1,
        'VP13_2', VP13_2, 
        'VP14', VP14, 
        'VP14_1', VP14_1, 
        'VP14_2', VP14_2
    )::varchar;
END;
$$ LANGUAGE plpgsql;

--PROBAR FUNCIÓN
--SELECT func_calc_model_it_lipe(62, 2023, 10, 12, 0.0, 0.0, 0.0);