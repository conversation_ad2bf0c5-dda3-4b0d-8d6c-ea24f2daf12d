import base64

from allauth.account.forms import SignupForm
from allauth.account.models import EmailAddress
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework import serializers

from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_admin import SellerAdministration
from muaytax.app_sellers.models.partner import Partner
from muaytax.dictionaries.models import Country


class FileBase64Serializer(serializers.Serializer):
    base64 = serializers.CharField()
    filename = serializers.CharField()

    def save(self, *args, **kwargs):
        return SimpleUploadedFile(
            name=self.validated_data["filename"],
            content=base64.b64decode(self.validated_data["base64"]),
        )


class SellerAdminSerializer(serializers.ModelSerializer):
    nationality = serializers.SlugRelatedField(
        slug_field="iso_code", queryset=Country.objects.all(), required=False
    )
    birth_country = serializers.SlugRelatedField(
        slug_field="iso_code", queryset=Country.objects.all(), required=False
    )
    passport_file = serializers.DictField(write_only=True, required=False)
    nif_file = serializers.DictField(write_only=True, required=False)
    assignment_document = serializers.DictField(write_only=True, required=False)

    class Meta:
        model = SellerAdministration
        exclude = ["seller"]

    def validate_passport_file(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    def validate_nif_file(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    def validate_assignment_document(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()


class PartnerSerializer(serializers.ModelSerializer):
    birth_country = serializers.SlugRelatedField(
        slug_field="iso_code", queryset=Country.objects.all(), required=False
    )
    nif_file = serializers.DictField(write_only=True, required=False)

    class Meta:
        model = Partner
        exclude = ["seller"]

    def validate_nif_file(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()


class sellerCreateSerializer(serializers.ModelSerializer):

    email = serializers.EmailField(write_only=True)
    country_registration = serializers.SlugRelatedField(
        slug_field="iso_code", queryset=Country.objects.all(), required=False
    )
    establishment_file = serializers.DictField(write_only=True, required=False)
    bank_account_ownership_certificate = serializers.DictField(
        write_only=True, required=False
    )
    amazon_screenshot = serializers.DictField(write_only=True, required=False)
    eu_vat_registration_certificate = serializers.DictField(
        write_only=True, required=False
    )
    selleradmin = serializers.DictField(write_only=True)
    partners = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
    )

    class Meta:
        model = Seller
        exclude = ["user"]

    def validate_establishment_file(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    def validate_bank_account_ownership_certificate(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    def validate_amazon_screenshot(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    def validate_eu_vat_registration_certificate(self, value):
        serializer = FileBase64Serializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer.save()

    def validate_selleradmin(self, value):
        serializer = SellerAdminSerializer(data=value)
        serializer.is_valid(raise_exception=True)
        return serializer

    def validate_partners(self, value):
        aux = []
        for item in value:
            serializer = PartnerSerializer(data=item)
            serializer.is_valid(raise_exception=True)
            aux.append(serializer)
        return aux

    def validate(self, data):
        email = data.pop("email")
        user_form = SignupForm(
            {
                "email": email,
                "username": email.split("@")[0],
                "password1": "!123456.",
                "password2": "!123456.",
            }
        )
        if not user_form.is_valid():
            raise serializers.ValidationError(user_form.errors)
        self.context["user_form"] = user_form
        return data

    def create(self, validated_data):
        print(validated_data)
        selleradmin_serializer = validated_data.pop("selleradmin")
        partners_serializer = validated_data.pop("partners")

        user_form = self.context["user_form"]
        user = user_form.save(self.context["request"])

        validated_data["user"] = user
        instance: Seller = super().create(validated_data)

        selleradmin_serializer.save(seller=instance)
        for partner_serializer in partners_serializer:
            partner_serializer.save(seller=instance)

        email_address: EmailAddress = EmailAddress.objects.get(
            user=user, email=user.email
        )
        #email_address.send_confirmation(request=self.context["request"], signup=True)

        return instance