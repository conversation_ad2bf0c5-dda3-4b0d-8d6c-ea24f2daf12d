DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_gets_customers_mod347') THEN
        DROP FUNCTION func_gets_customers_mod347(sellerid INTEGER, date_year INTEGER);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_gets_customers_mod347(sellerid INTEGER, date_year INTEGER)
RETURNS jsonb AS $$
DECLARE
    result_json jsonb := '[]';
    json_object JSONB;
BEGIN

    SELECT jsonb_agg(sub_data) INTO result_json
    
    
    FROM (
        SELECT
            cust.id,
            cust.nif_cif_iva,
            cust.name,
            adr.address,
            cust.zip,
            cust.country_id as country,
            ROUND(SUM(
                CASE
                WHEN EXTRACT(MONTH FROM inv.expedition_date) BETWEEN 1 AND 3 THEN con.total_euros 
                ELSE 0 
                END)::numeric, 2) AS q1_total,
            ROUND(SUM(
                CASE
                WHEN EXTRACT(MONTH FROM inv.expedition_date) BETWEEN 4 AND 6 THEN con.total_euros 
                ELSE 0 
                END)::numeric, 2) AS q2_total,
            ROUND(SUM(
                CASE
                WHEN EXTRACT(MONTH FROM inv.expedition_date) BETWEEN 7 AND 9 THEN con.total_euros 
                ELSE 0 
                END)::numeric, 2) AS q3_total,
            ROUND(SUM(
                CASE
                WHEN EXTRACT(MONTH FROM inv.expedition_date) BETWEEN 10 AND 12 THEN con.total_euros 
                ELSE 0 
                END)::numeric, 2) AS q4_total,
            ROUND(SUM(con.total_euros)::numeric, 2) AS total_euros,
            'Clave B - Ventas' AS clave
        FROM
            customers_customer cust
        LEFT JOIN address_address adr ON adr.id = cust.customer_address_id
        INNER JOIN invoices_invoice inv ON inv.customer_id = cust.id
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        LEFT JOIN (
            SELECT DISTINCT
                inv_irpf.id AS irpf_invoice_id
            FROM
                invoices_invoice inv_irpf
            INNER JOIN invoices_concept con_irpf ON con_irpf.invoice_id = inv_irpf.id
            WHERE
				EXTRACT(YEAR FROM inv_irpf.expedition_date) = date_year
                AND con_irpf.irpf_euros <> 0
                AND con_irpf.is_supplied IS NOT true
        ) has_irpf ON has_irpf.irpf_invoice_id = inv.id
        WHERE
            cust.seller_id = sellerid AND
            inv.tax_country_id = 'ES' AND
            EXTRACT(YEAR FROM inv.expedition_date) = date_year
            AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
            AND inv.status_id = 'revised'	
            AND NOT (inv.transaction_type_id LIKE 'intra-community-%'
                    OR inv.transaction_type_id LIKE 'import-%'
                    OR inv.transaction_type_id LIKE 'export-%'
                    OR inv.transaction_type_id LIKE '%-transfer')
            AND has_irpf IS NULL
            AND cust.name NOT ILIKE 'Clientes Particulares%'
            AND cust.name NOT ILIKE 'Clientes Miravia%'
            AND cust.name NOT ILIKE 'Clientes Shopify%'
            AND con.is_supplied IS NOT true
        GROUP BY
            cust.id,
            adr.address
        HAVING
            SUM(con.total_euros) > 3005.06
        ORDER BY
            cust.seller_id
    ) AS sub_data;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- SELECT func_gets_customers_mod347(253, 2023);