import json
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, UpdateView, CreateView, DeleteView
from django.http import HttpResponseBadRequest, HttpResponse, JsonResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.db import IntegrityError, transaction
from django.core.serializers import serialize
import os, string, random 

from muaytax.users.permissions import IsSellerShortnamePermission, IsManagerRolePermission
from muaytax.app_documents.forms.document import DocumentChangeForm, DocumentDeleteForm, DocumentCreateForm, CertUpdateForm
from muaytax.app_documents.models.document import Document
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.app_invoices.electronic_invoice.veriFactu import get_info_issuer_certificate, verify_cert_digital


class DocumentListView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    model = Document    
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def setTemplateByRole(self):
        self.template_name_suffix = "_list_seller"

        user_role = self.request.user.role             
        if user_role == "manager":
            self.template_name_suffix = "_list_manager"

        return self.template_name
    
    def get(self, request, *args, **kwargs):
        self.setTemplateByRole()
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        
        if self.request.user.role == "manager":
            docs = Document.objects.filter(seller_id = seller.id)
        else:
            docs = Document.objects.filter(seller_id = seller.id, privacy='public')
        return docs
    
    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)  
        for obj in context['object_list']:
            obj.file.name = obj.file.name.replace("uploads/", "").replace("documents/", "")
            # print("obj.file.url: " + obj.get_file_url() )
            # print("obj.file.name: " + obj.file.name)
            if obj.documentType.code=='ES-SIGNATURE':
                verify_cert, cert_path, cert_password, cert = verify_cert_digital(seller)
                context["cert_info"] = json.dumps(get_info_issuer_certificate(seller, cert)) if cert and verify_cert else None


        context["seller"] = seller
        return context
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class DocumentDetailView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), UpdateView):
    model = Document
    form_class = DocumentChangeForm
    template_name_suffix = "_detail"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
   
    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        response = None

        try:
            with transaction.atomic():
                form.instance.seller = seller
                response = super().form_valid(form)
        except IntegrityError:
            #  form.add_error('asin', 'Este código ASIN ya existe')
            response = self.form_invalid(form)

        return response

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        doc = get_object_or_404(Document, seller_id=seller.id, pk=self.kwargs["pk"])
        context = super().get_context_data(**kwargs)        
        context["seller"] = seller
        context["sellerVat"] = SellerVat.objects.filter(seller_id = seller.id)
        return context      

    def get_success_url(self) -> str:
        return reverse(
            "app_documents:document_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class DocumentDeleteView(LoginRequiredMixin, (IsSellerShortnamePermission and IsManagerRolePermission), SuccessMessageMixin, DeleteView):
    
    model = Document
    form_class = DocumentDeleteForm
    template_name_suffix = "_delete"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        doc = get_object_or_404(Document, seller_id=seller.id, pk=self.kwargs["pk"])
        context = super().get_context_data(**kwargs)        
        context["seller"] = seller
        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_documents:document_list",
            args=[self.object.seller.shortname],
        )
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class DocumentUpdateView(LoginRequiredMixin, SuccessMessageMixin, UpdateView):

    model = Document
    form_class = DocumentChangeForm
    success_message = _("Information successfully updated")
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))

class DocumentUploadView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def setTemplateByRole(self):
        self.template_name_suffix = "_upload_seller"

        user_role = self.request.user.role             
        if user_role == "manager":
            self.template_name_suffix = "_upload_manager"

        return self.template_name
    
    def get(self, request, *args, **kwargs):
        self.setTemplateByRole()
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        models = Document.objects.filter(seller_id = seller.id)
        return models
    
    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)  
        for obj in context['object_list']:
            obj.file.name = obj.file.name.replace("uploads/", "").replace("presented_models/", "")
        context["seller"] = seller
        context["seller_vat"] = SellerVat.objects.all().filter(seller_id=seller.id)
        context["countries"] = Country.objects.all().order_by("name")
        context["document_type"] = DocumentType.objects.all().order_by("description")
        context["json"] = {
            "seller": serialize("json", [context["seller"]]),
            "seller_vat": serialize("json", context["seller_vat"]),
            "countries": serialize("json", context["countries"]),
            "document_type": serialize("json", context["document_type"]),
        }
        return context
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("app_sellers:summary", args=[self.kwargs["shortname"]]))
    
class DocumentCreateView(LoginRequiredMixin, IsSellerShortnamePermission, CreateView):
    form_class = DocumentCreateForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'
    model = Document

    def get_form_kwargs(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        kwargs = super().get_form_kwargs()
        kwargs["data"] = kwargs["data"].copy()
        kwargs["data"]["seller"] = seller.id
        
        # Establecer la privacidad a 'public' por defecto para seller
        if self.request.user.role != 'manager':
            kwargs["data"]["privacy"] = 'public'
            
        return kwargs

    def get_success_url(self) -> str:
        self.create_document()
        return reverse(
            "app_documents:document_list",
            args=[self.object.seller.shortname],
        )

    def form_invalid(self, form):
        # Manejar el formulario inválido y mostrar errores
        if (form and form.errors):
            print("form_invalid: {}".format(form.errors))
        else:
            print("form_invalid: {}".format(form))
            form.errors = "Error in DocumentCreateView"
        return HttpResponseBadRequest(form.errors["file"])
    
    def form_valid(self, form):
        # Asegurarse de que la privacidad esté establecida a 'public' para los vendedores que no sean managers
        if self.request.user.role != 'manager':
            form.instance.privacy = 'public'
        response = super().form_valid(form)
        self.create_document()
        return response
    
    def handle_no_permission(self):
        # Redirigir cuando no hay permiso
        return HttpResponseBadRequest("No Permission")  
    
    def create_document(self):
        try:
            # Get Document
            document = Document.objects.get(pk=self.object.pk)

            # Obtener el vendedor y asignarlo al documento si no tiene uno
            seller = self.object.seller
            if document.seller is None:
                document.seller = seller
            document.save()
            print("Document Created: " + str(document.pk))          

        except Exception as err:
            print("Error in create_document: {}".format(repr(err)))

class DocumentUpdateCert(LoginRequiredMixin, IsSellerShortnamePermission, UpdateView):
    model = Document
    form_class = CertUpdateForm
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def post (self, request, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        if form.is_valid():
            return self.form_valid(form)
        else:
            return self.form_invalid(form)

    def form_invalid(self, form):
        if (form and form.errors):
            print("form_invalid: {}".format(form.errors))
        else:
            print("form_invalid: {}".format(form))
            form.errors = "Error in DocumentCreateView"
        return HttpResponseBadRequest(form.errors)
    
    def form_valid(self, form):
        self.object = form.save()
        password = Document.objects.get(pk=self.object.pk).get_password()
        verify_cert, cert_path, cert_password, cert = verify_cert_digital(self.object.seller)
        issuer_info = json.dumps(get_info_issuer_certificate(self.object.seller, cert)) if cert and verify_cert else None

        return JsonResponse(
            {
            'pk': self.object.pk,
            'password': password,
            'expiration_date': self.object.expiration_date,
            'issuer_info': issuer_info
            }
        )


    
    def handle_no_permission(self):
        # Redirigir cuando no hay permiso
        return HttpResponseBadRequest("No Permission")  

