from muaytax.app_sellers.models.seller import Seller
    
    # Obtener el vendedor
    seller = Seller.objects.get(shortname="2mventerprisellc")
    
    # Borrar la dirección asociada (elimina la instancia Address si quieres eliminar también el objeto relacionado)
    address_to_delete = seller.seller_address
    seller.seller_address = None
    seller.save()
    
    # Si también deseas borrar el objeto Address de la base de datos (¡solo si no está referenciado en otro sitio!)
    if address_to_delete:
     address_to_delete.delete()
    
    print(f"Dirección del seller {seller.shortname} eliminada correctamente.")