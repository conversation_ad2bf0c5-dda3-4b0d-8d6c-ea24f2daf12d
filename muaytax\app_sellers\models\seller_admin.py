from django.db import models
from django.urls import reverse

from muaytax.app_sellers.models.seller import Seller


class SellerAdministration(models.Model):

    email = models.EmailField(
        verbose_name="Email"
    )

    first_name = models.Char<PERSON>ield(
        max_length=150, 
        verbose_name="Nombre"
    )

    last_name = models.CharField(
        max_length=150, 
        verbose_name="Apellidos",
        blank=True, null=True,
    )

    phone_country = models.ForeignKey(
        "dictionaries.Country",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="selleradmin_phone_country",
        verbose_name="Pais Teléfono",
    )

    phone = models.Char<PERSON>ield(
        max_length=150,
        verbose_name="Teléfono",
        blank=True, null=True,
    )

    admin_address = models.ForeignKey(
        "address.Address",
        related_name="admin_address",
        on_delete=models.PROTECT,
        verbose_name="Dirección del administrador",
        blank=True, null=True,
    )

    birth_country = models.ForeignKey(
        "dictionaries.Country",
        related_name="admin_birth_country",
        on_delete=models.PROTECT,
        verbose_name="País de Nacimiento",
        blank=True, null=True,
    )

    birth_date = models.DateField(
        verbose_name="Fecha de Nacimiento",
        blank=True, null=True,
    )

    nationality = models.ForeignKey(
        "dictionaries.Country",
        related_name="admin_nationality",
        on_delete=models.PROTECT,
        verbose_name="Nacionalidad",
        blank=True, null=True,
    )

    id_type = models.ForeignKey(
        "dictionaries.IdentifierType",
        on_delete=models.PROTECT,
        verbose_name="Tipo de Identificación",
        blank=True, null=True,
    )

    id_number = models.CharField(
        max_length=50,
        verbose_name="ID Fiscal / DNI / Pasaporte",
        unique=True,
    )

    id_expedition_date = models.DateField(
        verbose_name="Fecha de Expedición",
        blank=True, null=True,
    )

    id_expiration_date = models.DateField(
        verbose_name="Fecha de Expiración/Caducidad",
        blank=True, null=True,
    )

    seller = models.OneToOneField(
        Seller, on_delete=models.CASCADE, verbose_name="empresa"
    )  

    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)  
    
    class Meta:
        verbose_name = "Administrador de la Empresa"
        verbose_name_plural = "Administradores de la Empresa"

    def __str__(self):
        return self.first_name or str(self.seller)

    def get_absolute_url(self):
        return reverse("app_sellers:detail", kwargs={"pk": self.pk})
    