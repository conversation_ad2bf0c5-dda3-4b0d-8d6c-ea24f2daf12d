
# Formulario IVA – Bloque de Migración por País

Este bloque gestiona la información de migración del gestor o gesrtoria anterior para cada país con solo mantenimiento contratado solicitando la doc necesaria por pais para solicitar y gestionar las nuevas declaraciones por nuestra empresa MUAYTAX.

---

## Estructura del Include

- **Archivo principal**: `migration_info_section.html`
- **Tabs Bootstrap por país** (`#migrationTab`) con navegación por flechas.
- **Contenido condicional** por país ISO (`FR`, `DE`, `GB`, `IT`).
- **Soporte para múltiples formularios** (`migration_forms[country_iso]`).

---

## Contenido del formulario

### Secciones comunes:
- Fecha de última declaración enviada por gestor anterior.
- Fecha de esa declaración.
- Si la próxima será presentada por Muaytax.
- Número de IVA y certificado (`vat_number_certificate_{iso}`).

### Países específicos:
- **FR**: Nombre y dirección del gestor anterior, `siret`.
- **DE**: `steuernummer`, `steuernummer_certificate`.
- **IT**: `codice_fiscale`.
- **GB**: 
  - `gov_gateway_user_id` (12 cifras)
  - Teléfono (prefijo + número)
  - `gov_gateway_password` (con toggle de visibilidad)

---

## Script JS

Define el módulo `MigrationFormModule` en `window` con:

### `methods`:
- `setupFieldCompletionCheck`: Muestra icono ✔ si todos los campos están completos.
- `validateGovUserId`: Valida ID de 12 cifras (solo numérico).
- `validateGovPhoneNumber`: Limpia teléfono de caracteres no numéricos.
- `togglePasswordVisibility`: Alterna visibilidad de contraseña.

### `handlers`:
- `handlerPreviousManagerFR`: Muestra/oculta campos según país/entidad.
- `setupArrowNavigation`: Navegación entre países por flechas.
- `setupPasswordToggleActivation`: Activa icono de visibilidad solo si hay contenido.

---

## Inicialización de contexto

Se inyectan desde backend:
- `migration_info_data` → Datos previos por país.
- `validation_migration` → JSON con campos completos.
- `context_label_migration_info` → Labels y tooltips por campo.

---

## Validaciones implementadas

- Campos requeridos marcados según país.
- Icono ✔ automático si todos los campos visibles están completos.
- Validación reactiva de ID (GB) y teléfono (GB).
- Mostrar/ocultar campos para Francia si no es UE.

---

## Archivos relacionados

- JS: `formIVA_migration_module.js` (modular o embebido en el include).
- JSONs: `json-migration-data`, `json-validation-migration`, `json-context-label-migration-info`.
- Include tooltip: `label_with_tooltip.html`.

---

## Módulo exportado

```js
window.MigrationFormModule = {
    methods: methodMigration,
    handlers: handlersMigration,
}
```
