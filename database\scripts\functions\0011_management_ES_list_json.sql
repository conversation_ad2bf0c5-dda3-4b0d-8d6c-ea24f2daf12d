DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_managementES_list_json') THEN
        DROP FUNCTION func_managementES_list_json(date_year INTEGER, date_period VARCHAR);
        DROP FUNCTION func_managementES_list_json(date_year INTEGER, date_period VARCHAR, entity VARCHAR);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_managementES_list_json(date_year INTEGER, date_period VARCHAR, entity VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
	legal_entity VARCHAR;
	first_month DATE;
	last_month DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	IF date_period = 'Q1' THEN
					first_month := date_year || '-01-01';
					last_month := date_year || '-04-01';
	ELSIF date_period = 'Q2' THEN
					first_month := date_year || '-04-01';
					last_month := date_year || '-07-01';
	ELSIF date_period = 'Q3' THEN
					first_month := date_year || '-07-01';
					last_month := date_year || '-10-01';
	ELSIF date_period = 'Q4' THEN
					first_month := date_year || '-10-01';
					last_month := (date_year + 1) || '-01-01';
				END IF;


    FOR inv_data IN


				SELECT DISTINCT
				-- SubSelect
				subselect.*,

				-- Mondel Min
				(
					CASE
						WHEN 'required' in (model_111, model_115, model_130, model_303, model_309, model_349, model_369) THEN 0
						WHEN 'warning09' in (model_369) THEN 1
						WHEN 'warning13' in (model_111) THEN 2
						WHEN 'warning' in (model_111, model_115, model_130, model_303, model_309, model_349, model_369) THEN 3
						WHEN 'disagreed' in (model_111, model_115, model_130, model_303, model_309, model_349, model_369) THEN 4
						WHEN 'agreed' in (model_111, model_115, model_130, model_303, model_309, model_349, model_369) THEN 5
						WHEN 'pending' in (model_111, model_115, model_130, model_303, model_309, model_349, model_369) THEN 6
						WHEN 'presented' in (model_111, model_115, model_130, model_303, model_309, model_349, model_369) THEN 7
						WHEN 'not-required' in (model_111, model_115, model_130, model_303, model_309, model_349, model_369) THEN 8
					ELSE 9
					END
				) AS model_min,

				-- Model Average
				(
					(
						CASE
							WHEN model_111 = 'required' THEN 0
							WHEN model_111 = 'warning13' THEN 1
							WHEN model_111 = 'disagreed' THEN 2
							WHEN model_111 = 'agreed' THEN 3
							WHEN model_111 = 'pending' THEN 4
							WHEN model_111 = 'presented' THEN 5
							WHEN model_111 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_115 = 'required' THEN 0
							WHEN model_115 = 'warning' THEN 1
							WHEN model_115 = 'disagreed' THEN 2
							WHEN model_115 = 'agreed' THEN 3
							WHEN model_115 = 'pending' THEN 4
							WHEN model_115 = 'presented' THEN 5
							WHEN model_115 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_130 = 'required' THEN 0
							WHEN model_130 = 'warning' THEN 1
							WHEN model_130 = 'disagreed' THEN 2
							WHEN model_130 = 'agreed' THEN 3
							WHEN model_130 = 'pending' THEN 4
							WHEN model_130 = 'presented' THEN 5
							WHEN model_130 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_303 = 'required' THEN 0
							WHEN model_303 = 'warning' THEN 1
							WHEN model_303 = 'disagreed' THEN 2
							WHEN model_303 = 'agreed' THEN 3
							WHEN model_303 = 'pending' THEN 4
							WHEN model_303 = 'presented' THEN 5
							WHEN model_303 = 'not-required' THEN 6
							ELSE 7
						END
					) + (
						CASE
							WHEN model_309 = 'required' THEN 0
							WHEN model_309 = 'warning' THEN 1
							WHEN model_309 = 'disagreed' THEN 2
							WHEN model_309 = 'agreed' THEN 3
							WHEN model_309 = 'pending' THEN 4
							WHEN model_309 = 'presented' THEN 5
							WHEN model_309 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_349 = 'required' THEN 0
							WHEN model_349 = 'warning' THEN 1
							WHEN model_349 = 'disagreed' THEN 2
							WHEN model_349 = 'agreed' THEN 3
							WHEN model_349 = 'pending' THEN 4
							WHEN model_349 = 'presented' THEN 5
							WHEN model_349 = 'not-required' THEN 6
							ELSE 7
						END
					) +	(
						CASE
							WHEN model_369 = 'required' THEN 0
							WHEN model_369 = 'warning09' THEN 1
							WHEN model_369 = 'disagreed' THEN 2
							WHEN model_369 = 'agreed' THEN 3
							WHEN model_369 = 'pending' THEN 4
							WHEN model_369 = 'presented' THEN 5
							WHEN model_369 = 'not-required' THEN 6
							ELSE 7
						END
					)
				) * 100 / 7 as model_avg

			FROM
			(

				SELECT DISTINCT
				-- id
				sel.id,

				-- seller name
				sel.name AS seller_name,

				-- shortname
				sel.shortname AS seller_shortname,

				sel.contracted_labor_payroll_date as contracted_labor_payroll_date,

				sel.contracted_labor_payroll_end_date as contracted_labor_payroll_end_date,

				-- email
				(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

				-- user name
				(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

				-- last login
				(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

				-- Num Invoices
				COUNT(DISTINCT inv.id) AS num_invoices,

				-- Num Pending Invoices
				COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

				-- Percentage Pending Invoices
				ROUND(COALESCE(
					100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
					0
				), 2) AS percentage_pending_invoices,

				-- external accounting
				CASE 
					WHEN sel.contracted_labor_payroll_date > last_month	THEN false
					WHEN sel.contracted_labor_payroll_date < first_month AND
					(
						sel.contracted_labor_payroll_end_date IS NULL OR
						sel.contracted_labor_payroll_end_date >= first_month
					) THEN true
					WHEN sel.contracted_labor_payroll_date >= first_month THEN true
					ELSE false
					END AS external_accounting,

				-- Month 1
				BOOL_OR (
					CASE WHEN
					txt.month_1 is True
					THEN true ELSE false END
				) AS month_1,

				-- Month 2
				BOOL_OR (
					CASE WHEN
					txt.month_2 is True
					THEN true ELSE false END
				) AS month_2,

				-- Month 3
				BOOL_OR (
					CASE WHEN
					txt.month_3 is True
					THEN true ELSE false END
				) AS month_3,

				-- Month 4
				BOOL_OR (
					CASE WHEN
					txt.month_4 is True
					THEN true ELSE false END
				) AS month_4,

				-- Month 5
				BOOL_OR (
					CASE WHEN
					txt.month_5 is True
					THEN true ELSE false END
				) AS month_5,

				-- Month 6
				BOOL_OR (
					CASE WHEN
					txt.month_6 is True
					THEN true ELSE false END
				) AS month_6,

				-- Month 7
				BOOL_OR (
					CASE WHEN
					txt.month_7 is True
					THEN true ELSE false END
				) AS month_7,

				-- Month 8
				BOOL_OR (
					CASE WHEN
					txt.month_8 is True
					THEN true ELSE false END
				) AS month_8,

				-- Month 9
				BOOL_OR (
					CASE WHEN
					txt.month_9 is True
					THEN true ELSE false END
				) AS month_9,

				-- Month 10
				BOOL_OR (
					CASE WHEN
					txt.month_10 is True
					THEN true ELSE false END
				) AS month_10,

				-- Month 11
				BOOL_OR (
					CASE WHEN
					txt.month_11 is True
					THEN true ELSE false END
				) AS month_11,

				-- Month 12
				BOOL_OR (
					CASE WHEN
					txt.month_12 is True
					THEN true ELSE false END
				) AS month_12,

				-- Model 111
				MAX(
					CASE WHEN pm.model_111 IS NOT null THEN pm.model_111
					WHEN pmf.model_111 IS NOT null THEN pmf.model_111
					ELSE
						CASE WHEN
                        (
                            -- Si el vendedor tiene una fecha de alta de nomina societaria anterior al fin de periodo
                            -- y no tiene una fecha de baja de nomina societaria posterior al inicio de periodo
                            (
                                sel.contracted_corporate_payroll_date < last_month AND 
								(
									sel.contracted_corporate_payroll_end_date IS NULL OR
									sel.contracted_corporate_payroll_end_date >= first_month
								)
							) 
							AND 
							(
                                -- Si el vendedor no tiene ninguna factura de nomina societaria dentro del periodo
                                (
                                    SELECT COUNT(pwi.invoice_id)
                                    FROM invoices_payrollworkerinvoice pwi
                                    INNER JOIN invoices_invoice inv ON inv.id = pwi.invoice_id
                                    WHERE inv.seller_id = sel.id
                                    AND inv.accounting_date >= first_month
                                    AND inv.accounting_date < last_month
                                    AND inv.status_id = 'revised'
                                    AND pwi.type_payroll = '1' -- '1' significa que el tipo de nomina es societaria
                                    GROUP BY inv.seller_id
                                    LIMIT 1
                                ) IS NULL
                            )
                        ) 
						OR 
						(
                            -- Si el vendedor tiene una fecha de alta de nomina laboral anterior al fin de periodo
                            -- y no tiene una fecha de baja de nomina laboral posterior al inicio de periodo
                            (
                                sel.contracted_labor_payroll_date < last_month AND 
								(
									sel.contracted_labor_payroll_end_date IS NULL OR
									sel.contracted_labor_payroll_end_date >= first_month
								)
							) 
							AND 
							(
                                -- Si el vendedor no tiene ninguna factura de nomina laboral dentro del periodo
                                (
                                    SELECT COUNT(pwi.invoice_id)
                                    FROM invoices_payrollworkerinvoice pwi
                                    INNER JOIN invoices_invoice inv ON inv.id = pwi.invoice_id
                                    WHERE inv.seller_id = sel.id
                                    AND inv.accounting_date >= first_month
                                    AND inv.accounting_date < last_month
                                    AND inv.status_id = 'revised'
                                    AND pwi.type_payroll = '2' -- '2' significa que el tipo de nomina es laboral
                                    GROUP BY inv.seller_id
                                    LIMIT 1
                                ) IS NULL
                            )
                        )
						THEN 'warning13'
						WHEN 
						(
							sel.contracted_accounting_date < last_month AND 
							(
								sel.contracted_accounting_end_date IS NULL OR 
								sel.contracted_accounting_end_date >= first_month 
							)
						)
						AND 
						(
							(
								inv.accounting_date >= first_month AND
								inv.accounting_date < last_month AND
								inv.total_irpf_euros > 0 AND
								inv.status_id != 'discard' AND
								inv.invoice_category_id = 'expenses' AND
								inv.account_expenses_id != '621'
							)
							OR 
							(
								sel.withholdings_payments_account_date < last_month AND
								(
									sel.withholdings_payments_account_end_date IS NULL OR 
									sel.withholdings_payments_account_end_date >= first_month
								)
							)
							OR pm_qty_this_year.model_111 > 0
						)
						THEN 'required'
						ELSE 'not-required' END
					END
				) as model_111,


				-- Model 115
				MAX(
					CASE WHEN pm.model_115 IS NOT null THEN pm.model_115
					WHEN pmf.model_115 IS NOT null THEN pmf.model_115
					ELSE
						CASE WHEN inv.accounting_date >= first_month AND
						inv.accounting_date < last_month AND
						inv.total_irpf_euros > 0 AND
						inv.status_id != 'discard' AND
						inv.invoice_category_id = 'expenses' AND
						inv.account_expenses_id = '621' AND
						--sel.contracted_accounting = True
						(sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
						THEN 'required' ELSE 'not-required' END
					END
				) as model_115,

				-- Model 130
				MAX(
					CASE WHEN pm.model_130 IS NOT null THEN pm.model_130
					WHEN pmf.model_130 IS NOT null THEN pmf.model_130
					ELSE
						CASE WHEN
						sel.legal_entity = 'self-employed' AND
						(sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
						--sel.contracted_accounting = True
						THEN 'required' ELSE 'not-required' END
					END
				) as model_130,

				-- Model 303
				MAX(
					CASE WHEN pm.model_303 IS NOT null THEN pm.model_303
					WHEN pmf.model_303 IS NOT null THEN pmf.model_303
					ELSE
						CASE WHEN
						--(sv.vat_country_id = 'ES' OR sel.contracted_accounting = True) AND
						--sel.contracted_accounting = True
						(sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
						--AND sel.eqtax is not True
						AND (qtax_reg.regime != 'surcharge' OR qtax_reg.regime IS NULL )
						THEN 'required' ELSE 'not-required' END
					END
				) as model_303,

				-- Model 309
				MAX(
					CASE WHEN pm.model_309 IS NOT null THEN pm.model_309
					WHEN pmf.model_309 IS NOT null THEN pmf.model_309
					ELSE
						CASE WHEN inv.accounting_date >= first_month AND
						inv.accounting_date < last_month AND
						inv.transaction_type_id LIKE 'intra-community-%' AND
						inv.status_id != 'discard' AND
						sel.legal_entity = 'self-employed' AND
						qtax_reg.regime = 'surcharge' AND
						--sel.eqtax is True AND
						--sel.contracted_accounting = True
						(sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
						THEN 'required' ELSE 'not-required' END
					END
				) as model_309,

				-- Model 349
				MAX(
					CASE WHEN pm.model_349 IS NOT null THEN pm.model_349
					WHEN pmf.model_349 IS NOT null THEN pmf.model_349
					ELSE
						CASE WHEN inv.accounting_date >= first_month AND
						inv.accounting_date < last_month AND
						inv.status_id != 'discard' AND
						inv.transaction_type_id LIKE 'intra-community-%' AND
						--sel.contracted_accounting = True
						(sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
						THEN 'required' ELSE 'not-required' END
					END
				) as model_349,

				-- Model 369
				MAX(
					CASE
							WHEN pm.model_369 IS NOT NULL THEN pm.model_369
							WHEN pmf.model_369 IS NOT NULL THEN pmf.model_369
							WHEN sel.oss IS TRUE THEN 'required'
							WHEN sel.oss IS NOT TRUE AND oss.invoice_oss = TRUE THEN 'warning09'
							ELSE 'not-required'
						END
				) as model_369

				FROM sellers_seller sel
				LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
			--LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id
				LEFT JOIN (
					SELECT
					sel.id as seller_id,
					BOOL_OR(CASE WHEN month = 1 AND status_id = 'processed' THEN true ELSE false END) 							AS month_1,
					BOOL_OR(CASE WHEN month = 2 AND status_id = 'processed' THEN true ELSE false END) 							AS month_2,
					BOOL_OR(CASE WHEN month = 3 AND status_id = 'processed' THEN true ELSE false END) 							AS month_3,
					BOOL_OR(CASE WHEN month = 4 AND status_id = 'processed' THEN true ELSE false END) 							AS month_4,
					BOOL_OR(CASE WHEN month = 5 AND status_id = 'processed' THEN true ELSE false END) 							AS month_5,
					BOOL_OR(CASE WHEN month = 6 AND status_id = 'processed' THEN true ELSE false END) 							AS month_6,
					BOOL_OR(CASE WHEN month = 7 AND status_id = 'processed' THEN true ELSE false END) 							AS month_7,
					BOOL_OR(CASE WHEN month = 8 AND status_id = 'processed' THEN true ELSE false END) 							AS month_8,
					BOOL_OR(CASE WHEN month = 9 AND status_id = 'processed' THEN true ELSE false END) 							AS month_9,
					BOOL_OR(CASE WHEN month = 10 AND status_id = 'processed' THEN true ELSE false END) 							AS month_10,
					BOOL_OR(CASE WHEN month = 11 AND status_id = 'processed' THEN true ELSE false END) 							AS month_11,
					BOOL_OR(CASE WHEN month = 12 AND status_id = 'processed' THEN true ELSE false END) 							AS month_12
					FROM sellers_seller sel
					LEFT JOIN importers_amazontxteur txt ON sel.id = txt.seller_id
					WHERE year = date_year
					GROUP BY sel.id
				) AS txt ON sel.id = txt.seller_id
				LEFT JOIN (
					SELECT
					sel.id as seller_id,
					MAX(CASE WHEN pm.model_id::text = 'ES-111' THEN pm.status_id END) as model_111,
					MAX(CASE WHEN pm.model_id::text = 'ES-115' THEN pm.status_id END) as model_115,
					MAX(CASE WHEN pm.model_id::text = 'ES-130' THEN pm.status_id END) as model_130,
					MAX(CASE WHEN pm.model_id::text = 'ES-303' THEN pm.status_id END) as model_303,
					MAX(CASE WHEN pm.model_id::text = 'ES-309' THEN pm.status_id END) as model_309,
					MAX(CASE WHEN pm.model_id::text = 'ES-349' THEN pm.status_id END) as model_349,
					MAX(CASE WHEN pm.model_id::text = 'ES-369' THEN pm.status_id END) as model_369
					FROM sellers_seller sel
					LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
					WHERE pm.year = date_year AND pm.period_id = date_period AND pm.country_id = 'ES'
					GROUP BY sel.id
				) AS pm ON sel.id = pm.seller_id
				LEFT JOIN (
					SELECT
					sel.id as seller_id,
					MAX(CASE WHEN pmf.model_id::text = 'ES-111' THEN pmf.status_id END) as model_111,
					MAX(CASE WHEN pmf.model_id::text = 'ES-115' THEN pmf.status_id END) as model_115,
					MAX(CASE WHEN pmf.model_id::text = 'ES-130' THEN pmf.status_id END) as model_130,
					MAX(CASE WHEN pmf.model_id::text = 'ES-303' THEN pmf.status_id END) as model_303,
					MAX(CASE WHEN pmf.model_id::text = 'ES-309' THEN pmf.status_id END) as model_309,
					MAX(CASE WHEN pmf.model_id::text = 'ES-349' THEN pmf.status_id END) as model_349,
					MAX(CASE WHEN pmf.model_id::text = 'ES-369' THEN pmf.status_id END) as model_369
					FROM sellers_seller sel
					LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
					WHERE pmf.year = date_year AND pmf.period_id = date_period AND pmf.country_id = 'ES'
					GROUP BY sel.id
				) AS pmf ON sel.id = pmf.seller_id
				LEFT JOIN (
					SELECT
					sel.id as seller_id,
					COALESCE(SUM(CASE WHEN pm.model_id::text = 'ES-111' THEN 1 ELSE 0 END), 0) as model_111,
					COALESCE(SUM(CASE WHEN pm.model_id::text = 'ES-115' THEN 1 ELSE 0 END), 0) as model_115,
					COALESCE(SUM(CASE WHEN pm.model_id::text = 'ES-130' THEN 1 ELSE 0 END), 0) as model_130,
					COALESCE(SUM(CASE WHEN pm.model_id::text = 'ES-303' THEN 1 ELSE 0 END), 0) as model_303,
					COALESCE(SUM(CASE WHEN pm.model_id::text = 'ES-309' THEN 1 ELSE 0 END), 0) as model_309,
					COALESCE(SUM(CASE WHEN pm.model_id::text = 'ES-349' THEN 1 ELSE 0 END), 0) as model_349,
					COALESCE(SUM(CASE WHEN pm.model_id::text = 'ES-369' THEN 1 ELSE 0 END), 0) as model_369
					FROM sellers_seller sel
					LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
					WHERE pm.year = date_year AND pm.country_id = 'ES'
					GROUP BY sel.id
				) as pm_qty_this_year ON sel.id = pm_qty_this_year.seller_id
				LEFT JOIN (
					SELECT DISTINCT sel.id, sel.shortname, sel.oss AS seller_oss, inv.is_oss AS invoice_oss
					FROM sellers_seller sel
					INNER JOIN invoices_invoice inv ON sel.id = inv.seller_id
					WHERE (sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
					--AND sel.contracted_accounting = True
					AND sel.oss IS NOT True
					AND inv.is_oss IS True
					AND inv.accounting_date >= first_month
					AND inv.accounting_date < last_month
					GROUP BY sel.id, inv.is_oss
					ORDER BY sel.id
				) AS oss ON oss.id = sel.id
				LEFT JOIN (
					SELECT DISTINCT 
					sel.id AS seller_id, 
					sva.regime_id AS regime
					FROM sellers_seller sel
					LEFT JOIN sellers_sellervat sv ON sv.seller_id = sel.id AND sv.vat_country_id ='ES'
					LEFT JOIN sellers_sellervatactivity sva on sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
					GROUP BY sel.id, sva.regime_id
					ORDER BY sel.id
				) AS qtax_reg ON sel.id = qtax_reg.seller_id
				-- WHERE sel.contracted_accounting = True OR sel.oss IS True
				WHERE
					(
						( entity = 'all' AND sel.legal_entity IN ('sl', 'self-employed') ) OR
						( entity = sel.legal_entity )
					)					
					AND
					(
						(sel.contracted_accounting_date < last_month AND (sel.contracted_accounting_end_date IS NULL OR  sel.contracted_accounting_end_date >= first_month ))
						OR
						(sel.oss_date < last_month AND (sel.oss_end_date IS NULL OR sel.oss_end_date >= first_month ))
						OR
						(sel.oss IS True)
					)
				-- AND (inv.status_id IN ('pending', 'revision-pending') OR (inv.accounting_date >= first_month AND inv.accounting_date < last_month))
				GROUP BY sel.id
				ORDER BY sel.id
			) AS subselect



		LOOP
			result_json := result_json || jsonb_build_object(
				'seller_id', inv_data.id,
				'seller_name', inv_data.seller_name,
				'shortname', inv_data.seller_shortname,
				'external_accounting', inv_data.external_accounting,
				'email', inv_data.email,
				'user_name', inv_data.user_name,
				'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
				'num_pending_invoices', inv_data.num_pending_invoices,
				'percentage_pending_invoices', inv_data.percentage_pending_invoices,
				'month1', inv_data.month_1,
				'month2', inv_data.month_2,
				'month3', inv_data.month_3,
				'month4', inv_data.month_4,
				'month5', inv_data.month_5,
				'month6', inv_data.month_6,
				'month7', inv_data.month_7,
				'month8', inv_data.month_8,
				'month9', inv_data.month_9,
				'month10', inv_data.month_10,
				'month11', inv_data.month_11,
				'month12', inv_data.month_12,
				'model_111', inv_data.model_111,
				'model_115', inv_data.model_115,
				'model_130', inv_data.model_130,
				'model_303', inv_data.model_303,
				'model_309', inv_data.model_309,
				'model_349', inv_data.model_349,
				'model_369', inv_data.model_369,
				'model_min', inv_data.model_min,
				'model_avg', inv_data.model_avg
			);

		END LOOP;

	RETURN result_json;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION func_managementES_list_json(date_year INTEGER, date_period VARCHAR) 
RETURNS jsonb AS $$
BEGIN
    RETURN func_managementES_list_json(date_year, date_period, 'all');
END;
$$ LANGUAGE plpgsql;

-- SELECT * FROM func_managementES_list_json('2023', 'Q2');
