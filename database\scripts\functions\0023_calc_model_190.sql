-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_190' ) THEN
	  DROP FUNCTION func_calc_model_es_190(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_190(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE

CA01 INTEGER:= 0;
CA02 NUMERIC:= 0;
CA03 NUMERIC:= 0;
PAG NUMERIC := 2;
OP NUMERIC := 1;
LAST_PAGE NUMERIC:= 0;
count_operators NUMERIC := 0;
-- total_amount := 
tmp_key VARCHAR := '';
tmp_value VARCHAR := '';
tmp_value2 VARCHAR := '';
tmp_json JSONB;
short_name VARCHAR := '';
total_operators RECORD;
operator RECORD;

inv_amount RECORD;
inv_irpf RECORD;
json_result JSONB;
json_operators JSONB;

BEGIN

	-- ASIGNAR VALORES A LAS VARIABLES 'shortname'
	SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;


	-- AMOUNT
	SELECT 
		ROUND(common_expenses.common_amount::numeric, 2) AS common_amount,
        ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_amount,
        ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric, 2) AS sum_amount
	INTO inv_amount
	FROM
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
		AND con.irpf > 0
		AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
		AND is_generated_amz IS NOT true
		AND is_txt_amz IS NOT true
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
		AND con.is_supplied IS NOT true
	) AS common_expenses,
	(
		SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
		FROM invoices_invoice inv
		INNER JOIN invoices_concept con ON con.invoice_id = inv.id
		WHERE inv.seller_id = sellerid
		AND EXTRACT(YEAR FROM accounting_date) = date_year
		AND EXTRACT(MONTH FROM accounting_date) >= month_min
		AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND status_id = 'revised'
		AND tax_country_id = 'ES'
		AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
		AND con.irpf > 0
		AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
		AND is_txt_amz IS true
		--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
		AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
		AND con.is_supplied IS NOT true
	) AS amz_expenses;

	-- IRPF
	SELECT 
        common_irpf.irpf AS common_irpf,
        amz_irpf.irpf AS amz_irpf,
        common_irpf.irpf + amz_irpf.irpf AS sum_irpf
    INTO inv_irpf
    FROM
    (
        SELECT DISTINCT 
					--COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) as irpf
					COALESCE(SUM(ROUND((con.quantity * con.amount_euros * con.irpf / 100)::numeric, 2)), 0) AS irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
				AND EXTRACT(MONTH FROM accounting_date) >= month_min
				AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
				AND tax_country_id = 'ES'
        AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
				AND con.irpf > 0
				AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
				--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
				AND con.is_supplied IS NOT true
    ) AS common_irpf,
    (
        SELECT DISTINCT 
					--COALESCE(SUM(con.amount_euros * con.irpf / 100),0) as irpf
					COALESCE(SUM(ROUND((con.amount_euros * con.irpf / 100)::numeric, 2)), 0) AS irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
				AND EXTRACT(MONTH FROM accounting_date) >= month_min
				AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
				AND tax_country_id = 'ES'
        AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
				AND con.irpf > 0
				AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
        AND is_txt_amz IS true
				--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
				AND con.is_supplied IS NOT true
    ) AS amz_irpf;

	json_operators :=  json_build_object();

	-- FOR PROVIDERS IRPF >= 7%
	FOR operator IN (
		SELECT DISTINCT
            prov.*,
            ROUND((COALESCE(common.amount_euros,0) + COALESCE(amz.amount_euros,0))::numeric ,2) AS sum_amount,
			ROUND((COALESCE(common.irpf,0) + COALESCE(amz.irpf,0))::numeric ,2) AS sum_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        LEFT JOIN providers_provider prov ON prov.id = inv.provider_id	
        LEFT JOIN (
            SELECT DISTINCT 
							prov.id,
							SUM(con.amount_euros * con.quantity) as amount_euros,
							SUM(con.quantity * con.amount_euros * con.irpf / 100) AS irpf
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
						AND con.is_supplied IS NOT true
						AND con.irpf >= 7
						AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS NOT true
						AND (inv.invoice_type_id != 'payroll' OR invoice_type_id is null)
						--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
						AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
            GROUP BY prov.id
        ) AS common ON common.id = inv.provider_id
		LEFT JOIN (
            SELECT DISTINCT 
							prov.id, 
							SUM(con.amount_euros) as amount_euros,
							SUM(con.amount_euros * con.irpf / 100)AS irpf
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
            AND con.irpf >= 7
						AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
						AND is_generated_amz IS NOT true
						AND is_txt_amz IS true
						AND (inv.invoice_type_id != 'payroll' OR invoice_type_id is null)
						--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
						AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
            AND con.is_supplied IS NOT true
						GROUP BY prov.id
        ) AS amz ON amz.id = inv.provider_id
		WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND provider_id IS NOT null
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
				AND con.irpf >= 7
				AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
				AND (inv.invoice_type_id != 'payroll' OR invoice_type_id is null)
				--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_generated_amz IS NOT true
				AND con.is_supplied IS NOT true
        GROUP BY prov.id, amz.amount_euros, common.amount_euros, amz.irpf, common.irpf
				-- GROUP BY prov.id
	) LOOP
		IF OP < 3 THEN
			IF operator.id is not null THEN

			-- NIF_CIF_IVA
			tmp_value := operator.nif_cif_iva;
			tmp_key := 'doc_' || PAG::text || '_nif_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--NAME
			tmp_value := operator.name;
			tmp_key := 'doc_' || PAG::text || '_name_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Provincia
			tmp_value := SUBSTRING(operator.zip FROM 1 FOR 2);
			tmp_key:= 'doc_' || PAG::text || '_provincia_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Clave
			tmp_value := 'G';
			tmp_key := 'doc_' || PAG::text || '_clave_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Subclave
			tmp_value := '01';
			tmp_key := 'doc_' || PAG::text || '_subclave_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			---------DATOS ADICIONALES NOMINAS diferente de clave de A (van vacíos pero son necesarios para que el json no falle al buscar las keys) -------
			--Año nacimiento
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_nacimiento_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Situación Familiar
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_sit_familiar_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Titularidad
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_titularidad_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--NIF conyuge
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_nif_conyuge_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Discapacidad
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_discapacidad_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Contrato
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_contrato_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Movilidad geográfica
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_movilidad_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			-- Amount
			tmp_value := operator.sum_amount;
			tmp_key := 'doc_' || PAG::text || '_base_imponible_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--IRPF
			tmp_value := operator.sum_irpf;
			tmp_key := 'doc_' || PAG::text || '_irpf_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			-- INCREMENTAR OPERADOR
			OP := OP + 1;
			END IF;
		END IF;
		IF OP = 3 THEN
			-- INCREMENTAR PAGINA
			PAG := PAG + 1;
			-- RESETEAR OPERADOR
			OP := 1;
		END IF;

		--COUNT OPERATOR
		count_operators := count_operators + 1;
	END LOOP;

	-- FOR PROVIDERS IRPF < 7%
	FOR operator IN (
		SELECT DISTINCT
            prov.*,
            ROUND((COALESCE(common.amount_euros,0) + COALESCE(amz.amount_euros,0))::numeric ,2) AS sum_amount,
			ROUND((COALESCE(common.irpf,0) + COALESCE(amz.irpf,0))::numeric ,2) AS sum_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
        LEFT JOIN (
            SELECT DISTINCT 
							prov.id,
							SUM(con.amount_euros * con.quantity) as amount_euros,
							SUM(con.quantity * con.amount_euros * con.irpf / 100) AS irpf
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
						AND con.irpf BETWEEN 1 AND 6
						AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
            AND is_generated_amz IS NOT true
            AND is_txt_amz IS NOT true
						AND (inv.invoice_type_id != 'payroll' OR invoice_type_id is null)
						--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
						AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
            AND con.is_supplied IS NOT true
						GROUP BY prov.id
        ) AS common ON common.id = inv.provider_id
		LEFT JOIN (
            SELECT DISTINCT 
						prov.id, 
						SUM(con.amount_euros) as amount_euros,
						SUM(con.amount_euros * con.irpf / 100)AS irpf
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            LEFT JOIN providers_provider prov ON prov.id = inv.provider_id
            WHERE inv.seller_id = sellerid
            AND EXTRACT(YEAR FROM accounting_date) = date_year
            AND EXTRACT(MONTH FROM accounting_date) >= month_min
            AND EXTRACT(MONTH FROM accounting_date) <= month_max
            AND provider_id IS NOT null
            AND status_id = 'revised'
            AND tax_country_id = 'ES'
            AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
            AND con.irpf BETWEEN 1 AND 6
						AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
						AND is_generated_amz IS NOT true
            AND is_txt_amz IS true
						AND (inv.invoice_type_id != 'payroll' OR invoice_type_id is null)
						--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
						AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
            AND con.is_supplied IS NOT true
						GROUP BY prov.id
        ) AS amz ON amz.id = inv.provider_id
		WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND provider_id IS NOT null
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
				AND con.irpf BETWEEN 1 AND 6
				AND (inv.account_expenses_id != '621' OR inv.account_expenses_id is null)
				AND (inv.invoice_type_id != 'payroll' OR invoice_type_id is null)
        AND is_generated_amz IS NOT true
				--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
				AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND con.is_supplied IS NOT true
				GROUP BY prov.id, amz.amount_euros, common.amount_euros, amz.irpf, common.irpf
				-- GROUP BY prov.id
	) LOOP
		IF OP < 3 THEN
			IF operator.id is not null THEN

			-- NIF_CIF_IVA
			tmp_value := operator.nif_cif_iva;
			tmp_key := 'doc_' || PAG::text || '_nif_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--NAME
			tmp_value := operator.name;
			tmp_key := 'doc_' || PAG::text || '_name_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Provincia
			tmp_value := SUBSTRING(operator.zip FROM 1 FOR 2);
			tmp_key:= 'doc_' || PAG::text || '_provincia_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Clave
			tmp_value := 'G';
			tmp_key := 'doc_' || PAG::text || '_clave_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Subclave
			tmp_value := '03';
			tmp_key := 'doc_' || PAG::text || '_subclave_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			---------DATOS ADICIONALES NOMINAS diferente de clave de A (van vacíos pero son necesarios para que el json no falle al buscar las keys) -------
			--Año nacimiento
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_nacimiento_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Situación Familiar
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_sit_familiar_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Titularidad
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_titularidad_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--NIF conyuge
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_nif_conyuge_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Discapacidad
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_discapacidad_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Contrato
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_contrato_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Movilidad geográfica
			tmp_value := '';
			tmp_key := 'doc_' || PAG::text || '_movilidad_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			-- Amount
			tmp_value := operator.sum_amount;
			tmp_key := 'doc_' || PAG::text || '_base_imponible_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--IRPF
			tmp_value := operator.sum_irpf;
			tmp_key := 'doc_' || PAG::text || '_irpf_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			-- INCREMENTAR OPERADOR
			OP := OP + 1;
			END IF;
		END IF;
		IF OP = 3 THEN
			-- INCREMENTAR PAGINA
			PAG := PAG + 1;
			-- RESETEAR OPERADOR
			OP := 1;
		END IF;

		--COUNT OPERATOR
		count_operators := count_operators + 1;
	END LOOP;

	--FOR EMPLOYERS
	FOR operator IN (
		SELECT DISTINCT
		work.*,
		ROUND(SUM(COALESCE(combined.amount_euros, 0)::numeric), 2) AS sum_amount,
		ROUND(SUM(COALESCE(combined.irpf, 0)::numeric), 2) AS sum_irpf
		FROM(
			SELECT DISTINCT
			SUM(con.amount_euros * con.quantity) as amount_euros,
			SUM(con.quantity * con.amount_euros * con.irpf / 100) AS irpf,
			work.id AS id
			FROM invoices_invoice inv
			INNER JOIN invoices_payrollworkerinvoice inv_work ON inv_work.invoice_id = inv.id
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			LEFT JOIN workers_worker work ON work.id = inv_work.worker_id
			WHERE inv.seller_id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND status_id = 'revised'
			AND tax_country_id = 'ES'
			AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
			AND inv.invoice_type_id = 'payroll'
			AND con.irpf > 0
			AND is_generated_amz IS NOT true
			AND is_txt_amz IS NOT true
			--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
			AND con.is_supplied IS NOT true
			GROUP BY work.id
		UNION
			SELECT DISTINCT
			SUM(con.amount_euros * con.quantity) as amount_euros,
			SUM(con.quantity * con.amount_euros * con.irpf / 100)AS irpf,
			work.id AS id
			FROM invoices_invoice inv
			INNER JOIN invoices_payrolladministratorinvoice inv_admin ON inv_admin.invoice_id = inv.id
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			LEFT JOIN workers_worker work ON work.id = inv_admin.worker_id
			WHERE inv.seller_id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND status_id = 'revised'
			AND tax_country_id = 'ES'
			AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
			AND inv.invoice_type_id = 'payroll'
			AND con.irpf > 0
			AND is_generated_amz IS NOT true
			AND is_txt_amz IS NOT true
			--AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy' OR invoice_category_id IS NULL) ) 
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
			AND con.is_supplied IS NOT true
			GROUP BY work.id
		) AS combined
		LEFT JOIN workers_worker work ON work.id = combined.id
		-- GROUP BY work.id, combined.amount_euros, combined.irpf
		GROUP BY work.id
	) LOOP
		IF OP < 3 THEN
			IF operator.id is not null THEN

			--NIF_NIE
			tmp_value := operator.nif_nie;
			tmp_key := 'doc_' || PAG::text || '_nif_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--NAME
			tmp_value := operator.first_name || ' ' || operator.last_name;
			tmp_key := 'doc_' || PAG::text || '_name_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--Provincia
			SELECT SUBSTRING(address_zip FROM 1 FOR 2) INTO tmp_value FROM address_address WHERE id = operator.address_id LIMIT 1;
			tmp_key:= 'doc_' || PAG::text || '_provincia_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			
			IF operator.worker_type = '1' THEN
				--Clave
				tmp_value := 'A';
				tmp_key := 'doc_' || PAG::text || '_clave_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Subclave E
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_subclave_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				---------DATOS ADICIONALES NOMINAS solo en clave de A -------

				--Año nacimiento
				tmp_value := TO_CHAR(operator.birthday, 'YYYY');
				tmp_key := 'doc_' || PAG::text || '_nacimiento_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Situación Familiar
				tmp_value := operator.marital_status;
				tmp_key := 'doc_' || PAG::text || '_sit_familiar_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Titularidad
				tmp_value := operator.is_titular;
				tmp_key := 'doc_' || PAG::text || '_titularidad_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--NIF conyuge
				IF operator.marital_status = '2' THEN
					tmp_value := operator.spouse_nif_nie;
					tmp_key := 'doc_' || PAG::text || '_nif_conyuge_op_' || OP::text;
					tmp_json := json_build_object(tmp_key, tmp_value);
					json_operators := jsonb_concat(json_operators, tmp_json);
				ELSE
					tmp_value := '';
					tmp_key := 'doc_' || PAG::text || '_nif_conyuge_op_' || OP::text;
					tmp_json := json_build_object(tmp_key, tmp_value);
					json_operators := jsonb_concat(json_operators, tmp_json);
				END IF;

				--Discapacidad
				tmp_value := operator.disability;
				tmp_key := 'doc_' || PAG::text || '_discapacidad_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Contrato
				tmp_value := operator.contract_type_id;
				tmp_key := 'doc_' || PAG::text || '_contrato_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Movilidad geográfica
				IF operator.geographical_mobility = 'yes' THEN
					tmp_value := '1';
					tmp_key := 'doc_' || PAG::text || '_movilidad_op_' || OP::text;
					tmp_json := json_build_object(tmp_key, tmp_value);
					json_operators := jsonb_concat(json_operators, tmp_json);
				ELSIF operator.geographical_mobility = 'no' THEN
					tmp_value := '0';
					tmp_key := 'doc_' || PAG::text || '_movilidad_op_' || OP::text;
					tmp_json := json_build_object(tmp_key, tmp_value);
					json_operators := jsonb_concat(json_operators, tmp_json);
				END IF;
			ELSIF  operator.worker_type = '2' THEN
				--Clave
				tmp_value := 'E';
				tmp_key := 'doc_' || PAG::text || '_clave_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Subclave E
				tmp_value := '04';
				tmp_key := 'doc_' || PAG::text || '_subclave_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);
			ELSIF  operator.worker_type = '3' THEN
				--Clave
				tmp_value := 'E';
				tmp_key := 'doc_' || PAG::text || '_clave_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Subclave E
				tmp_value := '01';
				tmp_key := 'doc_' || PAG::text || '_subclave_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);
			END IF;

			---------DATOS ADICIONALES NOMINAS diferente de clave de A (van vacíos pero son necesarios para que el json no falle al buscar las keys) -------
			IF operator.worker_type != '1' THEN
				--Año nacimiento
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_nacimiento_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Situación Familiar
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_sit_familiar_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Titularidad
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_titularidad_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--NIF conyuge
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_nif_conyuge_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Discapacidad
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_discapacidad_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Contrato
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_contrato_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);

				--Movilidad geográfica
				tmp_value := '';
				tmp_key := 'doc_' || PAG::text || '_movilidad_op_' || OP::text;
				tmp_json := json_build_object(tmp_key, tmp_value);
				json_operators := jsonb_concat(json_operators, tmp_json);
			END IF;

			-- Amount
			tmp_value := operator.sum_amount;
			tmp_key := 'doc_' || PAG::text || '_base_imponible_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			--IRPF
			tmp_value := operator.sum_irpf;
			tmp_key := 'doc_' || PAG::text || '_irpf_op_' || OP::text;
			tmp_json := json_build_object(tmp_key, tmp_value);
			json_operators := jsonb_concat(json_operators, tmp_json);

			-- INCREMENTAR OPERADOR
			OP := OP + 1;
			END IF;
		END IF;
		IF OP = 3 THEN
			-- INCREMENTAR PAGINA
			PAG := PAG + 1;
			-- RESETEAR OPERADOR
			OP := 1;
		END IF;

		--COUNT OPERATOR
		count_operators := count_operators + 1;
	END LOOP;

	LAST_PAGE:= ROUND((count_operators / 2),0);
	LAST_PAGE := LAST_PAGE + 1;

    --CASILLA 01: Número total de perceptores incluidos. O sea, cuantos proveedores y/o empleados estan contenidos en el documento. Tantos como CLAVES usen
	CA01 := count_operators;

	--CASILLA 02: Suma de todas las bases que han tenido retención. Todas las facturas TAX COUNTRY ES, con retención de IRPF del año y que NO sean alquileres (cuenta contable 621). Incluidas las nóminas laborales y de administradores
	CA02 := inv_amount.sum_amount;
	CA02 := ROUND( CA02 , 2 ); 

	--CASILLA 03: Suma de todas las retenciones de IRPF del año que no sean alquileres (cuenta contable 621). Incluidas las nóminas laborales y de administradores
	CA03 := inv_irpf.sum_irpf;
	CA03 := ROUND( CA03 , 2 );

-- MAKE JSON
json_result := jsonb_build_object(
	'CA01', CA01,
	'CA02', CA02,
	'CA03', CA03,
	'LAST_PAGE', LAST_PAGE
);

	-- RETURN JSON
	RETURN json_result || json_operators;
END;
$$ LANGUAGE plpgsql;


-- USAR LA FUNCION
-- SELECT func_calc_model_es_190(241, 2023, 01, 12);
-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_190(INTEGER,INTEGER,INTEGER,INTEGER);