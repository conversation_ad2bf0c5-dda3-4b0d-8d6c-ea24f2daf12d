/*
    description Of variables for build for theme layouts
        1) menu-caption-color
            List of color for sidebar menu caption

        2) brand-background
            List of color for logo background

        3) header-dark-background
            List of color for Dark Header

        4) header-light-background
            List of color for light Header

        5) menu-dark-background
            List of color for Dark sidebar menu

        6) menu-light-background
            List of color for light sidebar menu

        7) menu-active-color
            List of color for Active item highliter

        8) menu-icon-color
            List of color for sidebar menu items icon colors
*/
/**  =====================
     Dark css start
==========================  **/
body {
    color: #adb7be;
    background: #212224;
}

body.layout-6 .pcoded-content {
    background: #212224;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #c9d0d5;
}

.text-muted {
    color: #919ea7 !important;
}

hr {
    border-top-color: #282a2c;
}

.bg-light,
.bg-white {
    background-color: #282a2c !important;
}

.text-white {
    color: #c9d0d5 !important;
}

#timer.text-white,
.btn-theme,
.text-white i,
a.btn-theme,
i.text-white {
    color: #fff !important;
}

.label.text-white,
.send-chat .text-white {
    color: #fff !important;
}

.scroll-div > .scroll-element .scroll-bar {
    background-color: #090909;
}

.page-header-title + .breadcrumb > .breadcrumb-item a {
    color: #adb7be;
}

.page-header-title + .breadcrumb > .breadcrumb-item:last-child a {
    color: #c9d0d5;
}

text {
    fill: #adb7be !important;
}

.dropdown-item,
.page-link,
.text-secondary {
    color: #adb7be !important;
}

a {
    color: #adb7be;
}

a.text-secondary {
    color: #adb7be !important;
}

a.text-secondary:focus, a.text-secondary:hover {
    color: #adb7be;
}

a.text-secondary.btn, a.text-secondary.btn:Active, a.text-secondary.btn:focus, a.text-secondary.btn:hover {
    color: #fff !important;
}

.dropdown-divider {
    border: 1px solid #282a2c;
}

/* ==========  card css start  =========== */
.card {
    background: #323437;
    box-shadow: 0 1px 20px 0 rgba(0, 0, 0, 0.08);
}

.card .card-header {
    background-color: transparent;
    border-bottom: 1px solid #2b2c2f;
}

.card .card-header h5 {
    color: #9fabb3 !important;
}

.card .card-header h5:after {
    background-color: #46484c;
}

.card .card-header .card-header-right .btn.dropdown-toggle {
    color: #c9d0d5;
}

.card .card-footer {
    border-top: 1px solid #2d2f31;
    background: transparent;
}

.card.card-load .card-loader {
    background-color: rgba(33, 34, 36, 0.8);
}

.card.card-load .card-loader i {
    color: #04a9f5;
}

/* ==========  card css End  =========== */
/* ================================    Dropdown Start  ===================== */
.dropdown-menu {
    background-color: #323437;
    box-shadow: 0 1px 20px 0 rgba(0, 0, 0, 0.08);
}

.dropdown-menu.show:before {
    color: #323437;
    text-shadow: 0 -2px 2px rgba(0, 0, 0, 0.08);
}

.dropdown-menu > li > a {
    color: #adb7be;
}

.dropdown-menu > li.active, .dropdown-menu > li:active, .dropdown-menu > li:focus, .dropdown-menu > li:hover {
    background: rgba(173, 183, 190, 0.1);
}

.dropdown-menu > li.active > a, .dropdown-menu > li:active > a, .dropdown-menu > li:focus > a, .dropdown-menu > li:hover > a {
    background: transparent;
}

.dropdown-item:focus, .dropdown-item:hover {
    color: #adb7be;
    background-color: rgba(173, 183, 190, 0.1);
}

/* ====================  Navbar Start  ===================== */
.pcoded-navbar {
    box-shadow: 2px 0 20px 0 rgba(0, 0, 0, 0.08);
}

.pcoded-navbar .mobile-menu span {
    background-color: #adb7be;
}

.pcoded-navbar .mobile-menu span:after, .pcoded-navbar .mobile-menu span:before {
    background-color: #adb7be;
}

/* ===================  Navbar end  ===================== */
.pcoded-header {
    color: #adb7be;
}

.pcoded-header .dropdown-menu {
    color: #adb7be;
}

.pcoded-header .dropdown-menu a {
    color: #adb7be;
}

.pcoded-header .dropdown-menu > li > a {
    color: #adb7be;
}

.pcoded-header .dropdown-menu > li.active > a, .pcoded-header .dropdown-menu > li:active > a, .pcoded-header .dropdown-menu > li:focus > a, .pcoded-header .dropdown-menu > li:hover > a {
    background: transparent;
}

.pcoded-header .input-group .input-group-text,
.pcoded-header a,
.pcoded-header dropdown-toggle {
    color: #adb7be;
}

.pcoded-header .input-group .input-group-text:hover,
.pcoded-header a:hover,
.pcoded-header dropdown-toggle:hover {
    color: #fff;
}

.pcoded-header .main-search .search-close > .input-group-text {
    color: #adb7be;
}

.pcoded-header .main-search.open .input-group {
    background: #2d2f31;
    border-color: #465159;
}

.pcoded-header .main-search.open .input-group .search-btn .input-group-text {
    color: #fff;
}

.pcoded-header .dropdown.show:before {
    color: #323437;
    text-shadow: 0 -1px 2px rgba(0, 0, 0, 0.12);
}

.pcoded-header .dropdown .notification .noti-head {
    border-bottom: 1px solid #2d2f31;
}

.pcoded-header .dropdown .notification .noti-body li.notification:hover {
    background: rgba(4, 169, 245, 0.1);
}

.pcoded-header .dropdown .notification .noti-body li p strong {
    color: #c9d0d5;
}

.pcoded-header .dropdown .notification .noti-footer {
    border-top: 1px solid #2d2f31;
}

.pcoded-header .dropdown .profile-notification .pro-head {
    color: #adb7be;
    background: #46484c;
}

.pcoded-header .dropdown .profile-notification .pro-head .dud-logout {
    color: #adb7be;
}

.pcoded-header .dropdown.drp-user.show:before {
    color: #46484c;
}

.pcoded-header .dropdown .pro-body li a:hover {
    background: rgba(173, 183, 190, 0.1);
    background: transparent;
}

/**  =====================
      Chatting css start
==========================  **/
.header-chat,
.header-user-list {
    background-color: #282a2c;
}

.header-chat .h-list-header,
.header-user-list .h-list-header {
    border-bottom: 1px solid #1a1a1c;
}

.header-chat .h-list-header .input-group,
.header-user-list .h-list-header .input-group {
    background: transparent;
}

.header-chat .h-list-header a,
.header-user-list .h-list-header a {
    color: #adb7be;
}

.header-chat .h-list-header .form-control,
.header-user-list .h-list-header .form-control {
    background: #323437;
    color: #adb7be;
}

.header-chat.open,
.header-user-list.open {
    box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.2);
}

.header-user-list .h-list-body .userlist-box:after {
    background: #1a1a1c;
}

.header-user-list .h-list-body .userlist-box .live-status {
    background: #1dc4e9;
    color: #111;
}

.header-user-list .h-list-body .userlist-box .text-c-green {
    color: #1dc4e9;
}

.header-user-list.open .h-close-text i {
    color: #adb7be;
}

.header-user-list.open .h-close-text:after {
    color: #282a2c;
    text-shadow: -4px 0 7px rgba(0, 0, 0, 0.12);
}

.header-user-list.open.msg-open:after {
    color: rgba(4, 169, 245, 0.1);
}

.header-chat .h-list-body {
    background: #212224;
}

.header-chat .h-list-body .chat-messages .chat-menu-reply > div p {
    background: #2d2f31;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.1);
}

.header-chat .h-list-body .chat-messages .chat-menu-reply > div:before {
    color: #2d2f31;
    text-shadow: 7px 10px 20px rgba(0, 0, 0, 0.1);
}

.header-chat .h-list-body .chat-messages .chat-menu-content > div p {
    background: #0d0e0f;
    color: #fff;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.1);
}

.header-chat .h-list-body .chat-messages .chat-menu-content > div:before {
    color: #0d0e0f;
    text-shadow: -4px 4px 10px rgba(0, 0, 0, 0.15);
}

.header-chat .h-list-footer {
    background: #212224;
}

.header-chat .h-list-footer .input-group {
    background: #393b3f;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.1);
}

.header-chat .h-list-footer .input-group .form-control,
.header-chat .h-list-footer .input-group .input-group-text {
    color: #adb7be;
    background: transparent;
}

.header-chat .h-list-footer .input-group .btn-send .input-group-text {
    color: #fff;
}

.header-chat .h-list-footer .input-group:after {
    color: #393b3f;
    text-shadow: 4px 10px 20px rgba(0, 0, 0, 0.1);
}

.header-chat .h-list-footer .input-group .btn-primary {
    background: #101011;
    border-color: #101011;
}

.header-chat .h-list-footer .input-group .btn-success {
    background: #212224;
    border-color: #212224;
}

/**====== Chat css end ======**/
.border-bottom {
    border-bottom: 1px solid #2d2f31 !important;
}

.border-top {
    border-top: 1px solid #2d2f31 !important;
}

.task-list:after {
    background: #2d2f31;
}

.table td {
    border-top: 1px solid #2d2f31;
}

.table thead th {
    border-bottom: 1px solid #2d2f31;
}

/* ======================   basic componant   ================== */
.tooltip-inner {
    box-shadow: 0 0 15px rgba(17, 17, 17, 0.41);
}

.tooltip .arrow::before {
    text-shadow: 0 2px 3px rgba(17, 17, 17, 0.41);
}

.card .card-block code {
    background: #212224;
}

.breadcrumb {
    background: #212224;
}

.breadcrumb .breadcrumb-item.active {
    color: #adb7be;
}

.page-link {
    color: #007bff;
    background-color: #212224;
    border: 1px solid #090909;
}

.page-link:hover {
    background-color: #090909;
    border: 1px solid #090909;
}

.page-item.disabled .page-link {
    background-color: #212224;
    border: 1px solid #212224;
}

.blockquote {
    border-left-color: #212224;
}

.blockquote.text-end {
    border-right-color: #212224;
}

.blockquote-footer {
    color: #919ea7;
}

.table th,
.table thead th,
.table-bordered td,
.table-bordered th {
    border-color: #2d2f31;
}

.table-striped tbody tr:nth-of-type(2n+1) {
    background-color: #212224;
}

.footable .label-default,
.footable .pagination > .disabled > a,
.footable .pagination > li > a,
.footable.table-striped > tbody > tr:nth-child(odd) {
    background-color: rgba(9, 9, 9, 0.25);
    border: 1px solid #2d2f31;
    color: #919ea7;
}

.footable.table > tbody > tr > td,
.footable.table > tfoot > tr > td {
    border-top: 1px solid #2d2f31;
}

.footable.table > thead > tr > th {
    border-bottom: 2px solid #282a2c;
}

.footable-details.table-hover > tbody > tr:hover,
.footable.table-hover > tbody > tr:hover {
    background: #212224;
}

.form-material .form-control {
    border-color: #212224;
}

table.dataTable.table-striped.DTFC_Cloned tbody {
    background-color: #1a1a1c;
}

table.DTFC_Cloned tr {
    background-color: #323437;
}

.highcharts-background {
    fill: #323437 !important;
}

.progress {
    background-color: #1a1a1c;
}

.nav-tabs .nav-link {
    color: #adb7be;
    background: #212224 !important;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
    color: #fff;
    background: #323437 !important;
    box-shadow: 0 -4px 10px 0 rgba(0, 0, 0, 0.12);
}

.nav-pills {
    background: #212224;
    box-shadow: 0 1px 20px 0 rgba(0, 0, 0, 0.08);
}

.nav-pills .nav-link {
    color: #adb7be;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
    color: #fff !important;
    background: #04a9f5;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.2);
}

.nav-tabs .nav-link,
.tab-content {
    background: #323437;
    box-shadow: 0 1px 20px 0 rgba(0, 0, 0, 0.08);
}

.form-control-plaintext {
    color: #adb7be;
}

.input-group-text {
    border-color: #1a1a1c;
}

.custom-file-label,
.custom-select,
.form-control {
    background: #282a2c;
    color: #adb7be;
    border-color: #1a1a1c;
}

.custom-file-label:focus,
.custom-select:focus,
.form-control:focus {
    background: #262729;
    color: #adb7be;
}

.switch input[type=checkbox] + .cr {
    border: 1px solid #adb7be;
}

.custom-file-label::after {
    background-color: #1a1a1c;
    color: #adb7be;
    border-left: 1px solid #282a2c;
}

.form-control:disabled,
.form-control[readonly] {
    background: #232527;
    color: #a4b0b7;
}

.bootstrap-tagsinput {
    background: #282a2c;
    border: 1px solid #282a2c;
}

.input-group {
    background-color: #282a2c;
}

.dtp-content text {
    fill: #000 !important;
}

.select2-container--default .select2-selection--multiple,
.select2-container--default .select2-selection--single {
    background: #282a2c;
    border: 1px solid #212224;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered,
.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #adb7be;
}

.select2-container--default.select2-container--disabled .select2-selection--single {
    background: #282a2c;
}

.ms-container .ms-list {
    border: 1px solid #212224;
}

.ms-container .ms-optgroup-label {
    color: #adb7be;
}

.ms-container .ms-selectable,
.ms-container .ms-selection {
    background: #282a2c;
    color: #adb7be;
}

.ms-container .ms-selectable li.ms-elem-selectable,
.ms-container .ms-selectable li.ms-elem-selection,
.ms-container .ms-selection li.ms-elem-selectable,
.ms-container .ms-selection li.ms-elem-selection {
    border-bottom: 1px solid #212224;
    color: #adb7be;
    background: #282a2c;
}

.sw-theme-default .step-anchor {
    background: #282a2c;
}

.sw-theme-default .step-content,
.sw-theme-default .sw-container {
    background: #282a2c;
}

.sw-theme-default ul.step-anchor > li a {
    color: #adb7be;
    background: #282a2c;
}

.sw-theme-default ul.step-anchor > li a > h6,
.sw-theme-default ul.step-anchor > li a p {
    color: #adb7be !important;
}

.sw-theme-arrows .sw-container,
.sw-theme-circles .sw-container,
.sw-theme-dots .sw-container {
    background: #282a2c;
}

.sw-theme-arrows {
    border: 1px solid #232527;
}

.sw-theme-arrows .step-content,
.sw-theme-arrows .sw-container {
    background: #282a2c;
}

.sw-theme-arrows ul.step-anchor > li a {
    color: #adb7be;
    background: #282a2c;
}

.sw-theme-arrows ul.step-anchor > li a > h6,
.sw-theme-arrows ul.step-anchor > li a p {
    color: #adb7be;
}

.sw-theme-arrows ul.step-anchor > li a:after {
    border-left: 30px solid #282a2c;
}

.sw-theme-arrows ul.step-anchor > li a:before {
    border-left: 30px solid #101011;
}

.sw-theme-arrows > ul.step-anchor {
    background: #282a2c;
    border: 1px solid #232527;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #232527;
    border: 1px solid #323437;
}

.sw-theme-default > ul.step-anchor > li.done > a.nav-link:after {
    background: #393b3f;
}

.sw-theme-default > ul.step-anchor > li > a.nav-link:after {
    background: #101011;
}

.sw-theme-dots .step-content,
.sw-theme-dots .sw-toolbar,
.sw-theme-dots > ul.step-anchor {
    background: #282a2c;
}

.sw-theme-arrows > ul.step-anchor > li.done > a {
    background: #282a2c !important;
}

.sw-theme-arrows > ul.step-anchor > li.done > a:after {
    border-left: 30px solid #282a2c !important;
}

.sw-theme-arrows > ul.step-anchor > li.active > a {
    background: #101011 !important;
}

.sw-theme-arrows > ul.step-anchor > li.active > a:after {
    border-left: 30px solid #101011 !important;
}

.sw-theme-dots > ul.step-anchor > li.done > a {
    color: #c9d0d5;
}

.ck.ck-editor__main > .ck-editor__editable {
    background: #282a2c !important;
}

.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
    border-color: #232527 !important;
}

.ck.ck-toolbar__separator {
    background: #101011;
}

.document-editor__editable-container {
    background: #212224;
}

.document-editor__editable-container .ck-editor__editable.ck-editor__editable_inline {
    background: #212224;
    border: 1px solid #101011;
}

.document-editor {
    border: 1px solid #101011;
}

.ck-content .table table,
.ck-content .table table td,
.ck-content .table table th {
    border-color: #101011;
    background: #212224;
}

.ck.ck-toolbar {
    background: #212224;
    border: 1px solid #212224;
}

.document-editor__toolbar {
    border-bottom: 1px solid #101011;
}

.ck.ck-button .ck-button__label,
.ck.ck-icon {
    color: #adb7be;
}

.fc-state-default {
    background-color: #212224 !important;
    background-image: none;
    color: #adb7be !important;
    text-shadow: none !important;
    box-shadow: none !important;
}

.fc-unthemed td.fc-today {
    background: #212224;
}

.fullcalendar-card .fc-button {
    border-color: #323437;
}

.h-list-body .chat-messages .chat-menu-reply > div:before {
    color: #101011;
}

.h-list-body .chat-messages .chat-menu-reply > div p {
    background: #101011;
}

table.dataTable.fixedHeader-floating,
table.dataTable.fixedHeader-locked {
    background: #212224;
}

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed fc-list-heading td,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
    border-color: #282a2c;
}

.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-popover .fc-header {
    background-color: #282a2c;
}

.i-main .i-block {
    border: 1px solid #212224;
}

.invoice-total.table {
    background: #282a2c;
}

.filter-bar .navbar {
    background: #282a2c;
}

.task-board-left .task-right-header-revision,
.task-board-left .task-right-header-status,
.task-board-left .task-right-header-users {
    border-color: #282a2c;
}

.h-list-body .userlist-box:after {
    background: #282a2c;
}

.h-list-body .userlist-box.active {
    background: #282a2c;
}

.msg-card .msg-block > .row > div:before {
    background: #282a2c;
}

.msg-card .msg-user-chat {
    background: #282a2c;
}

.note-card .note-box-aside {
    border-right: 1px solid #282a2c;
}

.note-card .note-write {
    background: #282a2c;
}

.note-card .note-write:after, .note-card .note-write:before {
    border-left: 1px solid #232527;
}

.note-card .list-group-item,
.note-card .list-group-item.active {
    background: #282a2c;
    border-color: #232527;
    color: #adb7be;
}

.filter-bar .card-task .task-list-table i {
    color: #adb7be;
}

.task-data .dropdown-toggle:after,
.task-data i {
    color: #adb7be;
}

.table-columned > tbody > tr > td {
    border-left: 1px solid #2b2c2f;
}

#task-container li {
    background: #212224;
    border: 1px solid #212224;
    border-top: 5px solid #2d2f31;
    color: #adb7be;
}

.Active-visitor .card-active > div + div,
.card-social .card-active > div + div {
    border-left: 1px solid #282a2c;
}

.earning-date .bd-example .nav-pills .nav-link {
    color: #adb7be;
}

.bd-example-modal,
.bd-example-row {
    background: #282a2c;
}

pre[class*=language-] > code {
    box-shadow: -1px 0 0 0 #232527, 0 0 0 1px #282a2c;
}

.modal-content {
    background: #212224;
    border: 1px solid #2d2f31;
}

.modal-header {
    border-bottom: 1px solid #2d2f31;
}

.modal-footer {
    border-top: 1px solid #2d2f31;
}

.close {
    text-shadow: none;
    color: #adb7be;
}

/* ======================   Advanced componant   ================== */
.grid-stack {
    background: #282a2c;
}

.slider-track {
    background: #212224;
}

:not(pre) > code[class*=language-],
pre[class*=language-] {
    background: #212224;
}

.card .card-block pre[class*=language-] > code {
    box-shadow: -1px 0 0 0 #04a9f5, 0 0 0 1px #2d2f31;
    background: #212224;
    background-size: 3em 3em;
    background-origin: content-box;
    background-attachment: local;
}

code[class*=language-],
pre[class*=language-] {
    color: #adb7be;
}

.token.entity,
.token.operator,
.token.url,
.token.variable {
    background: transparent;
}

.nestable-lists {
    border-top: 2px solid #282a2c;
    border-bottom: 2px solid #282a2c;
}

#nestable2 .dd-item > button:before,
.dd-item > button {
    color: #adb7be;
}

#nestable2 .dd-handle,
.dd-handle {
    color: #adb7be;
    border: 1px solid #212224;
}

#nestable2 .dd-handle:hover,
.dd-handle:hover {
    color: #b6bec5;
    background: #212224;
}

.dd-placeholder {
    background: #282a2c;
    border-color: #adb7be;
}

.dd3-content,
.dd3-handle {
    color: #adb7be;
    border: 1px solid #323437;
    background: #212224;
}

.dd3-content:hover {
    color: #b6bec5;
    background: #212224;
}

.dropzone .dz-message {
    color: #b6bec5;
}

.chat-sanders .form-control,
.jstree-default .jstree-clicked {
    background: #212224;
}

.chat-sanders .card-header {
    background: linear-gradient(-135deg, #323437 0%, #212224 100%);
}

.earning-date .bd-example .nav-pills .nav-link.active {
    background: #212224;
    color: #fff;
}

.earning-date .bd-example .nav-pills .nav-link.active:after {
    border-bottom: 5px solid #212224;
}

.datepicker {
    color: #adb7be;
    background: #212224 !important;
}

.datepicker-dropdown.datepicker-orient-bottom:before {
    border-bottom-color: #212224 !important;
}

.datepicker-dropdown.datepicker-orient-bottom:after {
    border-bottom: 6px solid #212224 !important;
}

.datepicker-dropdown.datepicker-orient-top:before {
    display: none;
}

.datepicker-dropdown.datepicker-orient-top:after {
    border-top: 6px solid #212224 !important;
}

.dtp table.dtp-picker-days tr > td > a.selected {
    color: #fff !important;
}

.style-block .nav-pills {
    background: transparent;
}

.offline-box iframe {
    border: 1px solid #212224;
}

.chat-sanders .received-chat .msg {
    background: #212224;
}

.chat-sanders .received-chat .msg:after {
    border-bottom-color: #212224;
}

.trash {
    background: #212224;
    border-color: #323437;
}

.syntax-output {
    border-color: #212224;
}

.syntax-output pre {
    color: #adb7be;
}

.ck-content .image > figcaption {
    color: #adb7be !important;
    background: #323437 !important;
}

.message-mobile .task-right-header-status {
    border-bottom: 1px solid #282a2c;
}

.menu-styler .theme-color > a[data-value=reset] {
    color: #fff !important;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header {
    color: #fff;
    color: rgba(255, 255, 255, 0.8);
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .profile-notification li > a {
    color: #888;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .b-title {
    color: #fff;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown-menu {
    color: #888;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown-menu a {
    color: #888;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown-menu > li > a {
    color: #888;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown-menu > li.active, .pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown-menu > li:active, .pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown-menu > li:focus, .pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown-menu > li:hover {
    color: #888;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header a,
.pcoded-navbar.theme-horizontal ~ .pcoded-header dropdown-toggle {
    color: rgba(255, 255, 255, 0.8);
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header a:hover,
.pcoded-navbar.theme-horizontal ~ .pcoded-header dropdown-toggle:hover {
    color: #fff;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .main-search.open .input-group .search-btn .input-group-text {
    color: #fff;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown .profile-notification .pro-head {
    color: #fff;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header .dropdown .profile-notification .pro-head .dud-logout {
    color: #fff;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header:not([class*=header-]) {
    background: linear-gradient(-135deg, #1de9b6 0%, #1dc4e9 100%);
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header:not([class*=header-]) .b-bg {
    background: #fff;
}

.pcoded-navbar.theme-horizontal ~ .pcoded-header:not([class*=header-]) .b-bg i {
    color: #1de9b6;
    background-image: linear-gradient(-135deg, #1de9b6 0%, #1dc4e9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: unset;
}

@media only screen and (max-width: 991px) {
    .pcoded-header {
        background: #323437;
        color: #fff;
    }

    .pcoded-header > .collapse:not(.show) {
        background: #393b3f;
        box-shadow: 0 1px 20px 0 rgba(0, 0, 0, 0.08);
    }
}
