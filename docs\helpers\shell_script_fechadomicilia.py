# docker compose -f local.yml run --rm django python manage.py shell

import json
import os
from datetime import datetime
from muaytax.dictionaries.models.dates_direct_debit import DatesDirectDebit

# Ruta relativa al archivo JSON
json_path = os.path.join("docs", "helpers", "fechadomicilia.json")

# Cargar datos desde el archivo
with open(json_path, "r", encoding="utf-8") as f:
    data = json.load(f)

# Recorrer registros y crear/actualizar
for entry in data:
    # Convertir fechas de texto a datetime
    entry["date"] = datetime.fromisoformat(entry["date"])
    entry["start_date_aeat"] = datetime.fromisoformat(entry["start_date_aeat"])
    entry["date_aeat"] = datetime.fromisoformat(entry["date_aeat"])

    DatesDirectDebit.objects.update_or_create(
        period=entry["period"],
        model=entry["model"],
        year=entry["year"],
        defaults={
            "date": entry["date"],
            "start_date_aeat": entry["start_date_aeat"],
            "date_aeat": entry["date_aeat"],
        },
    )

print("Fechas de domiciliación cargadas correctamente.")
