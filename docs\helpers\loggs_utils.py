import os
import logging
from django.conf import settings

class LogHandler:
    """Clase para manejar logs con colores ANSI en Docker y entornos locales."""

    logger = logging.getLogger(__name__)

    # Detectar si estamos en Docker
    IN_DOCKER = os.path.exists("/.dockerenv")
    FORCE_COLOR = os.getenv("PY_COLORS", "0") == "1"

    # Variable de depuración desde settings
    debug = settings.DEBUG  

    # Habilitar colores si `DEBUG=True` o `PY_COLORS=1`
    USE_COLOR = debug or FORCE_COLOR

    # Definir los 8 colores básicos ANSI
    BLACK = "\033[30m" if USE_COLOR else ""
    RED = "\033[31m" if USE_COLOR else ""
    GREEN = "\033[32m" if USE_COLOR else ""
    YELLOW = "\033[33m" if USE_COLOR else ""
    BLUE = "\033[34m" if USE_COLOR else ""
    MAGENTA = "\033[35m" if USE_COLOR else ""
    CYAN = "\033[36m" if USE_COLOR else ""
    WHITE = "\033[37m" if USE_COLOR else ""
    RESET = "\033[0m" if USE_COLOR else ""

    # Diccionario de traducción de errores comunes
    ERROR_TRANSLATIONS = {
        # Errores comunes en Python (PY)
        "division by zero": "División por cero",
        "name '": "Variable no definida: ",
        "list index out of range": "Índice de lista fuera de rango",
        "tuple index out of range": "Índice de tupla fuera de rango",
        "string index out of range": "Índice de cadena fuera de rango",
        "key error": "Clave no encontrada en el diccionario",
        "attribute error": "El objeto no tiene el atributo especificado",
        "module not found error": "Módulo no encontrado",
        "type error": "Tipo de dato inválido en operación",
        "unsupported operand type(s)": "Tipos de operandos incompatibles",
        "value error": "Valor incorrecto para la operación",
        "invalid literal for int()": "No se puede convertir a entero",
        "zero division error": "Intento de dividir por cero",
        "indentation error": "Error de indentación",
        "unexpected indent": "Indentación inesperada",
        "syntax error": "Error de sintaxis",
        "eof while scanning string literal": "Fin de archivo inesperado en cadena de texto",
        "is not defined": "No está definido",
        "recursion error": "Se superó el límite de recursión",
        "memory error": "No hay suficiente memoria disponible",
        
        # Errores comunes en Django (DJ)
        "does not exist": "No existe en la base de datos",
        "multiple objects returned": "Se encontraron múltiples objetos en la consulta",
        "field required": "Campo requerido",
        "not null constraint failed": "El campo no puede ser nulo",
        "cannot be null": "No puede ser nulo",
        "invalid password": "Contraseña inválida",
        "authentication credentials were not provided": "No se proporcionaron credenciales de autenticación",
        "you do not have permission to perform this action": "No tienes permiso para realizar esta acción",
        "reverse for": "No se encontró una URL inversa válida",
        "no such table": "Tabla no encontrada en la base de datos",
        "table doesn't exist": "La tabla no existe en la base de datos",
        "database is locked": "La base de datos está bloqueada",
        "data truncated for column": "Los datos fueron truncados para la columna",
        "integrity error": "Error de integridad en la base de datos",
        "foreign key constraint failed": "Falló la restricción de clave foránea",
        "bad request": "Solicitud incorrecta",
        "csrf verification failed": "Fallo en la verificación CSRF",
        "request method not allowed": "Método de solicitud no permitido",
        "server error": "Error interno del servidor",
        "template does not exist": "No se encontró la plantilla especificada",
        "staticfiles cannot be found": "No se pueden encontrar archivos estáticos",
    }

    @classmethod
    def translate_error(cls, error_msg):
        """Reemplaza errores comunes en inglés por español."""
        error_msg = error_msg.lower()
        for en, es in cls.ERROR_TRANSLATIONS.items():
            if en in error_msg:
                return error_msg.replace(en, es)
        return f"{error_msg} (No traducido)"

    @classmethod
    def debug_status(cls):
        """Muestra información sobre Debug, Docker y Colores."""
        print(f"{cls.CYAN} Debug Mode: {cls.debug}{cls.RESET}")
        print(f"{cls.YELLOW} In Docker: {cls.IN_DOCKER}{cls.RESET}")
        print(f"{cls.RED} Force Colors: {cls.FORCE_COLOR}{cls.RESET}")
        print(f"{cls.GREEN} Colors Active: {cls.USE_COLOR}{cls.RESET}")

    @classmethod
    def print(cls, message: str, color=None):
        """Imprime mensajes en la consola con colores."""
        color = color or cls.CYAN
        print(f"{color}{message}{cls.RESET}")

    @classmethod
    def log_error(cls, error: Exception):
        """Registra errores en `logger.error` y los imprime con color."""
        error_msg = str(error)

        # Guardar en logs de Django sin colores
        cls.logger.error(f"Error: {error_msg}")

        # Mostrar en consola con colores si está habilitado
        print(f"{cls.RED} Error: {error_msg}{cls.RESET}")
