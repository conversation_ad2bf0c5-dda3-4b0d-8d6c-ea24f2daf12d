    📄 MODULO MigrationsInfoForm — DOCUMENTACIÓN TÉCNICA
    junio 2025 — ForIVA

    >> OBJETIVO: El formulario MigrationInfoForm captura los datos relacionados con la migración de un gestor anterior en servicios de mantenimiento de IVA contratados para países como 🇫🇷 Francia, 🇮🇹 Italia, 🇬🇧 Reino Unido y 🇩🇪 Alemania. Reúne datos desde:

        - Modelo SellerVatMigration,
        - Atributos del modelo SellerVat,
        - Documentos relacionados por país.

    >> FUENTES DE DATOS Y CAMPOS DEL FORMULARIO

        ┌─────────────────────────────────────────────────┐
        │        FUENTE PRINCIPAL: SellerVatMigration     │
        └─────────────────────────────────────────────────┘
    ┌────────────────────────────────────────────┬──────────────────────────────────┬───────────────┐
    │ CAMPO                                      │ ¿EN MODELO?    │ OBSERVACIONES                   │
    ├────────────────────────────────────────────┼────────────────┼─────────────────────────────────┤
    │ last_tax_declaration_date                  │     Si         │ Fecha declar. anterior          │
    │ last_tax_declaration_submitted_by_previous │     Si         │ Declaración anterior del gestor │
    │ first_tax_declaration_submitted_by_us      │     Si         │ Primera declaración de MuayTax  │
    │ previous_manager_name                      │     Si         │ Solo si FR y no-UE              │
    │ previous_manager_address (AddressForm)     │     Si         │ Solo si FR y no-UE              │
    │ gov_gateway_user_id                        │     Si         │ Solo para GB                    │
    │ gov_gateway_password                       │     Si         │ Solo para GB                    │
    │ gov_gateway_phone_number                   │     Si         │ Normalizado                     │
    │ gov_gateway_phone_country                  │     Si         │ Default = GB                    │
    └────────────────────────────────────────────┴────────────────┴─────────────────────────────────┘

        ┌───────────────────────────────────────────────┐
        │       FUENTE AUXILIAR: SellerVat              │
        └───────────────────────────────────────────────┘
    ┌──────────────────────┬──────────────┬────────────────────────────────────┐
    │ CAMPO                │ ¿EN MODELO?  │ OBSERVACIONES                      │
    ├──────────────────────┼──────────────┼────────────────────────────────────┤
    │ vat_number           │    Si        │ Siempre visible                    │
    │ siret                │    Si        │ Solo si país = "FR"                │
    │ steuernummer         │    Si        │ Solo si país = "DE"                │
    │ codice_fiscale       │    Si        │ Solo si país = "IT"                │
    │ vat_frequency        │    Si        │ Solo visible en el frontend        │
    └──────────────────────┴──────────────┴────────────────────────────────────┘

        ┌──────────────────────────────────────────────────────┐
        │        DOCUMENTOS ASOCIADOS POR PAÍS                 │
        └──────────────────────────────────────────────────────┘
    ┌───────────────────────────────┬────────────────────────────────────────┬────────────────────────────┐
    │ DOCUMENTO                     │               CLAVE                    │ OBSERVACIONES              │
    ├───────────────────────────────┼────────────────────────────────────────┼────────────────────────────┤
    │ Cert. NIF/IVA actual (FR, IT) │ FR-CERTIFICATEVATNUMBER, IT-...        │ Desde `Document`           │
    │ Cert. Steuernummer (DE)       │ DE-CERTIFICATEVATNUMBER                │ Campo especial             │
    └───────────────────────────────┴────────────────────────────────────────┴────────────────────────────┘

    >> LÓGICA DE VISIBILIDAD (CONDICIONES)

        ┌────────────────────────────┐
        │        CONDICIONES         │
        └────────────────────────────┘
    ┌────────────┬────────────────────────────────────────────────────────────┐
    │ País       │ Lógica de visibilidad                                      │
    ├────────────┼────────────────────────────────────────────────────────────┤
    │ FR         │ Mostrar `previous_manager_name` y dirección **si**         │
    │            │ `country_of_incorporation_is_ue == False`                  │
    ├────────────┼────────────────────────────────────────────────────────────┤
    │ GB         │ Mostrar campos de Government Gateway                       │
    ├────────────┼────────────────────────────────────────────────────────────┤
    │ DE         │ Mostrar `steuernummer`                                     │
    ├────────────┼────────────────────────────────────────────────────────────┤
    │ IT         │ Mostrar `codice_fiscale`                                   │
    └────────────┴────────────────────────────────────────────────────────────┘

    >> ESTRUCTURA DE INSTANCIACIÓN EN VISTA

        MigrationInfoForm(
            instance=migration_instance,
            seller_vat=vat,
            initial=data,
            country_of_incorporation_is_ue=country_of_incorporation_is_ue,
        )

    >> DIAGRAMA DE FLUJO DEL FORMULARIO (TEXTUAL - UNICODE)

                 INICIO MIGRATIONINFOFORM
                            │
                            ▼
        ¿Tiene SellerVat con maintenance_type="maintenance"? ───► NO ─────┐
                            │                                             │
                            ▼                                             ▼
            Recorrer seller_vats_maintenance                             FIN
                            │
                            ▼
            Obtener instance (SellerVatMigration)
                            │
                            ├─► Obtener datos comunes (vat_number, fechas, etc.)
                            │
                            ├─► Si ISO == "FR":
                            │       ├─► data["siret"]
                            │       └─► Si no es UE → incluir 
                            │                            ├─► previous_manager_name (Nombre del gestor/gestoria anterior) 
                            │                            └─► Address (direccion del gestor/gestoria anterior)
                            │
                            ├─► Si ISO == "IT": data["codice_fiscale"]
                            ├─► Si ISO == "DE": data["steuernummer"]
                            └─► Si ISO == "GB":
                            │        ├─► data["gov_gateway_*"] <-- Datos de acceso a la Hacienda del Reino Unido
                            │        └─► Default phone_country = "GB"
                            │
                            ▼
                    Generar mapeo documentos → doc_data
                            │
                            ▼
            Actualizar `data.update(doc_data)`
                            │
                            ▼
        Instanciar formulario + guardar en `migration_forms[iso]`
                            │
                            ▼
        Guardar json en `migration_info_data[block_key]`
                            │
                            ▼
                        SIGUIENTE ISO
                            │
                            ▼
                           FIN
            
    >> RESUMEN FINAL

        ┌────────────────────────────────────────────────────┐
        │           TABLA RESUMEN CAMPOS                     │
        └────────────────────────────────────────────────────┘
    ┌──────────────────────────────────────┬────────────────────┬─────────────────┬─────────────────────────────────────────────────┐
    │ CAMPO                                │ MODELO BASE        │ ¿EN FORMULARIO? │ NOTAS                                           │
    ├──────────────────────────────────────┼────────────────────┼─────────────────┼─────────────────────────────────────────────────┤
    │ vat_number                           │ SellerVat          │  Si (initial)   │ Visible siempre                                 │
    │ siret                                │ SellerVat          │  Si (FR)        │ Solo FR                                         │
    │ steuernummer                         │ SellerVat          │  Si (DE)        │ Solo DE                                         │
    │ codice_fiscale                       │ SellerVat          │  Si (IT)        │ Solo IT                                         │
    │ previous_manager_name                │ SellerVatMigration │  Si             │ Solo FR y no UE                                 │
    │ gov_gateway_user_id                  │ SellerVatMigration │  Si             │ Solo GB                                         │
    │ gov_gateway_phone_number             │ SellerVatMigration │  Si             │ Limpieza de símbolos                            │
    │ previous_manager_address             │ SellerVatMigration │  Si             │ AddressForm (solo FR no UE)                     │
    │ Documentos (CERTIFICATEVATNUMBER...) │ Document           │  Si (initial)   │ Generados por `generate_document_field_mapping` │
    └──────────────────────────────────────┴────────────────────┴─────────────────┴─────────────────────────────────────────────────┘


>>>> Flujo:

┌──────────────────────────┐
│ Inicio: Usuario pulsa    │
│ "Enviar formulario IVA"  │
└────────────┬─────────────┘
             │
             ▼
┌──────────────────────────┐
│ Validar bloques:         │
│ - companyInfo            │
│ - migrationInfo          │
│ - countryDocuments       │
└────────────┬─────────────┘
             │
   ¿Todos los campos        (✘ alguno vacío)
     requeridos OK?       ──────────────────────┐
             │                                  │
            ✔                                   ▼
             ▼                        ┌──────────────────────────┐
┌──────────────────────────┐          │ Mostrar errores          │
│ Generar JSON `iva_form`  │          │ y marcar pestañas con ❌ │
└────────────┬─────────────┘          └────────────┬─────────────┘
             │                                     │
             ▼                                     │
┌──────────────────────────┐                      │
│ Guardar JSON en          │                      │
│ `ProcessedForm.json_form`│◄────────────────────┘
└────────────┬─────────────┘
             │
             ▼
┌──────────────────────────┐
│ Mostrar mensaje de éxito │
│ y redirigir              │
└──────────────────────────┘
