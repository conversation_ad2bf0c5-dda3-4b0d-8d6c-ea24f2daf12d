import glob
import json
import os

import psycopg2
from django.conf import settings
from django.db import transaction
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.apps import apps

from muaytax.users.models import User
from muaytax.dictionaries.models.reconciliation_type import ReconciliationType
from muaytax.dictionaries.models.accounting_account import AccountingAccount
from muaytax.dictionaries.models.tax_responsability import TaxResponsability
from muaytax.dictionaries.models.transaction_type import TransactionType
from muaytax.dictionaries.models.txt_status import TXTStatus
from muaytax.dictionaries.models.vat_rates import VatRates
from muaytax.dictionaries.models.account_expenses import AccountExpenses
from muaytax.dictionaries.models.account_expenses_sub import SubAccountExpenses
from muaytax.dictionaries.models.account_sales import AccountSales
from muaytax.dictionaries.models.account_sales_sub import SubAccountSales
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.currencies import Currency
from muaytax.dictionaries.models.customer_type import CustomerType
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.dictionaries.models.economic_activity import EconomicActivity
from muaytax.dictionaries.models.identifier_type import IdentifierType
from muaytax.dictionaries.models.invoice_category import InvoiceCategory
from muaytax.dictionaries.models.invoice_discard_reason import InvoiceDiscardReason
from muaytax.dictionaries.models.invoice_status import InvoiceStatus
from muaytax.dictionaries.models.invoice_type import InvoiceType
from muaytax.dictionaries.models.marketplaces import Marketplaces
from muaytax.dictionaries.models.model import Model
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.dictionaries.models.operation_type import OperationType
from muaytax.dictionaries.models.partner_type import PartnerType
from muaytax.dictionaries.models.payment_methods import PaymentMethod
from muaytax.dictionaries.models.period import Period
from muaytax.dictionaries.models.presented_model_results import PresentedModelResults
from muaytax.dictionaries.models.provider_type import ProviderType
from muaytax.dictionaries.models.sellervat_epigraph_regime import SellerVatEpigraphRegime
from muaytax.dictionaries.models.sellervat_es_status_activation import SellerVatEsStatusActivation
from muaytax.dictionaries.models.sellervat_es_status_alta_iae import SellerVatEsStatusAltaIae
from muaytax.dictionaries.models.sellervat_es_status_c_digital import SellerVatEsStatusCDigital
from muaytax.dictionaries.models.sellervat_es_status_eori import SellerVatEsStatusEori
from muaytax.dictionaries.models.sellervat_es_status_vies import SellerVatEsStatusVies
from muaytax.dictionaries.models.sellervat_period import SellerVatPeriod
from muaytax.dictionaries.models.sellervat_quarter import SellerVatQuarter
from muaytax.dictionaries.models.sellervat_status import SellerVatStatus
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_status_process_color import SellerVatStatusProcessColor
from muaytax.dictionaries.models.presented_model_results import PresentedModelResults
from muaytax.dictionaries.models.model_status import ModelStatus
from muaytax.dictionaries.models.invoice_discard_reason import InvoiceDiscardReason
from muaytax.dictionaries.models.sellervat_period import SellerVatPeriod
from muaytax.dictionaries.models.sellervat_quarter import SellerVatQuarter
from muaytax.dictionaries.models.sellervat_status_process import SellerVatStatusProcess
from muaytax.dictionaries.models.sellervat_es_status_activation import SellerVatEsStatusActivation
from muaytax.dictionaries.models.sellervat_es_status_alta_iae import SellerVatEsStatusAltaIae
from muaytax.dictionaries.models.sellervat_es_status_c_digital import SellerVatEsStatusCDigital
from muaytax.dictionaries.models.sellervat_es_status_vies import SellerVatEsStatusVies
from muaytax.dictionaries.models.sellervat_es_status_eori import SellerVatEsStatusEori
from muaytax.dictionaries.models.sellervat_type import SellerVatType
from muaytax.dictionaries.models.bank_movement_status import BankMovementStatus
from muaytax.dictionaries.models.bank_movement_template import BankMovementTemplate
from muaytax.dictionaries.models.bank_type import BankType
from muaytax.dictionaries.models.importer_bankmovement_status import BankMovementImporterStatus
from muaytax.dictionaries.models.booking_subjects import BookingSubject
from muaytax.dictionaries.models.contract_type import ContractType
from muaytax.dictionaries.models.situation_seller_rental import SituationSellerRental
from muaytax.dictionaries.models.type_road import TypeRoad
from muaytax.dictionaries.models.province_code import ProvinceCode
from muaytax.dictionaries.models.municipality_code import MunicipalityCode
from muaytax.dictionaries.models.muaytax_departments import MuaytaxDepartment
from muaytax.dictionaries.models.movement_status import MovementStatus
from muaytax.dictionaries.models.states_usa import StatesUSA
from muaytax.dictionaries.models.general_settings import GeneralSettings
from muaytax.dictionaries.models.product_service import ProductService
from muaytax.dictionaries.models.permissions_in_class import Permissions_in_class
from muaytax.dictionaries.models.service_type import ServiceType
from muaytax.dictionaries.models.rap_category import RapCategory
from muaytax.dictionaries.models.type_form import TypeForm
from muaytax.dictionaries.models.task_type import TaskType
from muaytax.dictionaries.models.store_products import StoreProduct
from muaytax.dictionaries.models.us_zip_codes import UsZipCodes
from muaytax.dictionaries.models.official_amortization_table import AmortizationCoefficient
from muaytax.dictionaries.models.order_financial_status import OrderFinancialStatus
from muaytax.dictionaries.models.order_fulfillment_status import OrderFulfillmentStatus
from muaytax.dictionaries.models.maintenance_type import MaintenanceType

qty_new_folders = 0
qty_scripts = 0
qty_dictionaries = 0

# Function to Connect to Database
def connect_to_database():
    try:
        dbname = os.getenv("POSTGRES_DB")
        user = os.getenv("POSTGRES_USER")
        password = os.getenv("POSTGRES_PASSWORD")
        host = os.getenv("POSTGRES_HOST")
        port = os.getenv("POSTGRES_PORT")
        # print(f"Conectando a la base de datos... postgres://{user}:{password}@{host}:{port}/{dbname}")
        connection = psycopg2.connect(
            dbname=dbname,
            user=user,
            password=password,
            host=host,
            port=port
        )
        print(f"[   D B   ] ✅ Conexión exitosa a la base de datos.")
        return connection
    except (Exception, psycopg2.Error) as e:
        print(f"[   D B   ] ❌ Error al conectar a la base de datos: {e}")
        return None

# Function to Execute SQL Scripts from Scrit_Folder
def execute_sql_scripts(connection, script_folder):
    os.chdir(script_folder)
    script_files = glob.glob("*.sql")
    file_failed = ''
    error = False
    execution_ok = 0
    for script_file in sorted(script_files):
        try:
            with open(script_file, 'r') as file:
                script = file.read()

            with connection.cursor() as cursor:
                cursor.execute(script)
                connection.commit()
                execution_ok += 1

        except (Exception, psycopg2.Error) as e:
            error = True
            file_failed = script_file
            connection.rollback()
            break

    global qty_scripts
    qty_scripts = 0
    if error == True:
        print(f"[ SCRIPTS ] ❌ Error al ejecutar el script {file_failed}.")
    else:
        qty_scripts = execution_ok
        print(f"[ SCRIPTS ] ✅ Se han ejecutado {qty_scripts} scripts correctamente.")

# Function to Load All Scripts of Functions
def load_all_function_scripts():
    script_folder = str(settings.ROOT_DIR / "database" / "scripts" / "functions")
    connection = connect_to_database()

    if connection:
        execute_sql_scripts(connection, script_folder)
        connection.close()


# Function to Read JSON Files
def _get_data_json(folder, file):
    path = str(settings.ROOT_DIR / "muaytax" / folder / "data" / f"{file}.json")

    with open(path) as f:
        a = f.read()
        data = json.loads(a)
    return data

def _get_data_dictionary_json(file):
    return _get_data_json("dictionaries", file)


# Create Users
def create_user():
    data = _get_data_json("users", "users")
    with transaction.atomic():
        for item in data:
            User.objects.update_or_create(
                id=item.pop("id"),
                defaults=item,
            )


# Create Dictionaries
def create_dic_account_expenses():
    data = _get_data_dictionary_json("account_expenses")
    with transaction.atomic():
        for item in data:
            AccountExpenses.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Account Expenses dictionary created.")
    return 1

def create_dic_subaccount_expenses():
    data = _get_data_dictionary_json("account_expenses_sub")
    with transaction.atomic():
        for item in data:
            SubAccountExpenses.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Sub Account Expenses dictionary created.")
    return 1

def create_dic_account_sales():
    data = _get_data_dictionary_json("account_sales")
    with transaction.atomic():
        for item in data:
            AccountSales.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Account Sales dictionary created.")
    return 1

def create_dic_booking_subjects():
    data = _get_data_dictionary_json("booking_subjects")
    with transaction.atomic():
        for item in data:
            BookingSubject.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Booking Subjects dictionary created.")
    return 1

def create_dic_contract_type():
    data = _get_data_dictionary_json("contract_type")
    with transaction.atomic():
        for item in data:
            ContractType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Contract Type dictionary created.")
    return 1

def create_dic_subaccount_sales():
    data = _get_data_dictionary_json("account_sales_sub")
    with transaction.atomic():
        for item in data:
            SubAccountSales.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Sub Account Sales dictionary created.")
    return 1

def create_dic_countries():
    data = _get_data_dictionary_json("countries")
    with transaction.atomic():
        for item in data:
            Country.objects.update_or_create(
                iso_code=item.pop("iso_code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Countries dictionary created.")
    return 1

def create_dic_currencies():
    data = _get_data_dictionary_json("currencies")
    with transaction.atomic():
        for item in data:
            Currency.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Currencies dictionary created.")
    return 1

def create_dic_customer_type():
    data = _get_data_dictionary_json("customer_type")
    with transaction.atomic():
        for item in data:
            CustomerType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Customer Type dictionary created.")
    return 1

def create_dic_provider_type():
    data = _get_data_dictionary_json("provider_type")
    with transaction.atomic():
        for item in data:
            ProviderType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Provider Type dictionary created.")
    return 1

def create_dic_transaction_type():
    data = _get_data_dictionary_json("transaction_type")
    with transaction.atomic():
        for item in data:
            TransactionType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Transaction Type dictionary created.")
    return 1

def create_dic_vat_rates():
    data = _get_data_dictionary_json("vat_rates")
    with transaction.atomic():
        for item in data:
            VatRates.objects.update_or_create(
                country_code=item.pop("country_code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ VAT Rates dictionary created.")
    return 1

def create_dic_identifier_type():
    data = _get_data_dictionary_json("identifier_type")
    with transaction.atomic():
        for item in data:
            IdentifierType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Identifier Type dictionary created.")
    return 1

def create_dic_partner_type():
    data = _get_data_dictionary_json("partner_type")
    with transaction.atomic():
        for item in data:
            PartnerType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Partner Type dictionary created.")
    return 1

def create_dic_invoice_status():
    data = _get_data_dictionary_json("invoice_status")
    with transaction.atomic():
        for item in data:
            InvoiceStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Invoice Status dictionary created.")
    return 1

def create_dic_marketplaces():
    data = _get_data_dictionary_json("marketplaces")
    with transaction.atomic():
        for item in data:
            Marketplaces.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Marketplaces dictionary created.")
    return 1

def create_dic_operation_type():
    data = _get_data_dictionary_json("operation_type")
    with transaction.atomic():
        for item in data:
            OperationType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Operation Type dictionary created.")
    return 1

def create_dic_invoice_category():
    data = _get_data_dictionary_json("invoice_category")
    with transaction.atomic():
        for item in data:
            InvoiceCategory.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Invoice Category dictionary created.")
    return 1

def create_dic_payment_method():
    data = _get_data_dictionary_json("payment_method")
    with transaction.atomic():
        for item in data:
            PaymentMethod.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Payment Method dictionary created.")
    return 1

def create_dic_tax_responsability():
    data = _get_data_dictionary_json("tax_responsability")
    with transaction.atomic():
        for item in data:
            TaxResponsability.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Tax Responsability dictionary created.")
    return 1

def create_dic_economic_activity():
    data = _get_data_dictionary_json("economic_activity")
    with transaction.atomic():
        for item in data:
            EconomicActivity.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Economic Activity dictionary created.")
    return 1

def create_dic_invoice_type():
    data = _get_data_dictionary_json("invoice_type")
    with transaction.atomic():
        for item in data:
            InvoiceType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Invoice Type dictionary created.")
    return 1

def create_dic_period():
    data = _get_data_dictionary_json("period")
    with transaction.atomic():
        for item in data:
            Period.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Period dictionary created.")
    return 1

def create_dic_model():
    data = _get_data_dictionary_json("model")
    with transaction.atomic():
        for item in data:
            Model.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Model dictionary created.")
    return 1

def create_dic_txt_status():
    data = _get_data_dictionary_json("txt_status")
    with transaction.atomic():
        for item in data:
            TXTStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ TXT Status dictionary created.")
    return 1

def create_dic_sellervat_status():
    data = _get_data_dictionary_json("sellervat_status")
    with transaction.atomic():
        for item in data:
            SellerVatStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT Status dictionary created.")
    return 1

def create_dic_document_type():
    data = _get_data_dictionary_json("document_type")
    with transaction.atomic():
        for item in data:
            DocumentType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Document Type dictionary created.")
    return 1

def create_dic_sellervat_period():
    data = _get_data_dictionary_json("sellervat_period")
    with transaction.atomic():
        for item in data:
            SellerVatPeriod.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT Period dictionary created.")
    return 1

def create_dic_sellervat_quarter():
    data = _get_data_dictionary_json("sellervat_quarter")
    with transaction.atomic():
        for item in data:
            SellerVatQuarter.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT Quarter dictionary created.")
    return 1

def create_dic_invoice_discard_reason():
    data = _get_data_dictionary_json("invoice_discard_reason")
    with transaction.atomic():
        for item in data:
            InvoiceDiscardReason.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Invoice Discard Reason dictionary created.")
    return 1

def create_dic_sellervat_status_process():
    data = _get_data_dictionary_json("sellervat_status_process")
    with transaction.atomic():
        for item in data:
            SellerVatStatusProcess.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT Status Process dictionary created.")
    return 1

def create_dic_sellervat_status_process_color():
    data = _get_data_dictionary_json("sellervat_status_process_color")
    with transaction.atomic():
        for item in data:
            SellerVatStatusProcessColor.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT Status Process Color dictionary created.")
    return 1

def create_dic_sellervat_es_status_activation():
    data = _get_data_dictionary_json("sellervat_es_status_activation")
    with transaction.atomic():
        for item in data:
            SellerVatEsStatusActivation.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT ES Status Activation dictionary created.")
    return 1

def create_dic_sellervat_es_status_alta_iae():
    data = _get_data_dictionary_json("sellervat_es_status_alta_iae")
    with transaction.atomic():
        for item in data:
            SellerVatEsStatusAltaIae.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT ES Status Alta IAE dictionary created.")
    return 1

def create_dic_sellervat_es_status_c_digital():
    data = _get_data_dictionary_json("sellervat_es_status_c_digital")
    with transaction.atomic():
        for item in data:
            SellerVatEsStatusCDigital.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT ES Status C Digital dictionary created.")
    return 1

def create_dic_sellervat_es_status_vies():
    data = _get_data_dictionary_json("sellervat_es_status_vies")
    with transaction.atomic():
        for item in data:
            SellerVatEsStatusVies.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT ES Status VIES dictionary created.")
    return 1

def create_dic_sellervat_es_status_eori():
    data = _get_data_dictionary_json("sellervat_es_status_eori")
    with transaction.atomic():
        for item in data:
            SellerVatEsStatusEori.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT ES Status EORI dictionary created.")
    return 1

def create_dic_sellervat_type():
    data = _get_data_dictionary_json("sellervat_type")
    with transaction.atomic():
        for item in data:
            SellerVatType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT Type dictionary created.")
    return 1

def create_dic_model_status():
    data = _get_data_dictionary_json("model_status")
    with transaction.atomic():
        for item in data:
            ModelStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Model Status dictionary created.")
    return 1

def create_dic_presented_model_results():
    data = _get_data_dictionary_json("presented_model_results")
    with transaction.atomic():
        for item in data:
            PresentedModelResults.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Presented Model Results dictionary created.")
    return 1

def create_dic_bank_type():
    data = _get_data_dictionary_json("bank_type")
    with transaction.atomic():
        for item in data:
            BankType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Bank Type dictionary created.")
    return 1

def create_dic_movement_status():
    data = _get_data_dictionary_json("movement_status")
    with transaction.atomic():
        for item in data:
            MovementStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Movement Status dictionary created.")
    return 1

def create_dic_bank_movement_status():
    data = _get_data_dictionary_json("bank_movement_status")
    with transaction.atomic():
        for item in data:
            BankMovementStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Bank Movement Status dictionary created.")
    return 1

def create_dic_importer_bankmovement_status():
    data = _get_data_dictionary_json("importer_bankmovement_status")
    with transaction.atomic():
        for item in data:
            BankMovementImporterStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Importer Bank Movement Status dictionary created.")
    return 1

def create_dic_bank_movement_template():
    data = _get_data_dictionary_json("bank_movement_template")
    with transaction.atomic():
        for item in data:
            BankMovementTemplate.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Bank Movement Template dictionary created.")
    return 1

def create_dic_reconciliation_type():
    data = _get_data_dictionary_json("reconciliation_type")
    with transaction.atomic():
        for item in data:
            ReconciliationType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Reconciliation Type dictionary created.")
    return 1

def create_dic_accounting_account():
    data = _get_data_dictionary_json("accounting_account")
    with transaction.atomic():
        for item in data:
            AccountingAccount.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Accounting Account dictionary created.")
    return 1

def create_dic_sellervat_epigraph_regime():
    data = _get_data_dictionary_json("sellervat_epigraph_regime")
    with transaction.atomic():
        for item in data:
            SellerVatEpigraphRegime.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Seller VAT Epigraph Regime dictionary created.")
    return 1

def create_dic_situation_seller_rental():
    data = _get_data_dictionary_json("situation_seller_rental")
    with transaction.atomic():
        for item in data:
            SituationSellerRental.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Situation Seller Rental dictionary created.")
    return 1

def create_dic_type_road_seller_rental():
    data = _get_data_dictionary_json("type_road")
    with transaction.atomic():
        for item in data:
            TypeRoad.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Type Road Seller Rental dictionary created.")
    return 1

def create_dic_province_code():
    data = _get_data_dictionary_json("province_code")
    with transaction.atomic():
        for item in data:
            ProvinceCode.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Province Code dictionary created.")
    return 1

def create_dic_municipality_code():
    data = _get_data_dictionary_json("municipality_code")
    with transaction.atomic():
        for item in data:
            MunicipalityCode.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Municipality Code dictionary created.")
    return 1

def create_dic_muaytax_departments():
    data = _get_data_dictionary_json("muaytax_departments")
    with transaction.atomic():
        for item in data:
            MuaytaxDepartment.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Muaytax Departments dictionary created.")
    return 1

def create_dic_states_usa():
    data = _get_data_dictionary_json("states_usa")
    with transaction.atomic():
        for item in data:
            StatesUSA.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ States USA dictionary created.")
    return 1

def create_dic_general_settings():
    data = _get_data_dictionary_json("general_settings")
    with transaction.atomic():
        for item in data:
            GeneralSettings.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ General Settings dictionary created.")
    return 1

def create_dic_product_service():
    data = _get_data_dictionary_json("product_service")
    with transaction.atomic():
        for item in data:
            ProductService.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Product Service dictionary created.")
    return 1

def create_dic_permissions_in_class():
    data = _get_data_dictionary_json("permissions_in_class")
    with transaction.atomic():
        for item in data:
            Permissions_in_class.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Permissions in Class dictionary created.")
    return 1

def create_dic_services():
    data = _get_data_dictionary_json("service_type")
    with transaction.atomic():
        for item in data:
            ServiceType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Service Type dictionary created.")
    return 1

def create_dic_task_type():
    data = _get_data_dictionary_json("tasks_type")
    with transaction.atomic():
        for item in data:
            TaskType.objects.update_or_create(
                task_code=item.pop("task_code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Task Type dictionary created.")
    return 1

def create_dic_rap_category():
    data = _get_data_dictionary_json("rap_category")
    with transaction.atomic():
        for item in data:
            RapCategory.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ RAP Category dictionary created.")
    return 1

def create_dic_type_form():
    data = _get_data_dictionary_json("type_form")
    with transaction.atomic():
        for item in data:
            TypeForm.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Type Form dictionary created.")
    return 1

def create_dic_store_products():
    data = _get_data_dictionary_json("store_products")
    with transaction.atomic():
        for item in data:
            StoreProduct.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Store Products dictionary created.")
    return 1

def create_dic_us_zip_codes():
    data = _get_data_dictionary_json("us_zip_codes")
    with transaction.atomic():
        for item in data:
            UsZipCodes.objects.update_or_create(
                zip_code=item.pop("zip"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ US Zip Codes dictionary created.")
    return 1

def create_dic_amortization_coefficient_codes():
    data = _get_data_dictionary_json("official_amortization_table")
    with transaction.atomic():
        for item in data:
            AmortizationCoefficient.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Amortization Coefficient Codes dictionary created.")
    return 1


def create_dic_order_financial_status():
    data = _get_data_dictionary_json("order_financial_status")
    with transaction.atomic():
        for item in data:
            print(item)
            OrderFinancialStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Order Financial Status dictionary created.")
    return 1

def create_dic_order_fulfillment_status():
    data = _get_data_dictionary_json("order_fulfillment_status")
    with transaction.atomic():
        for item in data:
            print(item)
            OrderFulfillmentStatus.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Order Fulfillment Status dictionary created.")
    return 1

def create_dic_maintenance_type():
    data = _get_data_dictionary_json("maintenance_type")
    with transaction.atomic():
        for item in data:
            MaintenanceType.objects.update_or_create(
                code=item.pop("code"),
                defaults=item,
            )
    print("[  DICTS  ] ✅ Maintenance Type dictionary created.")
    return 1

# Load all dictionaries
def load_all_dictionaies():
    global qty_dictionaries
    qty_dictionaries = 0

    # create_user()
    qty_dictionaries += create_dic_countries()
    qty_dictionaries += create_dic_currencies()
    qty_dictionaries += create_dic_account_expenses()
    qty_dictionaries += create_dic_subaccount_expenses()
    qty_dictionaries += create_dic_account_sales()
    qty_dictionaries += create_dic_subaccount_sales()
    qty_dictionaries += create_dic_countries()
    qty_dictionaries += create_dic_currencies()
    qty_dictionaries += create_dic_customer_type()
    qty_dictionaries += create_dic_provider_type()
    qty_dictionaries += create_dic_transaction_type()
    qty_dictionaries += create_dic_vat_rates()
    qty_dictionaries += create_dic_identifier_type()
    qty_dictionaries += create_dic_partner_type()
    qty_dictionaries += create_dic_invoice_status()
    qty_dictionaries += create_dic_marketplaces()
    qty_dictionaries += create_dic_operation_type()
    qty_dictionaries += create_dic_invoice_category()
    qty_dictionaries += create_dic_payment_method()
    qty_dictionaries += create_dic_tax_responsability()
    qty_dictionaries += create_dic_economic_activity()
    qty_dictionaries += create_dic_invoice_type()
    qty_dictionaries += create_dic_period()
    qty_dictionaries += create_dic_model()
    qty_dictionaries += create_dic_txt_status()
    qty_dictionaries += create_dic_sellervat_status()
    qty_dictionaries += create_dic_document_type()
    qty_dictionaries += create_dic_sellervat_period()
    qty_dictionaries += create_dic_sellervat_quarter()
    qty_dictionaries += create_dic_invoice_discard_reason()
    qty_dictionaries += create_dic_sellervat_status_process()
    qty_dictionaries += create_dic_sellervat_es_status_activation()
    qty_dictionaries += create_dic_sellervat_es_status_alta_iae()
    qty_dictionaries += create_dic_sellervat_es_status_c_digital()
    qty_dictionaries += create_dic_sellervat_es_status_vies()
    qty_dictionaries += create_dic_sellervat_es_status_eori()
    qty_dictionaries += create_dic_model_status()
    qty_dictionaries += create_dic_presented_model_results()
    qty_dictionaries += create_dic_sellervat_type()
    qty_dictionaries += create_dic_sellervat_status_process_color()
    qty_dictionaries += create_dic_bank_type()
    qty_dictionaries += create_dic_movement_status()
    qty_dictionaries += create_dic_bank_movement_status()
    qty_dictionaries += create_dic_importer_bankmovement_status()
    qty_dictionaries += create_dic_bank_movement_template()
    qty_dictionaries += create_dic_reconciliation_type()
    qty_dictionaries += create_dic_accounting_account()
    qty_dictionaries += create_dic_booking_subjects()
    qty_dictionaries += create_dic_contract_type()
    qty_dictionaries += create_dic_situation_seller_rental()
    qty_dictionaries += create_dic_type_road_seller_rental()
    qty_dictionaries += create_dic_province_code()
    qty_dictionaries += create_dic_muaytax_departments()
    qty_dictionaries += create_dic_states_usa()
    qty_dictionaries += create_dic_general_settings()
    qty_dictionaries += create_dic_product_service()
    qty_dictionaries += create_dic_permissions_in_class()
    qty_dictionaries += create_dic_services()
    qty_dictionaries += create_dic_rap_category()
    qty_dictionaries += create_dic_type_form()
    qty_dictionaries += create_dic_task_type()
    qty_dictionaries += create_dic_store_products()
    qty_dictionaries += create_dic_amortization_coefficient_codes()
    qty_dictionaries += create_dic_order_financial_status()
    qty_dictionaries += create_dic_order_fulfillment_status()
    qty_dictionaries += create_dic_maintenance_type()
    # qty_dictionaries += create_dic_municipality_code()
    # qty_dictionaries += create_dic_us_zip_codes() #TODO: comentar cuando se rellene la tabla (hay mas de 40000 registros)

# Create Media Folders
def create_media_folders():
    # Create Folders in muaytax/media
    folders = [
        "generated_models",
        "generated_txt",
        "xls_invoice_list",
        "xls_entry_list",
        "xls_model_111",
        "uploads",
        "uploads/presented_models",
        "uploads/presented_models_recepipt",
        "uploads/presented_models/substitute_presentation",
        "uploads/nrc_receipt",
        "uploads/qr_verifactu"
    ]
    global qty_new_folders
    qty_new_folders = 0
    for folder in folders:
        new_folder = f"{settings.MEDIA_ROOT}/{folder}"
        if not os.path.exists(new_folder):
            qty_new_folders += 1
            os.makedirs(new_folder)

    if qty_new_folders > 0:
        print(f"[  MEDIA  ] ✅ Se han creado {qty_new_folders} carpetas nuevas en la carpeta media.")
    else:
        print(f"[  MEDIA  ] ❎ No se han creado nuevas carpetas en la carpeta media.")


# Print Summary of Initialization
def print_summary():
    global qty_new_folders, qty_scripts, qty_dictionaries, qty_permissions
    ##################################################################################################
    print(f"\n\n" + "#" * 60)
    print(f"##                 RESUMEN INICIALIZACION                 ##")
    print(f"#" * 60)
    print(f"## [  INIT   ] ✅   Inicialización completa." + "              ##")
    print(f"## [  INIT   ] 🗂️    {qty_new_folders} carpetas creadas en media." + "          ##")
    print(f"## [  INIT   ] 🔐  {qty_permissions} permisos creados." + "                ##")
    print(f"## [  INIT   ] 📜  {qty_scripts} scripts ejecutados." + "                 ##")
    print(f"## [  INIT   ] 📖  {qty_dictionaries} diccionarios creados/actuzalizados." + " ##")
    print("#" * 60 + "\n")
    ##################################################################################################

# Create Django permissions automatically
def create_django_permissions():
    """
    Crea automáticamente los permisos definidos en Meta.permissions de todos los modelos.
    Esto es necesario porque db.initialize() no ejecuta migraciones.
    """
    print("[  PERMS  ] 🔐 Creando permisos de Django...")

    created_permissions = 0

    try:
        # Obtener todos los modelos de la aplicación
        for model in apps.get_models():
            # Verificar si el modelo tiene permisos personalizados definidos
            if hasattr(model._meta, 'permissions') and model._meta.permissions:
                # Obtener el ContentType para este modelo
                content_type = ContentType.objects.get_for_model(model)

                # Crear cada permiso definido en Meta.permissions
                for codename, name in model._meta.permissions:
                    permission, created = Permission.objects.get_or_create(
                        codename=codename,
                        content_type=content_type,
                        defaults={'name': name}
                    )

                    if created:
                        created_permissions += 1
                        print(f"[  PERMS  ] ✅ Permiso creado: {model._meta.app_label}.{codename}")

    except Exception as e:
        print(f"[  PERMS  ] ❌ Error creando permisos: {e}")
        return 0

    if created_permissions > 0:
        print(f"[  PERMS  ] ✅ Se han creado {created_permissions} permisos nuevos.")
    else:
        print(f"[  PERMS  ] ❎ No se han creado permisos nuevos (ya existían).")

    return created_permissions

# Initialize Function: Load all dictionaries + Load All Functions Scripts
def initialize():
    # Set 0 to global variables
    global qty_new_folders, qty_scripts, qty_dictionaries, qty_permissions
    qty_new_folders = 0
    qty_scripts = 0
    qty_dictionaries = 0
    qty_permissions = 0
    print("\n")

    # Create Media Folders, Create Django Permissions, Load All Function Scripts and Load All Dictionaries. Later print summary.
    create_media_folders()
    qty_permissions = create_django_permissions()
    load_all_function_scripts()
    load_all_dictionaies()
    print_summary()
