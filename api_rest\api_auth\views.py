from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework import status

from api_rest.api_auth.models import PublicAccessCodeToken
from api_rest.api_auth.serializers import GetAccessTokenSerializer, VerifyAccessCodeSerializer
from api_rest.api_auth.notification import AuthNotification

from muaytax.app_sellers.models import Seller
from muaytax.app_sellers.serializers import SellerSerializer

class GetAccessTokenFromEmail(APIView):
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = GetAccessTokenSerializer(data=request.data)
        if serializer.is_valid():
            access_token = serializer.save()

            auth_notification = AuthNotification(to_email=access_token.email)
            auth_notification.access_token_notification(access_token=access_token.code)
            
            return Response(
                {"message": "The email was sent successfully. Please check your email for the access code."},
                status=status.HTTP_200_OK
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
class VerifyAccessToken(APIView):
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = VerifyAccessCodeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


        access_code = serializer.validated_data.get('access_code')
        email = serializer.validated_data.get('email')

        public_access_token = PublicAccessCodeToken.objects.filter(code=access_code, email=email, is_active=True).first()

        if not public_access_token:
            return Response(
                {"message": "El código de acceso no es válido. Por favor, verifique el código e intente nuevamente."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if public_access_token.is_expired():
            return Response(
                {"message": "El código de acceso ha expirado. Por favor, solicite un nuevo código de acceso."},
                status=status.HTTP_400_BAD_REQUEST
            )

        response_data = {
            "message": "El código de acceso es válido.",
            "private_token": public_access_token.private_token,
        }

        # Check if the user making the request has a seller account
        seller = Seller.objects.filter(user__email=email).first()
        if seller:
            seller_serializer = SellerSerializer(seller)
            response_data["seller"] = seller_serializer.data

        public_access_token.update_date_expired() # Esto hace que el código de acceso sea inválido después de ser verificado. Es decir, el código de acceso solo se puede usar una vez.

        return Response(response_data, status=status.HTTP_200_OK)