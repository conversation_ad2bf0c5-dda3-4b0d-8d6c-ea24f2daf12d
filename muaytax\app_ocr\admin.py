from django.contrib import admin

from muaytax.app_ocr.models import *
    
class PromptConfigurationAdmin(admin.ModelAdmin):
    list_display = ['processing_document', 'truncated_system_prompt', 'truncated_user_prompt', 'created_at', 'modified_at']
    search_fields = ['processing_document']
    list_display_links = ['processing_document']

admin.site.register(PromptConfiguration, PromptConfigurationAdmin)