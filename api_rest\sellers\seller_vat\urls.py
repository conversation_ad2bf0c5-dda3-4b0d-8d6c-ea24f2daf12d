from django.urls import path

from api_rest.sellers.seller_vat.views import *

app_name = "api_seller_vat"

urlpatterns = [
    path(
        '',
        view=ListSellerVatDRFView.as_view(),
        name='get_all_seller_vat'
    ),
    path(
        'add/',
        view=AddSellerVatDRFView.as_view(),
        name='add_seller_vat'
    ),
    path(
        '<int:pk>/',
        view=SellerVatDetailDRFView.as_view(),
        name='seller_vat_detail'
    ),
    path(
        'get-countries-not-registered/',
        view=GetCountriesNotRegistered.as_view(),
        name='get_countries_not_registered'
    ),
]

# urls específicas para el wizard de marketplace
urlpatterns += [
    path(
        'vats-shopify-wizard/',
        view=ListDraftSellerVatDRFView.as_view(),
        name='get_all_seller_vat_wizard_shopify'
    )
]