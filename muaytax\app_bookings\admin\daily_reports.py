from django.contrib import admin
from ..models import Bookings 

class BookingsInline(admin.TabularInline):
    model = Bookings
    extra = 0
    fields = ["id", "seller", "manager", "subject", "get_time"]
    readonly_fields = ["seller", "manager", "subject", "get_time"]
    can_delete = False
    show_change_link = True
    verbose_name = "Cita"
    verbose_name_plural = "Citas"
    ordering = ["date"]

    def has_add_permission(self, request, obj=None):
        return False
    
class DailyReportsAdmin(admin.ModelAdmin):
    inlines = [BookingsInline]
    list_display = ["id", "get_title", "department", "manager", "is_sent", "date_created"]
    search_fields = ["id","get_title", "department", "manager", "date_created", ]

    def get_title(self, obj):
        return obj.get_title()  # Llama al método del modelo para obtener la fecha
    get_title.short_description = 'Reporte'