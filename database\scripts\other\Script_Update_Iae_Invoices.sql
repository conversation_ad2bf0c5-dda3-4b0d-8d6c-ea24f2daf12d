UPDATE invoices_invoice
SET iae_id = COALESCE(
        (SELECT code FROM dictionaries_economicactivity WHERE code = CONCAT(invoices_invoice.tax_country_id, '-', invoices_invoice.iae_id)),
        (SELECT code FROM dictionaries_economicactivity WHERE code = invoices_invoice.iae_id),
        (SELECT code FROM dictionaries_economicactivity WHERE code = CONCAT(invoices_invoice.tax_country_id, '-', '665')),
        '665')
WHERE tax_country_id IS NOT NULL;
