from muaytax.utils.env_resources import cabecera_email_general
from muaytax.email_notifications.utils import get_mail_signature_info_muaytax
from muaytax.app_notifications.channels.email_notification.sender import EmailMessageNotification


class AuthNotification(EmailMessageNotification):
    def __init__(self, to_email=None):
        super().__init__()
        self.to_email = to_email
        self.from_email = "<EMAIL>"
        self.set_context(email=to_email)

    def access_token_notification(self, access_token: str):
        mail_signature_details = get_mail_signature_info_muaytax()
        self.template_name = "emails/get-access-code.html"
        self.subject = "MUAYTAX - Código de acceso"
        self.text_content = "Código de acceso"
        self.reply_to = mail_signature_details['email']
        self.set_context(
            access_token=access_token,
            company_info=mail_signature_details,
            cabecera_email_general=cabecera_email_general()
            )
        self.send_email()
