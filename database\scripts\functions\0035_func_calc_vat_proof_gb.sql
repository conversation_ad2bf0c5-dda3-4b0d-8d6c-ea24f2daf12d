CREATE OR REPLACE FUNCTION func_calc_model_vat_proof_gb(
    sellerid INTEGER, 
    first_date VARCHAR, 
    last_date VARCHAR
) RETURNS jsonb AS $$
BEGIN
    RETURN (
        WITH 
        filtered_invoices AS (
            SELECT inv.id, con.amount_euros, con.vat_euros, inv.invoice_category_id, inv.transaction_type_id
            FROM invoices_invoice inv
            INNER JOIN invoices_concept con ON con.invoice_id = inv.id
            WHERE
                inv.seller_id = sellerid
                AND inv.accounting_date >= TO_DATE(first_date, 'YYYY-MM-DD')
                AND inv.accounting_date <= TO_DATE(last_date, 'YYYY-MM-DD')
                AND inv.status_id = 'revised'
                AND inv.tax_country_id = 'GB'
                AND (NOT (inv.invoice_category_id IN ('expenses_copy', 'sales_copy')) OR inv.invoice_category_id IS NULL)
        ),
        expenses_base AS (
            SELECT 
                SUM(amount_euros) AS amount_expenses
            FROM filtered_invoices
            WHERE invoice_category_id = 'expenses'
        ),
        vat_expenses AS (
            SELECT 
                SUM(vat_euros) AS vat_expenses
            FROM filtered_invoices
            WHERE invoice_category_id = 'expenses'
        ),
        sales_base AS (
            SELECT 
                SUM(amount_euros) AS amount_sales
            FROM filtered_invoices
            WHERE transaction_type_id IN ('local-sale', 'local-refund')
        ),
        vat_sales AS (
            SELECT 
                SUM(vat_euros) AS vat_sales
            FROM filtered_invoices
            WHERE transaction_type_id IN ('local-sale', 'local-refund')
        )
        SELECT jsonb_build_object(
            'box_1', ROUND(COALESCE(vat_sales.vat_sales, 0)::numeric, 2),
            'box_2', 0.00,
            'box_3', ROUND(COALESCE(vat_sales.vat_sales, 0)::numeric, 2),
            'box_4', ROUND(COALESCE(vat_expenses.vat_expenses, 0)::numeric, 2),
            'box_5', ROUND(ABS(COALESCE(vat_sales.vat_sales, 0) - COALESCE(vat_expenses.vat_expenses, 0))::numeric, 2),
            'box_6', ROUND(COALESCE(sales_base.amount_sales, 0)::numeric, 2),
            'box_7', ROUND(COALESCE(expenses_base.amount_expenses, 0)::numeric, 2),
            'box_8', 0.00,
            'box_9', 0.00
        )
        FROM expenses_base, vat_expenses, sales_base, vat_sales
    );
END;
$$ LANGUAGE plpgsql;

-- SELECT func_calc_model_vat_proof_gb(108, '2023-02-01', '2023-05-01');