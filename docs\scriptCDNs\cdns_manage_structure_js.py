import os
import re
import json
import requests

# Definir la ruta donde están los templates y la carpeta donde se guardarán los archivos JSON de salida
template_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'muaytax', 'templates')
output_dir = os.path.dirname(__file__)
http_output_file = os.path.join(output_dir, 'cdnsjs_templates_mapping.json')
error_log_file = os.path.join(output_dir, 'cdnsjs_errors.json')

# Lista de URLs a excluir
exclusion_list = [
    "https://cdn.datatables.net/2.0.7/js/dataTables.js",
]

# Expresión regular para detectar URLs de CDN en los templates
cdn_pattern = re.compile(r'https?://[^\s\'"]+', re.IGNORECASE | re.DOTALL)
# Expresión regular para detectar subcadenas numéricas que puedan representar versiones
version_pattern = re.compile(r'\b\d+(\.\d+)*\b')

# Diccionarios para almacenar el mapeo de URLs a rutas locales
http_mapping = {'js': {}}
successful_mapping = {}  # Diccionario para almacenar solo las URLs que se descargaron con éxito
error_log = []  # Lista para almacenar los errores de descarga
excluded_urls = {}  # Diccionario para almacenar las URLs excluidas

# Función para crear un nombre de archivo único si ya existe un archivo con el mismo nombre
def generate_unique_filename(folder, base_name, ext):
    counter = 1
    potential_name = f"{base_name}.{ext}"
    while os.path.exists(os.path.join(folder, potential_name)):
        potential_name = f"{base_name}-vunknown.{ext}"
        counter += 1
    return potential_name

# Función para extraer la clave de agrupación, que es el dominio principal del URL
def extract_group_key(url):
    match = re.search(r'https?://([^/]+)', url)
    if match:
        domain = match.group(1)
        group_key = domain.split('.')[0]
        return group_key
    return "unknown"

# Función para extraer el tipo de archivo (basado en el nombre base del archivo)
def get_type(path):
    match = re.match(r'^[a-zA-Z]+', path)
    if match:
        return match.group(0)
    return ""

# Función para descargar un archivo desde una URL y guardarlo en una ruta específica
def download_file(url, local_path):
    try:
        # Asegurarse de que el directorio existe
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        response = requests.get(url)
        response.raise_for_status()  # Verifica si la solicitud fue exitosa (status code 200)
        with open(local_path, 'wb') as file:
            file.write(response.content)
        print(f"Archivo descargado y guardado en {local_path}")
        return True
    except requests.exceptions.RequestException as e:
        error_log.append({"url": url, "error": str(e), "stage": "primary"})
        print(f"Error al descargar {url}: {e}")
        return False

def process_additional_resources(local_path, base_dir, group, file_type, url):
    # Esta función está vacía porque no hay recursos adicionales que procesar para archivos JS
    return True

# Recorrer los archivos de los templates para encontrar y mapear URLs de CDNs
for root, dirs, files in os.walk(template_dir):
    for file in files:
        if file.endswith(".html"):  # Solo se consideran archivos HTML
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                matches = cdn_pattern.findall(content)  # Buscar todas las URLs que coincidan con el patrón de CDN
                for url in matches:
                    # Excluir URLs en la lista de exclusión
                    if any(url.startswith(exclusion) for exclusion in exclusion_list):
                        if url not in excluded_urls:
                            excluded_urls[url] = {"veces_encontrado": 1}
                        else:
                            excluded_urls[url]["veces_encontrado"] += 1
                        continue
                    
                    filename = url.split('/')[-1]  # Extraer el nombre del archivo desde la URL
                    file_ext = filename.split('.')[-1]  # Extraer la extensión del archivo (.js)
                    
                    # Buscar si hay una versión en la URL
                    version_match = version_pattern.search(url)
                    if version_match:
                        version = f"-v{version_match.group(0)}"
                    else:
                        version = ""
                    
                    # Determinar si el archivo es JavaScript
                    if file_ext == 'js':
                        group = 'js'
                    else:
                        continue  # Ignorar archivos que no sean .js
                    
                    sub_group = extract_group_key(url)  # Extraer el grupo clave (dominio principal)
                    
                    # Inicializar el subgrupo si no existe en el mapeo
                    if sub_group not in http_mapping[group]:
                        http_mapping[group][sub_group] = {}
                    
                    base_name = filename.rsplit('.', 1)[0]  # Obtener el nombre base del archivo sin extensión
                    file_type = get_type(base_name)  # Obtener el tipo de archivo basado en su nombre base

                    new_filename = f"{file_type}/{base_name}{version}.{file_ext}"  # Crear el nuevo nombre de archivo
                    
                    # Si el nombre de archivo ya existe, generar uno único
                    if new_filename in http_mapping[group][sub_group].values():
                        new_filename = generate_unique_filename(os.path.join(group, sub_group), base_name, file_ext)
                    
                    # Crear la ruta local para el archivo mapeado
                    local_path = os.path.join('assets','cdns_locals', group, new_filename).replace("\\", "/")
                    http_mapping[group][sub_group][url] = local_path

# Guardar el mapeo en el archivo JSON principal
with open(http_output_file, 'w', encoding='utf-8') as f:
    json.dump(http_mapping, f, indent=4)

print(f"Todos los CDNs encontrados y mapeados en {http_output_file}")

# --- Segunda parte: Crear estructura de archivos y descargar el contenido ---

# Definir la ruta base donde se crearán las carpetas js dentro de muaytax/static/assets
base_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'muaytax', 'static', 'assets', 'cdns_locals')

# Crear las carpetas y archivos según el JSON, y descargar el contenido de las URLs
def create_structure_from_json(json_file, base_dir):
    with open(json_file, 'r', encoding='utf-8') as f:
        cdn_mapping = json.load(f)

    for group in cdn_mapping:
        successful_mapping[group] = {}
        for sub_group in list(cdn_mapping[group]):  # Iterar sobre una copia del diccionario
            successful_mapping[group][sub_group] = {}
            for url, local_path in list(cdn_mapping[group][sub_group].items()):  # Iterar sobre una copia de los elementos del diccionario
                file = os.path.basename(local_path)  # Obtener el nombre del archivo

                file_type = get_type(file)  # Obtener el tipo de archivo basado en su nombre base

                # Definir la nueva ruta con la estructura deseada
                new_dir = os.path.join(base_dir, group, file_type)
                new_path = os.path.join(new_dir, file)

                # Descargar y guardar el archivo desde la URL
                if download_file(url, new_path):
                    # Procesar recursos adicionales si los hay
                    if process_additional_resources(new_path, base_dir, group, file_type, url):
                        # Crear el directorio si no existe
                        os.makedirs(new_dir, exist_ok=True)
                        successful_mapping[group][sub_group][url] = local_path  # Solo agregar al mapeo exitoso si la descarga es exitosa
                # Eliminar el archivo del mapeo exitoso si existe y la descarga falla
                elif url in successful_mapping[group][sub_group]:
                    del successful_mapping[group][sub_group][url]

    print(f"Carpetas, archivos creados y contenidos descargados con éxito en {base_dir}")

# Crear la estructura desde el JSON generado
create_structure_from_json(http_output_file, base_dir)

# Guardar el log de errores en un archivo JSON
if error_log or excluded_urls:
    with open(error_log_file, 'w', encoding='utf-8') as f:
        if excluded_urls:
            error_log.append({"excluidos": excluded_urls})
        json.dump(error_log, f, indent=4)
    print(f"Errores encontrados durante el proceso. Detalles guardados en {error_log_file}")
else:
    print("Proceso completado sin errores.")

# --- Tercera parte: Reemplazar URLs en templates ---

# Cargar el mapeo de URLs exitosas desde el JSON
log_changes = {}  # Diccionario para registrar los cambios realizados
files_modified_count = 0  # Contador de archivos modificados

# Función para reemplazar las URLs en el contenido del template
def replace_urls_in_content(content, successful_mapping):
    modified = False
    for group in successful_mapping:
        for sub_group in successful_mapping[group]:
            for cdn_url, local_url in successful_mapping[group][sub_group].items():
                # Reemplazar la URL CDN por la versión local con STATIC_URL
                replacement = f"{{{{ STATIC_URL }}}}{local_url}"
                if cdn_url in content:
                    content = content.replace(cdn_url, replacement)
                    modified = True  # Marcar que el archivo ha sido modificado
    return content, modified

# Recorrer los archivos de los templates y aplicar el reemplazo de URLs solo si la descarga fue exitosa
for root, dirs, files in os.walk(template_dir):
    for file in files:
        if file.endswith(".html"):
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            new_content, modified = replace_urls_in_content(content, successful_mapping)

            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                relative_path = os.path.relpath(file_path, start=template_dir)  # Obtener la ruta relativa del archivo modificado
                log_changes[relative_path] = {  # Guardar el contenido antiguo y nuevo en el log
                    "old_content": content,
                    "new_content": new_content
                }

                files_modified_count += 1  # Incrementar el contador de archivos modificados

# Imprimir el número de archivos modificados
print(f"Total de archivos modificados: {files_modified_count}")
