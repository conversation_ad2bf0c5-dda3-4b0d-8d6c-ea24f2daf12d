
--QUERY PARA SABER EL NÚMERO DE REGISTROS AFECTADOS
--SELECT count(*) FROM invoices_invoice WHERE is_txt_amz IS True AND customer_id IS NULL AND invoice_category_id = 'sales' AND status_id = 'revised' AND tax_country_id IS NOT NULL

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_invoicesTXT_whitout_customer') THEN
        DROP FUNCTION func_invoicesTXT_whitout_customer(inv_limit INTEGER);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_invoicesTXT_whitout_customer(inv_limit INTEGER)
RETURNS VOID AS $$

DECLARE
invoices RECORD;
seller RECORD;
customer RECORD;
marketplace RECORD;
last_cust VARCHAR;
new_cust VARCHAR;
cus_name VARCHAR;
country VARCHAR;

BEGIN
    FOR invoices IN (
        SELECT DISTINCT
            inv.id AS id,
            inv.marketplace_id AS market,
			inv.seller_id AS seller_id,
			inv.tax_country_id AS tax_country
            FROM invoices_invoice inv
            WHERE is_txt_amz IS True 
            AND customer_id IS NULL
            AND invoice_category_id = 'sales'
            AND status_id = 'revised'
			AND tax_country_id IS NOT NULL
			ORDER BY inv.seller_id
			LIMIT inv_limit
    )
    LOOP
		
		SELECT name INTO country FROM dictionaries_country WHERE iso_code = invoices.tax_country LIMIT 1;
		
        SELECT id, seller_id INTO customer
        FROM customers_customer
        --WHERE seller_id = invoices.seller_id AND (name = 'Clientes Particulares ' || CONCAT(UPPER(SUBSTRING(invoices.market FROM 1 FOR 1)), LOWER(SUBSTRING(invoices.market FROM 2))) OR name = 'Clientes Particulares Amazon' );
		WHERE seller_id = invoices.seller_id AND name = 'Clientes Particulares Amazon ' || country;

        IF customer IS NULL THEN
			RAISE NOTICE 'customer es null: customer_id=%, customer_seller=%, invoice_seller=%', customer.id, customer.seller_id, invoices.seller_id;
			SELECT customer_number INTO last_cust FROM customers_customer WHERE seller_id = invoices.seller_id AND customer_number IS NOT NULL ORDER BY customer_number DESC LIMIT 1;
			new_cust := COALESCE(last_cust, '000000')::INTEGER + 1;
            new_cust := LPAD(new_cust::VARCHAR, 6, '0');
			--RAISE NOTICE 'customer_number: last_cust=%, new_cust=%', last_cust, new_cust ;
--             IF invoices.market IS NULL THEN
--                 cus_name := 'Clientes Particulares Amazon';
--             ELSE
--                 cus_name := 'Clientes Particulares ' || CONCAT(UPPER(SUBSTRING(invoices.market FROM 1 FOR 1)), LOWER(SUBSTRING(invoices.market FROM 2)));
--             END IF;
			
			cus_name := 'Clientes Particulares Amazon ' || country;
			--RAISE NOTICE 'cus name %', cus_name;

			INSERT INTO customers_customer 
				(name, 
				 customer_number, 
				 account_sales_id, 
				 country_id, 
				 customer_type_id, 
				 seller_id,
				 created_at,
				 modified_at)
				 VALUES
				 (cus_name,
				  new_cust,
				  '700',
				  invoices.tax_country,
				  'B2C',
				  invoices.seller_id,
				  CURRENT_TIMESTAMP,
				  CURRENT_TIMESTAMP)
				 RETURNING id INTO customer;
				 
				UPDATE invoices_invoice
				SET customer_id = customer.id
				WHERE id = invoices.id;
				--RAISE NOTICE 'customer asignado %', invoices.id ;
			
		ELSE
			--RAISE NOTICE 'customer NO es null: customer_id=%, customer_seller=%, invoice_seller=%', customer.id, customer.seller_id, invoices.seller_id;
			UPDATE invoices_invoice
			SET customer_id = customer.id
			WHERE id = invoices.id;
			--RAISE NOTICE 'customer asignado %', invoices.id ;
		END IF;
        
    END LOOP;


END;
$$ LANGUAGE plpgsql;

--select func_invoicesTXT_whitout_customer(20000);