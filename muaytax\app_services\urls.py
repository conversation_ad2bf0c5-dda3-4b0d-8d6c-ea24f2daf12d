from django.urls import path

from muaytax.app_services.views import raps, services

app_name = "app_services"
urlpatterns = [
  path(
    "sellers/<shortname>/services/",
    view=services.ServiceListView.as_view(),
    name="service_list",
  ),
  path(
    "sellers/<shortname>/serviceListDT/",
    view=services.ServiceListViewDT.as_view(),
    name="service_list_DT",
  ),
  path(
    "sellers/<shortname>/servicesUpdateAsync/",
    view=services.ServiceUpdateAsync.as_view(),
    name="service_updateAsync",
  ),
  path(
    "sellers/<shortname>/services/detail/<pk>/",
    view=services.ServiceUpdateView.as_view(),
    name="service_update",
  ),
  path(
    "sellers/<shortname>/reopen-form/",
    view=services.ReopenProcessedFormView.as_view(),
    name="reopen_processed_form",
  ),
  path (
    "sellers/<shortname>/rap-form/<rap_pk>/",
    view=raps.SellerRapRequest.as_view(),
    name="rap_request",
  ),
  path(
    "sellers/<shortname>/addRapRecord/",
    view=raps.AddRapRecord.as_view(),
    name="add_rap_record",
  ),
  path(
    "sellers/<shortname>/rapList/",
    view=raps.RapListView.as_view(),
    name="rap_list",
  ),
  path(
    "sellers/<shortname>/rapListJson/",
    view=raps.RapListViewJson.as_view(),
    name="rap_list_DT",
  )
]
