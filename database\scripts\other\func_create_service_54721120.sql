--QUERY PARA SABER EL NUMERO DE REGISTROS AFECTADOS
-- SELECT DISTINCT sel.id, sel.shortname, m5472.year
-- FROM documents_presentedm54721120 m5472
-- INNER JOIN sellers_seller sel ON sel.id = m5472.seller_id
-- LEFT JOIN (
--     SELECT m5472_inner.seller_id, m5472_inner.year
--     FROM documents_presentedm54721120 m5472_inner
--     INNER JOIN sellers_seller sel_inner ON sel_inner.id = m5472_inner.seller_id
--     INNER JOIN services_service serv ON serv.seller_id = sel_inner.id
--     WHERE serv.service_name_id = 'model_54721120'
-- ) AS filter ON filter.seller_id = sel.id AND filter.year = m5472.year
-- WHERE filter.seller_id IS NULL;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_create_service_54721120') THEN
        DROP FUNCTION func_create_service_54721120(seller_limit INTEGER);
    END IF;
END $$;


CREATE OR REPLACE FUNCTION func_create_service_54721120(seller_limit INTEGER)
RETURNS jsonb AS $$

DECLARE
m5472instances RECORD;
service_name_value VARCHAR;
-- quantity_value INTEGER;


tmp_key VARCHAR := '';
tmp_value VARCHAR := '';
tmp_json JSONB;
json_sellers JSONB := '{}'::JSONB; 

BEGIN

  --Busca todas las instancias de Form54721120 que no tengan un servicio 54721120 de ese año asociado
  FOR m5472instances IN (
    SELECT  DISTINCT
      COUNT(*) OVER() AS total_rows,
      sel.id AS sel_id, 
      sel.shortname AS short_name, 
      m5472.year AS m5472_year, 
      sel.is_5472_1120_inactive_contracted AS is_inactive,
      m5472.id AS m5472_id
    FROM documents_presentedm54721120 m5472
    INNER JOIN sellers_seller sel ON sel.id = m5472.seller_id
    LEFT JOIN (
      SELECT 
        m5472_inner.seller_id,
        m5472_inner.year
      FROM documents_presentedm54721120 m5472_inner
      INNER JOIN sellers_seller sel_inner ON sel_inner.id = m5472_inner.seller_id
      INNER JOIN services_service serv ON serv.seller_id = sel_inner.id
      WHERE
        (serv.service_name_id = 'model_54721120' OR serv.service_name_id = 'model_54721120_limited')
        AND serv.year = m5472_inner.year
    ) AS filter ON filter.seller_id = sel.id AND filter.year = m5472.year
    -- WHERE filter.seller_id IS NULL
  )
  LOOP
        --SHORTNAME DEL SELLER
        tmp_value := m5472instances.short_name || ' - ' || m5472instances.m5472_year;
        tmp_key := m5472instances.m5472_id;
        tmp_json := json_build_object(tmp_key, tmp_value);
        json_sellers := jsonb_concat(json_sellers, tmp_json);

        IF m5472instances.is_inactive THEN
          -- quantity_value := 10;
          service_name_value := 'model_54721120_limited';
        ELSE
          -- quantity_value := -1;
          service_name_value := 'model_54721120';
        END IF;



      -- Check if the service already exists
      IF NOT EXISTS (
        SELECT 1
        FROM services_service
        WHERE seller_id = m5472instances.sel_id
          AND service_name_id = service_name_value
          AND year = m5472instances.m5472_year
      ) THEN
        INSERT INTO services_service (
          seller_id,
          service_name_id,
          year,
          quantity,
          created_at,
          modified_at
        ) VALUES(
          m5472instances.sel_id,
          service_name_value,
          m5472instances.m5472_year,
          1,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        );
      END IF;
        

  END LOOP;

  RETURN json_sellers;

END $$ LANGUAGE plpgsql;

--SELECT func_create_service_54721120(10);

----------------------------------------------------------------------------------------------------
--------------------CREAR SERVICIO 5472-1120 A PARTIR DE UN PROCESO DE VENTA------------------------

--QUERY PARA SABER EL NUMERO DE REGISTROS AFECTADOS
-- SELECT DISTINCT
--     sel.id, 
--     sel.name, 
--     sel.shortname, 
--     pc.product_name, 
--     TO_CHAR(pc.contracting_date, 'DD/MM/YYYY') AS formatted_date
-- FROM 
--     sellers_sellerprocess pc 
-- INNER JOIN 
--     sellers_seller sel ON sel.id = pc.seller_id
-- LEFT JOIN (
--     SELECT serv.seller_id
--     FROM services_service serv
--     INNER JOIN sellers_seller sel_inner ON sel_inner.id = serv.seller_id
--     WHERE serv.service_name_id = 'model_54721120' AND serv.year IN ('2021','2022', '2023', '2024')
-- ) AS filter ON filter.seller_id = sel.id
-- WHERE 
--     filter.seller_id IS NULL
--     AND product_id IN 
--     ('11062', '11066', '6631', '7453', '1498', '17238', '17237', '17235', '17234', '17233', '17232','17231', '17230');


DO $$
BEGIN 
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_create_service_54721120_from_process') THEN
        DROP FUNCTION func_create_service_54721120_from_process(seller_limit INTEGER);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_create_service_54721120_from_process(seller_limit INTEGER)
RETURNS jsonb AS $$

DECLARE
m5472process RECORD;
-- quantity_value INTEGER;
service_name_value VARCHAR;
year_value INTEGER;

tmp_key VARCHAR := '';
tmp_value VARCHAR := '';
tmp_json JSONB;
json_sellers JSONB := '{}'::JSONB;

BEGIN

  --Busca todas las instancias de Form54721120 que no tengan un servicio 5472-1120 contratado

  FOR m5472process IN (
    SELECT DISTINCT
        COUNT(*) OVER() AS total_rows,
        sel.id AS sel_id,
        sel.shortname AS shortname,
        pc.product_id AS product_id,
        pc.product_name AS productName,
        pc.contracting_date AS contracting_date,
        pc.id AS pc_id
    FROM
        sellers_sellerprocess pc
    INNER JOIN
        sellers_seller sel ON sel.id = pc.seller_id
    LEFT JOIN (
        SELECT
          serv.seller_id,
          serv.id
        FROM services_service serv
        INNER JOIN sellers_seller sel_inner ON sel_inner.id = serv.seller_id
        WHERE 
          (serv.service_name_id = 'model_54721120' OR serv.service_name_id = 'model_54721120_limited')
          AND serv.year IN ('2021','2022', '2023', '2024')
    ) AS filter ON filter.seller_id = sel.id
    WHERE
        -- filter.seller_id IS NULL
        -- AND 
        product_id IN 
        ('11062', '11066', '6631', '7453', '1498', '17238', '17237', '17235', '17234', '17233', '17232','17231', '17230')
  )
  LOOP
      --SHORTNAME DEL SELLER
      tmp_value := m5472process.shortname || ' - ' || m5472process.product_id || ' - ' || m5472process.productName || ' - ' || TO_CHAR(m5472process.contracting_date, 'DD/MM/YYYY');
      tmp_key := m5472process.pc_id;
      tmp_json := json_build_object(tmp_key, tmp_value);
      json_sellers := jsonb_concat(json_sellers, tmp_json);

      IF m5472process.product_id IN ('11062', '6631', '17230', '17232', '17234', '17238' ) THEN
        -- quantity_value := -1;
        service_name_value := 'model_54721120';
      ELSE
        -- quantity_value := 10;
        service_name_value := 'model_54721120_limited';
      END IF;

      IF m5472process.product_id IN ('17230', '17231') THEN
        year_value := 2021;
      ELSIF m5472process.product_id IN ('6631', '17232', '17233') THEN
        year_value := 2022;
      ELSIF m5472process.product_id IN ('11062', '11066', '17235') THEN
        year_value := 2023;
      ELSIF m5472process.product_id IN ('17237', '17238') THEN
        year_value := 2024;
      END IF;

      -- INSERT INTO services_service (
      --   seller_id,
      --   service_name_id,
      --   year,
      --   contracting_date,
      --   quantity,
      --   created_at,
      --   modified_at
      -- ) VALUES(
      --   m5472process.sel_id,
      --   service_name_value,
      --   year_value,
      --   m5472process.contracting_date,
      --   1,
      --   CURRENT_TIMESTAMP,
      --   CURRENT_TIMESTAMP
      -- );

      -- Check if the service already exists
      IF NOT EXISTS (
        SELECT 1
        FROM services_service
        WHERE seller_id = m5472process.sel_id
          AND service_name_id = service_name_value
          AND year = year_value
      ) THEN
        INSERT INTO services_service (
          seller_id,
          service_name_id,
          year,
          contracting_date,
          quantity,
          created_at,
          modified_at
        ) VALUES(
          m5472process.sel_id,
          service_name_value,
          year_value,
          m5472process.contracting_date,
          1,
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        );
      END IF;
  END LOOP;


-- RETURN JSON con los seller afectados y qué años del 184 pertenecen a cada uno
	RETURN json_sellers;

END;
$$ LANGUAGE plpgsql;

--SELECT func_create_service_54721120_from_process(1);