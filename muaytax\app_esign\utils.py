import os
import tempfile
import pdfkit

from django.utils import timezone
from django.template.loader import render_to_string


def get_lipe_signing_pdf(model):
    template_path = "documents/signing_documents/sign_lipe.html"
    today = timezone.now()
    temp_dir = tempfile.mkdtemp()

    file_name = f"{model.seller.shortname}_Lipe_periodo_.pdf"

    data = {
        "model": model,
        "today": today,
    }

    rendered_template = render_to_string(template_path, data)

    output_path = os.path.join(temp_dir, file_name)

    pdfkit.from_string(rendered_template, output_path)

    return output_path

