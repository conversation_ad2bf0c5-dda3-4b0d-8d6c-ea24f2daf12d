--QUERY PARA SABER EL NÚMERO DE REGISTROS AFECTADOS
-- SELECT count(*)
-- FROM invoices_invoice invDUA
-- LEFT JOIN invoices_invoice inv ON inv.id = invDUA.related_invoice_id
-- WHERE invDUA.related_invoice_id IS NOT NULL AND invDUA.invoice_type_id = 'import-dua'
-- AND (NOT (invDUA.invoice_category_id = 'expenses_copy' OR invDUA.invoice_category_id = 'sales_copy') OR invDUA.invoice_category_id IS NULL)
-- AND (NOT (inv.invoice_category_id = 'expenses_copy' OR inv.invoice_category_id = 'sales_copy') OR inv.invoice_category_id IS NULL)
-- AND (invDUA.is_dua_completed = FALSE OR inv.is_dua_completed = FALSE)


-- SELECT invDUA.invoice_type_id, invDUA.id, invDUA.related_invoice_id, invDUA.invoice_category_id, inv.invoice_category_id, inv.invoice_type_id, inv.invoice_category_id, 
-- invDUA.seller_id
-- FROM invoices_invoice invDUA
-- LEFT JOIN invoices_invoice inv ON inv.id = invDUA.related_invoice_id
-- WHERE invDUA.related_invoice_id IS NOT NULL AND invDUA.invoice_type_id = 'import-dua'
-- AND (NOT (invDUA.invoice_category_id = 'expenses_copy' OR invDUA.invoice_category_id = 'sales_copy') OR invDUA.invoice_category_id IS NULL)
-- AND (NOT (inv.invoice_category_id = 'expenses_copy' OR inv.invoice_category_id = 'sales_copy') OR inv.invoice_category_id IS NULL)
-- AND (invDUA.is_dua_completed = FALSE OR inv.is_dua_completed = FALSE)

  -- SELECT id, is_dua_completed, accounting_date
  -- FROM invoices_invoice
  -- WHERE EXTRACT(YEAR FROM accounting_date) = 2023
  -- AND invoice_type_id IN ('import-dua', 'import-expenses', 'import-invoice')


DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_migrate_importDUA_inv') THEN
        DROP FUNCTION func_migrate_importDUA_inv(inv_limit INTEGER);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_migrate_importDUA_inv(inv_limit INTEGER)
RETURNS VOID AS $$
DECLARE

invoiceDUA_imp RECORD;
invoiceExp_DUA RECORD;

BEGIN

  --ACTUALIZAR FACTURAS RELACIÓN (DUA -> IMPORT-INVOICE)
  FOR invoiceDUA_imp IN(
    SELECT DISTINCT
      invDUA.id AS invDUA_id,
      invDUA.related_invoice_id AS related_invoice_idDUA,
      inv.id AS inv_id
    FROM invoices_invoice invDUA
    LEFT JOIN invoices_invoice inv ON inv.id = invDUA.related_invoice_id
    WHERE invDUA.related_invoice_id IS NOT NULL AND invDUA.invoice_type_id = 'import-dua'
    AND (invDUA.is_dua_completed = FALSE OR inv.is_dua_completed = FALSE)
    AND (NOT (invDUA.invoice_category_id = 'expenses_copy' OR invDUA.invoice_category_id = 'sales_copy') OR invDUA.invoice_category_id IS NULL)
    AND (NOT (inv.invoice_category_id = 'expenses_copy' OR inv.invoice_category_id = 'sales_copy') OR inv.invoice_category_id IS NULL)
    LIMIT inv_limit
  )
  LOOP
    UPDATE invoices_invoice
    SET is_dua_completed = TRUE
    WHERE id IN (invoiceDUA_imp.invDUA_id, invoiceDUA_imp.related_invoice_idDUA);

    INSERT INTO invoices_invoice_related_invoice_many
      (
        from_invoice_id,
        to_invoice_id
      )
      VALUES(
        invoiceDUA_imp.invDUA_id,
        invoiceDUA_imp.related_invoice_idDUA
      );
  END LOOP;

  --ACTUALIZAR FACTURAS RELACIÓN (IMPORT-EXPENSES -> DUA)
  FOR invoiceExp_DUA IN(
    SELECT DISTINCT
      invEXP.id AS invEXP_id,
      invEXP.related_invoice_id AS related_invoice_idEXP,
      invDUA.id AS invDUA_id
    FROM invoices_invoice invEXP
    LEFT JOIN invoices_invoice invDUA ON invDUA.id = invEXP.related_invoice_id
    WHERE invEXP.related_invoice_id IS NOT NULL AND invEXP.invoice_type_id = 'import-expenses'
    AND (invEXP.is_dua_completed = FALSE OR invDUA.is_dua_completed = FALSE)
    AND (NOT (invEXP.invoice_category_id = 'expenses_copy' OR invEXP.invoice_category_id = 'sales_copy') OR invEXP.invoice_category_id IS NULL)
    AND (NOT (invDUA.invoice_category_id = 'expenses_copy' OR invDUA.invoice_category_id = 'sales_copy') OR invDUA.invoice_category_id IS NULL)
    LIMIT inv_limit
  )
  LOOP
    UPDATE invoices_invoice
    SET is_dua_completed = TRUE
    WHERE id IN (invoiceExp_DUA.invEXP_id, invoiceExp_DUA.invDUA_id);

    INSERT INTO invoices_invoice_related_invoice_many
      (
        from_invoice_id,
        to_invoice_id
      )
      VALUES(
        invoiceExp_DUA.invDUA_id,
        invoiceExp_DUA.invEXP_id
      );
  END LOOP;

  UPDATE invoices_invoice
  SET is_dua_completed = TRUE
  WHERE ((EXTRACT(YEAR FROM accounting_date) IN (2021, 2022, 2023)) OR (EXTRACT(YEAR FROM created_at) IN (2021, 2022, 2023)))
  AND invoice_type_id IN ('import-dua', 'import-expenses', 'import-invoice');



END;
$$ LANGUAGE plpgsql;
--SELECT func_migrate_importDUA_inv(5);