from django import forms
from django.core.validators import FileExtensionValidator
from muaytax.app_banks.models.bank import Bank
from muaytax.app_banks.models.movement import Movement

class MovementChangeForm(forms.ModelForm):
    class Meta:
        model = Movement
        exclude = []
        
class MovementDeleteForm(forms.Form):
    class Meta:
        model = Movement
        exclude = []

class MovementCreateForm(forms.ModelForm):
    class Meta:
        model = Movement
        exclude = []
        # fields = ["file"]
        file = forms.FileField(validators=[FileExtensionValidator(["txt", "csb", "aeb", "csb43", "aeb43"])])