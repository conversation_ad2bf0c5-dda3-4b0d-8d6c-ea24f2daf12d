/* =======================================================
  FUENTE DE COLORES GENERALES:  formIVA
======================================================= */
:root {
  /* === Tonos grises === */
  --color-grey-border: #838383;              /* Borde general */
  --color-grey-light: #e9ecef;               /* Fondos claros tipo Bootstrap */
  --color-grey: #9bb4d1;                     /* Borde personalizado en campos textarea */
  --color-grey-soft: #f0f0f0;                /* Fondo suave */
  --color-grey-file: #ced4da;                /* Input file info background */
  --color-grey-border-light: #dddddd;        /* Gris claro para bordes suaves */
  --color-grey-medium: #6c757d;              /* Gris medio tipo Bootstrap */
  --color-grey-border-alt: #d6d6d6;          /* Borde alternativo */
  --color-grey-light-readonly: #e8effb;      /* Fondo de inputs readonly */
  
  /* === Tonos oscuros y neutros === */
  --color-black: #000;                       /* Negro absoluto */
  --color-black-blue: #0F1C4D;               /* Azul muy oscuro */
  --color-black-gray: #1C1F24;               /* Gris muy oscuro */
  --color-dark: #495057;                     /* Gris oscuro tipo texto Bootstrap */
  
  /* === Tonos azules === */
  --color-blue: #569ae7;                     /* Azul para focus y bordes activos */
  --color-blue-tooltip: #e5f6ff;             /* Fondo de tooltip informativo */
  --color-blue-text: #00567c;                /* Texto técnico, tipo nota */
  --color-blue-link: #337ab7;                /* Enlaces estándar */
  --color-blue-main: #0d6efd;                /* Azul Bootstrap principal */
  --color-blue-dark: #031549;                   /* Azul Botones, tooltip, ...
  
  /* === Tonos rojos === */
  --color-red: #dc3545;                      /* Rojo Bootstrap general */
  --color-red-dark: #842029;                 /* Texto de error Bootstrap */
  --color-red-soft: #FFF4F4;                 /* Fondo de error suave */
  --color-red-border: #E0364C;               /* Bordes de error o validaciones */
  --color-red-modal: #a94442;                /* Texto o mensaje en modal de error */
  --color-red-tooltip: #F5C2C7;              /* Tooltip tipo advertencia */
  --color-red-hover-dark: rgb(95, 10, 10);   /* Hover oscuro para enlaces o botones rojos */
  
  /* === Tonos verdes === */
  --color-green: #04ac64;                    /* Confirmación/validación principal */
  --color-green-light: #36e093;              /* Verde brillante (secundario) */
  --color-green-success: #28C76F;            /* Verde de éxito Bootstrap-like */
  --color-green-tooltip: #3c763d;            /* Tooltip tipo “ejemplo” o info extra */

  /* === Tonos naranjas === */
  --color-orange: #ff7b00;                   /* Naranja corporativo personalizado */
  --color-orange-btn: #EA5455;               /* Botón naranja alternativo */

  /* === Blanco === */
  --color-white: #fff;                       /* Color de fondo y texto neutro */
}


/* En la  APP */
/* :root { */
  /* ======================================================
     BRAND COLORS - PRIMARY
  ====================================================== */
  /* --color-brand-primary-dark:       #00AD65;
  --color-brand-primary-medium:     #36E093;
  --color-brand-primary-light:      #D9FFEE; */

  /* ======================================================
     BRAND COLORS - NEUTRAL
  ====================================================== */
  /* --color-brand-neutral-darkest:    #031549;
  --color-brand-neutral-dark:       #687392;
  --color-brand-neutral-light:      #E5ECF7;
  --color-brand-neutral-lightest:   #F7FAFF; */

  /* ======================================================
     BRAND COLORS - SECONDARY
  ====================================================== */
  /* --color-brand-secondary-dark:     #4038A3;
  --color-brand-secondary-medium:   #8F69FC;
  --color-brand-secondary-light:    #ECE5FF; */

  /* ======================================================
     NEUTRAL COLORS (GENERAL PURPOSE)
  ====================================================== */
  /* --color-neutral-darkest:          #000000;
  --color-neutral-dark:             #4F606B;
  --color-neutral-medium:           #91ABBC;
  --color-neutral-light:            #E9EEF2;
  --color-neutral-lightest:         #FFFFFF; */

  /* ======================================================
     SUPPORT COLORS
  ====================================================== */
  /* --color-support-dark:             #2A7993;
  --color-support-medium:           #4DD3FF;
  --color-support-light:            #DBF6FF; */

  /* ======================================================
     FEEDBACK COLORS
  ====================================================== */
  /* --color-feedback-error:           #E0364C;
  --color-feedback-alert:           #FFCA33;
  --color-feedback-info:            #00ABE2;
  --color-feedback-success:         #93EF67; */

  /* ======================================================
     GRADIENT COLORS
  ====================================================== */
  /* --color-gradient-lightest:        #ffffff; 
  --color-gradient-darkest:         #000000; */
/* } */
