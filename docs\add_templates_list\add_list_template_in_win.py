import os

# Ruta donde se crearán los archivos dentro de muaytax/templates/sellers/include/service_iva/
TEMPLATES_PATH = "muaytax/templates/sellers/include/service_iva/"

# Lista de archivos a crear
FILES = [
    "general_info.html",
    "migration_info.html",
    "members_info.html",
    "country_documents.html",
    "final_summary.html",
    "seller_vat_partners.html"
]

def create_files():
    """Crea los archivos vacíos en la ruta especificada."""
    if not os.path.exists(TEMPLATES_PATH):
        os.makedirs(TEMPLATES_PATH)

    for file_name in FILES:
        file_path = os.path.join(TEMPLATES_PATH, file_name)
        if not os.path.exists(file_path):
            with open(file_path, "w", encoding="utf-8") as f:
                pass  # Crea el archivo vacío
            print(f"Archivo creado: {file_path}")
        else:
            print(f"El archivo ya existe: {file_path}")

if __name__ == "__main__":
    create_files()
