from django import forms
from muaytax.app_banks.models.entry import Entry

class EntryCreateForm(forms.ModelForm):
    class Meta:
        model = Entry
        fields = [
            'entry_seller',
            'entry_date',
            'entry_num',
            'entry_document',
            'entry_concept',
            'entry_accounting_account',
            'entry_reconciliation',
            'entry_invoice',
            'entry_accounting_account_description',
            'entry_debit',
            'entry_credit',
            'entry_export',
            'is_entry_balanced_result',
            'used_in_export'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:  # si existe la instancia
            # Filtra el queryset para entry_invoice
            if self.instance.entry_invoice:
                self.fields['entry_invoice'].queryset = self.fields['entry_invoice'].queryset.filter(pk=self.instance.entry_invoice.pk)
            else:
                self.fields['entry_invoice'].queryset = self.fields['entry_invoice'].queryset.none()

            # Filtra el queryset para entry_reconciliation
            if self.instance.entry_reconciliation:
                self.fields['entry_reconciliation'].queryset = self.fields['entry_reconciliation'].queryset.filter(pk=self.instance.entry_reconciliation.pk)
            else:
                self.fields['entry_reconciliation'].queryset = self.fields['entry_reconciliation'].queryset.none()
        else:
            # Si no hay instancia, vacía los querysets para evitar cargar opciones innecesarias
            self.fields['entry_invoice'].queryset = self.fields['entry_invoice'].queryset.none()
            self.fields['entry_reconciliation'].queryset = self.fields['entry_reconciliation'].queryset.none()
            
class EntryEditForm(forms.ModelForm):
    class Meta:
        model = Entry
        fields = [
            'entry_seller',
            'entry_date',
            'entry_num',
            'entry_document',
            'entry_concept',
            'entry_accounting_account',
            'entry_reconciliation',
            'entry_invoice',
            'entry_accounting_account_description',
            'entry_debit',
            'entry_credit',
            'entry_export',
            'is_entry_balanced_result',
            'used_in_export'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:  # si existe la instancia
            # Filtra el queryset para entry_invoice
            if self.instance.entry_invoice:
                self.fields['entry_invoice'].queryset = self.fields['entry_invoice'].queryset.filter(pk=self.instance.entry_invoice.pk)
            else:
                self.fields['entry_invoice'].queryset = self.fields['entry_invoice'].queryset.none()

            # Filtra el queryset para entry_reconciliation
            if self.instance.entry_reconciliation:
                self.fields['entry_reconciliation'].queryset = self.fields['entry_reconciliation'].queryset.filter(pk=self.instance.entry_reconciliation.pk)
            else:
                self.fields['entry_reconciliation'].queryset = self.fields['entry_reconciliation'].queryset.none()
        else:
            # Si no hay instancia, vacía los querysets para evitar cargar opciones innecesarias
            self.fields['entry_invoice'].queryset = self.fields['entry_invoice'].queryset.none()
            self.fields['entry_reconciliation'].queryset = self.fields['entry_reconciliation'].queryset.none()

class EntryDeleteForm(forms.ModelForm):
    class Meta:
        model = Entry
        fields = []