DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_seller_vat_IT_yearly') THEN
        DROP FUNCTION func_seller_vat_IT_yearly(date_year INTEGER, date_period VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_seller_vat_IT_yearly(date_year INTEGER, date_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    -- inv_data RECORD;
	first_month DATE;
	last_month DATE;
	last_year DATE;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
BEGIN
	IF date_period = '0A' THEN 
        first_month := date_year || '-01-01';
        last_month := (date_year + 1) || '-01-01';
    END IF;
				
	last_year = date_year ||'-01-01';

	SELECT jsonb_agg(sub_data) INTO result_json
	FROM (
    -- FOR inv_data IN
		SELECT DISTINCT 
					-- SubSelect
					subselect.*, 

					-- Model Min
					(
						CASE 
							WHEN 'required' in (anualle) THEN 0
							WHEN 'warning01' in (anualle) THEN 1
							WHEN 'warning02' in (anualle) THEN 2
							WHEN 'warning04' in (anualle) THEN 3
							WHEN 'disagreed' in (anualle) THEN 4
							WHEN 'agreed' in (anualle) THEN 5
							WHEN 'pending' in (anualle) THEN 6
							WHEN 'presented' in (anualle) THEN 7
							WHEN 'not-required' in (anualle) THEN 8
						ELSE 9
						END 
					) AS model_min,

					-- Model Average
					(
						(
							CASE
								WHEN anualle = 'required' THEN 0
								WHEN anualle = 'warning01' THEN 1
								WHEN anualle = 'warning02' THEN 2
								WHEN anualle = 'warning04' THEN 3
								WHEN anualle = 'disagreed' THEN 4
								WHEN anualle = 'agreed' THEN 5
								WHEN anualle = 'pending' THEN 6
								WHEN anualle = 'presented' THEN 7
								WHEN anualle = 'not-required' THEN 8
								ELSE 9
							END)
					) * 100 / 9 as model_avg

					FROM 
				(

						SELECT DISTINCT 
								-- id
								sel.id,

								-- seller name
								sel.name AS seller_name,

								-- shortname
								sel.shortname AS shortname,

								-- email
								(SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,

								-- user name
								(SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,

								-- gestor asignado
								MAX(sv.manager_assigned_id) AS manager_assigned,

								-- last login
								(SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,

								-- Type Representation (sellervat IT)
								(SELECT it_representation_type  FROM sellers_sellervat sv WHERE sv.seller_id = sel.id AND sv.vat_country_id = 'IT' LIMIT 1) AS type_representation,

								-- Cod fiscale (sellervat IT)
								(SELECT codice_fiscale  FROM sellers_sellervat sv WHERE sv.seller_id = sel.id AND sv.vat_country_id = 'IT' LIMIT 1) AS cod_fiscale,

								-- Vat Number (sellervat IT)
								(SELECT vat_number  FROM sellers_sellervat sv WHERE sv.seller_id = sel.id AND sv.vat_country_id = 'IT' LIMIT 1) AS vat_number,

								-- Num Invoices
								COUNT(DISTINCT inv.id) AS num_invoices,

								-- Num Pending Invoices
								COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,

								-- Percentage Pending Invoices
								ROUND(COALESCE(
									100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
									0
								), 2) AS percentage_pending_invoices,
								
								MAX(
                                    CASE WHEN pm.anualle IS NOT NULL THEN pm.anualle
									WHEN pmf.anualle IS NOT NULL THEN pmf.anualle
									ELSE
										CASE 
									WHEN i.num_inv_null_revised > 0
										THEN 'warning04' 
									WHEN 	
											--sv.is_contracted = FALSE
											(
												(
													sv.activation_date IS NULL AND
													sv.contracting_date IS NULL
												) OR
												(
													sv.deactivation_date < first_month AND
													sv.activation_date IS NOT NULL
												) OR
												(
													sv.contracting_date IS NOT NULL AND
													sv.end_contracting_date < first_month
												) OR
												(
													sv.activation_date > last_month
												) OR
												(
													sv.contracting_date > last_month
												)
											) AND sv.vat_country_id = 'IT' AND i.num_inv >0
										THEN 'warning01'
									WHEN i.num_inv > 0 AND NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'IT' AND sv.seller_id = sel.id)
										THEN 'warning02'		
									WHEN sv.vat_country_id = 'IT'  AND (
										(
											sv.activation_date IS NOT NULL AND
											sv.activation_date < last_month AND
											( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month)
										) OR  (
											sv.activation_date IS NULL AND
											sv.contracting_date IS NOT NULL AND
											sv.contracting_date <= last_month AND
											( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_month)
										)
									)
										THEN 'required'
									ELSE 'not-required' END
                                    END
								) AS anualle
											
								FROM sellers_seller sel
								LEFT JOIN invoices_invoice inv ON (sel.id = inv.seller_id AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL ))
								LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id						
                                LEFT JOIN (
									SELECT
									sel.id as seller_id,
									MAX(CASE WHEN pm.model_id::text = 'IT-VATANNUALE' THEN pm.status_id END) as anualle
									FROM sellers_seller sel
									LEFT JOIN documents_presentedmodel pm ON sel.id = pm.seller_id
									WHERE pm.year = date_year AND (pm.period_id = date_period or pm.period_id = '0A') AND pm.country_id = 'IT' 
									GROUP BY sel.id	
								) AS pm ON sel.id = pm.seller_id
								LEFT JOIN (
									SELECT
									sel.id as seller_id,
									MAX(CASE WHEN pmf.model_id::text = 'IT-VATANNUALE' THEN pmf.status_id END) as anualle
									FROM sellers_seller sel
									LEFT JOIN documents_presentedmodelforced pmf ON sel.id = pmf.seller_id
									WHERE pmf.year = date_year AND (pmf.period_id = date_period or pmf.period_id = '0A') AND pmf.country_id = 'IT' 
									GROUP BY sel.id		
								) AS pmf ON sel.id = pmf.seller_id
								LEFT JOIN(
									SELECT
									sel.id as seller_id,
									COUNT(DISTINCT CASE WHEN (i.accounting_date >= first_month AND i.accounting_date < last_month) OR (i.accounting_date IS NULL AND NOT i.status_id = 'discard') THEN i.id END) as num_inv,
									COUNT(DISTINCT CASE WHEN  (i.accounting_date IS NULL AND i.status_id = 'revised') THEN i.id END) as num_inv_null_revised
									FROM sellers_seller sel
									LEFT JOIN invoices_invoice i ON sel.id = i.seller_id
									WHERE tax_country_id = 'IT'
									AND is_txt_amz IS NOT TRUE
									GROUP BY sel.id
								) AS i ON sel.id = i.seller_id

								WHERE 
									(
										sv.vat_country_id = 'IT'  AND (
											(
												sv.activation_date IS NOT NULL AND
												sv.activation_date < last_month AND
												( sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month)
											) OR  (
												sv.activation_date IS NULL AND
												sv.contracting_date IS NOT NULL AND
												sv.contracting_date <= last_month AND
												( sv.end_contracting_date IS NULL OR sv.end_contracting_date >= first_month)
											)
										)
									)
									 OR (
										--sv.is_contracted = FALSE AND
										(
											(
												sv.activation_date IS NULL AND
												sv.contracting_date IS NULL
											) OR
											(
												sv.deactivation_date < first_month AND
												sv.activation_date IS NOT NULL
											) OR
											(
												sv.contracting_date IS NOT NULL AND
												sv.end_contracting_date < first_month
											) OR
											(
												sv.activation_date > last_month
											) OR
											(
												sv.contracting_date > last_month
											)
										) AND
										sv.vat_country_id = 'IT'  AND i.num_inv > 0
									) 
									OR ( i.num_inv > 0 AND ( NOT EXISTS (SELECT 1 FROM sellers_sellervat sv WHERE sv.vat_country_id = 'IT' AND sv.seller_id = sel.id )))
								GROUP BY sel.id
								ORDER BY sel.id
				) AS subselect
	) AS sub_data;
			
		-- 	LOOP
		-- 	result_json := result_json || jsonb_build_object(
		-- 		'seller_id', inv_data.id,
		-- 		'seller_name', inv_data.seller_name,
		-- 		'shortname', inv_data.seller_shortname,
		-- 		'email', inv_data.email,
		-- 		'user_name', inv_data.user_name,
		-- 		'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
		-- 		'num_pending_invoices', inv_data.num_pending_invoices,
		-- 		'percentage_pending_invoices', inv_data.percentage_pending_invoices,
		-- 		'anualle', inv_data.anualle,
		-- 		'model_min', inv_data.model_min,
		-- 		'model_avg', inv_data.model_avg
		-- 	);
			
		-- END LOOP;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;

-- USE FUNCTION
-- SELECT func_seller_vat_IT_yearly(2023, '0A');