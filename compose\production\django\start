#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset
pwd

if [ -f "import-db.json" ]; then
    echo "Flush database"
    python manage.py flush --noinput

    echo "Load database"
    python manage.py loaddata --ignorenonexistent --exclude auth.permission --exclude contenttypes import-db.json
    
    echo "Remove import-db.json"
    rm -rf import-db.json

    echo "Migrate"
    python manage.py migrate
    
    echo "Runserver"
    # python manage.py runserver 0.0.0.0:8000
    python /app/manage.py collectstatic --noinput
    /usr/local/bin/gunicorn config.wsgi --bind 0.0.0.0:5000 --chdir=/app
else
    echo "Migrate"
    python manage.py migrate

    echo "Runserver"
    # python manage.py runserver 0.0.0.0:8000
    python /app/manage.py collectstatic --noinput
    /usr/local/bin/gunicorn config.wsgi --bind 0.0.0.0:5000 --chdir=/app
fi



