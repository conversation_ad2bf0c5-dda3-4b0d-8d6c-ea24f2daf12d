import os
import unicodedata
from geopy.geocoders import GoogleV3
from geopy.exc import GeocoderTimedOut, GeocoderServiceError

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.translation import gettext as _

from muaytax.app_address.serializers import AddressMainSerializer


class ValidateAddressView(APIView):
    # permission_classes = [IsAuthenticated]  # Descomentar si se requiere autenticación. Para pruebas, se puede comentar esta línea.
    permission_classes = []  # Descomentar si no se requiere autenticación. Para pruebas.

    def post(self, request, *args, **kwargs):
        """
        Valida la dirección proporcionada por el usuario utilizando la API de Google Geocoding.
        Checkea si la dirección coincide con los datos proporcionados y devuelve la información geográfica.
        Si la dirección no coincide, devuelve un mensaje de error con las diferencias encontradas.
        """
        serializer = AddressMainSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Validación y normalización de la dirección
        validated_data = serializer.validated_data
        address = validated_data.get("address", "")
        number = validated_data.get("address_number", "")
        zip_code = validated_data.get("address_zip", "")
        city = validated_data.get("address_city", "")
        country_obj = validated_data.get("address_country")
        country_code = str(country_obj).strip()

        full_address = f"{address} {number}, {zip_code} {city}, {country_code}"

        # Se verifica si la clave de API de Google está configurada
        api_key = os.getenv("GELOCATOR_GOOGLEV3_KEY")
        if not api_key:
            return Response(
                {"error": "GoogleV3 API key not configured."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Se intenta geocodificar la dirección completa
        try:
            geolocator = GoogleV3(api_key=api_key)
            location = geolocator.geocode(full_address, timeout=10, language="es")

            if not location:
                return Response({
                    "valid": False,
                    "code": "not_found",
                    "message": "Dirección no encontrada o incompleta"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Se obtienen los componentes de la dirección desde la respuesta de Google
            components = location.raw.get("address_components", [])

            def get_component(type_name):
                for comp in components:
                    if type_name in comp["types"]:
                        return comp["long_name"]
                return None

            # Se extraen los componentes relevantes de la dirección
            google_number = get_component("street_number") or None
            google_zip = get_component("postal_code") or None
            google_city = get_component("locality") or get_component("postal_town") or None
            google_country = get_component("country") or None

            # diccionario para almacenar las diferencias encontradas
            differences = {}


            if number and google_number:
                if self.normalize_text(number) not in self.normalize_text(google_number):
                    differences["address_number"] = {
                        "code": "mismatch",
                        "expected": number,
                        "actual": google_number
                    }
            elif number and not google_number and (number.lower() not in ['s/n', 's/a']):
                differences["address_number"] = {
                    "code": "not_found_in_google_response",
                    "expected": number,
                    "actual": None
                }

            if zip_code and google_zip:
                if google_zip != zip_code:
                    differences["postal_code"] = {
                        "code": "mismatch",
                        "expected": zip_code,
                        "actual": google_zip
                    }
            elif zip_code and not google_zip and (zip_code.lower() not in ['s/n', 's/a']):
                differences["postal_code"] = {
                    "code": "not_found_in_google_response",
                    "expected": zip_code,
                    "actual": None
                }

            if google_city:
                if not self.normalize_text(city) in self.normalize_text(google_city):
                    differences["city"] = {
                        "code": "mismatch",
                        "expected": city,
                        "actual": google_city
                    }
            else:
                differences["city"] = {
                    "code": "not_found_in_google_response",
                    "expected": city,
                    "actual": None
                }

            if google_country:
                if google_country and country_code.upper() not in google_country.upper():
                    differences["country"] = {
                        "code": "mismatch",
                        "expected": country_code.upper(),
                        "actual": google_country.upper()
                    }
            else:
                differences["country"] = {
                    "code": "not_found_in_google_response",
                    "expected": country_code.upper(),
                    "actual": None
                }

            # Si hay diferencias, se devuelve un mensaje de error con las discrepancias
            if differences:
                return Response({
                    "valid": False,
                    "code": "not_matching_address",
                    "message": "La dirección no coincide exactamente con los datos proporcionados.",
                    "resolved": {
                        "formatted_address": location.address,
                        "number": google_number,
                        "postal_code": google_zip,
                        "city": google_city,
                        "country": google_country
                    },
                    "differences": differences
                }, status=status.HTTP_400_BAD_REQUEST)

            # Si la dirección coincide, se devuelve la información geográfica
            return Response({
                "valid": True,
                "formatted_address": location.address,
                "latitude": location.latitude,
                "longitude": location.longitude,
                "place_id": location.raw.get("place_id"),
                "number": google_number,
                "postal_code": google_zip,
                "city": google_city,
                "country": google_country
            }, status=status.HTTP_200_OK)

        # Manejo de excepciones específicas de geocodificación
        except (GeocoderTimedOut, GeocoderServiceError) as e:
            return Response({
                "valid": False,
                "message": str(e)
            }, status=status.HTTP_502_BAD_GATEWAY)

        # Manejo de excepciones generales
        except Exception as e:
            return Response({
                "valid": False,
                "message": f"Unexpected error: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    def normalize_text(self, text):
        if not text:
            return ""
        text = unicodedata.normalize("NFKD", text)
        return "".join(c for c in text if not unicodedata.combining(c)).lower().strip()