import uuid

from rest_framework import serializers
from django.db import transaction, IntegrityError

from muaytax.app_sellers.models import Seller, SellerVat
from muaytax.app_address.models import Address
from muaytax.dictionaries.models import Country
from muaytax.app_marketplaces.models.wizard import MarketplaceWizardDraft

from muaytax.dictionaries.serializers import CountrySerializer
from muaytax.app_address.serializers import AddressMainSerializer

class SellerVatSerializer(serializers.ModelSerializer):
    """
    Serializer for the Seller VAT model.
    """
    vat_number = serializers.CharField(required=True, allow_null=False)
    vat_country = serializers.PrimaryKeyRelatedField(
        queryset=Country.objects.all(),
        required=True,
        allow_null=False,
    )
    vat_address = AddressMainSerializer(required=True)

    class Meta:
        model = SellerVat
        fields = [
            # 'seller',
            'id',
            'vat_number',
            'vat_country',
            'vat_address',
            'contracting_date',
            'contracting_discontinue',
            'start_contracting_date',
            'end_contracting_date',
            'activation_date',
            'deactivation_date',
            'is_contracted',
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        """Crea un nuevo Seller VAT."""
        seller = self.context.get('seller')
        wizard_id = self.context.get('draft_wizard_id')

        vat_address_data = validated_data.pop('vat_address')
        vat_country = validated_data.get('vat_country')

        with transaction.atomic():
            # aseguramos que no haya un SellerVat existente para el vendedor y el país de IVA
            if SellerVat.objects.filter(seller=seller, vat_country=vat_country).exists():
                raise serializers.ValidationError({"error": "Ya existe un IVA de el país indicado para este vendedor."})

            vat_address = Address.objects.create(**vat_address_data)

            try:
                seller_vat = SellerVat.objects.create(
                    seller=seller,
                    vat_address=vat_address,
                    **validated_data
                )

                if wizard_id:
                    wizard_uuid = uuid.UUID(wizard_id)
                    wizard_session, created = MarketplaceWizardDraft.objects.get_or_create(
                        seller=seller,
                        wizard_id=wizard_uuid,
                        defaults={"wizard_data": {"seller_vat_ids": [seller_vat.pk]}}
                    )

                    if not created:
                        wizard_data = wizard_session.wizard_data or {}
                        vat_ids = wizard_data.get("seller_vat_ids", [])
                        if seller_vat.pk not in vat_ids:
                            vat_ids.append(seller_vat.pk)
                            wizard_data["seller_vat_ids"] = vat_ids
                            wizard_session.wizard_data = wizard_data
                            wizard_session.save()
            
            except Exception as e:
                vat_address.delete()
                raise serializers.ValidationError({"error": str(e)})

            return seller_vat
    
    def update(self, instance, validated_data):
        address_data = validated_data.pop("vat_address", None)

        if address_data:
            if instance.vat_address:
                address_instance = instance.vat_address
                for attr, value in address_data.items():
                    setattr(address_instance, attr, value)
                address_instance.save()
            else:
                address_instance = Address.objects.create(**address_data)
                instance.vat_address = address_instance

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['vat_country'] = CountrySerializer(instance.vat_country).data
        data['vat_address'] = AddressMainSerializer(instance.vat_address).data if instance.vat_address else None
        return data
