from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, UpdateView, CreateView, DeleteView, View
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.db import IntegrityError, transaction

from muaytax.users.permissions import IsSellerShortnamePermission
from muaytax.app_products.forms.product import ProductAmzChangeForm, ProductAmzDeleteForm
from muaytax.app_products.models.product_amz import ProductAmz
from muaytax.app_sellers.models.seller import Seller


class ProductAmzListView(LoginRequiredMixin, IsSellerShortnamePermission, ListView):
    model = ProductAmz
    template_name_suffix = "_list"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_queryset(self):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        product = ProductAmz.objects.filter(seller_id = seller.id)
        return product
    
    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)        
        context["seller"] = seller
        return context
    
    def handle_no_permission(self):
            return HttpResponseRedirect(reverse("home"))

class ProductAmzDetailView(LoginRequiredMixin, IsSellerShortnamePermission, UpdateView):
    model = ProductAmz
    form_class = ProductAmzChangeForm
    template_name_suffix = "_detail"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        form.instance.asin_absolute = str(seller.pk) + ";" + str(form.instance.asin)
        form.instance.seller = seller
        try:
            with transaction.atomic():
                seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
                form.instance.asin_absolute = str(seller.pk) + ";" + str(form.instance.asin)
                form.instance.seller = seller
                response = super().form_valid(form)
        except IntegrityError:
            form.add_error('asin', 'Este código ASIN ya existe')
            response = self.form_invalid(form)
        return response

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        product = get_object_or_404(ProductAmz, seller_id=seller.id, pk=self.kwargs["pk"])
        context = super().get_context_data(**kwargs)        
        context["seller"] = seller
        return context      

    def get_success_url(self) -> str:
        return reverse(
            "app_products:amz_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ProductAmzNewView(LoginRequiredMixin, IsSellerShortnamePermission, CreateView):
    model = ProductAmz
    form_class = ProductAmzChangeForm
    template_name_suffix = "_detail"

    def form_valid(self, form):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        form.instance.asin_absolute = str(seller.pk) + ";" + str(form.instance.asin)
        form.instance.seller = seller
        try:
            with transaction.atomic():
                seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
                form.instance.asin_absolute = str(seller.pk) + ";" + str(form.instance.asin)
                form.instance.seller = seller
                response = super().form_valid(form)
        except IntegrityError:
            form.add_error('asin', 'Este código ASIN ya existe')
            response = self.form_invalid(form)        
        return response

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        context = super().get_context_data(**kwargs)        
        context["seller"] = seller
        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_products:amz_list",
            args=[self.object.seller.shortname],
        )

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ProductAmzDeleteView(LoginRequiredMixin, IsSellerShortnamePermission, SuccessMessageMixin, DeleteView):
    
    model = ProductAmz
    form_class = ProductAmzDeleteForm
    template_name_suffix = "_delete"
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def get_context_data(self, **kwargs):
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        product = get_object_or_404(ProductAmz, seller_id=seller.id, pk=self.kwargs["pk"])
        context = super().get_context_data(**kwargs)        
        context["seller"] = seller
        return context

    def get_success_url(self) -> str:
        return reverse(
            "app_products:amz_list",
            args=[self.object.seller.shortname],
        )
    
    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ProductAmzUpdateView(LoginRequiredMixin, SuccessMessageMixin, UpdateView):

    model = ProductAmz
    form_class = ProductAmzChangeForm
    success_message = _("Information successfully updated")
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class ProductAmzMassiveUpdateView(LoginRequiredMixin, SuccessMessageMixin, View):

    success_message = _("Information successfully updated")
    slug_url_kwarg = 'shortname'
    slug_field = 'shortname'

    def post(self, request, *args, **kwargs):
        price=request.POST.get('price')
        seller = get_object_or_404(Seller, shortname=self.kwargs["shortname"])
        products = ProductAmz.objects.filter(seller_id=seller.id, price = None)
        products.update(price=price)
        return HttpResponseRedirect(reverse("app_products:amz_list", args=[seller.shortname]))

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))
