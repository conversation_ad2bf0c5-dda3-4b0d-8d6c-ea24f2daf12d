from django.contrib import admin
from muaytax.app_documents.forms.document import DocumentForm

class DocumentAdmin(admin.ModelAdmin):
    form = DocumentForm
    list_display = ["id", "documentType", "year", "sellerVat", "seller", "file","_encrypted_password","expiration_date", "created_at", "modified_at", "privacy", "is_notified_expiration"]
    search_fields = [
        "id",
        "file",
        "documentType__description",
        "year",
        "sellerVat__vat_country__name",
        "sellerVat__vat_number",
        "seller__name",
        "created_at",
        "modified_at",
        "privacy"
    ]
