-- <PERSON><PERSON><PERSON>IN<PERSON> LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_303' ) THEN
	  DROP FUNCTION func_calc_model_es_303(INTEGER,INTEGER,INTEGER,INTEGER);
	  DROP FUNCTION func_calc_model_es_303(INTEGER,INTEGER,INTEGER,INTEGER,NUMERIC,NUMERIC);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_303(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER, prev_ca71 NUMERIC, prev_ca87 NUMERIC)
RETURNS varchar AS $$
DECLARE
    CA01 NUMERIC := 0;
    CA02 NUMERIC := 0;
    CA03 NUMERIC := 0;

    CA04 NUMERIC := 0;
    CA05 NUMERIC := 0;
    CA06 NUMERIC := 0;

    CA07 NUMERIC := 0;
    CA08 NUMERIC := 0;
    CA09 NUMERIC := 0;

    CA10 NUMERIC := 0;
    CA11 NUMERIC := 0;

    CA12 NUMERIC := 0;
    CA13 NUMERIC := 0;

    CA14 NUMERIC := 0;
    CA15 NUMERIC := 0;

    CA16 NUMERIC := 0;
    CA17 NUMERIC := 0;
    CA18 NUMERIC := 0;

    CA19 NUMERIC := 0;
    CA20 NUMERIC := 0;
    CA21 NUMERIC := 0;

    CA22 NUMERIC := 0;
    CA23 NUMERIC := 0;
    CA24 NUMERIC := 0;

    CA25 NUMERIC := 0;
    CA26 NUMERIC := 0;

    CA27 NUMERIC := 0;

    CA28 NUMERIC := 0;
    CA29 NUMERIC := 0;

    CA32 NUMERIC := 0;
    CA33 NUMERIC := 0;

    CA36 NUMERIC := 0;
    CA37 NUMERIC := 0;

    CA40 NUMERIC := 0;
    CA41 NUMERIC := 0;

    CA45 NUMERIC := 0;

    CA46 NUMERIC := 0;

    CA59 NUMERIC := 0;

    CA60 NUMERIC := 0;

    CA120 NUMERIC := 0;

    CA123 NUMERIC := 0;

    CA124 NUMERIC := 0;

    CA64 NUMERIC := 0;

    CA66 NUMERIC := 0;

    CA77 NUMERIC := 0;

    CA110 NUMERIC := 0;

    CA78 NUMERIC := 0;

    CA87 NUMERIC := 0;

    CA69 NUMERIC := 0;

    CA71 NUMERIC := 0;

    CA58 NUMERIC := 0;
    CA76 NUMERIC := 0;
    CA70 NUMERIC := 0;

    short_name VARCHAR := '';
    inv_amount4 RECORD;
    inv_amount10 RECORD;
    inv_amount21 RECORD;
    inv_eqtax05 RECORD;
    inv_eqtax140 RECORD;
    inv_eqtax520 RECORD;
    inv_intra_expenses RECORD;
    inv_intra_sales RECORD;
    inv_extra RECORD;
	inv_local_refund RECORD;
    inv_local_refund_eqtax RECORD;
    inv_local_expenses RECORD;
    inv_import_dua RECORD;
    inv_local_credit RECORD;
    inv_export_sale_refund RECORD;
    inv_sales_marketplace RECORD;
    inv_oss_not_ES RECORD;
    inv_oss_ES RECORD;
	json_result_1 JSONB;
	json_result_2 JSONB;
BEGIN
    -- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

    -- AMOUNT 4%
    SELECT
        ROUND(common_sales.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales.common_amount + amz_sales.amz_amount)::numeric, 2) AS sum_amount
    INTO inv_amount4
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat = '4'
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat = '4'
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    -- AMOUNT 10%
    SELECT
        ROUND(common_sales.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales.common_amount + amz_sales.amz_amount)::numeric, 2) AS sum_amount
    INTO inv_amount10
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat = '10'
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid  AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat = '10'
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    -- AMOUNT 21%
    SELECT
        ROUND(common_sales.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales.common_amount + amz_sales.amz_amount)::numeric, 2) AS sum_amount
    INTO inv_amount21
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat = '21'
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat = '21'
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales;

    -- EQTAX 0.5%
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_sales_vat.common_vat::numeric, 2) AS common_sales_vat,
        ROUND(amz_sales_vat.amz_vat::numeric, 2) AS amz_sales_vat,
        ROUND((common_sales_vat.common_vat + amz_sales_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_eqtax05
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 0.5
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 0.5
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 0.5
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 0.5
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_vat;

    -- EQTAX 1.40%
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_sales_vat.common_vat::numeric, 2) AS common_sales_vat,
        ROUND(amz_sales_vat.amz_vat::numeric, 2) AS amz_sales_vat,
        ROUND((common_sales_vat.common_vat + amz_sales_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_eqtax140
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 1.40
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 1.40
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 1.40
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 1.40
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_vat;

    -- EQTAX 5.2%
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_sales_vat.common_vat::numeric, 2) AS common_sales_vat,
        ROUND(amz_sales_vat.amz_vat::numeric, 2) AS amz_sales_vat,
        ROUND((common_sales_vat.common_vat + amz_sales_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_eqtax520
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 5.20
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 5.20
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 5.20
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax = 5.20
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-sale'
        --AND transaction_type_id NOT LIKE '%-transfer'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_vat;

    -- INTRA-COMMUNITY-EXPENSE + INTRA-COMMUNITY-CREDIT + INBOUND TRANSFER
    SELECT
        ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses_amount,
        ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses_amount,
        ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric, 2) AS sum_amount
    INTO inv_intra_expenses
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id in ('intra-community-expense', 'inbound-transfer', 'intra-community-credit')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id in ('intra-community-expense', 'inbound-transfer', 'intra-community-credit')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses;

    -- INTRA-COMMUNITY-SALE + INTRA-COMMUNITY-REFUND + OUTGOING-TRANSFER
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(outgoing_transfer_sales.amz_amount::numeric, 2) AS outgoing_transfer_sales,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount
    INTO inv_intra_sales
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		LEFT JOIN customers_customer cus ON cus.id = inv.customer_id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND customer_id IS NOT null
        AND status_id = 'revised'
		AND invoice_category_id = 'sales'
        AND tax_country_id = 'ES'
		AND (
			( transaction_type_id in ('intra-community-sale', 'intra-community-refund') ) OR 
			( transaction_type_id = 'outgoing-transfer' ) 
		) 
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
		LEFT JOIN customers_customer cus ON cus.id = inv.customer_id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND inv.customer_id IS NOT null
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND (
			( transaction_type_id in ('intra-community-sale', 'intra-community-refund') ) OR 
			( transaction_type_id = 'outgoing-transfer' ) 
		)  
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND inv.customer_id IS NOT null
        AND status_id = 'revised'
        AND departure_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'outgoing-transfer'	
        AND is_txt_amz IS true
    ) AS outgoing_transfer_sales;

    -- EXTRA-EXPENSES
    SELECT
        ROUND(reverse_charge_invoices.common_amount::numeric, 2) AS reverse_charge_invoices_amount,
        ROUND(no_reverse_charge_common_expenses.common_amount::numeric, 2) AS no_reverse_charge_common_expenses_amount,
        ROUND(no_reverse_charge_common_credit.common_amount::numeric, 2) AS no_reverse_charge_common_credit_amount,
        ROUND(common_expenses.common_amount::numeric, 2) AS common_expenses_amount,
        ROUND(amz_expenses.amz_amount::numeric, 2) AS amz_expenses_amount,
        ROUND((common_expenses.common_amount + amz_expenses.amz_amount)::numeric, 2) AS sum_expenses_amount,
        ROUND(common_credit.common_amount::numeric, 2) AS common_credit_amount,
        ROUND(amz_credit.amz_amount::numeric, 2) AS amz_credit_amount,
        ROUND((common_credit.common_amount + amz_credit.amz_amount)::numeric, 2) AS sum_credit_amount,
        ROUND((amz_expenses.amz_amount + amz_credit.amz_amount + no_reverse_charge_common_credit.common_amount + no_reverse_charge_common_expenses.common_amount + reverse_charge_invoices.common_amount)::numeric, 2) AS sum_amount
    INTO inv_extra
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND invoice_category_id NOT LIKE '%_copy'
		-- AND invoice_category_id = 'expenses'
		-- AND transaction_type_id = 'extra-expense'
		-- AND is_generated_amz IS NOT true
        -- AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND is_reverse_charge IS true
        AND con.is_supplied IS NOT true
    ) AS reverse_charge_invoices,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'extra-expense'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true

    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'extra-expense'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
		AND is_reverse_charge IS NOT true
        AND con.is_supplied IS NOT true
    ) AS no_reverse_charge_common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'extra-expense'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'extra-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'extra-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND is_reverse_charge IS NOT true
        AND con.is_supplied IS NOT true
    ) AS no_reverse_charge_common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'extra-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_credit;


    -- LOCAL REFUND AMOUNT / VAT
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount,
		ROUND(common_sales_vat.common_vat::numeric, 2) AS common_sales_vat,
        ROUND(amz_sales_vat.amz_vat::numeric, 2) AS amz_sales_vat,
        ROUND((common_sales_vat.common_vat + amz_sales_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_local_refund
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id =  sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
	 (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_vat;


    -- LOCAL REFUND EQTAX > 0
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_sales_vat.common_vat::numeric, 2) AS common_sales_vat,
        ROUND(amz_sales_vat.amz_vat::numeric, 2) AS amz_sales_vat,
        ROUND((common_sales_vat.common_vat + amz_sales_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_local_refund_eqtax
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax > 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax > 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax > 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
		AND eqtax > 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id = 'local-refund'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_vat;


    -- LOCAL EXPENSES (AMOUNT + VAT)
    SELECT
        ROUND(common_expenses_amount.common_amount::numeric, 2) AS common_expenses_amount,
        ROUND(amz_expenses_amount.amz_amount::numeric, 2) AS amz_expenses_amount,
        ROUND((common_expenses_amount.common_amount + amz_expenses_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_expenses_vat.common_vat::numeric, 2) AS common_expenses_vat,
        ROUND(amz_expenses_vat.amz_vat::numeric, 2) AS amz_expenses_vat,
        ROUND((common_expenses_vat.common_vat + amz_expenses_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_local_expenses
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        AND invoice_type_id != 'ticket'
		AND transaction_type_id = 'local-expense'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND con.vat IN (4, 5, 10, 21)
    ) AS common_expenses_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
        AND invoice_type_id != 'ticket'
		AND transaction_type_id = 'local-expense'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND con.vat IN (4, 5, 10, 21)
    ) AS amz_expenses_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND con.vat IN (4, 5, 10, 21)
    ) AS common_expenses_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-expense'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
        AND con.vat IN (4, 5, 10, 21)
    ) AS amz_expenses_vat;


    -- IMPORT DUA (AMOUNT + VAT)
    SELECT
        ROUND(common_expenses_amount.common_amount::numeric, 2) AS common_expenses_amount,
        ROUND(amz_expenses_amount.amz_amount::numeric, 2) AS amz_expenses_amount,
        ROUND((common_expenses_amount.common_amount + amz_expenses_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_expenses_vat.common_vat::numeric, 2) AS common_expenses_vat,
        ROUND(amz_expenses_vat.amz_vat::numeric, 2) AS amz_expenses_vat,
        ROUND((common_expenses_vat.common_vat + amz_expenses_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_import_dua
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.vat_euros * con.quantity),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.vat_euros),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'import-dua'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses_vat;

    -- LOCAL CREDIT (AMOUNT + VAT)
    SELECT
        ROUND(common_expenses_amount.common_amount::numeric, 2) AS common_expenses_amount,
        ROUND(amz_expenses_amount.amz_amount::numeric, 2) AS amz_expenses_amount,
        ROUND((common_expenses_amount.common_amount + amz_expenses_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_expenses_vat.common_vat::numeric, 2) AS common_expenses_vat,
        ROUND(amz_expenses_vat.amz_vat::numeric, 2) AS amz_expenses_vat,
        ROUND((common_expenses_vat.common_vat + amz_expenses_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_local_credit
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_expenses_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
		AND vat != 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'expenses'
		AND transaction_type_id = 'local-credit'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_expenses_vat;


    -- EXPORT SALE + EXPORT REFUND
    SELECT
        ROUND(common_sales.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales.common_amount + amz_sales.amz_amount)::numeric, 2) AS sum_amount
    INTO inv_export_sale_refund
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('export-sale', 'export-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('export-sale', 'export-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales;


    -- SALES, VAT=0%, Responsability: Marketplace
    SELECT
        ROUND(common_sales.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales.common_amount + amz_sales.amz_amount)::numeric, 2) AS sum_amount
    INTO inv_sales_marketplace
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND vat = 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
        AND tax_responsibility_id = 'marketplace'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND vat = 0
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
		AND invoice_category_id = 'sales'
        AND tax_responsibility_id = 'marketplace'
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales;


    -- OSS: Tax country != ES
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_sales_vat.common_vat::numeric, 2) AS common_sales_vat,
        ROUND(amz_sales_vat.amz_vat::numeric, 2) AS amz_sales_vat,
        ROUND((common_sales_vat.common_vat + amz_sales_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_oss_not_ES
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid --AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id --AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		--AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid --AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id --AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		--AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid --AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id --AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		--AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid --AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id --AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		--AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_vat;

    -- OSS: Tax country = ES && Departure country != ES
    SELECT
        ROUND(common_sales_amount.common_amount::numeric, 2) AS common_sales_amount,
        ROUND(amz_sales_amount.amz_amount::numeric, 2) AS amz_sales_amount,
        ROUND((common_sales_amount.common_amount + amz_sales_amount.amz_amount)::numeric, 2) AS sum_amount,        
        ROUND(common_sales_vat.common_vat::numeric, 2) AS common_sales_vat,
        ROUND(amz_sales_vat.amz_vat::numeric, 2) AS amz_sales_vat,
        ROUND((common_sales_vat.common_vat + amz_sales_vat.amz_vat)::numeric, 2) AS sum_vat
    INTO inv_oss_ES
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        -- AND departure_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        -- AND departure_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_amount,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity * con.vat / 100),0) as common_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        -- AND departure_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS common_sales_vat,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.vat / 100),0) as amz_vat
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        INNER JOIN sellers_sellervat sv ON sv.seller_id = sellerid AND sv.vat_country_id ='ES'
        INNER JOIN sellers_sellervatactivity sva ON sva.sellervat_id = sv.id AND sva.sellervat_activity_iae_id LIKE 'ES%'
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        -- AND departure_country_id != 'ES'
		AND invoice_category_id = 'sales'
		AND transaction_type_id in ('oss','oss-refund')
		AND is_generated_amz IS NOT true
        AND is_txt_amz IS true
        AND (sva.regime_id != 'surcharge' OR sva.regime_id IS NULL)
		AND inv.iae_id = sva.sellervat_activity_iae_id
        AND con.is_supplied IS NOT true
    ) AS amz_sales_vat;
    

    -- CASILLA 01: total_amount (Base imponible) IVA 4% 
    CA01 := inv_amount4.sum_amount;
    CA01 := ROUND( CA01 , 2 );

    -- CASILLA 02: 4% (IVA)
    CA02 := 4;
    CA02 := ROUND( CA02 , 2 );

    -- CASILLA 03: CA01 * 4%
    CA03 := CA01 * 0.04;
    CA03 := ROUND( CA03 , 2 );

    -- CASILLA 04: total_amount (Base imponible) IVA 10%
    CA04 := inv_amount10.sum_amount;
    CA04 := ROUND( CA04 , 2 );

    -- CASILLA 05: 10% (IVA)
    CA05 := 10;
    CA05 := ROUND( CA05 , 2 );

    -- CASILLA 06: CA04 * 10%
    CA06 := CA04 * 0.10;
    CA06 := ROUND( CA06 , 2 );

    -- CASILLA 07: total_amount (Base imponible) IVA 21%
    CA07 := inv_amount21.sum_amount;
    CA07 := ROUND( CA07 , 2 );

    -- CASILLA 08: 21% (IVA)
    CA08 := 21;
    CA08 := ROUND( CA08 , 2 );

    -- CASILLA 09: CA07 * 21%
    CA09 := CA07 * 0.21;
    CA09 := ROUND( CA09 , 2 );

    -- CASILLA 10: Intra-Community-Expense + Intra-Community-Credit + Inbound Transfer
    CA10 := inv_intra_expenses.sum_amount;
    IF CA10 <= 0 THEN
        CA10 := 0.0;
    END IF;
    CA10 := ROUND( CA10 , 2 );

    --  CASILLA 11: El 21% de la casilla 10
    CA11 := CA10 * 0.21;
    CA11 := ROUND( CA11 , 2 );

    -- CASILLA 12: Extra-Expenses
    CA12 := inv_extra.sum_amount;
    CA12 := ROUND( CA12 , 2 );
    IF CA12 <= 0 THEN
        CA12 := 0.0;
    END IF;

    -- CASILLA 13: El 21% de la casilla 12
    CA13 := CA12 * 0.21;
    CA13 := ROUND( CA13 , 2 );

    -- CASILLA 14: Local-Refund Amount
    CA14 := inv_local_refund.sum_amount;
    CA14 := ROUND( CA14 , 2 );

	 -- CASILLA 15: Local-Refund VAT
    CA15 := inv_local_refund.sum_vat;
    CA15 := ROUND( CA15 , 2 );

    -- CASILLA 16: total_amount (Base imponible) EQTAX 0.5%
    CA16 := inv_eqtax05.sum_amount;
    CA16 := ROUND( CA16 , 2 );

    -- CASILLA 17: 0.5% (IVA)
    CA17 := 0.5;
    CA17 := ROUND( CA17 , 2 );

    -- CASILLA 18: total_vat (IVA) EQTAX 0.5%
    -- CA18 := inv_eqtax05.sum_vat;
    CA18 := CA16 * 0.005;
    CA18 := ROUND( CA18 , 2 );

    -- CASILLA 19: total_amount (Base imponible) EQTAX 1.40%
    CA19 := inv_eqtax140.sum_amount;
    CA19 := ROUND( CA19 , 2 );

    -- CASILLA 20: 1.40% (IVA)
    CA20 := 1.40;
    CA20 := ROUND( CA20 , 2 );

    -- CASILLA 21: total_vat (IVA) EQTAX 1.40%
    -- CA21 := inv_eqtax140.sum_vat;
    CA21 := CA19 * 0.014;
    CA21 := ROUND( CA21 , 2 );

    -- CASILLA 22: total_amount (Base imponible) EQTAX 5.20%
    CA22 := inv_eqtax520.sum_amount;
    CA22 := ROUND( CA22 , 2 );

    -- CASILLA 23: 5.20% (IVA)
    CA23 := 5.20;
    CA23 := ROUND( CA23 , 2 );

    -- CASILLA 24: total_vat (IVA) EQTAX 5.20%
    -- CA24 := inv_eqtax520.sum_vat;
    CA24 := CA22 * 0.052;
    CA24 := ROUND( CA24 , 2 );

    -- CASILLA 25: total_amount (Base imponible) inv_local_refund_eqtax
    CA25 := inv_local_refund_eqtax.sum_amount;
    CA25 := ROUND( CA25 , 2 );

    -- CASILLA 26: total_vat (IVA) inv_local_refund_eqtax
    CA26 := inv_local_refund_eqtax.sum_vat;
    CA26 := ROUND( CA26 , 2 );

    -- CASILLA 27: CA03 + CA06 + CA09 + CA11 + CA13 + CA15 + CA18 + CA21 + CA24 + CA26
    CA27 := CA03 + CA06 + CA09 + CA11 + CA13 + CA15 + CA18 + CA21 + CA24 + CA26;
    CA27 := ROUND( CA27 , 2 );

    -- CASILLA 28: Local Expenses Amount
    CA28 := inv_local_expenses.sum_amount + CA12;
    CA28 := ROUND( CA28 , 2 );

    -- CASILLA 29: Local Expenses VAT
    CA29 := inv_local_expenses.sum_vat + CA13;
    CA29 := ROUND( CA29 , 2 );

    -- CASILLA 32: Import DUA Amount
    CA32 := inv_import_dua.sum_vat / 0.21;
    CA32 := ROUND( CA32 , 2 );

    -- CASILLA 33: Import DUA VAT
    CA33 := inv_import_dua.sum_vat;
    CA33 := ROUND( CA33 , 2 );

    -- CASILLA 36: CA10
    CA36 := CA10;
    CA36 := ROUND( CA36 , 2 );

    -- CASILLA 37: CA11
    CA37 := CA11;
    CA37 := ROUND( CA37 , 2 );

    -- CASILLA 40: Local Credit Amount
    CA40 := inv_local_credit.sum_amount;
    CA40 := ROUND( CA40 , 2 );

    -- CASILLA 41: Local Credit VAT
    CA41 := inv_local_credit.sum_vat;
    CA41 := ROUND( CA41 , 2 );

    -- CASILLA 45: CA29 + CA33 + CA37 + CA41
    CA45 := CA29 + CA33 + CA37 + CA41;
    CA45 := ROUND( CA45 , 2 );

    -- CASILLA 46: CA27 - CA45
    CA46 := CA27 - CA45;
    CA46 := ROUND( CA46 , 2 );

    -- CASILLA 59: INTRA-COMMUNITY-SALE + INTRA-COMMUNITY-REFUND + OUTGOING-TRANSFER IF > 0 ELSE 0
    CA59 := inv_intra_sales.sum_amount;
    IF CA59 <= 0 THEN
        CA59 := 0.0;
    END IF;
    CA59 := ROUND( CA59 , 2 );

    -- CASILLA 60: Export Sale + Export Refund
    CA60 := inv_export_sale_refund.sum_amount;
    CA60 := ROUND( CA60 , 2 );

    -- CASILLA 120: Sales Marketplace (VAT 0%)
    CA120 := inv_sales_marketplace.sum_amount;
    CA120 := ROUND( CA120 , 2 );

    -- CASILA 123: OSS (Tax country != ES)
    CA123 := inv_oss_not_ES.sum_amount;
    CA123 := ROUND( CA123 , 2 );

    -- CASILA 124: OSS (Tax country = ES)
    CA124 := inv_oss_ES.sum_amount;
    CA124 := ROUND( CA124 , 2 );

    -- CASILLA 64: CA46 + CA58 + CA76
    CA64 := CA46 + CA58 + CA76;
    CA64 := ROUND( CA64 , 2 );

    -- CASILLA 66: Lo mismo que en la casilla 64
    CA66 := CA64;
    CA66 := ROUND( CA66 , 2 );

    -- CASILLA 77: 0
    CA77 := 0;
    CA77 := ROUND( CA77 , 2 );

    -- CASILLA 110: Campo 71 Anterior + Campo 87 Anterior
    CA110 := prev_ca71 + prev_ca87;
    CA110 := ROUND( CA110 , 2 );


    -- CASILLA 78
    CA78 := 0;
    IF month_min = 10 AND month_max = 12 THEN
        CA78 := CA110;
    ELSE
        IF CA66 < CA110 THEN
            CA78 := CA66;
            IF CA66 <= 0 THEN
                CA78 := 0.0;
            END IF;
        ELSE
            CA78 := CA110;
        END IF;
    END IF;

    CA78 := ROUND( CA78 , 2 );

    -- CASILLA 87: Casilla 110 - Casilla 78
    CA87 := CA110 - CA78;
    IF CA87 <= 0 THEN
        CA87 := 0.0;
    END IF;
    CA87 := ROUND( CA87 , 2 );

    -- CASILLA 71 = Casilla 66 - Casilla 78
    CA71 := CA66 - CA78;
    CA71 := ROUND( CA71 , 2 );

    -- CASILLA 69 = Casilla 66 - Casilla 78
    CA69 := CA66 - CA78;
    CA69 := ROUND( CA69 , 2 );

    -- CASILLA 71 = Casilla 69 - Casilla 70
    CA71 := CA69 - CA70;
    CA71 := ROUND( CA71 , 2 );


	
	-- ROUND
	-- CA01 := ROUND( CA01 , 2 );
    --CA02 := ROUND( CA02 , 2 );
    --CA03 := ROUND( CA03 , 2 );
    --CA04 := ROUND( CA04 , 2 );
    --CA05 := ROUND( CA05 , 2 );
    --CA06 := ROUND( CA06 , 2 );
    --CA07 := ROUND( CA07 , 2 );
    --CA08 := ROUND( CA08 , 2 );
    --CA09 := ROUND( CA09 , 2 );
	--CA10 := ROUND( CA10 , 2 );
    --CA11 := ROUND( CA11 , 2 );
    --CA12 := ROUND( CA12 , 2 );
    --CA13 := ROUND( CA13 , 2 );
    --CA14 := ROUND( CA14 , 2 );
    --CA15 := ROUND( CA15 , 2 );
    --CA16 := ROUND( CA16 , 2 );
    --CA17 := ROUND( CA17 , 2 );
    --CA18 := ROUND( CA18 , 2 );
    --CA19 := ROUND( CA19 , 2 );
    --CA20 := ROUND( CA20 , 2 );
    --CA21 := ROUND( CA21 , 2 );
    --CA22 := ROUND( CA22 , 2 );
    --CA23 := ROUND( CA23 , 2 );
    --CA24 := ROUND( CA24 , 2 );
    --CA25 := ROUND( CA25 , 2 );
    --CA26 := ROUND( CA26 , 2 );
    --CA27 := ROUND( CA27 , 2 );
    --CA28 := ROUND( CA28 , 2 );
    --CA29 := ROUND( CA29 , 2 );
    --CA32 := ROUND( CA32 , 2 );
    --CA33 := ROUND( CA33 , 2 );
    --CA36 := ROUND( CA36 , 2 );
    --CA37 := ROUND( CA37 , 2 );
    --CA40 := ROUND( CA40 , 2 );
    --CA41 := ROUND( CA41 , 2 );
    --CA45 := ROUND( CA45 , 2 );
    --CA46 := ROUND( CA46 , 2 );
    --CA59 := ROUND( CA59 , 2 );
    --CA60 := ROUND( CA60 , 2 );
    --CA120 := ROUND( CA120 , 2 );
    --CA123 := ROUND( CA123 , 2 );
    --CA124 := ROUND( CA124 , 2 );
    --CA64 := ROUND( CA64 , 2 );
    --CA66 := ROUND( CA66 , 2 );
    --CA77 := ROUND( CA77 , 2 );
    --CA110 := ROUND( CA110 , 2 );
    --CA78 := ROUND( CA78 , 2 );
    --CA87 := ROUND( CA87 , 2 );
    --CA69 := ROUND( CA69 , 2 );
    --CA71 := ROUND( CA71 , 2 );


    -- MAKE JSON (BY PARTS BECAUSE OF LIMIT OF 100 ARGUMENTES IN FUNCTION CALL)
    json_result_1 := json_build_object(
        'Shortname', short_name,
        'inv_amount4', inv_amount4,
        'inv_amount10', inv_amount10,
        'inv_amount21', inv_amount21,
        'inv_eqtax05', inv_eqtax05,
        'inv_eqtax140', inv_eqtax140,
        'inv_eqtax520', inv_eqtax520,
        'inv_intra_expenses', inv_intra_expenses,
        'inv_intra_sales', inv_intra_sales,
        'inv_extra', inv_extra,
        'inv_local_refund', inv_local_refund,
        'inv_local_refund_eqtax', inv_local_refund_eqtax,
        'inv_local_expenses', inv_local_expenses,
        'inv_import_dua', inv_import_dua,
        'inv_local_credit', inv_local_credit,
        'inv_export_sale_refund', inv_export_sale_refund,
        'inv_sales_marketplace', inv_sales_marketplace,
        'inv_oss_not_ES', inv_oss_not_ES,
        'inv_oss_ES', inv_oss_ES,
        'prev_ca71', prev_ca71,
        'prev_ca87', prev_ca87
    );
    
	json_result_2 := json_build_object(
        'CA01', CA01, 'CA02', CA02, 'CA03', CA03,
        'CA04', CA04, 'CA05', CA05, 'CA06', CA06,
        'CA07', CA07, 'CA08', CA08, 'CA09', CA09,
        'CA10', CA10, 'CA11', CA11, 
        'CA12', CA12, 'CA13', CA13, 
        'CA14', CA14, 'CA15', CA15,
        'CA16', CA16, 'CA17', CA17, 'CA18', CA18,
        'CA19', CA19, 'CA20', CA20, 'CA21', CA21,
        'CA22', CA22, 'CA23', CA23, 'CA24', CA24,
        'CA25', CA25, 'CA26', CA26, 
        'CA27', CA27,
        'CA28', CA28, 'CA29', CA29,
        'CA32', CA32, 'CA33', CA33,
        'CA36', CA36, 'CA37', CA37,
        'CA40', CA40, 'CA41', CA41,
        'CA45', CA45, 
        'CA46', CA46,
        'CA59', CA59,
        'CA60', CA60,
        'CA120', CA120,
        'CA123', CA123,
        'CA124', CA124,
        'CA64', CA64,
        'CA66', CA66,
        'CA77', CA77,
        'CA110', CA110,
        'CA78', CA78,
        'CA87', CA87,
        'CA69', CA69,
        'CA71', CA71
    ); -- THIS JSON IS 100 ARGUMENTS EXACTLY
	
	-- RETURN JSON
	RETURN jsonb_concat(json_result_1, json_result_2)::varchar;
END;
$$ LANGUAGE plpgsql;

-- CREAR / REMPLAZAR FUNCION (SIN CASILLAS PREVIAS)
CREATE OR REPLACE FUNCTION func_calc_model_es_303(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
BEGIN
	RETURN (SELECT func_calc_model_es_303(sellerid, date_year, month_min, month_max, 0, 0)::varchar);
END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION
-- SELECT func_calc_model_es_303(5, 2023, 01, 09);
-- SELECT func_calc_model_es_303(5, 2023, 01, 09, 0, 0);
-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_303(INTEGER,INTEGER,INTEGER,INTEGER);
-- DROP FUNCTION func_calc_model_es_303(INTEGER,INTEGER,INTEGER,INTEGER,NUMERIC,NUMERIC);