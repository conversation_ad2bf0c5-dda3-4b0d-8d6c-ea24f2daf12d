DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_sellervat_list_json') THEN
        DROP FUNCTION func_sellervat_list_json(type_list VARCHAR);
    END IF;
END $$;
CREATE OR REPLACE FUNCTION func_sellervat_list_json(type_list VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
	legal_entity VARCHAR;
    result_json jsonb := '[]'; -- Inicializamos como una lista JSON vacía
	process VARCHAR;
BEGIN

	
    FOR inv_data IN
		SELECT
		s.id AS seller_id,
		s.shortname AS shortname,
		sv.status_process_color_id AS color_process,
		sv.status_priority,
		sv.id AS sellervat_id,
		s.name AS seller_name,
		u.name AS user_name,
		s.legal_entity As legal_entity,
		dic_country.name AS country,
		sv.vat_number AS vat_number,
		dic_type.description AS type,
		dic_vat_status.description AS vat_status,
		dic_status_process.description AS status_process,
		dic_es_status_activation.description AS es_status_activation,
		dic_es_status_altaiae.description AS es_status_altaiae,
		dic_es_estatus_cdigital.description AS es_status_cdigital,
		dic_es_status_vies.description AS es_status_vies,
		dic_es_status_eori.description AS es_status_eori,
		sv.contracting_date AS contracting_date,
		sv.total_promedy_days AS promedy_days,
		sv.activation_date AS activation_date

		FROM
			sellers_seller s
		LEFT JOIN
			users_user u ON S.user_id = u.id
		LEFT JOIN
			sellers_sellervat sv ON s.id = sv.seller_id
		LEFT JOIN
			dictionaries_country dic_country ON sv.vat_country_id = dic_country.iso_code
		LEFT JOIN
			dictionaries_sellervattype dic_type ON sv.type_id = dic_type.code
		LEFT JOIN
			dictionaries_sellervatstatus dic_vat_status ON sv.vat_status_id = dic_vat_status.code
		LEFT JOIN
			dictionaries_sellervatstatusprocess dic_status_process ON sv.status_process_id = dic_status_process.code
		LEFT JOIN
			dictionaries_sellervatesstatusactivation dic_es_status_activation ON sv.es_status_activation_id = dic_es_status_activation.code
		LEFT JOIN
			dictionaries_sellervatesstatusaltaiae dic_es_status_altaiae ON sv.es_status_altaiae_id =  dic_es_status_altaiae.code
		LEFT JOIN
			 dictionaries_sellervatesstatuscdigital dic_es_estatus_cdigital ON sv.es_status_cdigital_id = dic_es_estatus_cdigital.code
		LEFT JOIN
			 dictionaries_sellervatesstatusvies dic_es_status_vies ON sv.es_status_vies_id = dic_es_status_vies.code
		LEFT JOIN
			 dictionaries_sellervatesstatuseori dic_es_status_eori ON sv.es_status_eori_id = dic_es_status_eori.code
		WHERE
			CASE 
				WHEN type_list = 'all' THEN 
					NOT (
						(s.legal_entity = 'llc' AND sv.vat_country_id = 'US') OR
						(s.legal_entity = 'sl' AND sv.vat_country_id= 'ES') OR
						(s.legal_entity = 'self-employed' AND sv.vat_country_id = 'ES') OR
						(sv.is_contracted = 'false')

					)
				ELSE
		
					NOT (
						(s.legal_entity = 'llc' AND sv.vat_country_id = 'US') OR
						(s.legal_entity = 'sl' AND sv.vat_country_id= 'ES') OR
						(s.legal_entity = 'self-employed' AND sv.vat_country_id = 'ES') OR
						(sv.is_contracted = 'false')
					) AND (CASE
							WHEN type_list = 'active' THEN sv.vat_status_id = 'processing'
							WHEN type_list = 'finish' THEN sv.vat_status_id = 'on'
							ELSE FALSE
						  END)
				END
	LOOP
		
		IF inv_data.legal_entity = 'llc' THEN legal_entity = 'LLC';
		ELSIF inv_data.legal_entity = 'sl' THEN legal_entity = 'SL';
		ELSIF inv_data.legal_entity = 'self-employed' THEN legal_entity = 'Autónomo';
		ELSE legal_entity = '';
		END IF;
		
	
	result_json := result_json || jsonb_build_object(
		'seller_id', inv_data.seller_id,
		'sellervat_id', inv_data.sellervat_id,
		'shortname', inv_data.shortname,
		'color_process', inv_data.color_process,
		'status_priority', inv_data.status_priority,
		'name', inv_data.seller_name,
		'user_name', inv_data.user_name,
		'legal_entity', legal_entity,
		'country', inv_data.country,
		'vat_numer', inv_data.vat_number,
		'type', inv_data.type,
		'vat_status', inv_data.vat_status,
		'status_process', inv_data.status_process,
		'es_status_activation', inv_data.es_status_activation,
		'es_status_alta_iae', inv_data.es_status_altaiae,
		'es_status_cdigital', inv_data.es_status_cdigital,
		'es_status_vies', inv_data.es_status_vies,
		'es_status_eori', inv_data.es_status_eori,
		'contracting_date', inv_data.contracting_date,
		'promedy_days', inv_data.promedy_days,
		'activation_date', inv_data.activation_date
	);
	
	END LOOP;
	
	RETURN result_json;
END;
$$ LANGUAGE plpgsql;