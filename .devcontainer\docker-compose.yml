version: '3'

volumes:
  muaytax_local_postgres_data: {}
  muaytax_local_postgres_data_backups: {}
  redis_data: {}

services:
  django:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    image: muaytax_local_django_dev
    restart: on-failure:5
    container_name: dev_muaytax_local_django
    platform: linux/x86_64
    depends_on:
      - postgres
      - redis
    volumes:
      - ..:/app:z
      - ../muaytax/media:/app/muaytax/media:z
    env_file:
      - ../.envs/.local/.celery
      - ../.envs/.local/.django
      - ../.envs/.local/.postgres
    ports:
      - "8000:8000"
    command: sleep infinity

  redis:
    build:
      context: ..
      dockerfile: ./compose/local/redis/Dockerfile
    restart: on-failure:5
    container_name: dev_redis_local
    volumes:
      - ..:/app:z
      - ../muaytax/media:/app/muaytax/media:z
      - redis_data:/data
    env_file:
      - ../.envs/.local/.celery
    command: ["/bin/sh", "/start-redis"]

  postgres:
    build:
      context: ..
      dockerfile: ./compose/production/postgres/Dockerfile
    image: muaytax_production_postgres
    shm_size: '1g'
    restart: on-failure:5
    container_name: dev_muaytax_local_postgres
    volumes:
      - muaytax_local_postgres_data:/var/lib/postgresql/data:Z
      - muaytax_local_postgres_data_backups:/backups:z
    env_file:
      - ../.envs/.local/.postgres
    ports:
      - "5432:5432"
