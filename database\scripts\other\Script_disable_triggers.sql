-----------------------------------------------------------------------------------------------------------------------

-- Al intentar hacer la migración es probable que la consola nos devuelva un error tipo:
-- "psycopg2.errors.ObjectInUse: cannot ALTER TABLE "invoices_invoice" because it has pending trigger events"

-- Ocurre cuando intentas realizar una operación de modificación (ALTER TABLE) en una tabla en PostgreSQL, 
-- pero hay eventos de desencadenadores (triggers) pendientes asociados a esa tabla

-- POSIBLE SOLUCIÓN DESACTIVAR TEMPORALMENTE LOS TRIGGERS DE ESA TABLA
ALTER TABLE "invoices_invoice" DISABLE TRIGGER ALL;

-- << HACER LA MIGRACIÓN >>

--REACTIVAR LOS TRIGGERS
ALTER TABLE "invoices_invoice" ENABLE TRIGGER ALL;

--LISTAR LOS TRIGGERS ASOCIADOS A LA TABLA
SELECT tgname AS trigger_name, tgrelid::regclass AS table_name, tgenabled
FROM pg_trigger
WHERE tgrelid = 'invoices_invoice'::regclass;

-- Si la columna "tgenabled" devuelve O es que el trigger está habilitado
-- Si la columna "tgenabled" devuelve D es que el trigger está deshabilitado