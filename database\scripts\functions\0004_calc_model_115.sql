-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_115' ) THEN
	  DROP FUNCTION func_calc_model_es_115(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_115(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE
    CA01 NUMERIC := 0;
    CA02 NUMERIC := 0;
    CA03 NUMERIC := 0;
    CA04 NUMERIC := 0;
    CA05 NUMERIC := 0;
    alquileres_id VARCHAR := '621';
    short_name VARCHAR := '';
    total_providers INTEGER := 0;
    inv_amount RECORD;
    inv_irpf RECORD;
BEGIN
    -- <PERSON><PERSON>NAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;

    -- TOTAL PROVIDERS
    SELECT COUNT(DISTINCT inv.provider_id) as total_providers
    INTO total_providers
    FROM invoices_invoice inv
    INNER JOIN invoices_concept con ON con.invoice_id = inv.id
    WHERE inv.seller_id = sellerid
    AND EXTRACT(YEAR FROM accounting_date) = date_year
    AND EXTRACT(MONTH FROM accounting_date) >= month_min
    AND EXTRACT(MONTH FROM accounting_date) <= month_max
    AND status_id = 'revised'
    AND tax_country_id = 'ES'
    AND (transaction_type_id LIKE 'local-expense' OR transaction_type_id LIKE 'local-credit')
    AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
    AND con.irpf > 0
    AND con.is_supplied IS NOT true
    AND inv.account_expenses_id = alquileres_id;

    -- AMOUNT
    SELECT
        common_expenses.common_amount AS common_expenses_amount,
        amz_expenses.amz_amount AS amz_expenses_amount,
        common_credit.common_amount AS common_credit_amount,
        amz_credit.amz_amount AS amz_credit_amount,
        common_expenses.common_amount + amz_expenses.amz_amount + common_credit.common_amount + amz_credit.amz_amount AS sum_amount
    INTO inv_amount
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND is_generated_amz IS NOT true
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_credit;

    -- IRPF
    SELECT
        common_expenses.common_irpf AS common_expenses_irpf,
        amz_expenses.amz_irpf AS amz_expenses_irpf,
        common_credit.common_irpf AS common_credit_irpf,
        amz_credit.amz_irpf AS amz_credit_irpf,
        common_expenses.common_irpf + amz_expenses.amz_irpf + common_credit.common_irpf + amz_credit.amz_irpf AS sum_irpf
    INTO inv_irpf
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) as common_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.irpf / 100),0) as amz_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) as common_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.irpf / 100),0) as amz_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND inv.account_expenses_id = alquileres_id
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_credit;
    

    -- CASILLA 01: total providers distinct (Nº Proveedores distintos)
    CA01 := total_providers;
    CA01 := CA01;

    -- CASILLA 02: total_amount (Base imponible)
    CA02 := inv_amount.sum_amount;
    CA02 := ROUND( CA02 , 2 );

    -- CASILLA 03: total_irpf (IRPF)
    CA03 := inv_irpf.sum_irpf;
    CA03 := ROUND( CA03 , 2 );

    -- CASILLA 04: Resultado a ingresar de la anterior o anteriores declaraciones del mismo concepto, ejercicio y período
    CA04 := 0;
    CA04 := ROUND( CA04 , 2 );

    -- CASILLA 05: Casilla 03 + Casilla 04
    CA05 := CA03 - CA04;
    CA05 := ROUND( CA05 , 2 );
	
	-- ROUND
	--CA01 := ROUND( CA01 , 2 );
    --CA02 := ROUND( CA02 , 2 );
    --CA03 := ROUND( CA03 , 2 );
	--CA04 := ROUND( CA04 , 2 );
    --CA05 := ROUND( CA05 , 2 );

    -- RETURN JSON
	RETURN json_build_object(
        'Shortname', short_name, 'CA01', CA01, 'CA02', CA02, 'CA03', CA03, 'CA04', CA04, 'CA05', CA05
    )::varchar;
END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION
-- SELECT func_calc_model_es_115(230, 2023, 01, 06);

-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_115(INTEGER,INTEGER,INTEGER,INTEGER);