# 📄 Documentación Técnica – Include `company_info_form.html`

Este bloque representa el subformulario de Información de la Empresa dentro del formulario principal `formIVA`.

---

## 1. Estructura General

El formulario está dividido en dos secciones:

### Sección 1: Datos generales
- `entity_name`: Nombre de la entidad.
- `country_of_incorporation` y `entity_type`: País y tipo de entidad.
- `company_address`: Subformulario anidado con campos de dirección.
- `phone_country` y `phone_national`: Campo de teléfono dividido (prefijo y número nacional).

### Sección 2: Documentos de empresa
- `nif_registration`: Número fiscal del país de constitución.
- `country_incorporation_tax_number`: Archivo del NIF/EIN.
- `bank_certificate`: Certificado bancario.
- `incorporation_entity_certificate`: Documento de constitución.
- `entity_extract_updated`: Extracto actualizado.
- `amazon_sell` y `amazon_vies_screenshot`: Lógica condicional para ventas en Amazon.

---

## 2. Etiquetas dinámicas y tooltips

Se usan `label_with_tooltip.html` con los parámetros:
- `id`: ID del label (clave para contexto).
- `is_required`: Campo obligatorio.
- `has_help_tooltip`: Si debe mostrar tooltip.
- `text="__dynamic__"`: Si la etiqueta debe variar por entidad/país.

---

## ⚙️ 3. JSON de contexto (cargado desde el backend)

```html
{{ company_info_data|json_script:"json-company-info" }}
{{ all_product_services_json|json_script:"json-all-options" }}
{{ validation_company_info|json_script:"json-validation" }}
{{ context_label_company_info|json_script:"json-context-label-company-info" }}
```

---

## 4. Módulo JavaScript – `window.CompanyFormModule`

Define dos objetos:

### `methods` (lógica pura)
- `filterProductServices(...)`: Filtra productos según tipo de actividad.
- `initProductField(...)`: Inicializa campo `products_and_services`.
- `initializePhoneFromAddress(...)`: Extrae número nacional del teléfono.

### `handlers` (eventos y observadores)
- `observeAddressCountryForPhonePrefix(...)`: Sincroniza país de dirección y prefijo.
- `handleActivityTypeChange(...)`: Evento para seleccionar productos.
- `handleImageModalLinks(...)`: Muestra imagen modal de ejemplo Amazon.
- `handleChange(...)`: Observador reactivo para etiquetas dinámicas.
- `addActivityTypeChangeListener(...)`: Listener de cambio de tipo de actividad.

---

## 5. Modal de ejemplo Amazon

Incluye un modal estático con `#modal-example_amazon_vies_screenshot` para mostrar una captura visual como ayuda al usuario.

---

## 6. Reactividad y sincronización

- El prefijo del teléfono (`phone_country`) se autocompleta desde `address_country` si está vacío.
- Si el número (`contact_phone`) ya incluye el prefijo, se extrae automáticamente el número nacional.
- El campo `amazon_vies_screenshot` se muestra solo si `amazon_sell = True`.

---

## 7. Buenas prácticas aplicadas

| Aspecto        | Implementación                                                   |
|----------------|------------------------------------------------------------------|
| Modularidad    | Todos los campos separados en `form-group` o `row`.             |
| Tooltips       | Soporte dinámico y estático con contexto JSON.                  |
| JS encapsulado | `CompanyFormModule` bien separado por responsabilidad.          |
| Validación     | Soporte visual de errores y campos requeridos.                  |
| UX             | Modal de ayuda visual, autocompletado de teléfono y filtros.    |

---
