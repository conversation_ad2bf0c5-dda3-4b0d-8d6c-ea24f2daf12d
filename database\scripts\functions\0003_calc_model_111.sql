-- ELIMINAR LAS FUNCIONES SI EXISTEN
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_calc_model_es_111' ) THEN
	  DROP FUNCTION func_calc_model_es_111(INTEGER,INTEGER,INTEGER,INTEGER);
	END IF;
END $$;

-- CREAR / REMPLAZAR FUNCION (COMPLETO)
CREATE OR REPLACE FUNCTION func_calc_model_es_111(sellerid INTEGER, date_year INTEGER, month_min INTEGER, month_max INTEGER)
RETURNS varchar AS $$
DECLARE
    CA01 NUMERIC := 0;
    CA02 NUMERIC := 0;
    CA03 NUMERIC := 0;
    CA04 NUMERIC := 0;
    CA05 NUMERIC := 0;
    CA06 NUMERIC := 0;
    CA07 NUMERIC := 0;
    CA08 NUMERIC := 0;
    CA09 NUMERIC := 0;
    CA12 NUMERIC := 0;
    CA15 NUMERIC := 0;
    CA18 NUMERIC := 0;
    CA21 NUMERIC := 0;
    CA24 NUMERIC := 0;
    CA27 NUMERIC := 0;
    CA28 NUMERIC := 0;
    CA29 NUMERIC := 0;
    CA30 NUMERIC := 0;
    alquileres_id VARCHAR := '621';
    short_name VARCHAR := '';
    total_providers INTEGER := 0;
    inv_worker RECORD;
    inv_amount RECORD;
    inv_irpf RECORD;
BEGIN
    -- ASIGNAR VALORES A LAS VARIABLES 'shortname'
    SELECT shortname INTO short_name FROM sellers_seller WHERE id = sellerid;
    
    --<< NOMINAS >>--
    SELECT DISTINCT
        SUM(DISTINCT combined.worker_id) as total_workers,
        SUM(DISTINCT combined.worker_with_salary_in_kind) as total_workers_with_salary_in_kind,
		SUM(ROUND(COALESCE(combined.amount_euros,0)::numeric ,2)) AS sum_amount,
		SUM(ROUND(COALESCE(combined.irpf,0)::numeric ,2)) AS sum_irpf,
        SUM(ROUND(COALESCE(combined.salary_in_kind,0)::numeric ,2)) AS sum_salary_in_kind,
        SUM(ROUND(COALESCE(combined.irpf_salary_in_kind,0)::numeric ,2)) AS sum_irpf_salary_in_kind
        INTO inv_worker
		FROM(
			SELECT DISTINCT
			COALESCE(SUM(con.amount_euros * con.quantity),0) as amount_euros,
			COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) AS irpf,
            COALESCE(SUM(inv_work.salary_in_kind),0) as salary_in_kind,
            COALESCE(SUM(inv_work.salary_in_kind * inv_work.percentage_irpf / 100),0) as irpf_salary_in_kind,
			COUNT(DISTINCT inv_work.worker_id) AS worker_id,
            COUNT(DISTINCT CASE WHEN inv_work.salary_in_kind != 0 THEN inv_work.worker_id END) AS worker_with_salary_in_kind
			FROM invoices_invoice inv
			INNER JOIN invoices_payrollworkerinvoice inv_work ON inv_work.invoice_id = inv.id
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			LEFT JOIN workers_worker work ON work.id = inv_work.worker_id
			WHERE inv.seller_id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND status_id = 'revised'
			AND tax_country_id = 'ES'
			AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
			AND inv.invoice_type_id = 'payroll'
			AND con.irpf > 0
			AND is_generated_amz IS NOT true
			AND is_txt_amz IS NOT true
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND con.is_supplied IS NOT true
		UNION
			SELECT DISTINCT
			COALESCE(SUM(con.amount_euros * con.quantity),0) as amount_euros,
			COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) AS irpf,
            COALESCE(SUM(inv_admin.salary_in_kind),0) as salary_in_kind,
            COALESCE(SUM(inv_admin.salary_in_kind * inv_admin.percentage_irpf / 100),0) as irpf_salary_in_kind,
			COUNT(DISTINCT inv_admin.worker_id) AS worker_id,
            COUNT(DISTINCT CASE WHEN inv_admin.salary_in_kind != 0 THEN inv_admin.worker_id END) AS worker_with_salary_in_kind
			FROM invoices_invoice inv
			INNER JOIN invoices_payrolladministratorinvoice inv_admin ON inv_admin.invoice_id = inv.id
			INNER JOIN invoices_concept con ON con.invoice_id = inv.id
			LEFT JOIN workers_worker work ON work.id = inv_admin.worker_id
			WHERE inv.seller_id = sellerid
			AND EXTRACT(YEAR FROM accounting_date) = date_year
			AND EXTRACT(MONTH FROM accounting_date) >= month_min
			AND EXTRACT(MONTH FROM accounting_date) <= month_max
			AND status_id = 'revised'
			AND tax_country_id = 'ES'
			AND (transaction_type_id = 'local-expense' OR transaction_type_id = 'local-refund')
			AND inv.invoice_type_id = 'payroll'
			AND con.irpf > 0
			AND is_generated_amz IS NOT true
			AND is_txt_amz IS NOT true
			AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL)
            AND con.is_supplied IS NOT true
	) AS combined;






    --<< NOMINAS >>--

    -- TOTAL PROVIDERS
    SELECT COUNT(DISTINCT inv.provider_id) as total_providers
    INTO total_providers
    FROM invoices_invoice inv
    INNER JOIN invoices_concept con ON con.invoice_id = inv.id
    WHERE inv.seller_id = sellerid
    AND EXTRACT(YEAR FROM accounting_date) = date_year
    AND EXTRACT(MONTH FROM accounting_date) >= month_min
    AND EXTRACT(MONTH FROM accounting_date) <= month_max
    AND status_id = 'revised'
    AND tax_country_id = 'ES'
    AND (transaction_type_id LIKE 'local-expense' OR transaction_type_id LIKE 'local-credit')
    AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
    AND con.irpf > 0
    AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
    AND con.is_supplied IS NOT true
    AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL);

    -- AMOUNT
    SELECT
        common_expenses.common_amount AS common_expenses_amount,
        amz_expenses.amz_amount AS amz_expenses_amount,
        common_credit.common_amount AS common_credit_amount,
        amz_credit.amz_amount AS amz_credit_amount,
        common_expenses.common_amount + amz_expenses.amz_amount + common_credit.common_amount + amz_credit.amz_amount AS sum_amount
    INTO inv_amount
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.quantity),0) as common_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros),0) as amz_amount
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_credit;

    -- IRPF
    SELECT
        common_expenses.common_irpf AS common_expenses_irpf,
        amz_expenses.amz_irpf AS amz_expenses_irpf,
        common_credit.common_irpf AS common_credit_irpf,
        amz_credit.amz_irpf AS amz_credit_irpf,
        common_expenses.common_irpf + amz_expenses.amz_irpf + common_credit.common_irpf + amz_credit.amz_irpf AS sum_irpf
    INTO inv_irpf
    FROM
    (
        SELECT DISTINCT COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) as common_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.irpf / 100),0) as amz_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-expense'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_expenses,
    (
        SELECT DISTINCT COALESCE(SUM(con.quantity * con.amount_euros * con.irpf / 100),0) as common_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_generated_amz IS NOT true
        AND is_txt_amz IS NOT true
        AND con.is_supplied IS NOT true
    ) AS common_credit,
    (
        SELECT DISTINCT COALESCE(SUM(con.amount_euros * con.irpf / 100),0) as amz_irpf
        FROM invoices_invoice inv
        INNER JOIN invoices_concept con ON con.invoice_id = inv.id
        WHERE inv.seller_id = sellerid
        AND EXTRACT(YEAR FROM accounting_date) = date_year
        AND EXTRACT(MONTH FROM accounting_date) >= month_min
        AND EXTRACT(MONTH FROM accounting_date) <= month_max
        AND status_id = 'revised'
        AND tax_country_id = 'ES'
        AND transaction_type_id LIKE 'local-credit'
        AND con.irpf > 0
        AND (inv.account_expenses_id != alquileres_id OR inv.account_expenses_id is NULL)
        AND (NOT(invoice_type_id = 'payroll') OR invoice_type_id IS NULL)
        AND (NOT (invoice_category_id = 'expenses_copy' OR invoice_category_id = 'sales_copy') OR invoice_category_id IS NULL) 
        AND is_txt_amz IS true
        AND con.is_supplied IS NOT true
    ) AS amz_credit;
    
    --CASILLA 01: cantidad de trabajadores con nominas
    CA01 := inv_worker.total_workers;

    --CASILLA 02: total_amount (nóminas)
    CA02 := inv_worker.sum_amount;
    CA02 := ROUND( CA02 , 2 );

    --CASILLA 03: total_irpf (nóminas)
    CA03 := inv_worker.sum_irpf;
    CA03 := ROUND( CA03 , 2 );

    --CASILLA 04: total trabajadores con nominas que continene salario en especie != 0 (nóminas)
    CA04 := inv_worker.total_workers_with_salary_in_kind;

    --CASILLA 05: total base salario en especie (nóminas)
    CA05 := inv_worker.sum_salary_in_kind;
    CA05 := ROUND( CA05 , 2 );

    --CASILLA 06: total irpf salario en especie (nóminas)
    CA06 := inv_worker.sum_irpf_salary_in_kind;
    CA06 := ROUND( CA06 , 2 );

    -- CASILLA 07: total providers distinct (Nº Proveedores distintos)
    CA07 := total_providers;
    CA07 := CA07;

    -- CASILLA 08: total_amount (Base imponible)
    CA08 := inv_amount.sum_amount;
    CA08 := ROUND( CA08 , 2 );

    -- CASILLA 09: total_irpf (IRPF)
    CA09 := inv_irpf.sum_irpf;
    CA09 := ROUND( CA09 , 2 );

    -- CASILLA 28: Casilla 3 + Casilla 6 + Casilla 9 + Casilla 12 + Casilla 15 + Casilla 18 + Casilla 21 + Casilla 24 + Casilla 27
    CA28 := CA03 + CA06 + CA09 + CA12 + CA15 + CA18 + CA21 + CA24 + CA27;
    CA28 := ROUND( CA28 , 2 );
    -- CASILLA 30: Casilla 28 - Casilla 29
    CA29 := ROUND( CA29 , 2 );
    CA30 := CA28 - CA29;
    CA30 := ROUND( CA30 , 2 );
	
	-- ROUND
	--CA07 := ROUND( CA07 , 2 );
    --CA08 := ROUND( CA08 , 2 );
    --CA09 := ROUND( CA09 , 2 );
	--CA28 := ROUND( CA28 , 2 );
    --CA29 := ROUND( CA29 , 2 );
	CA30 := ROUND( CA30 , 2 );

    -- RETURN 'Updated ' || counter || ' invoices ' || ' | self_employed: ' || seller_self_employed || ' - eqtax: ' || seller_eqtax;
	RETURN json_build_object(
        'Shortname', short_name,
        'CA01', CA01, 'CA02', CA02, 'CA03', CA03,'CA04', CA04,  'CA05', CA05,
        'CA06', CA06, 'CA07', CA07, 'CA08', CA08, 'CA09', CA09, 'CA10', '',
        'CA11', '', 'CA12', '', 'CA13', '', 'CA14', '', 'CA15', '',
        'CA16', '', 'CA17', '', 'CA18', '', 'CA19', '', 'CA20', '',
        'CA21', '', 'CA22', '', 'CA23', '', 'CA24', '', 'CA25', '',
        'CA26', '', 'CA27', '', 'CA28', CA28, 'CA29', CA29, 'CA30', CA30
    )::varchar;
END;
$$ LANGUAGE plpgsql;

-- USAR LA FUNCION
-- SELECT func_calc_model_es_111(241, 2023, 01, 06);

-- BORRAR FUNCION
-- DROP FUNCTION func_calc_model_es_111(INTEGER,INTEGER,INTEGER,INTEGER);