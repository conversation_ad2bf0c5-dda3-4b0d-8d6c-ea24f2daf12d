-- Borra referencias en banks_reconciliation primero:
DELETE FROM banks_reconciliation
WHERE movement_id IN (
    SELECT id FROM banks_movement
    WHERE bank_id = 139
    AND created_at >= '2025-06-03 08:30:00+00:00'
    AND created_at < '2025-06-03 08:32:00+00:00'
)
OR movement_transfer_id IN (
    SELECT id FROM banks_movement
    WHERE bank_id = 139
    AND created_at >= '2025-06-03 08:30:00+00:00'
    AND created_at < '2025-06-03 08:32:00+00:00'
);

--<PERSON>pu<PERSON> borra los movimientos originales en banks_movement:
DELETE FROM banks_movement
WHERE bank_id = 139
AND created_at >= '2025-06-03 08:30:00+00:00'
AND created_at < '2025-06-03 08:32:00+00:00';

--Finalmente borra el registro del importador:
DELETE FROM importers_bankmovementsimporter WHERE id = 846;
